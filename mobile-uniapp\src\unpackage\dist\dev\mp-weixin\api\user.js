"use strict";
const utils_request = require("../utils/request.js");
const login = (params) => {
  return utils_request.request.post("/auth/login", params);
};
const register = (params) => {
  return utils_request.request.post("/auth/register", params);
};
const logout = () => {
  return utils_request.request.post("/auth/logout");
};
const refreshToken = (refreshToken2) => {
  return utils_request.request.post("/auth/refresh", { refreshToken: refreshToken2 });
};
const getUserInfo = () => {
  return utils_request.request.get("/user/profile");
};
const updateUserInfo = (params) => {
  return utils_request.request.put("/user/profile", params);
};
const uploadAvatar = (file) => {
  const formData = new FormData();
  formData.append("avatar", file);
  return utils_request.request.post("/user/avatar", formData, {
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};
const changePassword = (params) => {
  return utils_request.request.put("/user/password", params);
};
const getUserStats = () => {
  return utils_request.request.get("/user/stats");
};
const getUserSettings = () => {
  return utils_request.request.get("/user/settings");
};
const updateUserSettings = (settings) => {
  return utils_request.request.put("/user/settings", settings);
};
const sendVerificationCode = (params) => {
  return utils_request.request.post("/auth/verification-code", params);
};
const verifyCode = (params) => {
  return utils_request.request.post("/auth/verify-code", params);
};
const resetPassword = (params) => {
  return utils_request.request.post("/auth/reset-password", params);
};
const bindContact = (params) => {
  return utils_request.request.post("/user/bind-contact", params);
};
const unbindContact = (params) => {
  return utils_request.request.post("/user/unbind-contact", params);
};
const getUserHistory = (params) => {
  return utils_request.request.get("/user/history", { params });
};
const deleteHistory = (ids) => {
  return utils_request.request.delete("/user/history", { data: { ids } });
};
const clearHistory = (type) => {
  return utils_request.request.delete("/user/history/clear", { data: { type } });
};
const getUserFavorites = (params) => {
  return utils_request.request.get("/user/favorites", { params });
};
const addFavorite = (params) => {
  return utils_request.request.post("/user/favorites", params);
};
const removeFavorite = (id) => {
  return utils_request.request.delete(`/user/favorites/${id}`);
};
const checkFavorite = (targetId, type) => {
  return utils_request.request.get("/user/favorites/check", { params: { targetId, type } });
};
const userApi = {
  login,
  register,
  logout,
  refreshToken,
  getUserInfo,
  updateUserInfo,
  uploadAvatar,
  changePassword,
  getUserStats,
  getUserSettings,
  updateUserSettings,
  sendVerificationCode,
  verifyCode,
  resetPassword,
  bindContact,
  unbindContact,
  getUserHistory,
  deleteHistory,
  clearHistory,
  getUserFavorites,
  addFavorite,
  removeFavorite,
  checkFavorite
};
exports.userApi = userApi;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/user.js.map
