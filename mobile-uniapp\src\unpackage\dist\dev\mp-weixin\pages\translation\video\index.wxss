/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 导入自定义样式变量 */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
.video-translation-page.data-v-2bac73e5 {
  min-height: 100vh;
  background-color: #F9FAFB;
}
.page-header.data-v-2bac73e5 {
  background-color: #FFFFFF;
  padding: 48rpx 32rpx;
  text-align: center;
  border-bottom: 1px solid #E5E7EB;
}
.page-header .page-title.data-v-2bac73e5 {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #111827;
  margin-bottom: 16rpx;
}
.page-header .page-desc.data-v-2bac73e5 {
  font-size: 28rpx;
  color: #374151;
}
.language-selector.data-v-2bac73e5 {
  background-color: #FFFFFF;
  padding: 32rpx;
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.language-selector .language-item.data-v-2bac73e5 {
  flex: 1;
  text-align: center;
}
.language-selector .language-item .language-label.data-v-2bac73e5 {
  display: block;
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 16rpx;
}
.language-selector .swap-button.data-v-2bac73e5 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F3F4F6;
  border-radius: 9999rpx;
  margin: 0 32rpx;
}
.language-selector .swap-button.data-v-2bac73e5:active {
  background-color: #E5E7EB;
}
.section-title.data-v-2bac73e5 {
  padding: 32rpx 32rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.section-title .title-text.data-v-2bac73e5 {
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
}
.section-title .title-desc.data-v-2bac73e5 {
  font-size: 24rpx;
  color: #6B7280;
}
.section-title .result-actions.data-v-2bac73e5 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.upload-section.data-v-2bac73e5 {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.upload-section .upload-area.data-v-2bac73e5 {
  margin: 0 32rpx 32rpx;
  border: 2px dashed #E5E7EB;
  border-radius: 16rpx;
  padding: 48rpx;
}
.upload-section .upload-area .upload-content.data-v-2bac73e5 {
  text-align: center;
}
.upload-section .upload-area .upload-content .upload-text.data-v-2bac73e5 {
  display: block;
  font-size: 36rpx;
  font-weight: 500;
  color: #111827;
  margin: 32rpx 0 16rpx;
}
.upload-section .upload-area .upload-content .upload-desc.data-v-2bac73e5 {
  display: block;
  font-size: 28rpx;
  color: #374151;
  margin-bottom: 16rpx;
}
.upload-section .upload-area .upload-content .upload-limit.data-v-2bac73e5 {
  font-size: 24rpx;
  color: #6B7280;
}
.upload-section .upload-area .file-info.data-v-2bac73e5 {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.upload-section .upload-area .file-info .video-preview .preview-video.data-v-2bac73e5 {
  width: 100%;
  height: 400rpx;
  border-radius: 16rpx;
  background-color: #F3F4F6;
}
.upload-section .upload-area .file-info .file-details.data-v-2bac73e5 {
  flex: 1;
}
.upload-section .upload-area .file-info .file-details .file-name.data-v-2bac73e5 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.upload-section .upload-area .file-info .file-details .file-size.data-v-2bac73e5,
.upload-section .upload-area .file-info .file-details .file-duration.data-v-2bac73e5,
.upload-section .upload-area .file-info .file-details .file-resolution.data-v-2bac73e5 {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 4rpx;
}
.upload-section .upload-area .file-info .file-actions.data-v-2bac73e5 {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
}
.translation-options.data-v-2bac73e5 {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.translation-options .options-list.data-v-2bac73e5 {
  padding: 0 32rpx;
}
.translation-options .options-list .option-item.data-v-2bac73e5 {
  padding: 32rpx 0;
  border-bottom: 1px solid #E5E7EB;
}
.translation-options .options-list .option-item.data-v-2bac73e5:last-child {
  border-bottom: none;
}
.translation-options .options-list .option-item .option-label.data-v-2bac73e5 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 24rpx;
}
.translation-options .options-list .option-item .subtitle-style-options .style-preview.data-v-2bac73e5 {
  background-color: #F3F4F6;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  text-align: center;
}
.translation-options .options-list .option-item .subtitle-style-options .style-preview .preview-text.data-v-2bac73e5 {
  font-weight: 500;
}
.translation-options .options-list .option-item .subtitle-style-options .style-controls .control-item.data-v-2bac73e5 {
  margin-bottom: 24rpx;
}
.translation-options .options-list .option-item .subtitle-style-options .style-controls .control-item .control-label.data-v-2bac73e5 {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 16rpx;
}
.translation-options .options-list .option-item .subtitle-style-options .style-controls .control-item .color-picker.data-v-2bac73e5 {
  display: flex;
  gap: 16rpx;
}
.translation-options .options-list .option-item .subtitle-style-options .style-controls .control-item .color-picker .color-option.data-v-2bac73e5 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  border: 2px solid transparent;
}
.translation-options .options-list .option-item .subtitle-style-options .style-controls .control-item .color-picker .color-option.active.data-v-2bac73e5 {
  border-color: #667eea;
}
.translation-options .translate-button.data-v-2bac73e5 {
  padding: 0 32rpx 32rpx;
}
.translation-progress.data-v-2bac73e5 {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
  padding: 32rpx;
}
.translation-progress .progress-header.data-v-2bac73e5 {
  text-align: center;
  margin-bottom: 32rpx;
}
.translation-progress .progress-header .progress-title.data-v-2bac73e5 {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8rpx;
}
.translation-progress .progress-header .progress-desc.data-v-2bac73e5 {
  font-size: 24rpx;
  color: #374151;
}
.translation-progress .progress-bar.data-v-2bac73e5 {
  margin-bottom: 48rpx;
}
.translation-progress .progress-steps.data-v-2bac73e5 {
  display: flex;
  justify-content: space-between;
}
.translation-progress .progress-steps .step-item.data-v-2bac73e5 {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.translation-progress .progress-steps .step-item .step-icon.data-v-2bac73e5 {
  width: 64rpx;
  height: 64rpx;
  border-radius: 9999rpx;
  background-color: #F3F4F6;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.translation-progress .progress-steps .step-item .step-icon.active.data-v-2bac73e5, .translation-progress .progress-steps .step-item .step-icon.completed.data-v-2bac73e5 {
  background-color: #EFF6FF;
}
.translation-progress .progress-steps .step-item .step-text.data-v-2bac73e5 {
  font-size: 20rpx;
  color: #6B7280;
  text-align: center;
}
.translation-progress .progress-steps .step-item .step-text.active.data-v-2bac73e5 {
  color: #111827;
  font-weight: 500;
}
.translation-result.data-v-2bac73e5 {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.translation-result .result-content.data-v-2bac73e5 {
  padding: 0 32rpx 32rpx;
}
.translation-result .result-content .video-result .result-video.data-v-2bac73e5 {
  width: 100%;
  height: 400rpx;
  border-radius: 16rpx;
  background-color: #F3F4F6;
  margin-bottom: 24rpx;
}
.translation-result .result-content .video-result .video-info.data-v-2bac73e5 {
  text-align: center;
}
.translation-result .result-content .video-result .video-info .video-title.data-v-2bac73e5 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
}
.translation-result .result-content .video-result .video-info .video-size.data-v-2bac73e5 {
  font-size: 24rpx;
  color: #374151;
}
.translation-result .result-content .subtitle-result .subtitle-preview.data-v-2bac73e5 {
  background-color: #F3F4F6;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  max-height: 600rpx;
  overflow-y: auto;
}
.translation-result .result-content .subtitle-result .subtitle-preview .subtitle-item.data-v-2bac73e5 {
  margin-bottom: 32rpx;
}
.translation-result .result-content .subtitle-result .subtitle-preview .subtitle-item.data-v-2bac73e5:last-child {
  margin-bottom: 0;
}
.translation-result .result-content .subtitle-result .subtitle-preview .subtitle-item .subtitle-time.data-v-2bac73e5 {
  font-size: 20rpx;
  color: #6B7280;
  margin-bottom: 8rpx;
}
.translation-result .result-content .subtitle-result .subtitle-preview .subtitle-item .subtitle-original.data-v-2bac73e5 {
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 8rpx;
}
.translation-result .result-content .subtitle-result .subtitle-preview .subtitle-item .subtitle-translated.data-v-2bac73e5 {
  font-size: 28rpx;
  color: #111827;
  font-weight: 500;
}
.translation-result .result-content .subtitle-result .subtitle-preview .subtitle-more.data-v-2bac73e5 {
  text-align: center;
  padding: 24rpx;
}
.translation-result .result-content .subtitle-result .subtitle-preview .subtitle-more .more-text.data-v-2bac73e5 {
  font-size: 24rpx;
  color: #6B7280;
}
.translation-result .result-content .subtitle-result .subtitle-download.data-v-2bac73e5 {
  text-align: center;
}
.translation-history.data-v-2bac73e5 {
  background-color: #FFFFFF;
}
.translation-history .history-list.data-v-2bac73e5 {
  padding: 0 32rpx 32rpx;
}
.translation-history .history-list .history-item.data-v-2bac73e5 {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  background-color: #F3F4F6;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}
.translation-history .history-list .history-item.data-v-2bac73e5:last-child {
  margin-bottom: 0;
}
.translation-history .history-list .history-item.data-v-2bac73e5:active {
  background-color: #E5E7EB;
}
.translation-history .history-list .history-item .history-thumbnail.data-v-2bac73e5 {
  position: relative;
  width: 120rpx;
  height: 80rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.translation-history .history-list .history-item .history-thumbnail .thumbnail-image.data-v-2bac73e5 {
  width: 100%;
  height: 100%;
  background-color: #E5E7EB;
}
.translation-history .history-list .history-item .history-thumbnail .play-overlay.data-v-2bac73e5 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 48rpx;
  height: 48rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 9999rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.translation-history .history-list .history-item .history-content.data-v-2bac73e5 {
  flex: 1;
}
.translation-history .history-list .history-item .history-content .history-title.data-v-2bac73e5 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.translation-history .history-list .history-item .history-content .history-desc.data-v-2bac73e5 {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 8rpx;
}
.translation-history .history-list .history-item .history-content .history-time.data-v-2bac73e5 {
  font-size: 20rpx;
  color: #6B7280;
}
.translation-history .history-list .history-item .history-actions.data-v-2bac73e5 {
  padding: 16rpx;
}
@media screen and (max-width: 480px) {
.language-selector.data-v-2bac73e5 {
    flex-direction: column;
    gap: 24rpx;
}
.language-selector .swap-button.data-v-2bac73e5 {
    transform: rotate(90deg);
}
.progress-steps.data-v-2bac73e5 {
    flex-wrap: wrap;
    gap: 24rpx;
}
.progress-steps .step-item.data-v-2bac73e5 {
    flex: 0 0 calc(50% - 24rpx);
}
.upload-area .file-info .video-preview .preview-video.data-v-2bac73e5 {
    height: 300rpx;
}
.result-content .video-result .result-video.data-v-2bac73e5 {
    height: 300rpx;
}
}
@media (prefers-color-scheme: dark) {
.video-translation-page.data-v-2bac73e5 {
    background-color: #1a1a1a;
}
.page-header.data-v-2bac73e5,
.language-selector.data-v-2bac73e5,
.upload-section.data-v-2bac73e5,
.translation-options.data-v-2bac73e5,
.translation-progress.data-v-2bac73e5,
.translation-result.data-v-2bac73e5,
.translation-history.data-v-2bac73e5 {
    background-color: #2d2d2d;
    border-color: #404040;
}
.upload-area.data-v-2bac73e5,
.style-preview.data-v-2bac73e5,
.subtitle-preview.data-v-2bac73e5,
.history-item.data-v-2bac73e5 {
    background-color: #404040;
    border-color: #555555;
}
.swap-button.data-v-2bac73e5,
.step-icon.data-v-2bac73e5 {
    background-color: #404040;
}
.preview-video.data-v-2bac73e5,
.result-video.data-v-2bac73e5 {
    background-color: #555555;
}
.thumbnail-image.data-v-2bac73e5 {
    background-color: #666666;
}
}