{"version": 3, "file": "request.js", "sources": ["utils/request.ts"], "sourcesContent": ["/**\n * 网络请求工具\n * 基于 uni.request 封装，支持拦截器、错误处理等\n */\n\nimport { useUserStore } from '@/store/modules/user'\n\n// 请求配置接口\ninterface RequestConfig {\n  url: string\n  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'\n  data?: any\n  header?: Record<string, string>\n  timeout?: number\n  dataType?: string\n  responseType?: string\n  sslVerify?: boolean\n  withCredentials?: boolean\n  firstIpv4?: boolean\n}\n\n// 响应数据接口\ninterface ResponseData<T = any> {\n  code: number\n  message: string\n  data: T\n  success: boolean\n  timestamp?: number\n}\n\n// API基础URL配置\nconst getBaseURL = () => {\n  // #ifdef H5\n  return '/api'\n  // #endif\n  \n  // #ifdef MP\n  return 'https://your-api-domain.com/api'\n  // #endif\n  \n  // #ifdef APP-PLUS\n  return 'https://your-api-domain.com/api'\n  // #endif\n  \n  return 'http://localhost:8000/api'\n}\n\nconst BASE_URL = getBaseURL()\n\n// 请求超时时间\nconst TIMEOUT = 10000\n\n// 请求拦截器\nconst requestInterceptor = (config: RequestConfig) => {\n  // 添加基础URL\n  if (!config.url.startsWith('http')) {\n    config.url = BASE_URL + config.url\n  }\n  \n  // 设置默认请求头\n  config.header = {\n    'Content-Type': 'application/json',\n    ...config.header\n  }\n  \n  // 添加认证token\n  const userStore = useUserStore()\n  if (userStore.token) {\n    config.header.Authorization = `Bearer ${userStore.token}`\n  }\n  \n  // 设置超时时间\n  config.timeout = config.timeout || TIMEOUT\n  \n  // 打印请求日志\n  console.log(`[REQUEST] ${config.method || 'GET'} ${config.url}`, config.data)\n  \n  return config\n}\n\n// 响应拦截器\nconst responseInterceptor = (response: any, config: RequestConfig) => {\n  console.log(`[RESPONSE] ${config.url}`, response)\n  \n  const { statusCode, data } = response\n  \n  // HTTP状态码检查\n  if (statusCode >= 200 && statusCode < 300) {\n    // 业务状态码检查\n    if (data.code === 0 || data.success === true) {\n      return Promise.resolve(data)\n    } else {\n      // 业务错误处理\n      handleBusinessError(data, config)\n      return Promise.reject(data)\n    }\n  } else {\n    // HTTP错误处理\n    handleHttpError(statusCode, config)\n    return Promise.reject(response)\n  }\n}\n\n// 错误拦截器\nconst errorInterceptor = (error: any, config: RequestConfig) => {\n  console.error(`[REQUEST ERROR] ${config.url}`, error)\n  \n  // 网络错误处理\n  if (error.errMsg) {\n    if (error.errMsg.includes('timeout')) {\n      showError('请求超时，请检查网络连接')\n    } else if (error.errMsg.includes('fail')) {\n      showError('网络连接失败，请检查网络设置')\n    } else {\n      showError('网络请求失败')\n    }\n  }\n  \n  return Promise.reject(error)\n}\n\n// 业务错误处理\nconst handleBusinessError = (data: ResponseData, config: RequestConfig) => {\n  const { code, message } = data\n  \n  switch (code) {\n    case 401:\n      // 未授权，跳转登录\n      handleUnauthorized()\n      break\n    case 403:\n      showError('没有权限访问')\n      break\n    case 404:\n      showError('请求的资源不存在')\n      break\n    case 500:\n      showError('服务器内部错误')\n      break\n    default:\n      if (message) {\n        showError(message)\n      }\n  }\n}\n\n// HTTP错误处理\nconst handleHttpError = (statusCode: number, config: RequestConfig) => {\n  switch (statusCode) {\n    case 400:\n      showError('请求参数错误')\n      break\n    case 401:\n      handleUnauthorized()\n      break\n    case 403:\n      showError('禁止访问')\n      break\n    case 404:\n      showError('请求地址不存在')\n      break\n    case 500:\n      showError('服务器内部错误')\n      break\n    case 502:\n      showError('网关错误')\n      break\n    case 503:\n      showError('服务不可用')\n      break\n    case 504:\n      showError('网关超时')\n      break\n    default:\n      showError(`请求失败 (${statusCode})`)\n  }\n}\n\n// 处理未授权\nconst handleUnauthorized = () => {\n  const userStore = useUserStore()\n  \n  // 清除登录状态\n  userStore.logout()\n  \n  showError('登录已过期，请重新登录')\n  \n  // 跳转到登录页\n  uni.reLaunch({\n    url: '/pages/user/login/index'\n  })\n}\n\n// 显示错误提示\nconst showError = (message: string) => {\n  uni.showToast({\n    title: message,\n    icon: 'error',\n    duration: 2000\n  })\n}\n\n// 主要请求函数\nconst request = <T = any>(config: RequestConfig): Promise<ResponseData<T>> => {\n  return new Promise((resolve, reject) => {\n    // 请求拦截\n    const finalConfig = requestInterceptor(config)\n    \n    uni.request({\n      ...finalConfig,\n      success: (response) => {\n        responseInterceptor(response, finalConfig)\n          .then(resolve)\n          .catch(reject)\n      },\n      fail: (error) => {\n        errorInterceptor(error, finalConfig)\n          .then(resolve)\n          .catch(reject)\n      }\n    })\n  })\n}\n\n// 便捷方法\nexport const http = {\n  get: <T = any>(url: string, params?: any, config?: Partial<RequestConfig>) => {\n    return request<T>({\n      url: params ? `${url}?${new URLSearchParams(params).toString()}` : url,\n      method: 'GET',\n      ...config\n    })\n  },\n  \n  post: <T = any>(url: string, data?: any, config?: Partial<RequestConfig>) => {\n    return request<T>({\n      url,\n      method: 'POST',\n      data,\n      ...config\n    })\n  },\n  \n  put: <T = any>(url: string, data?: any, config?: Partial<RequestConfig>) => {\n    return request<T>({\n      url,\n      method: 'PUT',\n      data,\n      ...config\n    })\n  },\n  \n  delete: <T = any>(url: string, config?: Partial<RequestConfig>) => {\n    return request<T>({\n      url,\n      method: 'DELETE',\n      ...config\n    })\n  },\n  \n  patch: <T = any>(url: string, data?: any, config?: Partial<RequestConfig>) => {\n    return request<T>({\n      url,\n      method: 'PATCH',\n      data,\n      ...config\n    })\n  }\n}\n\n// 文件上传\nexport const uploadFile = (config: {\n  url: string\n  filePath: string\n  name: string\n  formData?: Record<string, any>\n  header?: Record<string, string>\n}) => {\n  return new Promise((resolve, reject) => {\n    const userStore = useUserStore()\n    \n    const uploadConfig = {\n      url: config.url.startsWith('http') ? config.url : BASE_URL + config.url,\n      filePath: config.filePath,\n      name: config.name,\n      formData: config.formData,\n      header: {\n        ...config.header,\n        Authorization: userStore.token ? `Bearer ${userStore.token}` : ''\n      },\n      success: (response: any) => {\n        console.log('[UPLOAD SUCCESS]', response)\n        \n        try {\n          const data = typeof response.data === 'string' \n            ? JSON.parse(response.data) \n            : response.data\n          \n          if (data.success || data.code === 0) {\n            resolve(data)\n          } else {\n            showError(data.message || '上传失败')\n            reject(data)\n          }\n        } catch (error) {\n          console.error('解析上传响应失败:', error)\n          reject(error)\n        }\n      },\n      fail: (error: any) => {\n        console.error('[UPLOAD ERROR]', error)\n        showError('文件上传失败')\n        reject(error)\n      }\n    }\n    \n    uni.uploadFile(uploadConfig)\n  })\n}\n\nexport default request\n"], "names": ["useUserStore", "uni"], "mappings": ";;;AA+BA,MAAM,aAAa,MAAM;AAMhB,SAAA;AAQT;AAEA,MAAM,WAAW,WAAW;AAG5B,MAAM,UAAU;AAGhB,MAAM,qBAAqB,CAAC,WAA0B;AAEpD,MAAI,CAAC,OAAO,IAAI,WAAW,MAAM,GAAG;AAC3B,WAAA,MAAM,WAAW,OAAO;AAAA,EACjC;AAGA,SAAO,SAAS;AAAA,IACd,gBAAgB;AAAA,IAChB,GAAG,OAAO;AAAA,EAAA;AAIZ,QAAM,YAAYA,mBAAAA;AAClB,MAAI,UAAU,OAAO;AACnB,WAAO,OAAO,gBAAgB,UAAU,UAAU,KAAK;AAAA,EACzD;AAGO,SAAA,UAAU,OAAO,WAAW;AAGnCC,gBAAA,MAAA,MAAA,OAAA,0BAAY,aAAa,OAAO,UAAU,KAAK,IAAI,OAAO,GAAG,IAAI,OAAO,IAAI;AAErE,SAAA;AACT;AAGA,MAAM,sBAAsB,CAAC,UAAe,WAA0B;AACpEA,sBAAY,MAAA,OAAA,0BAAA,cAAc,OAAO,GAAG,IAAI,QAAQ;AAE1C,QAAA,EAAE,YAAY,KAAS,IAAA;AAGzB,MAAA,cAAc,OAAO,aAAa,KAAK;AAEzC,QAAI,KAAK,SAAS,KAAK,KAAK,YAAY,MAAM;AACrC,aAAA,QAAQ,QAAQ,IAAI;AAAA,IAAA,OACtB;AAEL,0BAAoB,IAAY;AACzB,aAAA,QAAQ,OAAO,IAAI;AAAA,IAC5B;AAAA,EAAA,OACK;AAEL,oBAAgB,UAAkB;AAC3B,WAAA,QAAQ,OAAO,QAAQ;AAAA,EAChC;AACF;AAGA,MAAM,mBAAmB,CAAC,OAAY,WAA0B;AAC9DA,sBAAc,MAAA,SAAA,2BAAA,mBAAmB,OAAO,GAAG,IAAI,KAAK;AAGpD,MAAI,MAAM,QAAQ;AAChB,QAAI,MAAM,OAAO,SAAS,SAAS,GAAG;AACpC,gBAAU,cAAc;AAAA,IACf,WAAA,MAAM,OAAO,SAAS,MAAM,GAAG;AACxC,gBAAU,gBAAgB;AAAA,IAAA,OACrB;AACL,gBAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AAEO,SAAA,QAAQ,OAAO,KAAK;AAC7B;AAGA,MAAM,sBAAsB,CAAC,MAAoB,WAA0B;AACnE,QAAA,EAAE,MAAM,QAAY,IAAA;AAE1B,UAAQ,MAAM;AAAA,IACZ,KAAK;AAEgB;AACnB;AAAA,IACF,KAAK;AACH,gBAAU,QAAQ;AAClB;AAAA,IACF,KAAK;AACH,gBAAU,UAAU;AACpB;AAAA,IACF,KAAK;AACH,gBAAU,SAAS;AACnB;AAAA,IACF;AACE,UAAI,SAAS;AACX,kBAAU,OAAO;AAAA,MACnB;AAAA,EACJ;AACF;AAGA,MAAM,kBAAkB,CAAC,YAAoB,WAA0B;AACrE,UAAQ,YAAY;AAAA,IAClB,KAAK;AACH,gBAAU,QAAQ;AAClB;AAAA,IACF,KAAK;AACgB;AACnB;AAAA,IACF,KAAK;AACH,gBAAU,MAAM;AAChB;AAAA,IACF,KAAK;AACH,gBAAU,SAAS;AACnB;AAAA,IACF,KAAK;AACH,gBAAU,SAAS;AACnB;AAAA,IACF,KAAK;AACH,gBAAU,MAAM;AAChB;AAAA,IACF,KAAK;AACH,gBAAU,OAAO;AACjB;AAAA,IACF,KAAK;AACH,gBAAU,MAAM;AAChB;AAAA,IACF;AACY,gBAAA,SAAS,UAAU,GAAG;AAAA,EACpC;AACF;AAGA,MAAM,qBAAqB,MAAM;AAC/B,QAAM,YAAYD,mBAAAA;AAGlB,YAAU,OAAO;AAEjB,YAAU,aAAa;AAGvBC,gBAAAA,MAAI,SAAS;AAAA,IACX,KAAK;AAAA,EAAA,CACN;AACH;AAGA,MAAM,YAAY,CAAC,YAAoB;AACrCA,gBAAAA,MAAI,UAAU;AAAA,IACZ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,EAAA,CACX;AACH;AAGM,MAAA,UAAU,CAAU,WAAoD;AAC5E,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEhC,UAAA,cAAc,mBAAmB,MAAM;AAE7CA,kBAAAA,MAAI,QAAQ;AAAA,MACV,GAAG;AAAA,MACH,SAAS,CAAC,aAAa;AACrB,4BAAoB,UAAU,WAAW,EACtC,KAAK,OAAO,EACZ,MAAM,MAAM;AAAA,MACjB;AAAA,MACA,MAAM,CAAC,UAAU;AACf,yBAAiB,OAAO,WAAW,EAChC,KAAK,OAAO,EACZ,MAAM,MAAM;AAAA,MACjB;AAAA,IAAA,CACD;AAAA,EAAA,CACF;AACH;AAGO,MAAM,OAAO;AAAA,EAClB,KAAK,CAAU,KAAa,QAAc,WAAoC;AAC5E,WAAO,QAAW;AAAA,MAChB,KAAK,SAAS,GAAG,GAAG,IAAI,IAAI,gBAAgB,MAAM,EAAE,SAAU,CAAA,KAAK;AAAA,MACnE,QAAQ;AAAA,MACR,GAAG;AAAA,IAAA,CACJ;AAAA,EACH;AAAA,EAEA,MAAM,CAAU,KAAa,MAAY,WAAoC;AAC3E,WAAO,QAAW;AAAA,MAChB;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA,GAAG;AAAA,IAAA,CACJ;AAAA,EACH;AAAA,EAEA,KAAK,CAAU,KAAa,MAAY,WAAoC;AAC1E,WAAO,QAAW;AAAA,MAChB;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA,GAAG;AAAA,IAAA,CACJ;AAAA,EACH;AAAA,EAEA,QAAQ,CAAU,KAAa,WAAoC;AACjE,WAAO,QAAW;AAAA,MAChB;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IAAA,CACJ;AAAA,EACH;AAAA,EAEA,OAAO,CAAU,KAAa,MAAY,WAAoC;AAC5E,WAAO,QAAW;AAAA,MAChB;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA,GAAG;AAAA,IAAA,CACJ;AAAA,EACH;AACF;AAGa,MAAA,aAAa,CAAC,WAMrB;AACJ,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,YAAYD,mBAAAA;AAElB,UAAM,eAAe;AAAA,MACnB,KAAK,OAAO,IAAI,WAAW,MAAM,IAAI,OAAO,MAAM,WAAW,OAAO;AAAA,MACpE,UAAU,OAAO;AAAA,MACjB,MAAM,OAAO;AAAA,MACb,UAAU,OAAO;AAAA,MACjB,QAAQ;AAAA,QACN,GAAG,OAAO;AAAA,QACV,eAAe,UAAU,QAAQ,UAAU,UAAU,KAAK,KAAK;AAAA,MACjE;AAAA,MACA,SAAS,CAAC,aAAkB;AAC1BC,sBAAA,MAAA,MAAA,OAAA,2BAAY,oBAAoB,QAAQ;AAEpC,YAAA;AACI,gBAAA,OAAO,OAAO,SAAS,SAAS,WAClC,KAAK,MAAM,SAAS,IAAI,IACxB,SAAS;AAEb,cAAI,KAAK,WAAW,KAAK,SAAS,GAAG;AACnC,oBAAQ,IAAI;AAAA,UAAA,OACP;AACK,sBAAA,KAAK,WAAW,MAAM;AAChC,mBAAO,IAAI;AAAA,UACb;AAAA,iBACO,OAAO;AACdA,wBAAA,MAAA,MAAA,SAAA,2BAAc,aAAa,KAAK;AAChC,iBAAO,KAAK;AAAA,QACd;AAAA,MACF;AAAA,MACA,MAAM,CAAC,UAAe;AACpBA,sBAAA,MAAA,MAAA,SAAA,2BAAc,kBAAkB,KAAK;AACrC,kBAAU,QAAQ;AAClB,eAAO,KAAK;AAAA,MACd;AAAA,IAAA;AAGFA,wBAAI,WAAW,YAAY;AAAA,EAAA,CAC5B;AACH;;;;"}