<template>
  <view class="text-processor-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">文本处理工具</text>
      <text class="page-desc">文本格式化、统计分析、转换等功能</text>
    </view>

    <!-- 工具选择 -->
    <view class="tools-selector">
      <view class="section-title">
        <text class="title-text">选择处理工具</text>
      </view>
      
      <view class="tools-tabs">
        <view 
          class="tab-item"
          v-for="tool in textTools"
          :key="tool.id"
          :class="{ active: selectedTool === tool.id }"
          @click="selectTool(tool.id)"
        >
          <u-icon :name="tool.icon" size="18" :color="selectedTool === tool.id ? '#2563EB' : '#6B7280'"></u-icon>
          <text class="tab-text">{{ tool.name }}</text>
        </view>
      </view>
    </view>

    <!-- 文本输入区域 -->
    <view class="text-input-section">
      <view class="section-title">
        <text class="title-text">输入文本</text>
        <view class="input-actions">
          <u-icon name="file-text" size="16" color="#2563EB" @click="importFromFile"></u-icon>
          <u-icon name="clipboard" size="16" color="#2563EB" margin-left="8" @click="pasteFromClipboard"></u-icon>
          <u-icon name="close-circle" size="16" color="#EF4444" margin-left="8" @click="clearInput"></u-icon>
        </view>
      </view>
      
      <view class="input-area">
        <textarea 
          class="text-input"
          v-model="inputText"
          placeholder="请输入或粘贴要处理的文本内容..."
          :maxlength="10000"
          @input="onTextInput"
        ></textarea>
        
        <view class="input-stats">
          <text class="stat-item">字符: {{ textStats.characters }}</text>
          <text class="stat-item">单词: {{ textStats.words }}</text>
          <text class="stat-item">行数: {{ textStats.lines }}</text>
          <text class="stat-item">段落: {{ textStats.paragraphs }}</text>
        </view>
      </view>
    </view>

    <!-- 工具选项 -->
    <view class="tool-options" v-if="inputText && selectedTool">
      <view class="section-title">
        <text class="title-text">{{ getToolName(selectedTool) }}选项</text>
      </view>
      
      <view class="options-content">
        <!-- 格式化选项 -->
        <view class="option-group" v-if="selectedTool === 'format'">
          <view class="option-item">
            <text class="option-label">格式化类型</text>
            <u-radio-group v-model="formatOptions.type">
              <u-radio name="capitalize" label="首字母大写"></u-radio>
              <u-radio name="uppercase" label="全部大写"></u-radio>
              <u-radio name="lowercase" label="全部小写"></u-radio>
              <u-radio name="title" label="标题格式"></u-radio>
            </u-radio-group>
          </view>
          
          <view class="option-item">
            <text class="option-label">其他选项</text>
            <view class="checkbox-group">
              <u-checkbox v-model="formatOptions.removeExtraSpaces" label="移除多余空格"></u-checkbox>
              <u-checkbox v-model="formatOptions.removeEmptyLines" label="移除空行"></u-checkbox>
              <u-checkbox v-model="formatOptions.trimLines" label="去除行首尾空格"></u-checkbox>
            </view>
          </view>
        </view>

        <!-- 统计选项 -->
        <view class="option-group" v-if="selectedTool === 'stats'">
          <view class="stats-display">
            <view class="stat-card">
              <text class="stat-number">{{ detailedStats.characters }}</text>
              <text class="stat-label">总字符数</text>
            </view>
            <view class="stat-card">
              <text class="stat-number">{{ detailedStats.charactersNoSpaces }}</text>
              <text class="stat-label">不含空格</text>
            </view>
            <view class="stat-card">
              <text class="stat-number">{{ detailedStats.words }}</text>
              <text class="stat-label">单词数</text>
            </view>
            <view class="stat-card">
              <text class="stat-number">{{ detailedStats.sentences }}</text>
              <text class="stat-label">句子数</text>
            </view>
            <view class="stat-card">
              <text class="stat-number">{{ detailedStats.paragraphs }}</text>
              <text class="stat-label">段落数</text>
            </view>
            <view class="stat-card">
              <text class="stat-number">{{ detailedStats.readingTime }}</text>
              <text class="stat-label">阅读时间(分钟)</text>
            </view>
          </view>
        </view>

        <!-- 转换选项 -->
        <view class="option-group" v-if="selectedTool === 'convert'">
          <view class="option-item">
            <text class="option-label">转换类型</text>
            <u-radio-group v-model="convertOptions.type">
              <u-radio name="markdown" label="转换为Markdown"></u-radio>
              <u-radio name="html" label="转换为HTML"></u-radio>
              <u-radio name="json" label="转换为JSON"></u-radio>
              <u-radio name="csv" label="转换为CSV"></u-radio>
            </u-radio-group>
          </view>
        </view>

        <!-- 查找替换选项 -->
        <view class="option-group" v-if="selectedTool === 'replace'">
          <view class="option-item">
            <text class="option-label">查找内容</text>
            <input 
              class="option-input"
              v-model="replaceOptions.findText"
              placeholder="输入要查找的文本"
            />
          </view>
          
          <view class="option-item">
            <text class="option-label">替换为</text>
            <input 
              class="option-input"
              v-model="replaceOptions.replaceText"
              placeholder="输入替换后的文本"
            />
          </view>
          
          <view class="option-item">
            <text class="option-label">选项</text>
            <view class="checkbox-group">
              <u-checkbox v-model="replaceOptions.caseSensitive" label="区分大小写"></u-checkbox>
              <u-checkbox v-model="replaceOptions.wholeWord" label="全词匹配"></u-checkbox>
              <u-checkbox v-model="replaceOptions.useRegex" label="使用正则表达式"></u-checkbox>
            </view>
          </view>
          
          <view class="replace-preview" v-if="replaceOptions.findText">
            <text class="preview-label">找到 {{ findMatches }} 处匹配</text>
          </view>
        </view>

        <!-- 编码选项 -->
        <view class="option-group" v-if="selectedTool === 'encode'">
          <view class="option-item">
            <text class="option-label">编码类型</text>
            <u-radio-group v-model="encodeOptions.type">
              <u-radio name="base64" label="Base64编码"></u-radio>
              <u-radio name="url" label="URL编码"></u-radio>
              <u-radio name="html" label="HTML实体编码"></u-radio>
              <u-radio name="unicode" label="Unicode编码"></u-radio>
            </u-radio-group>
          </view>
          
          <view class="option-item">
            <text class="option-label">操作</text>
            <u-radio-group v-model="encodeOptions.operation">
              <u-radio name="encode" label="编码"></u-radio>
              <u-radio name="decode" label="解码"></u-radio>
            </u-radio-group>
          </view>
        </view>
      </view>
      
      <view class="process-button" v-if="selectedTool !== 'stats'">
        <u-button 
          type="primary" 
          size="large"
          :loading="isProcessing"
          @click="processText"
          :custom-style="{ width: '100%' }"
        >
          {{ isProcessing ? '处理中...' : '开始处理' }}
        </u-button>
      </view>
    </view>

    <!-- 处理结果 -->
    <view class="processing-result" v-if="processedText">
      <view class="section-title">
        <text class="title-text">处理结果</text>
        <view class="result-actions">
          <u-icon name="copy" size="18" color="#2563EB" @click="copyResult"></u-icon>
          <u-icon name="download" size="18" color="#2563EB" margin-left="12" @click="downloadResult"></u-icon>
          <u-icon name="share" size="18" color="#2563EB" margin-left="12" @click="shareResult"></u-icon>
        </view>
      </view>
      
      <view class="result-content">
        <textarea 
          class="result-text"
          :value="processedText"
          readonly
          :style="{ height: getResultHeight() }"
        ></textarea>
        
        <view class="result-stats">
          <text class="stat-item">原文: {{ textStats.characters }} 字符</text>
          <text class="stat-item">结果: {{ getProcessedStats().characters }} 字符</text>
          <text class="stat-item">变化: {{ getChangePercent() }}%</text>
        </view>
      </view>
    </view>

    <!-- 处理历史 -->
    <view class="processing-history" v-if="textHistory.length > 0">
      <view class="section-title">
        <text class="title-text">处理历史</text>
        <u-button 
          type="text" 
          text="清空" 
          size="small"
          @click="clearHistory"
          :custom-style="{ color: '#EF4444', fontSize: '24rpx' }"
        ></u-button>
      </view>
      
      <view class="history-list">
        <view 
          class="history-item"
          v-for="item in textHistory.slice(0, 5)"
          :key="item.id"
          @click="loadHistoryItem(item)"
        >
          <view class="history-icon">
            <u-icon :name="getToolIcon(item.tool)" size="20" color="#2563EB"></u-icon>
          </view>
          <view class="history-content">
            <text class="history-title">{{ getToolName(item.tool) }}</text>
            <text class="history-desc">{{ item.preview }}</text>
            <text class="history-time">{{ formatTime(item.createdAt) }}</text>
          </view>
          <view class="history-actions">
            <u-icon name="refresh" size="16" color="#6B7280"></u-icon>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import dayjs from 'dayjs'

// 响应式数据
const selectedTool = ref<string>('format')
const inputText = ref<string>('')
const processedText = ref<string>('')
const isProcessing = ref<boolean>(false)
const textHistory = ref<any[]>([])

// 处理选项
const formatOptions = ref({
  type: 'capitalize',
  removeExtraSpaces: true,
  removeEmptyLines: false,
  trimLines: true
})

const convertOptions = ref({
  type: 'markdown'
})

const replaceOptions = ref({
  findText: '',
  replaceText: '',
  caseSensitive: false,
  wholeWord: false,
  useRegex: false
})

const encodeOptions = ref({
  type: 'base64',
  operation: 'encode'
})

// 文本工具
const textTools = ref([
  {
    id: 'format',
    name: '格式化',
    icon: 'text-format'
  },
  {
    id: 'stats',
    name: '统计分析',
    icon: 'chart-bar'
  },
  {
    id: 'convert',
    name: '格式转换',
    icon: 'swap'
  },
  {
    id: 'replace',
    name: '查找替换',
    icon: 'find-replace'
  },
  {
    id: 'encode',
    name: '编码转换',
    icon: 'code'
  }
])

// 计算属性
const textStats = computed(() => {
  if (!inputText.value) {
    return { characters: 0, words: 0, lines: 0, paragraphs: 0 }
  }

  const text = inputText.value
  const characters = text.length
  const words = text.trim() ? text.trim().split(/\s+/).length : 0
  const lines = text.split('\n').length
  const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim()).length

  return { characters, words, lines, paragraphs }
})

const detailedStats = computed(() => {
  if (!inputText.value) {
    return {
      characters: 0,
      charactersNoSpaces: 0,
      words: 0,
      sentences: 0,
      paragraphs: 0,
      readingTime: 0
    }
  }

  const text = inputText.value
  const characters = text.length
  const charactersNoSpaces = text.replace(/\s/g, '').length
  const words = text.trim() ? text.trim().split(/\s+/).length : 0
  const sentences = text.split(/[.!?]+/).filter(s => s.trim()).length
  const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim()).length
  const readingTime = Math.ceil(words / 200) // 假设每分钟200词

  return {
    characters,
    charactersNoSpaces,
    words,
    sentences,
    paragraphs,
    readingTime
  }
})

const findMatches = computed(() => {
  if (!inputText.value || !replaceOptions.value.findText) return 0

  try {
    let searchText = replaceOptions.value.findText
    let flags = 'g'

    if (!replaceOptions.value.caseSensitive) {
      flags += 'i'
    }

    if (replaceOptions.value.useRegex) {
      const regex = new RegExp(searchText, flags)
      const matches = inputText.value.match(regex)
      return matches ? matches.length : 0
    } else {
      if (replaceOptions.value.wholeWord) {
        searchText = `\\b${searchText}\\b`
      }
      const regex = new RegExp(searchText, flags)
      const matches = inputText.value.match(regex)
      return matches ? matches.length : 0
    }
  } catch (error) {
    return 0
  }
})

// 页面加载
onMounted(() => {
  loadTextHistory()
})

// 监听文本变化
watch(inputText, () => {
  if (selectedTool.value === 'stats') {
    // 统计工具实时更新
  }
})

// 选择工具
const selectTool = (toolId: string) => {
  selectedTool.value = toolId
  processedText.value = ''
}

// 文本输入事件
const onTextInput = () => {
  processedText.value = ''
}

// 从文件导入
const importFromFile = () => {
  // #ifdef H5
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.txt,.md,.json,.csv'
  input.onchange = (e: any) => {
    const file = e.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        inputText.value = e.target?.result as string
      }
      reader.readAsText(file)
    }
  }
  input.click()
  // #endif

  // #ifdef APP-PLUS || MP
  uni.chooseFile({
    count: 1,
    type: 'file',
    extension: ['.txt', '.md', '.json', '.csv'],
    success: (res) => {
      // 读取文件内容
      uni.showToast({
        title: '文件导入功能开发中',
        icon: 'none'
      })
    }
  })
  // #endif
}

// 从剪贴板粘贴
const pasteFromClipboard = () => {
  uni.getClipboardData({
    success: (res) => {
      inputText.value = res.data
      uni.showToast({
        title: '粘贴成功',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '粘贴失败',
        icon: 'error'
      })
    }
  })
}

// 清空输入
const clearInput = () => {
  inputText.value = ''
  processedText.value = ''
}

// 处理文本
const processText = async () => {
  if (!inputText.value) return

  try {
    isProcessing.value = true

    // 模拟处理延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    let result = ''

    switch (selectedTool.value) {
      case 'format':
        result = formatText(inputText.value)
        break
      case 'convert':
        result = convertText(inputText.value)
        break
      case 'replace':
        result = replaceText(inputText.value)
        break
      case 'encode':
        result = encodeText(inputText.value)
        break
      default:
        result = inputText.value
    }

    processedText.value = result

    // 添加到历史记录
    const historyItem = {
      id: Date.now().toString(),
      tool: selectedTool.value,
      originalText: inputText.value,
      processedText: result,
      preview: inputText.value.substring(0, 50) + (inputText.value.length > 50 ? '...' : ''),
      createdAt: new Date().toISOString()
    }

    textHistory.value.unshift(historyItem)
    saveTextHistory()

    uni.showToast({
      title: '处理完成',
      icon: 'success'
    })

  } catch (error) {
    console.error('处理失败:', error)
    uni.showToast({
      title: '处理失败',
      icon: 'error'
    })
  } finally {
    isProcessing.value = false
  }
}

// 格式化文本
const formatText = (text: string) => {
  let result = text

  // 移除多余空格
  if (formatOptions.value.removeExtraSpaces) {
    result = result.replace(/\s+/g, ' ')
  }

  // 移除空行
  if (formatOptions.value.removeEmptyLines) {
    result = result.replace(/\n\s*\n/g, '\n')
  }

  // 去除行首尾空格
  if (formatOptions.value.trimLines) {
    result = result.split('\n').map(line => line.trim()).join('\n')
  }

  // 应用格式化类型
  switch (formatOptions.value.type) {
    case 'uppercase':
      result = result.toUpperCase()
      break
    case 'lowercase':
      result = result.toLowerCase()
      break
    case 'capitalize':
      result = result.charAt(0).toUpperCase() + result.slice(1).toLowerCase()
      break
    case 'title':
      result = result.replace(/\w\S*/g, (txt) =>
        txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
      )
      break
  }

  return result
}

// 转换文本
const convertText = (text: string) => {
  switch (convertOptions.value.type) {
    case 'markdown':
      // 简单的文本到Markdown转换
      return text.split('\n\n').map(paragraph =>
        paragraph.trim() ? `${paragraph}\n` : ''
      ).join('\n')

    case 'html':
      // 文本到HTML转换
      return `<html>\n<body>\n${text.split('\n\n').map(p =>
        p.trim() ? `<p>${p.replace(/\n/g, '<br>')}</p>` : ''
      ).join('\n')}\n</body>\n</html>`

    case 'json':
      // 文本到JSON转换
      const lines = text.split('\n').filter(line => line.trim())
      return JSON.stringify({ lines }, null, 2)

    case 'csv':
      // 文本到CSV转换（简单实现）
      return text.split('\n').map(line => `"${line.replace(/"/g, '""')}"`).join('\n')

    default:
      return text
  }
}

// 替换文本
const replaceText = (text: string) => {
  if (!replaceOptions.value.findText) return text

  try {
    let searchText = replaceOptions.value.findText
    let flags = 'g'

    if (!replaceOptions.value.caseSensitive) {
      flags += 'i'
    }

    if (replaceOptions.value.useRegex) {
      const regex = new RegExp(searchText, flags)
      return text.replace(regex, replaceOptions.value.replaceText)
    } else {
      if (replaceOptions.value.wholeWord) {
        searchText = `\\b${searchText}\\b`
      }
      const regex = new RegExp(searchText, flags)
      return text.replace(regex, replaceOptions.value.replaceText)
    }
  } catch (error) {
    console.error('替换失败:', error)
    return text
  }
}

// 编码文本
const encodeText = (text: string) => {
  try {
    switch (encodeOptions.value.type) {
      case 'base64':
        if (encodeOptions.value.operation === 'encode') {
          // #ifdef H5
          return btoa(unescape(encodeURIComponent(text)))
          // #endif
          // #ifdef APP-PLUS || MP
          return uni.arrayBufferToBase64(new TextEncoder().encode(text))
          // #endif
        } else {
          // #ifdef H5
          return decodeURIComponent(escape(atob(text)))
          // #endif
          // #ifdef APP-PLUS || MP
          return new TextDecoder().decode(uni.base64ToArrayBuffer(text))
          // #endif
        }

      case 'url':
        return encodeOptions.value.operation === 'encode'
          ? encodeURIComponent(text)
          : decodeURIComponent(text)

      case 'html':
        if (encodeOptions.value.operation === 'encode') {
          return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;')
        } else {
          return text
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
        }

      case 'unicode':
        if (encodeOptions.value.operation === 'encode') {
          return text.split('').map(char =>
            '\\u' + char.charCodeAt(0).toString(16).padStart(4, '0')
          ).join('')
        } else {
          return text.replace(/\\u[\dA-Fa-f]{4}/g, (match) =>
            String.fromCharCode(parseInt(match.replace('\\u', ''), 16))
          )
        }

      default:
        return text
    }
  } catch (error) {
    console.error('编码失败:', error)
    return text
  }
}

// 复制结果
const copyResult = () => {
  if (!processedText.value) return

  uni.setClipboardData({
    data: processedText.value,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      })
    }
  })
}

// 下载结果
const downloadResult = () => {
  if (!processedText.value) return

  uni.showToast({
    title: '下载功能开发中',
    icon: 'none'
  })
}

// 分享结果
const shareResult = () => {
  if (!processedText.value) return

  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    summary: processedText.value.substring(0, 100),
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    }
  })
}

// 工具函数
const getToolName = (toolId: string) => {
  const tool = textTools.value.find(t => t.id === toolId)
  return tool?.name || '未知工具'
}

const getToolIcon = (toolId: string) => {
  const tool = textTools.value.find(t => t.id === toolId)
  return tool?.icon || 'text'
}

const getResultHeight = () => {
  const lines = processedText.value.split('\n').length
  return Math.min(Math.max(lines * 40, 200), 600) + 'rpx'
}

const getProcessedStats = () => {
  if (!processedText.value) {
    return { characters: 0, words: 0 }
  }

  const characters = processedText.value.length
  const words = processedText.value.trim() ? processedText.value.trim().split(/\s+/).length : 0

  return { characters, words }
}

const getChangePercent = () => {
  const original = textStats.value.characters
  const processed = getProcessedStats().characters

  if (original === 0) return 0

  return Math.round(((processed - original) / original) * 100)
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

// 历史记录相关
const saveTextHistory = () => {
  uni.setStorageSync('text_processing_history', textHistory.value)
}

const loadTextHistory = () => {
  const history = uni.getStorageSync('text_processing_history') || []
  textHistory.value = history
}

const clearHistory = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有处理历史吗？',
    success: (res) => {
      if (res.confirm) {
        textHistory.value = []
        saveTextHistory()
        uni.showToast({
          title: '清空成功',
          icon: 'success'
        })
      }
    }
  })
}

const loadHistoryItem = (item: any) => {
  inputText.value = item.originalText
  processedText.value = item.processedText
  selectedTool.value = item.tool
}

// 页面下拉刷新
const onPullDownRefresh = () => {
  loadTextHistory()
  uni.stopPullDownRefresh()
}

// 导出方法供页面使用
defineExpose({
  onPullDownRefresh
})
</script>

<style lang="scss" scoped>
.text-processor-page {
  min-height: 100vh;
  background-color: $bg-secondary;
}

// 页面头部
.page-header {
  background-color: $bg-primary;
  padding: $spacing-6 $spacing-4;
  text-align: center;
  border-bottom: 1px solid $border-primary;

  .page-title {
    display: block;
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: $spacing-2;
  }

  .page-desc {
    font-size: $font-size-base;
    color: $text-secondary;
  }
}

// 通用区块标题
.section-title {
  padding: $spacing-4 $spacing-4 $spacing-3;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title-text {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
  }

  .input-actions,
  .result-actions {
    display: flex;
    align-items: center;
    gap: $spacing-2;
  }
}

// 工具选择
.tools-selector {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .tools-tabs {
    display: flex;
    padding: 0 $spacing-4 $spacing-4;
    gap: $spacing-2;
    overflow-x: auto;

    .tab-item {
      flex: 0 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: $spacing-3;
      background-color: $bg-tertiary;
      border-radius: $radius-lg;
      border: 2px solid transparent;
      min-width: 120rpx;

      &.active {
        border-color: $primary;
        background-color: $primary-50;
      }

      .tab-text {
        font-size: $font-size-sm;
        color: $text-primary;
        margin-top: $spacing-1;
      }
    }
  }
}

// 文本输入区域
.text-input-section {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .input-area {
    padding: 0 $spacing-4 $spacing-4;

    .text-input {
      width: 100%;
      min-height: 300rpx;
      padding: $spacing-3;
      border: 1px solid $border-primary;
      border-radius: $radius-md;
      font-size: $font-size-base;
      color: $text-primary;
      background-color: $bg-tertiary;
      line-height: $line-height-relaxed;

      &::placeholder {
        color: $text-quaternary;
      }
    }

    .input-stats {
      display: flex;
      justify-content: space-between;
      margin-top: $spacing-2;
      padding: $spacing-2 $spacing-3;
      background-color: $bg-tertiary;
      border-radius: $radius-md;

      .stat-item {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }
  }
}

// 工具选项
.tool-options {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .options-content {
    padding: 0 $spacing-4;

    .option-group {
      .option-item {
        margin-bottom: $spacing-4;

        .option-label {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-3;
        }

        .option-input {
          width: 100%;
          padding: $spacing-2 $spacing-3;
          border: 1px solid $border-primary;
          border-radius: $radius-md;
          font-size: $font-size-base;
          color: $text-primary;
          background-color: $bg-tertiary;

          &::placeholder {
            color: $text-quaternary;
          }
        }

        .checkbox-group {
          display: flex;
          flex-direction: column;
          gap: $spacing-2;
        }
      }

      .stats-display {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: $spacing-3;

        .stat-card {
          background-color: $bg-tertiary;
          border-radius: $radius-lg;
          padding: $spacing-4;
          text-align: center;

          .stat-number {
            display: block;
            font-size: $font-size-xl;
            font-weight: $font-weight-bold;
            color: $primary;
            margin-bottom: $spacing-1;
          }

          .stat-label {
            font-size: $font-size-sm;
            color: $text-secondary;
          }
        }
      }

      .replace-preview {
        background-color: $bg-tertiary;
        border-radius: $radius-md;
        padding: $spacing-3;

        .preview-label {
          font-size: $font-size-sm;
          color: $text-secondary;
        }
      }
    }
  }

  .process-button {
    padding: 0 $spacing-4 $spacing-4;
  }
}

// 处理结果
.processing-result {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .result-content {
    padding: 0 $spacing-4 $spacing-4;

    .result-text {
      width: 100%;
      padding: $spacing-3;
      border: 1px solid $border-primary;
      border-radius: $radius-md;
      font-size: $font-size-base;
      color: $text-primary;
      background-color: $bg-tertiary;
      line-height: $line-height-relaxed;
      resize: none;
      margin-bottom: $spacing-3;
    }

    .result-stats {
      display: flex;
      justify-content: space-between;
      padding: $spacing-2 $spacing-3;
      background-color: $bg-tertiary;
      border-radius: $radius-md;

      .stat-item {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }
  }
}

// 处理历史
.processing-history {
  background-color: $bg-primary;

  .history-list {
    padding: 0 $spacing-4 $spacing-4;

    .history-item {
      display: flex;
      align-items: center;
      gap: $spacing-3;
      padding: $spacing-3;
      background-color: $bg-tertiary;
      border-radius: $radius-lg;
      margin-bottom: $spacing-2;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        background-color: $gray-200;
      }

      .history-icon {
        width: 64rpx;
        height: 64rpx;
        background-color: $primary-50;
        border-radius: $radius-lg;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .history-content {
        flex: 1;

        .history-title {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-1;
        }

        .history-desc {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .history-time {
          font-size: $font-size-xs;
          color: $text-tertiary;
        }
      }

      .history-actions {
        padding: $spacing-2;
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 480px) {
  .tools-tabs {
    .tab-item {
      min-width: 100rpx;
      padding: $spacing-2;
    }
  }

  .stats-display {
    grid-template-columns: 1fr;
  }

  .input-stats {
    flex-direction: column;
    gap: $spacing-1;

    .stat-item {
      text-align: center;
    }
  }

  .result-stats {
    flex-direction: column;
    gap: $spacing-1;

    .stat-item {
      text-align: center;
    }
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .text-processor-page {
    background-color: #1a1a1a;
  }

  .page-header,
  .tools-selector,
  .text-input-section,
  .tool-options,
  .processing-result,
  .processing-history {
    background-color: #2d2d2d;
    border-color: #404040;
  }

  .tab-item,
  .text-input,
  .option-input,
  .stat-card,
  .replace-preview,
  .result-text,
  .input-stats,
  .result-stats,
  .history-item {
    background-color: #404040;
    border-color: #555555;
  }

  .tab-item.active {
    background-color: #1e3a8a;
    border-color: #3b82f6;
  }

  .text-input,
  .option-input,
  .result-text {
    color: #ffffff;

    &::placeholder {
      color: #888888;
    }
  }

  .history-icon {
    background-color: #1e3a8a;
  }
}
</style>
