import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { translationApi, type LanguageInfo, type TranslationHistory } from '@/api/translation'

export const useTranslationStore = defineStore('translation', () => {
  // 状态
  const languages = ref<LanguageInfo[]>([])
  const currentSourceLang = ref<string>('auto')
  const currentTargetLang = ref<string>('zh-CN')
  const translationHistory = ref<TranslationHistory[]>([])
  const favorites = ref<TranslationHistory[]>([])
  const isTranslating = ref<boolean>(false)
  const lastTranslation = ref<{
    source: string
    target: string
    sourceLang: string
    targetLang: string
  } | null>(null)

  // 计算属性
  const sourceLanguage = computed(() => {
    return languages.value.find(lang => lang.code === currentSourceLang.value)
  })

  const targetLanguage = computed(() => {
    return languages.value.find(lang => lang.code === currentTargetLang.value)
  })

  const recentLanguagePairs = computed(() => {
    const pairs = new Set<string>()
    const recentPairs: Array<{ source: string; target: string }> = []
    
    translationHistory.value.slice(0, 10).forEach(item => {
      const pairKey = `${item.source_lang}-${item.target_lang}`
      if (!pairs.has(pairKey)) {
        pairs.add(pairKey)
        recentPairs.push({
          source: item.source_lang,
          target: item.target_lang
        })
      }
    })
    
    return recentPairs.slice(0, 5)
  })

  // 加载支持的语言
  const loadLanguages = async () => {
    try {
      const response = await translationApi.getLanguages()
      if (response.success) {
        languages.value = response.data
      }
    } catch (error) {
      console.error('加载语言列表失败:', error)
    }
  }

  // 文本翻译
  const translateText = async (text: string, options?: {
    sourceLang?: string
    targetLang?: string
    domain?: string
    style?: string
  }) => {
    if (!text.trim()) {
      throw new Error('请输入要翻译的文本')
    }

    try {
      isTranslating.value = true
      
      const sourceLang = options?.sourceLang || currentSourceLang.value
      const targetLang = options?.targetLang || currentTargetLang.value
      
      const response = await translationApi.translateText({
        text: text.trim(),
        source_lang: sourceLang,
        target_lang: targetLang,
        domain: options?.domain,
        style: options?.style
      })

      if (response.success) {
        // 保存最后一次翻译
        lastTranslation.value = {
          source: text,
          target: response.data.translated_text,
          sourceLang: response.data.source_lang,
          targetLang: response.data.target_lang
        }

        // 更新当前语言设置
        if (sourceLang === 'auto') {
          currentSourceLang.value = response.data.source_lang
        }

        // 添加到历史记录
        await loadHistory()

        return response.data
      } else {
        throw new Error(response.message || '翻译失败')
      }
    } catch (error: any) {
      console.error('翻译失败:', error)
      throw error
    } finally {
      isTranslating.value = false
    }
  }

  // 语言检测
  const detectLanguage = async (text: string) => {
    if (!text.trim()) return null

    try {
      const response = await translationApi.detectLanguage(text.trim())
      if (response.success) {
        return response.data
      }
    } catch (error) {
      console.error('语言检测失败:', error)
    }
    return null
  }

  // 加载翻译历史
  const loadHistory = async (page = 1, limit = 20) => {
    try {
      const response = await translationApi.getHistory({ page, limit })
      if (response.success) {
        if (page === 1) {
          translationHistory.value = response.data.items
        } else {
          translationHistory.value.push(...response.data.items)
        }
        return response.data
      }
    } catch (error) {
      console.error('加载翻译历史失败:', error)
    }
    return null
  }

  // 删除历史记录
  const deleteHistoryItem = async (id: string) => {
    try {
      const response = await translationApi.deleteHistory(id)
      if (response.success) {
        translationHistory.value = translationHistory.value.filter(item => item.id !== id)
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('删除历史记录失败:', error)
      uni.showToast({
        title: '删除失败',
        icon: 'error'
      })
    }
  }

  // 清空历史记录
  const clearHistory = async () => {
    try {
      const response = await translationApi.clearHistory()
      if (response.success) {
        translationHistory.value = []
        uni.showToast({
          title: '清空成功',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('清空历史记录失败:', error)
      uni.showToast({
        title: '清空失败',
        icon: 'error'
      })
    }
  }

  // 收藏翻译
  const toggleFavorite = async (id: string, isFavorite: boolean) => {
    try {
      const response = isFavorite 
        ? await translationApi.unfavoriteTranslation(id)
        : await translationApi.favoriteTranslation(id)
      
      if (response.success) {
        // 更新历史记录中的收藏状态
        const historyItem = translationHistory.value.find(item => item.id === id)
        if (historyItem) {
          (historyItem as any).is_favorite = !isFavorite
        }

        // 更新收藏列表
        if (isFavorite) {
          favorites.value = favorites.value.filter(item => item.id !== id)
        } else {
          await loadFavorites()
        }

        uni.showToast({
          title: isFavorite ? '取消收藏' : '收藏成功',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('操作收藏失败:', error)
      uni.showToast({
        title: '操作失败',
        icon: 'error'
      })
    }
  }

  // 加载收藏列表
  const loadFavorites = async (page = 1, limit = 20) => {
    try {
      const response = await translationApi.getFavorites({ page, limit })
      if (response.success) {
        if (page === 1) {
          favorites.value = response.data.items
        } else {
          favorites.value.push(...response.data.items)
        }
        return response.data
      }
    } catch (error) {
      console.error('加载收藏列表失败:', error)
    }
    return null
  }

  // 交换源语言和目标语言
  const swapLanguages = () => {
    if (currentSourceLang.value === 'auto') {
      uni.showToast({
        title: '自动检测语言无法交换',
        icon: 'none'
      })
      return
    }

    const temp = currentSourceLang.value
    currentSourceLang.value = currentTargetLang.value
    currentTargetLang.value = temp
  }

  // 设置语言
  const setSourceLanguage = (langCode: string) => {
    currentSourceLang.value = langCode
  }

  const setTargetLanguage = (langCode: string) => {
    currentTargetLang.value = langCode
  }

  // 使用最近的语言对
  const useLanguagePair = (sourceLang: string, targetLang: string) => {
    currentSourceLang.value = sourceLang
    currentTargetLang.value = targetLang
  }

  return {
    // 状态
    languages,
    currentSourceLang,
    currentTargetLang,
    translationHistory,
    favorites,
    isTranslating,
    lastTranslation,

    // 计算属性
    sourceLanguage,
    targetLanguage,
    recentLanguagePairs,

    // 方法
    loadLanguages,
    translateText,
    detectLanguage,
    loadHistory,
    deleteHistoryItem,
    clearHistory,
    toggleFavorite,
    loadFavorites,
    swapLanguages,
    setSourceLanguage,
    setTargetLanguage,
    useLanguagePair
  }
}, {
  persist: {
    key: 'translation-store',
    paths: ['currentSourceLang', 'currentTargetLang', 'lastTranslation']
  }
})
