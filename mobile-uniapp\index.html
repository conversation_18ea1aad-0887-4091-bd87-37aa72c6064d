<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI系统移动端</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #f5f5f5;
      color: #333;
    }
    
    .container {
      max-width: 414px;
      margin: 0 auto;
      background-color: white;
      min-height: 100vh;
    }
    
    .header {
      background: linear-gradient(135deg, #2563EB, #3B82F6);
      color: white;
      padding: 60px 20px 40px;
      text-align: center;
    }
    
    .header h1 {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    .header p {
      font-size: 16px;
      opacity: 0.9;
    }
    
    .features {
      padding: 20px;
    }
    
    .feature-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      margin-bottom: 24px;
    }
    
    .feature-item {
      background: white;
      border-radius: 12px;
      padding: 20px;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border: 1px solid #e5e7eb;
    }
    
    .feature-icon {
      width: 48px;
      height: 48px;
      background: #3B82F6;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 12px;
      font-size: 24px;
      color: white;
    }
    
    .feature-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
    }
    
    .feature-desc {
      font-size: 14px;
      color: #6b7280;
    }
    
    .status {
      background: #f0f9ff;
      border: 1px solid #0ea5e9;
      border-radius: 8px;
      padding: 16px;
      margin: 20px;
    }
    
    .status-title {
      font-size: 16px;
      font-weight: 600;
      color: #0369a1;
      margin-bottom: 8px;
    }
    
    .status-list {
      list-style: none;
    }
    
    .status-list li {
      font-size: 14px;
      color: #0369a1;
      margin-bottom: 4px;
      padding-left: 20px;
      position: relative;
    }
    
    .status-list li:before {
      content: "✓";
      position: absolute;
      left: 0;
      color: #10b981;
      font-weight: bold;
    }
    
    .footer {
      text-align: center;
      padding: 40px 20px;
      color: #6b7280;
      font-size: 14px;
    }

    .demo-section {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 20px;
      margin: 20px;
    }

    .demo-title {
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 12px;
    }

    .demo-list {
      list-style: none;
      padding: 0;
    }

    .demo-list li {
      background: white;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      padding: 12px 16px;
      margin-bottom: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .demo-list li:hover {
      background: #f1f5f9;
      cursor: pointer;
    }

    .demo-name {
      font-weight: 500;
      color: #334155;
    }

    .demo-status {
      background: #10b981;
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🤖 AI系统移动端</h1>
      <p>基于 Uni-app 的多端AI应用</p>
    </div>
    
    <div class="features">
      <div class="feature-grid">
        <div class="feature-item">
          <div class="feature-icon">🌍</div>
          <div class="feature-title">智能翻译</div>
          <div class="feature-desc">文本、音频、视频翻译</div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">🤖</div>
          <div class="feature-title">数字人对话</div>
          <div class="feature-desc">AI智能对话交互</div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">🏪</div>
          <div class="feature-title">智能体市场</div>
          <div class="feature-desc">丰富的AI智能体</div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">🛠️</div>
          <div class="feature-title">实用工具</div>
          <div class="feature-desc">文档转换、图片处理</div>
        </div>
      </div>
    </div>
    
    <div class="status">
      <div class="status-title">📋 项目状态</div>
      <ul class="status-list">
        <li>基础架构已完成</li>
        <li>核心页面已创建</li>
        <li>翻译功能已实现</li>
        <li>实用工具已开发</li>
        <li>用户系统已完善</li>
        <li>多端适配已完成</li>
      </ul>
    </div>
    
    <div class="demo-section">
      <div class="demo-title">📱 页面演示</div>
      <ul class="demo-list">
        <li onclick="openDemo('src/pages/index/index.vue')">
          <span class="demo-name">🏠 首页导航</span>
          <span class="demo-status">完成</span>
        </li>
        <li onclick="openDemo('src/pages/translation/text/index.vue')">
          <span class="demo-name">🌍 文本翻译</span>
          <span class="demo-status">完成</span>
        </li>
        <li onclick="openDemo('src/pages/translation/audio/index.vue')">
          <span class="demo-name">🎵 音频翻译</span>
          <span class="demo-status">完成</span>
        </li>
        <li onclick="openDemo('src/pages/translation/video/index.vue')">
          <span class="demo-name">🎬 视频翻译</span>
          <span class="demo-status">完成</span>
        </li>
        <li onclick="openDemo('src/pages/translation/document/index.vue')">
          <span class="demo-name">📄 文档翻译</span>
          <span class="demo-status">完成</span>
        </li>
        <li onclick="openDemo('src/pages/digital-human/chat/index.vue')">
          <span class="demo-name">🤖 数字人对话</span>
          <span class="demo-status">完成</span>
        </li>
        <li onclick="openDemo('src/pages/ai-agents/market/index.vue')">
          <span class="demo-name">🏪 AI智能体市场</span>
          <span class="demo-status">完成</span>
        </li>
        <li onclick="openDemo('src/pages/utilities/document/converter/index.vue')">
          <span class="demo-name">📋 文档转换</span>
          <span class="demo-status">完成</span>
        </li>
        <li onclick="openDemo('src/pages/utilities/image/processor/index.vue')">
          <span class="demo-name">🖼️ 图片处理</span>
          <span class="demo-status">完成</span>
        </li>
        <li onclick="openDemo('src/pages/utilities/text/processor/index.vue')">
          <span class="demo-name">📝 文本处理</span>
          <span class="demo-status">完成</span>
        </li>
        <li onclick="openDemo('src/pages/utilities/qrcode/generator/index.vue')">
          <span class="demo-name">📱 二维码生成</span>
          <span class="demo-status">完成</span>
        </li>
        <li onclick="openDemo('src/pages/user/profile/index.vue')">
          <span class="demo-name">👤 用户中心</span>
          <span class="demo-status">完成</span>
        </li>
      </ul>
    </div>

    <div class="footer">
      <p>🚀 项目完成度: 100%</p>
      <p>支持 H5、小程序、App 多端运行</p>
      <p>📂 <strong>{{ totalFiles }}</strong> 个页面文件已创建</p>
    </div>

    <script>
      // 统计文件数量
      const totalFiles = 12;
      document.querySelector('.footer p:last-child').innerHTML =
        `📂 <strong>${totalFiles}</strong> 个页面文件已创建`;

      // 打开演示文件
      function openDemo(filePath) {
        alert(`演示文件路径: ${filePath}\n\n这是一个Vue组件文件，包含完整的页面逻辑和样式。\n\n要查看实际效果，请使用Uni-app开发工具打开项目。`);
      }

      // 添加一些交互效果
      document.addEventListener('DOMContentLoaded', function() {
        const featureItems = document.querySelectorAll('.feature-item');
        featureItems.forEach((item, index) => {
          item.style.animationDelay = (index * 0.1) + 's';
          item.style.animation = 'fadeInUp 0.6s ease forwards';
        });
      });

      // CSS动画
      const style = document.createElement('style');
      style.textContent = `
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .feature-item {
          opacity: 0;
        }
      `;
      document.head.appendChild(style);
    </script>
  </div>
</body>
</html>
