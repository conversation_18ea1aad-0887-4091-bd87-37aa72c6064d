<template>
  <div class="dashboard-container">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="user-greeting">
          <a-avatar :size="80" :src="userAvatar || '/logo.png'" class="user-avatar" />
          <div class="greeting-text">
            <h1>欢迎回来，{{ userName }}！</h1>
            <p>AI专业平台管理系统 - {{ currentDate }}</p>
          </div>
        </div>
        <div class="quick-stats">
          <div class="stat-item">
            <div class="stat-icon digital-human">
              <robot-outlined />
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ platformStats.digitalHumans }}</div>
              <div class="stat-label">数字人</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon users">
              <team-outlined />
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ platformStats.totalUsers }}</div>
              <div class="stat-label">用户</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon translations">
              <global-outlined />
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ platformStats.translations }}</div>
              <div class="stat-label">翻译任务</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon videos">
              <video-camera-outlined />
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ platformStats.videos }}</div>
              <div class="stat-label">视频生成</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <a-row :gutter="24" class="main-content">
      <!-- 平台概览 -->
      <a-col :xs="24" :lg="16">
        <a-card title="平台数据概览" :bordered="false" class="overview-card">
          <div class="chart-container">
            <div ref="chartRef" style="width: 100%; height: 300px;"></div>
          </div>
        </a-card>
      </a-col>

      <!-- 快捷操作 -->
      <a-col :xs="24" :lg="8">
        <a-card title="快捷操作" :bordered="false" class="actions-card">
          <div class="action-grid">
            <div class="action-item" @click="navigateTo('/system/user')">
              <div class="action-icon user-mgmt">
                <user-outlined />
              </div>
              <div class="action-text">
                <div class="action-title">用户管理</div>
                <div class="action-desc">管理系统用户</div>
              </div>
            </div>
            <div class="action-item" @click="navigateTo('/digital-human')">
              <div class="action-icon dh-mgmt">
                <robot-outlined />
              </div>
              <div class="action-text">
                <div class="action-title">数字人管理</div>
                <div class="action-desc">管理数字人资源</div>
              </div>
            </div>
            <div class="action-item" @click="navigateTo('/monitor/log')">
              <div class="action-icon log-mgmt">
                <file-text-outlined />
              </div>
              <div class="action-text">
                <div class="action-title">系统日志</div>
                <div class="action-desc">查看操作日志</div>
              </div>
            </div>
            <div class="action-item" @click="navigateTo('/system/settings')">
              <div class="action-icon settings">
                <setting-outlined />
              </div>
              <div class="action-text">
                <div class="action-title">系统设置</div>
                <div class="action-desc">配置系统参数</div>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="24" style="margin-top: 24px">
      <!-- 快捷操作区 -->
      <a-col :xs="24" :md="12" :lg="6">
        <a-card class="shortcut-card" :bordered="false">
          <template #title>
            <div class="card-title">
              <environment-outlined />
              <span>快捷操作</span>
            </div>
          </template>
          <div class="shortcuts">
            <a-row :gutter="[16, 16]">
              <a-col :span="12" v-for="item in shortcuts" :key="item.title">
                <div class="shortcut-item" @click="navigate(item.route)">
                  <component :is="item.icon" class="shortcut-icon" />
                  <div class="shortcut-title">{{ item.title }}</div>
                </div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>

      <!-- 系统信息 -->
      <a-col :xs="24" :md="12" :lg="6">
        <a-card class="system-card" :bordered="false">
          <template #title>
            <div class="card-title">
              <desktop-outlined />
              <span>系统信息</span>
            </div>
          </template>
          <a-descriptions :column="1" size="small">
            <a-descriptions-item label="系统版本">v1.0.0</a-descriptions-item>
            <a-descriptions-item label="Node版本">{{ systemInfo.nodeVersion }}</a-descriptions-item>
            <a-descriptions-item label="服务器时间">{{ systemInfo.serverTime }}</a-descriptions-item>
            <a-descriptions-item label="服务器IP">{{ systemInfo.serverIp }}</a-descriptions-item>
            <a-descriptions-item label="运行时长">{{ systemInfo.uptime }}</a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>

      <!-- 待办任务 -->
      <a-col :xs="24" :md="12" :lg="12">
        <a-card class="todo-card" :bordered="false">
          <template #title>
            <div class="card-title">
              <check-circle-outlined />
              <span>待办任务</span>
            </div>
          </template>
          <template #extra>
            <a-button type="link" @click="refreshTodos">刷新</a-button>
          </template>
          <a-list
            :loading="todosLoading"
            :data-source="todos"
            :pagination="false"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <div class="todo-title">
                      <a-tag :color="item.priority === 'high' ? 'red' : item.priority === 'medium' ? 'orange' : 'blue'">
                        {{ item.priority === 'high' ? '高' : item.priority === 'medium' ? '中' : '低' }}
                      </a-tag>
                      <span>{{ item.title }}</span>
                    </div>
                  </template>
                  <template #description>
                    <div class="todo-desc">{{ item.description }}</div>
                    <div class="todo-time">截止时间: {{ item.deadline }}</div>
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <a-button type="link" size="small" @click="completeTodo(item)">完成</a-button>
                </template>
              </a-list-item>
            </template>
            <template #empty>
              <a-empty description="暂无待办任务" />
            </template>
          </a-list>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="24" style="margin-top: 24px">
      <!-- 活跃用户统计 -->
      <a-col :span="24">
        <a-card :bordered="false">
          <template #title>
            <div class="card-title">
              <line-chart-outlined />
              <span>用户活跃度</span>
            </div>
          </template>
          <template #extra>
            <a-radio-group v-model:value="chartTimeRange" button-style="solid" size="small">
              <a-radio-button value="week">本周</a-radio-button>
              <a-radio-button value="month">本月</a-radio-button>
              <a-radio-button value="year">本年</a-radio-button>
            </a-radio-group>
          </template>
          <div class="chart-container" ref="chartRef"></div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import * as echarts from 'echarts';
import {
  RobotOutlined,
  TeamOutlined,
  GlobalOutlined,
  VideoCameraOutlined,
  UserOutlined,
  FileTextOutlined,
  SettingOutlined
} from '@ant-design/icons-vue';

// 模拟数据
const getDashboardData = () => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data: {
          user: {
            name: '管理员',
            avatar: '',
            role: '超级管理员'
          },
          stats: {
            totalUsers: 3582,
            totalRoles: 8,
            totalMenus: 56,
            logCount: 198
          },
          systemInfo: {
            nodeVersion: 'v16.15.0',
            serverTime: new Date().toLocaleString(),
            serverIp: '*************',
            uptime: '10天4小时26分钟'
          },
          todos: [
            {
              id: 1,
              title: '系统安全更新',
              description: '需要对系统进行安全更新，修复潜在漏洞',
              priority: 'high',
              deadline: '2023-10-15 18:00'
            },
            {
              id: 2,
              title: '用户反馈处理',
              description: '处理用户提交的反馈信息，优先级中',
              priority: 'medium',
              deadline: '2023-10-16 12:00'
            },
            {
              id: 3,
              title: '数据库备份',
              description: '执行每周数据库备份任务',
              priority: 'medium',
              deadline: '2023-10-17 10:00'
            },
            {
              id: 4,
              title: '更新操作手册',
              description: '更新系统操作手册，添加新功能使用说明',
              priority: 'low',
              deadline: '2023-10-20 18:00'
            }
          ],
          chartData: {
            week: {
              xAxis: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
              series: [
                {
                  name: '活跃用户',
                  data: [120, 132, 101, 134, 90, 230, 210]
                },
                {
                  name: '新增用户',
                  data: [20, 32, 18, 34, 25, 38, 35]
                }
              ]
            },
            month: {
              xAxis: Array.from({ length: 30 }, (_, i) => `${i + 1}日`),
              series: [
                {
                  name: '活跃用户',
                  data: Array.from({ length: 30 }, () => Math.floor(Math.random() * 300 + 100))
                },
                {
                  name: '新增用户',
                  data: Array.from({ length: 30 }, () => Math.floor(Math.random() * 50 + 10))
                }
              ]
            },
            year: {
              xAxis: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
              series: [
                {
                  name: '活跃用户',
                  data: [900, 850, 950, 1000, 1100, 1050, 1150, 1200, 1100, 1050, 1150, 1200]
                },
                {
                  name: '新增用户',
                  data: [120, 132, 101, 134, 90, 230, 210, 180, 190, 230, 245, 260]
                }
              ]
            }
          }
        }
      });
    }, 500);
  });
};

export default defineComponent({
  name: 'Dashboard',
  components: {
    RobotOutlined,
    TeamOutlined,
    GlobalOutlined,
    VideoCameraOutlined,
    UserOutlined,
    FileTextOutlined,
    SettingOutlined
  },
  setup() {
    const router = useRouter();
    const chartRef = ref(null);

    // 当前日期
    const currentDate = computed(() => {
      const now = new Date();
      return now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      });
    });

    // 用户信息
    const userName = ref('管理员');
    const userAvatar = ref('/logo.png');
    const userRole = ref('系统管理员');

    // 平台统计数据
    const platformStats = reactive({
      digitalHumans: 156,
      totalUsers: 2847,
      translations: 8932,
      videos: 1245
    });

    // 导航到指定页面
    const navigateTo = (path) => {
      router.push(path);
    };

    // 初始化图表
    const initChart = () => {
      if (!chartRef.value) return;

      const chart = echarts.init(chartRef.value);
      const option = {
        title: {
          text: '平台使用趋势',
          left: 'center',
          textStyle: {
            color: '#333',
            fontSize: 16,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['数字人创建', '翻译任务', '视频生成', '用户注册'],
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '数字人创建',
            type: 'line',
            smooth: true,
            data: [12, 18, 25, 32, 28, 45, 56],
            itemStyle: { color: '#667eea' }
          },
          {
            name: '翻译任务',
            type: 'line',
            smooth: true,
            data: [45, 52, 68, 75, 89, 102, 125],
            itemStyle: { color: '#764ba2' }
          },
          {
            name: '视频生成',
            type: 'line',
            smooth: true,
            data: [8, 12, 15, 22, 18, 28, 35],
            itemStyle: { color: '#f093fb' }
          },
          {
            name: '用户注册',
            type: 'line',
            smooth: true,
            data: [25, 35, 42, 58, 65, 78, 95],
            itemStyle: { color: '#52c41a' }
          }
        ]
      };

      chart.setOption(option);

      // 响应式调整
      window.addEventListener('resize', () => {
        chart.resize();
      });
    };

    // 组件挂载时初始化
    onMounted(async () => {
      await nextTick();
      initChart();
    });

    return {
      currentDate,
      userName,
      userAvatar,
      userRole,
      platformStats,
      chartRef,
      navigateTo
    };
  }
});
</script>

<style lang="less" scoped>
.dashboard-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: calc(100vh - 64px);
}

.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 30px 30px;
    animation: float 20s ease-in-out infinite;
  }

  .banner-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;

    .user-greeting {
      display: flex;
      align-items: center;

      .user-avatar {
        margin-right: 24px;
        border: 3px solid rgba(255, 255, 255, 0.3);
      }

      .greeting-text {
        h1 {
          margin: 0 0 8px 0;
          font-size: 32px;
          font-weight: 700;
        }

        p {
          margin: 0;
          font-size: 16px;
          opacity: 0.9;
        }
      }
    }

    .quick-stats {
      display: flex;
      gap: 32px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 16px;

        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;

          &.digital-human {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
          }

          &.users {
            background: rgba(82, 196, 26, 0.2);
            color: #52c41a;
          }

          &.translations {
            background: rgba(24, 144, 255, 0.2);
            color: #1890ff;
          }

          &.videos {
            background: rgba(245, 34, 45, 0.2);
            color: #f5222d;
          }
        }

        .stat-info {
          .stat-number {
            font-size: 28px;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 14px;
            opacity: 0.8;
          }
        }
      }
    }
  }
}

.main-content {
  .overview-card, .actions-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    :deep(.ant-card-head) {
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }
  }

  .chart-container {
    padding: 16px 0;
  }

  .action-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;

    .action-item {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #f8fafc;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &:hover {
        background: #fff;
        border-color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.15);
      }

      .action-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        margin-right: 16px;

        &.user-mgmt {
          background: linear-gradient(135deg, #667eea, #764ba2);
          color: white;
        }

        &.dh-mgmt {
          background: linear-gradient(135deg, #f093fb, #f5576c);
          color: white;
        }

        &.log-mgmt {
          background: linear-gradient(135deg, #4facfe, #00f2fe);
          color: white;
        }

        &.settings {
          background: linear-gradient(135deg, #43e97b, #38f9d7);
          color: white;
        }
      }

      .action-text {
        .action-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 4px;
        }

        .action-desc {
          font-size: 13px;
          color: #666;
        }
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .welcome-banner .banner-content {
    flex-direction: column;
    gap: 24px;
    text-align: center;

    .quick-stats {
      justify-content: center;
      flex-wrap: wrap;
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .welcome-banner {
    padding: 24px 20px;

    .banner-content .quick-stats {
      gap: 16px;

      .stat-item {
        flex-direction: column;
        text-align: center;
        gap: 8px;
      }
    }
  }

  .action-grid {
    grid-template-columns: 1fr;
  }
}
</style> 