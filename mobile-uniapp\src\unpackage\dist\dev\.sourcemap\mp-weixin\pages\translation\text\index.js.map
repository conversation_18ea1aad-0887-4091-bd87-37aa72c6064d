{"version": 3, "file": "index.js", "sources": ["pages/translation/text/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdHJhbnNsYXRpb24vdGV4dC9pbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"translation-page\">\n    <!-- 语言选择栏 -->\n    <view class=\"language-bar\">\n      <view class=\"language-selector\">\n        <u-button \n          type=\"text\" \n          :text=\"sourceLanguage?.name || '自动检测'\" \n          @click=\"showSourceLanguagePicker = true\"\n          :custom-style=\"{ color: '#2563EB', fontSize: '28rpx' }\"\n        >\n          <u-icon name=\"arrow-down\" size=\"12\" color=\"#2563EB\" margin-left=\"4\"></u-icon>\n        </u-button>\n      </view>\n      \n      <view class=\"swap-button\" @click=\"handleSwapLanguages\">\n        <u-icon name=\"arrow-left-right\" size=\"20\" color=\"#6B7280\"></u-icon>\n      </view>\n      \n      <view class=\"language-selector\">\n        <u-button \n          type=\"text\" \n          :text=\"targetLanguage?.name || '中文'\" \n          @click=\"showTargetLanguagePicker = true\"\n          :custom-style=\"{ color: '#2563EB', fontSize: '28rpx' }\"\n        >\n          <u-icon name=\"arrow-down\" size=\"12\" color=\"#2563EB\" margin-left=\"4\"></u-icon>\n        </u-button>\n      </view>\n    </view>\n\n    <!-- 快速语言对 -->\n    <view class=\"quick-pairs\" v-if=\"recentLanguagePairs.length > 0\">\n      <scroll-view class=\"pairs-scroll\" scroll-x=\"true\" show-scrollbar=\"false\">\n        <view class=\"pairs-list\">\n          <view \n            class=\"pair-item\" \n            v-for=\"(pair, index) in recentLanguagePairs\" \n            :key=\"index\"\n            @click=\"useLanguagePair(pair.source, pair.target)\"\n          >\n            <text class=\"pair-text\">{{ getLanguageName(pair.source) }} → {{ getLanguageName(pair.target) }}</text>\n          </view>\n        </view>\n      </scroll-view>\n    </view>\n\n    <!-- 翻译输入区域 -->\n    <view class=\"translation-container\">\n      <!-- 源文本输入 -->\n      <view class=\"input-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">{{ sourceLanguage?.name || '自动检测' }}</text>\n          <view class=\"header-actions\">\n            <u-icon \n              v-if=\"sourceText\" \n              name=\"close-circle-fill\" \n              size=\"18\" \n              color=\"#9CA3AF\" \n              @click=\"clearSourceText\"\n            ></u-icon>\n            <u-icon \n              name=\"mic\" \n              size=\"18\" \n              color=\"#2563EB\" \n              margin-left=\"12\"\n              @click=\"startVoiceInput\"\n            ></u-icon>\n          </view>\n        </view>\n        \n        <textarea \n          class=\"text-input\"\n          v-model=\"sourceText\"\n          placeholder=\"请输入要翻译的文本...\"\n          :maxlength=\"5000\"\n          auto-height\n          @input=\"handleInputChange\"\n          @focus=\"inputFocused = true\"\n          @blur=\"inputFocused = false\"\n        ></textarea>\n        \n        <view class=\"input-footer\">\n          <text class=\"char-count\">{{ sourceText.length }}/5000</text>\n          <u-button \n            type=\"primary\" \n            size=\"small\"\n            :loading=\"isTranslating\"\n            :disabled=\"!sourceText.trim()\"\n            @click=\"handleTranslate\"\n          >\n            {{ isTranslating ? '翻译中...' : '翻译' }}\n          </u-button>\n        </view>\n      </view>\n\n      <!-- 翻译结果 -->\n      <view class=\"result-section\" v-if=\"translationResult\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">{{ targetLanguage?.name || '中文' }}</text>\n          <view class=\"header-actions\">\n            <u-icon \n              name=\"copy\" \n              size=\"18\" \n              color=\"#2563EB\" \n              @click=\"copyResult\"\n            ></u-icon>\n            <u-icon \n              name=\"share\" \n              size=\"18\" \n              color=\"#2563EB\" \n              margin-left=\"12\"\n              @click=\"shareResult\"\n            ></u-icon>\n            <u-icon \n              :name=\"isResultFavorite ? 'heart-fill' : 'heart'\" \n              size=\"18\" \n              :color=\"isResultFavorite ? '#EF4444' : '#2563EB'\"\n              margin-left=\"12\"\n              @click=\"toggleResultFavorite\"\n            ></u-icon>\n          </view>\n        </view>\n        \n        <view class=\"result-content\">\n          <text class=\"result-text\" selectable>{{ translationResult.translated_text }}</text>\n        </view>\n        \n        <view class=\"result-footer\">\n          <text class=\"result-info\">\n            置信度: {{ Math.round(translationResult.confidence * 100) }}% | \n            字数: {{ translationResult.word_count }}\n          </text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 历史记录 -->\n    <view class=\"history-section\" v-if=\"translationHistory.length > 0\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">翻译历史</text>\n        <u-button \n          type=\"text\" \n          text=\"查看全部\" \n          size=\"small\"\n          @click=\"showHistoryModal = true\"\n          :custom-style=\"{ color: '#2563EB', fontSize: '24rpx' }\"\n        ></u-button>\n      </view>\n      \n      <view class=\"history-list\">\n        <view \n          class=\"history-item\" \n          v-for=\"(item, index) in translationHistory.slice(0, 3)\" \n          :key=\"item.id\"\n          @click=\"useHistoryItem(item)\"\n        >\n          <view class=\"history-content\">\n            <text class=\"history-source\">{{ item.source_text }}</text>\n            <text class=\"history-target\">{{ item.translated_text }}</text>\n          </view>\n          <view class=\"history-meta\">\n            <text class=\"history-langs\">{{ getLanguageName(item.source_lang) }} → {{ getLanguageName(item.target_lang) }}</text>\n            <text class=\"history-time\">{{ formatTime(item.created_at) }}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 源语言选择器 -->\n    <u-picker\n      v-model=\"showSourceLanguagePicker\"\n      :columns=\"sourceLanguageColumns\"\n      @confirm=\"onSourceLanguageConfirm\"\n      @cancel=\"showSourceLanguagePicker = false\"\n    ></u-picker>\n\n    <!-- 目标语言选择器 -->\n    <u-picker\n      v-model=\"showTargetLanguagePicker\"\n      :columns=\"targetLanguageColumns\"\n      @confirm=\"onTargetLanguageConfirm\"\n      @cancel=\"showTargetLanguagePicker = false\"\n    ></u-picker>\n\n    <!-- 历史记录弹窗 -->\n    <u-modal \n      v-model=\"showHistoryModal\" \n      title=\"翻译历史\"\n      :show-cancel-button=\"true\"\n      cancel-text=\"清空\"\n      confirm-text=\"关闭\"\n      @cancel=\"handleClearHistory\"\n      @confirm=\"showHistoryModal = false\"\n    >\n      <view class=\"history-modal\">\n        <scroll-view class=\"history-scroll\" scroll-y=\"true\" style=\"height: 600rpx;\">\n          <view \n            class=\"history-modal-item\" \n            v-for=\"item in translationHistory\" \n            :key=\"item.id\"\n            @click=\"useHistoryItem(item)\"\n          >\n            <view class=\"modal-item-content\">\n              <text class=\"modal-source\">{{ item.source_text }}</text>\n              <text class=\"modal-target\">{{ item.translated_text }}</text>\n              <view class=\"modal-meta\">\n                <text class=\"modal-langs\">{{ getLanguageName(item.source_lang) }} → {{ getLanguageName(item.target_lang) }}</text>\n                <text class=\"modal-time\">{{ formatTime(item.created_at) }}</text>\n              </view>\n            </view>\n            <u-icon \n              name=\"delete\" \n              size=\"16\" \n              color=\"#EF4444\"\n              @click.stop=\"deleteHistoryItem(item.id)\"\n            ></u-icon>\n          </view>\n        </scroll-view>\n      </view>\n    </u-modal>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useTranslationStore } from '@/store/modules/translation'\nimport { type TranslationResponse, type TranslationHistory } from '@/api/translation'\nimport dayjs from 'dayjs'\nimport relativeTime from 'dayjs/plugin/relativeTime'\n\ndayjs.extend(relativeTime)\n\n// Store\nconst translationStore = useTranslationStore()\n\n// 响应式数据\nconst sourceText = ref<string>('')\nconst translationResult = ref<TranslationResponse | null>(null)\nconst inputFocused = ref<boolean>(false)\nconst showSourceLanguagePicker = ref<boolean>(false)\nconst showTargetLanguagePicker = ref<boolean>(false)\nconst showHistoryModal = ref<boolean>(false)\nconst isResultFavorite = ref<boolean>(false)\n\n// 计算属性\nconst {\n  languages,\n  currentSourceLang,\n  currentTargetLang,\n  translationHistory,\n  isTranslating,\n  sourceLanguage,\n  targetLanguage,\n  recentLanguagePairs\n} = translationStore\n\n// 语言选择器数据\nconst sourceLanguageColumns = computed(() => {\n  const autoDetect = [{ text: '自动检测', value: 'auto' }]\n  const languageOptions = languages.map(lang => ({\n    text: lang.name,\n    value: lang.code\n  }))\n  return [autoDetect.concat(languageOptions)]\n})\n\nconst targetLanguageColumns = computed(() => {\n  const languageOptions = languages.filter(lang => lang.code !== 'auto').map(lang => ({\n    text: lang.name,\n    value: lang.code\n  }))\n  return [languageOptions]\n})\n\n// 页面加载\nonMounted(async () => {\n  // 加载语言列表\n  await translationStore.loadLanguages()\n\n  // 加载翻译历史\n  await translationStore.loadHistory()\n\n  // 如果有上次的翻译结果，显示它\n  if (translationStore.lastTranslation) {\n    sourceText.value = translationStore.lastTranslation.source\n    translationResult.value = {\n      translated_text: translationStore.lastTranslation.target,\n      source_lang: translationStore.lastTranslation.sourceLang,\n      target_lang: translationStore.lastTranslation.targetLang,\n      confidence: 0.95,\n      word_count: translationStore.lastTranslation.source.split(' ').length,\n      char_count: translationStore.lastTranslation.source.length\n    }\n  }\n})\n\n// 监听输入变化，实现防抖翻译\nlet inputTimer: NodeJS.Timeout | null = null\nconst handleInputChange = () => {\n  if (inputTimer) {\n    clearTimeout(inputTimer)\n  }\n\n  // 清除之前的翻译结果\n  if (translationResult.value) {\n    translationResult.value = null\n    isResultFavorite.value = false\n  }\n\n  // 如果开启了自动翻译且文本长度合适，则自动翻译\n  if (sourceText.value.trim().length > 0 && sourceText.value.trim().length < 500) {\n    inputTimer = setTimeout(() => {\n      handleTranslate()\n    }, 1000) // 1秒后自动翻译\n  }\n}\n\n// 执行翻译\nconst handleTranslate = async () => {\n  if (!sourceText.value.trim()) {\n    uni.showToast({\n      title: '请输入要翻译的文本',\n      icon: 'none'\n    })\n    return\n  }\n\n  try {\n    const result = await translationStore.translateText(sourceText.value.trim())\n    translationResult.value = result\n    isResultFavorite.value = false\n\n    // 检查是否已收藏\n    checkIfFavorite()\n\n  } catch (error: any) {\n    uni.__f__('error','at pages/translation/text/index.vue:338','翻译失败:', error)\n    uni.showToast({\n      title: error.message || '翻译失败',\n      icon: 'error'\n    })\n  }\n}\n\n// 检查是否已收藏\nconst checkIfFavorite = () => {\n  if (!translationResult.value) return\n\n  // 这里可以根据实际需求检查当前翻译是否已收藏\n  // 暂时设为false\n  isResultFavorite.value = false\n}\n\n// 清除源文本\nconst clearSourceText = () => {\n  sourceText.value = ''\n  translationResult.value = null\n  isResultFavorite.value = false\n}\n\n// 开始语音输入\nconst startVoiceInput = () => {\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  uni.showToast({\n    title: '小程序暂不支持语音输入',\n    icon: 'none'\n  })\n\n}\n\n// 复制翻译结果\nconst copyResult = () => {\n  if (!translationResult.value) return\n\n  uni.setClipboardData({\n    data: translationResult.value.translated_text,\n    success: () => {\n      uni.showToast({\n        title: '复制成功',\n        icon: 'success'\n      })\n    },\n    fail: () => {\n      uni.showToast({\n        title: '复制失败',\n        icon: 'error'\n      })\n    }\n  })\n}\n\n// 分享翻译结果\nconst shareResult = () => {\n  if (!translationResult.value) return\n\n  const shareContent = `原文: ${sourceText.value}\\n译文: ${translationResult.value.translated_text}`\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  uni.setClipboardData({\n    data: shareContent,\n    success: () => {\n      uni.showToast({\n        title: '内容已复制，可分享到其他应用',\n        icon: 'success'\n      })\n    }\n  })\n\n}\n\n// 切换收藏状态\nconst toggleResultFavorite = async () => {\n  if (!translationResult.value) return\n\n  // 这里需要根据实际的收藏逻辑来实现\n  // 暂时只是切换本地状态\n  isResultFavorite.value = !isResultFavorite.value\n\n  uni.showToast({\n    title: isResultFavorite.value ? '收藏成功' : '取消收藏',\n    icon: 'success'\n  })\n}\n\n// 交换语言\nconst handleSwapLanguages = () => {\n  translationStore.swapLanguages()\n\n  // 如果有翻译结果，交换源文本和目标文本\n  if (translationResult.value) {\n    const temp = sourceText.value\n    sourceText.value = translationResult.value.translated_text\n    translationResult.value = null\n    isResultFavorite.value = false\n  }\n}\n\n// 语言选择确认\nconst onSourceLanguageConfirm = (value: any) => {\n  translationStore.setSourceLanguage(value[0].value)\n  showSourceLanguagePicker.value = false\n\n  // 如果有文本，重新翻译\n  if (sourceText.value.trim()) {\n    handleTranslate()\n  }\n}\n\nconst onTargetLanguageConfirm = (value: any) => {\n  translationStore.setTargetLanguage(value[0].value)\n  showTargetLanguagePicker.value = false\n\n  // 如果有文本，重新翻译\n  if (sourceText.value.trim()) {\n    handleTranslate()\n  }\n}\n\n// 使用语言对\nconst useLanguagePair = (sourceLang: string, targetLang: string) => {\n  translationStore.useLanguagePair(sourceLang, targetLang)\n\n  // 如果有文本，重新翻译\n  if (sourceText.value.trim()) {\n    handleTranslate()\n  }\n}\n\n// 使用历史记录项\nconst useHistoryItem = (item: TranslationHistory) => {\n  sourceText.value = item.source_text\n  translationResult.value = {\n    translated_text: item.translated_text,\n    source_lang: item.source_lang,\n    target_lang: item.target_lang,\n    confidence: 0.95,\n    word_count: item.word_count || item.source_text.split(' ').length,\n    char_count: item.source_text.length\n  }\n\n  // 设置语言\n  translationStore.setSourceLanguage(item.source_lang)\n  translationStore.setTargetLanguage(item.target_lang)\n\n  // 关闭弹窗\n  showHistoryModal.value = false\n\n  // 检查收藏状态\n  checkIfFavorite()\n}\n\n// 删除历史记录项\nconst deleteHistoryItem = async (id: string) => {\n  uni.showModal({\n    title: '确认删除',\n    content: '确定要删除这条翻译记录吗？',\n    success: async (res) => {\n      if (res.confirm) {\n        await translationStore.deleteHistoryItem(id)\n      }\n    }\n  })\n}\n\n// 清空历史记录\nconst handleClearHistory = () => {\n  uni.showModal({\n    title: '确认清空',\n    content: '确定要清空所有翻译历史吗？此操作不可恢复。',\n    success: async (res) => {\n      if (res.confirm) {\n        await translationStore.clearHistory()\n        showHistoryModal.value = false\n      }\n    }\n  })\n}\n\n// 获取语言名称\nconst getLanguageName = (langCode: string) => {\n  if (langCode === 'auto') return '自动检测'\n  const lang = languages.find(l => l.code === langCode)\n  return lang?.name || langCode\n}\n\n// 格式化时间\nconst formatTime = (time: string) => {\n  return dayjs(time).fromNow()\n}\n\n// 页面下拉刷新\nconst onPullDownRefresh = async () => {\n  try {\n    await translationStore.loadHistory()\n    uni.showToast({\n      title: '刷新成功',\n      icon: 'success'\n    })\n  } catch (error) {\n    uni.showToast({\n      title: '刷新失败',\n      icon: 'error'\n    })\n  } finally {\n    uni.stopPullDownRefresh()\n  }\n}\n\n// 导出方法供页面使用\ndefineExpose({\n  onPullDownRefresh\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.translation-page {\n  min-height: 100vh;\n  background-color: $bg-secondary;\n}\n\n// 语言选择栏\n.language-bar {\n  background-color: $bg-primary;\n  padding: $spacing-4;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  border-bottom: 1px solid $border-primary;\n\n  .language-selector {\n    flex: 1;\n    text-align: center;\n  }\n\n  .swap-button {\n    width: 80rpx;\n    height: 80rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background-color: $bg-tertiary;\n    border-radius: $radius-full;\n    margin: 0 $spacing-4;\n\n    &:active {\n      background-color: $gray-200;\n    }\n  }\n}\n\n// 快速语言对\n.quick-pairs {\n  background-color: $bg-primary;\n  padding: $spacing-2 0;\n  border-bottom: 1px solid $border-primary;\n\n  .pairs-scroll {\n    white-space: nowrap;\n\n    .pairs-list {\n      display: flex;\n      padding: 0 $spacing-4;\n      gap: $spacing-2;\n\n      .pair-item {\n        flex-shrink: 0;\n        background-color: $bg-tertiary;\n        border-radius: $radius-full;\n        padding: $spacing-1 $spacing-3;\n\n        .pair-text {\n          font-size: $font-size-xs;\n          color: $text-secondary;\n          white-space: nowrap;\n        }\n\n        &:active {\n          background-color: $gray-200;\n        }\n      }\n    }\n  }\n}\n\n// 翻译容器\n.translation-container {\n  padding: $spacing-4;\n  gap: $spacing-4;\n  display: flex;\n  flex-direction: column;\n}\n\n// 输入区域\n.input-section {\n  background-color: $bg-primary;\n  border-radius: $radius-lg;\n  overflow: hidden;\n  box-shadow: $shadow-sm;\n\n  .section-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: $spacing-3 $spacing-4;\n    background-color: $bg-tertiary;\n    border-bottom: 1px solid $border-primary;\n\n    .section-title {\n      font-size: $font-size-sm;\n      font-weight: $font-weight-medium;\n      color: $text-secondary;\n    }\n\n    .header-actions {\n      display: flex;\n      align-items: center;\n      gap: $spacing-2;\n    }\n  }\n\n  .text-input {\n    width: 100%;\n    min-height: 200rpx;\n    padding: $spacing-4;\n    font-size: $font-size-base;\n    line-height: $line-height-relaxed;\n    color: $text-primary;\n    background-color: transparent;\n    border: none;\n    outline: none;\n    resize: none;\n\n    &::placeholder {\n      color: $text-quaternary;\n    }\n  }\n\n  .input-footer {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: $spacing-3 $spacing-4;\n    background-color: $bg-tertiary;\n    border-top: 1px solid $border-primary;\n\n    .char-count {\n      font-size: $font-size-xs;\n      color: $text-tertiary;\n    }\n  }\n}\n\n// 结果区域\n.result-section {\n  background-color: $bg-primary;\n  border-radius: $radius-lg;\n  overflow: hidden;\n  box-shadow: $shadow-sm;\n\n  .section-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: $spacing-3 $spacing-4;\n    background-color: $primary-50;\n    border-bottom: 1px solid $primary-200;\n\n    .section-title {\n      font-size: $font-size-sm;\n      font-weight: $font-weight-medium;\n      color: $primary-700;\n    }\n\n    .header-actions {\n      display: flex;\n      align-items: center;\n      gap: $spacing-2;\n    }\n  }\n\n  .result-content {\n    padding: $spacing-4;\n\n    .result-text {\n      font-size: $font-size-base;\n      line-height: $line-height-relaxed;\n      color: $text-primary;\n      word-break: break-word;\n    }\n  }\n\n  .result-footer {\n    padding: $spacing-3 $spacing-4;\n    background-color: $bg-tertiary;\n    border-top: 1px solid $border-primary;\n\n    .result-info {\n      font-size: $font-size-xs;\n      color: $text-tertiary;\n    }\n  }\n}\n\n// 历史记录区域\n.history-section {\n  background-color: $bg-primary;\n  border-radius: $radius-lg;\n  overflow: hidden;\n  box-shadow: $shadow-sm;\n\n  .section-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: $spacing-3 $spacing-4;\n    background-color: $bg-tertiary;\n    border-bottom: 1px solid $border-primary;\n\n    .section-title {\n      font-size: $font-size-sm;\n      font-weight: $font-weight-medium;\n      color: $text-secondary;\n    }\n  }\n\n  .history-list {\n    .history-item {\n      padding: $spacing-4;\n      border-bottom: 1px solid $border-primary;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &:active {\n        background-color: $bg-tertiary;\n      }\n\n      .history-content {\n        margin-bottom: $spacing-2;\n\n        .history-source {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          margin-bottom: $spacing-1;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n\n        .history-target {\n          display: block;\n          font-size: $font-size-base;\n          color: $text-primary;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n      }\n\n      .history-meta {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n\n        .history-langs {\n          font-size: $font-size-xs;\n          color: $text-tertiary;\n        }\n\n        .history-time {\n          font-size: $font-size-xs;\n          color: $text-quaternary;\n        }\n      }\n    }\n  }\n}\n\n// 历史记录弹窗\n.history-modal {\n  .history-scroll {\n    .history-modal-item {\n      display: flex;\n      align-items: flex-start;\n      padding: $spacing-3;\n      border-bottom: 1px solid $border-primary;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &:active {\n        background-color: $bg-tertiary;\n      }\n\n      .modal-item-content {\n        flex: 1;\n        margin-right: $spacing-3;\n\n        .modal-source {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          margin-bottom: $spacing-1;\n          line-height: $line-height-snug;\n        }\n\n        .modal-target {\n          display: block;\n          font-size: $font-size-base;\n          color: $text-primary;\n          margin-bottom: $spacing-2;\n          line-height: $line-height-snug;\n        }\n\n        .modal-meta {\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n\n          .modal-langs {\n            font-size: $font-size-xs;\n            color: $text-tertiary;\n          }\n\n          .modal-time {\n            font-size: $font-size-xs;\n            color: $text-quaternary;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 响应式适配\n@media screen and (max-width: 480px) {\n  .translation-container {\n    padding: $spacing-3;\n  }\n\n  .input-section .text-input {\n    min-height: 160rpx;\n  }\n\n  .language-bar {\n    padding: $spacing-3;\n  }\n}\n\n// 暗色模式适配\n@media (prefers-color-scheme: dark) {\n  .translation-page {\n    background-color: #1a1a1a;\n  }\n\n  .input-section,\n  .result-section,\n  .history-section {\n    background-color: #2d2d2d;\n    border-color: #404040;\n  }\n\n  .section-header {\n    background-color: #404040;\n  }\n\n  .text-input {\n    color: #ffffff;\n\n    &::placeholder {\n      color: #888888;\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/AI_system/mobile-uniapp/src/pages/translation/text/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["dayjs", "relativeTime", "useTranslationStore", "ref", "computed", "onMounted", "uni"], "mappings": ";;;;;;;;;;;;;;AAuOAA,wBAAM,OAAOC,cAAAA,YAAY;AAGzB,UAAM,mBAAmBC,0BAAAA;AAGnB,UAAA,aAAaC,kBAAY,EAAE;AAC3B,UAAA,oBAAoBA,kBAAgC,IAAI;AACxD,UAAA,eAAeA,kBAAa,KAAK;AACjC,UAAA,2BAA2BA,kBAAa,KAAK;AAC7C,UAAA,2BAA2BA,kBAAa,KAAK;AAC7C,UAAA,mBAAmBA,kBAAa,KAAK;AACrC,UAAA,mBAAmBA,kBAAa,KAAK;AAGrC,UAAA;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACE,IAAA;AAGE,UAAA,wBAAwBC,cAAAA,SAAS,MAAM;AAC3C,YAAM,aAAa,CAAC,EAAE,MAAM,QAAQ,OAAO,QAAQ;AAC7C,YAAA,kBAAkB,UAAU,IAAI,CAAS,UAAA;AAAA,QAC7C,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,MACZ,EAAA;AACF,aAAO,CAAC,WAAW,OAAO,eAAe,CAAC;AAAA,IAAA,CAC3C;AAEK,UAAA,wBAAwBA,cAAAA,SAAS,MAAM;AACrC,YAAA,kBAAkB,UAAU,OAAO,CAAA,SAAQ,KAAK,SAAS,MAAM,EAAE,IAAI,CAAS,UAAA;AAAA,QAClF,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,MACZ,EAAA;AACF,aAAO,CAAC,eAAe;AAAA,IAAA,CACxB;AAGDC,kBAAAA,UAAU,YAAY;AAEpB,YAAM,iBAAiB;AAGvB,YAAM,iBAAiB;AAGvB,UAAI,iBAAiB,iBAAiB;AACzB,mBAAA,QAAQ,iBAAiB,gBAAgB;AACpD,0BAAkB,QAAQ;AAAA,UACxB,iBAAiB,iBAAiB,gBAAgB;AAAA,UAClD,aAAa,iBAAiB,gBAAgB;AAAA,UAC9C,aAAa,iBAAiB,gBAAgB;AAAA,UAC9C,YAAY;AAAA,UACZ,YAAY,iBAAiB,gBAAgB,OAAO,MAAM,GAAG,EAAE;AAAA,UAC/D,YAAY,iBAAiB,gBAAgB,OAAO;AAAA,QAAA;AAAA,MAExD;AAAA,IAAA,CACD;AAGD,QAAI,aAAoC;AACxC,UAAM,oBAAoB,MAAM;AAC9B,UAAI,YAAY;AACd,qBAAa,UAAU;AAAA,MACzB;AAGA,UAAI,kBAAkB,OAAO;AAC3B,0BAAkB,QAAQ;AAC1B,yBAAiB,QAAQ;AAAA,MAC3B;AAGI,UAAA,WAAW,MAAM,KAAA,EAAO,SAAS,KAAK,WAAW,MAAM,OAAO,SAAS,KAAK;AAC9E,qBAAa,WAAW,MAAM;AACZ;WACf,GAAI;AAAA,MACT;AAAA,IAAA;AAIF,UAAM,kBAAkB,YAAY;AAClC,UAAI,CAAC,WAAW,MAAM,QAAQ;AAC5BC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MACF;AAEI,UAAA;AACF,cAAM,SAAS,MAAM,iBAAiB,cAAc,WAAW,MAAM,MAAM;AAC3E,0BAAkB,QAAQ;AAC1B,yBAAiB,QAAQ;AAGT;eAET,OAAY;AACnBA,sBAAA,MAAI,MAAM,SAAQ,2CAA0C,SAAS,KAAK;AAC1EA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,IAAA;AAIF,UAAM,kBAAkB,MAAM;AAC5B,UAAI,CAAC,kBAAkB;AAAO;AAI9B,uBAAiB,QAAQ;AAAA,IAAA;AAI3B,UAAM,kBAAkB,MAAM;AAC5B,iBAAW,QAAQ;AACnB,wBAAkB,QAAQ;AAC1B,uBAAiB,QAAQ;AAAA,IAAA;AAI3B,UAAM,kBAAkB,MAAM;AAmC5BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAKH,UAAM,aAAa,MAAM;AACvB,UAAI,CAAC,kBAAkB;AAAO;AAE9BA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM,kBAAkB,MAAM;AAAA,QAC9B,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,QACA,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,kBAAkB;AAAO;AAExB,YAAA,eAAe,OAAO,WAAW,KAAK;AAAA,MAAS,kBAAkB,MAAM,eAAe;AAyB5FA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM;AAAA,QACN,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;AAKH,UAAM,uBAAuB,YAAY;AACvC,UAAI,CAAC,kBAAkB;AAAO;AAIb,uBAAA,QAAQ,CAAC,iBAAiB;AAE3CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,iBAAiB,QAAQ,SAAS;AAAA,QACzC,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIH,UAAM,sBAAsB,MAAM;AAChC,uBAAiB,cAAc;AAG/B,UAAI,kBAAkB,OAAO;AACd,mBAAW;AACb,mBAAA,QAAQ,kBAAkB,MAAM;AAC3C,0BAAkB,QAAQ;AAC1B,yBAAiB,QAAQ;AAAA,MAC3B;AAAA,IAAA;AAII,UAAA,0BAA0B,CAAC,UAAe;AAC9C,uBAAiB,kBAAkB,MAAM,CAAC,EAAE,KAAK;AACjD,+BAAyB,QAAQ;AAG7B,UAAA,WAAW,MAAM,QAAQ;AACX;MAClB;AAAA,IAAA;AAGI,UAAA,0BAA0B,CAAC,UAAe;AAC9C,uBAAiB,kBAAkB,MAAM,CAAC,EAAE,KAAK;AACjD,+BAAyB,QAAQ;AAG7B,UAAA,WAAW,MAAM,QAAQ;AACX;MAClB;AAAA,IAAA;AAII,UAAA,kBAAkB,CAAC,YAAoB,eAAuB;AACjD,uBAAA,gBAAgB,YAAY,UAAU;AAGnD,UAAA,WAAW,MAAM,QAAQ;AACX;MAClB;AAAA,IAAA;AAII,UAAA,iBAAiB,CAAC,SAA6B;AACnD,iBAAW,QAAQ,KAAK;AACxB,wBAAkB,QAAQ;AAAA,QACxB,iBAAiB,KAAK;AAAA,QACtB,aAAa,KAAK;AAAA,QAClB,aAAa,KAAK;AAAA,QAClB,YAAY;AAAA,QACZ,YAAY,KAAK,cAAc,KAAK,YAAY,MAAM,GAAG,EAAE;AAAA,QAC3D,YAAY,KAAK,YAAY;AAAA,MAAA;AAId,uBAAA,kBAAkB,KAAK,WAAW;AAClC,uBAAA,kBAAkB,KAAK,WAAW;AAGnD,uBAAiB,QAAQ;AAGT;IAAA;AAIZ,UAAA,oBAAoB,OAAO,OAAe;AAC9CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACT,kBAAA,iBAAiB,kBAAkB,EAAE;AAAA,UAC7C;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,kBAAM,iBAAiB;AACvB,6BAAiB,QAAQ;AAAA,UAC3B;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IAAA;AAIG,UAAA,kBAAkB,CAAC,aAAqB;AAC5C,UAAI,aAAa;AAAe,eAAA;AAChC,YAAM,OAAO,UAAU,KAAK,CAAK,MAAA,EAAE,SAAS,QAAQ;AACpD,cAAO,6BAAM,SAAQ;AAAA,IAAA;AAIjB,UAAA,aAAa,CAAC,SAAiB;AAC5B,aAAAN,oBAAM,IAAI,EAAE;IAAQ;AAI7B,UAAM,oBAAoB,YAAY;AAChC,UAAA;AACF,cAAM,iBAAiB;AACvBM,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,eACM,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACAA,sBAAA,MAAI,oBAAoB;AAAA,MAC1B;AAAA,IAAA;AAIW,aAAA;AAAA,MACX;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9lBD,GAAG,WAAW,eAAe;"}