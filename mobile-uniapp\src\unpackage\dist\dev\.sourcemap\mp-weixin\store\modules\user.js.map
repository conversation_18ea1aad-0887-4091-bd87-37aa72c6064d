{"version": 3, "file": "user.js", "sources": ["store/modules/user.ts"], "sourcesContent": ["import { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\nimport user<PERSON><PERSON>, { type UserInfo as ApiUserInfo, type LoginParams, type LoginResponse } from '@/api/user'\n\nexport interface UserInfo {\n  id: string\n  username: string\n  email: string\n  avatar?: string\n  nickname?: string\n  phone?: string\n  createTime?: string\n  lastLoginTime?: string\n}\n\nexport const useUserStore = defineStore('user', () => {\n  // 状态\n  const token = ref<string>('')\n  const userInfo = ref<UserInfo | null>(null)\n  const isLoggedIn = ref<boolean>(false)\n  const loginLoading = ref<boolean>(false)\n\n  // 计算属性\n  const isAuthenticated = computed(() => {\n    return !!token.value && !!userInfo.value\n  })\n\n  const userDisplayName = computed(() => {\n    if (!userInfo.value) return ''\n    return userInfo.value.nickname || userInfo.value.username || '用户'\n  })\n\n  // 登录\n  const login = async (credentials: { username: string; password: string }) => {\n    try {\n      loginLoading.value = true\n      \n      const response = await userApi.login(credentials)\n      \n      if (response.success) {\n        token.value = response.data.token\n        userInfo.value = response.data.user\n        isLoggedIn.value = true\n        \n        // 保存到本地存储\n        uni.setStorageSync('token', token.value)\n        uni.setStorageSync('userInfo', userInfo.value)\n        \n        return { success: true }\n      } else {\n        throw new Error(response.message || '登录失败')\n      }\n    } catch (error: any) {\n      console.error('登录失败:', error)\n      return { \n        success: false, \n        message: error.message || '登录失败，请检查网络连接' \n      }\n    } finally {\n      loginLoading.value = false\n    }\n  }\n\n  // 退出登录\n  const logout = async () => {\n    try {\n      // 调用退出登录API\n      await userApi.logout()\n    } catch (error) {\n      console.error('退出登录API调用失败:', error)\n    } finally {\n      // 清除本地状态\n      token.value = ''\n      userInfo.value = null\n      isLoggedIn.value = false\n      \n      // 清除本地存储\n      uni.removeStorageSync('token')\n      uni.removeStorageSync('userInfo')\n      \n      // 跳转到登录页\n      uni.reLaunch({\n        url: '/pages/user/login/index'\n      })\n    }\n  }\n\n  // 检查登录状态\n  const checkLoginStatus = async () => {\n    try {\n      // 从本地存储获取token\n      const localToken = uni.getStorageSync('token')\n      const localUserInfo = uni.getStorageSync('userInfo')\n      \n      if (localToken && localUserInfo) {\n        token.value = localToken\n        userInfo.value = localUserInfo\n        isLoggedIn.value = true\n        \n        // 验证token是否有效\n        try {\n          const response = await userApi.getUserInfo()\n          if (response.success) {\n            userInfo.value = response.data\n          } else {\n            // token无效，清除登录状态\n            await logout()\n          }\n        } catch (error) {\n          console.error('验证token失败:', error)\n          // 网络错误时保持本地登录状态，但不清除\n        }\n      }\n    } catch (error) {\n      console.error('检查登录状态失败:', error)\n    }\n  }\n\n  // 更新用户信息\n  const updateUserInfo = async (updateData: Partial<UserInfo>) => {\n    try {\n      const response = await userApi.updateUserInfo(updateData)\n      \n      if (response.success) {\n        userInfo.value = { ...userInfo.value, ...response.data }\n        \n        // 更新本地存储\n        uni.setStorageSync('userInfo', userInfo.value)\n        \n        return { success: true }\n      } else {\n        throw new Error(response.message || '更新失败')\n      }\n    } catch (error: any) {\n      console.error('更新用户信息失败:', error)\n      return { \n        success: false, \n        message: error.message || '更新失败' \n      }\n    }\n  }\n\n  // 修改密码\n  const changePassword = async (passwordData: {\n    oldPassword: string\n    newPassword: string\n  }) => {\n    try {\n      const response = await userApi.changePassword(passwordData)\n      \n      if (response.success) {\n        uni.showToast({\n          title: '密码修改成功',\n          icon: 'success'\n        })\n        return { success: true }\n      } else {\n        throw new Error(response.message || '密码修改失败')\n      }\n    } catch (error: any) {\n      console.error('修改密码失败:', error)\n      return { \n        success: false, \n        message: error.message || '密码修改失败' \n      }\n    }\n  }\n\n  // 上传头像\n  const uploadAvatar = async (filePath: string) => {\n    try {\n      const response = await userApi.uploadAvatar(filePath)\n      \n      if (response.success) {\n        if (userInfo.value) {\n          userInfo.value.avatar = response.data.avatar\n          uni.setStorageSync('userInfo', userInfo.value)\n        }\n        \n        uni.showToast({\n          title: '头像上传成功',\n          icon: 'success'\n        })\n        \n        return { success: true, avatar: response.data.avatar }\n      } else {\n        throw new Error(response.message || '头像上传失败')\n      }\n    } catch (error: any) {\n      console.error('上传头像失败:', error)\n      return { \n        success: false, \n        message: error.message || '头像上传失败' \n      }\n    }\n  }\n\n  // 获取用户统计信息\n  const getUserStats = async () => {\n    try {\n      const response = await userApi.getUserStats()\n      return response.success ? response.data : null\n    } catch (error) {\n      console.error('获取用户统计失败:', error)\n      return null\n    }\n  }\n\n  return {\n    // 状态\n    token,\n    userInfo,\n    isLoggedIn,\n    loginLoading,\n    \n    // 计算属性\n    isAuthenticated,\n    userDisplayName,\n    \n    // 方法\n    login,\n    logout,\n    checkLoginStatus,\n    updateUserInfo,\n    changePassword,\n    uploadAvatar,\n    getUserStats\n  }\n}, {\n  // 持久化配置\n  persist: {\n    key: 'user-store',\n    paths: ['token', 'userInfo', 'isLoggedIn']\n  }\n})\n"], "names": ["defineStore", "ref", "computed", "userApi", "uni"], "mappings": ";;;AAea,MAAA,eAAeA,cAAAA,YAAY,QAAQ,MAAM;AAE9C,QAAA,QAAQC,kBAAY,EAAE;AACtB,QAAA,WAAWA,kBAAqB,IAAI;AACpC,QAAA,aAAaA,kBAAa,KAAK;AAC/B,QAAA,eAAeA,kBAAa,KAAK;AAGjC,QAAA,kBAAkBC,cAAAA,SAAS,MAAM;AACrC,WAAO,CAAC,CAAC,MAAM,SAAS,CAAC,CAAC,SAAS;AAAA,EAAA,CACpC;AAEK,QAAA,kBAAkBA,cAAAA,SAAS,MAAM;AACrC,QAAI,CAAC,SAAS;AAAc,aAAA;AAC5B,WAAO,SAAS,MAAM,YAAY,SAAS,MAAM,YAAY;AAAA,EAAA,CAC9D;AAGK,QAAA,QAAQ,OAAO,gBAAwD;AACvE,QAAA;AACF,mBAAa,QAAQ;AAErB,YAAM,WAAW,MAAMC,SAAAA,QAAQ,MAAM,WAAW;AAEhD,UAAI,SAAS,SAAS;AACd,cAAA,QAAQ,SAAS,KAAK;AACnB,iBAAA,QAAQ,SAAS,KAAK;AAC/B,mBAAW,QAAQ;AAGfC,sBAAAA,MAAA,eAAe,SAAS,MAAM,KAAK;AACnCA,sBAAAA,MAAA,eAAe,YAAY,SAAS,KAAK;AAEtC,eAAA,EAAE,SAAS;MAAK,OAClB;AACL,cAAM,IAAI,MAAM,SAAS,WAAW,MAAM;AAAA,MAC5C;AAAA,aACO,OAAY;AACnBA,oBAAA,MAAc,MAAA,SAAA,+BAAA,SAAS,KAAK;AACrB,aAAA;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAAA;AAAA,IAC5B,UACA;AACA,mBAAa,QAAQ;AAAA,IACvB;AAAA,EAAA;AAIF,QAAM,SAAS,YAAY;AACrB,QAAA;AAEF,YAAMD,SAAAA,QAAQ;aACP,OAAO;AACdC,oBAAA,MAAc,MAAA,SAAA,+BAAA,gBAAgB,KAAK;AAAA,IAAA,UACnC;AAEA,YAAM,QAAQ;AACd,eAAS,QAAQ;AACjB,iBAAW,QAAQ;AAGnBA,0BAAI,kBAAkB,OAAO;AAC7BA,0BAAI,kBAAkB,UAAU;AAGhCA,oBAAAA,MAAI,SAAS;AAAA,QACX,KAAK;AAAA,MAAA,CACN;AAAA,IACH;AAAA,EAAA;AAIF,QAAM,mBAAmB,YAAY;AAC/B,QAAA;AAEI,YAAA,aAAaA,cAAAA,MAAI,eAAe,OAAO;AACvC,YAAA,gBAAgBA,cAAAA,MAAI,eAAe,UAAU;AAEnD,UAAI,cAAc,eAAe;AAC/B,cAAM,QAAQ;AACd,iBAAS,QAAQ;AACjB,mBAAW,QAAQ;AAGf,YAAA;AACI,gBAAA,WAAW,MAAMD,iBAAQ;AAC/B,cAAI,SAAS,SAAS;AACpB,qBAAS,QAAQ,SAAS;AAAA,UAAA,OACrB;AAEL,kBAAM,OAAO;AAAA,UACf;AAAA,iBACO,OAAO;AACdC,wBAAA,MAAA,MAAA,SAAA,gCAAc,cAAc,KAAK;AAAA,QAEnC;AAAA,MACF;AAAA,aACO,OAAO;AACdA,oBAAA,qDAAc,aAAa,KAAK;AAAA,IAClC;AAAA,EAAA;AAII,QAAA,iBAAiB,OAAO,eAAkC;AAC1D,QAAA;AACF,YAAM,WAAW,MAAMD,SAAAA,QAAQ,eAAe,UAAU;AAExD,UAAI,SAAS,SAAS;AACpB,iBAAS,QAAQ,EAAE,GAAG,SAAS,OAAO,GAAG,SAAS;AAG9CC,sBAAAA,MAAA,eAAe,YAAY,SAAS,KAAK;AAEtC,eAAA,EAAE,SAAS;MAAK,OAClB;AACL,cAAM,IAAI,MAAM,SAAS,WAAW,MAAM;AAAA,MAC5C;AAAA,aACO,OAAY;AACnBA,oBAAA,qDAAc,aAAa,KAAK;AACzB,aAAA;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAAA;AAAA,IAE9B;AAAA,EAAA;AAII,QAAA,iBAAiB,OAAO,iBAGxB;AACA,QAAA;AACF,YAAM,WAAW,MAAMD,SAAAA,QAAQ,eAAe,YAAY;AAE1D,UAAI,SAAS,SAAS;AACpBC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACM,eAAA,EAAE,SAAS;MAAK,OAClB;AACL,cAAM,IAAI,MAAM,SAAS,WAAW,QAAQ;AAAA,MAC9C;AAAA,aACO,OAAY;AACnBA,oBAAA,MAAc,MAAA,SAAA,gCAAA,WAAW,KAAK;AACvB,aAAA;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAAA;AAAA,IAE9B;AAAA,EAAA;AAII,QAAA,eAAe,OAAO,aAAqB;AAC3C,QAAA;AACF,YAAM,WAAW,MAAMD,SAAAA,QAAQ,aAAa,QAAQ;AAEpD,UAAI,SAAS,SAAS;AACpB,YAAI,SAAS,OAAO;AACT,mBAAA,MAAM,SAAS,SAAS,KAAK;AAClCC,wBAAAA,MAAA,eAAe,YAAY,SAAS,KAAK;AAAA,QAC/C;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAED,eAAO,EAAE,SAAS,MAAM,QAAQ,SAAS,KAAK;MAAO,OAChD;AACL,cAAM,IAAI,MAAM,SAAS,WAAW,QAAQ;AAAA,MAC9C;AAAA,aACO,OAAY;AACnBA,oBAAA,MAAc,MAAA,SAAA,gCAAA,WAAW,KAAK;AACvB,aAAA;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAAA;AAAA,IAE9B;AAAA,EAAA;AAIF,QAAM,eAAe,YAAY;AAC3B,QAAA;AACI,YAAA,WAAW,MAAMD,iBAAQ;AACxB,aAAA,SAAS,UAAU,SAAS,OAAO;AAAA,aACnC,OAAO;AACdC,oBAAA,qDAAc,aAAa,KAAK;AACzB,aAAA;AAAA,IACT;AAAA,EAAA;AAGK,SAAA;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ,GAAG;AAAA;AAAA,EAED,SAAS;AAAA,IACP,KAAK;AAAA,IACL,OAAO,CAAC,SAAS,YAAY,YAAY;AAAA,EAC3C;AACF,CAAC;;"}