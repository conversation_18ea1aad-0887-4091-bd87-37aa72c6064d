import { http, uploadFile } from '@/utils/request'

// AI智能体相关接口
export interface AIAgent {
  id: string
  name: string
  description: string
  avatar: string
  category: string
  tags: string[]
  rating: number
  usage_count: number
  price: number
  is_free: boolean
  is_featured: boolean
  creator: {
    id: string
    name: string
    avatar: string
  }
  capabilities: string[]
  model_info: {
    model_name: string
    version: string
    parameters: Record<string, any>
  }
  created_at: string
  updated_at: string
}

export interface AgentCategory {
  id: string
  name: string
  icon: string
  description: string
  agent_count: number
}

export interface ChatSession {
  id: string
  agent_id: string
  agent_name: string
  title: string
  created_at: string
  updated_at: string
  message_count: number
  last_message?: string
}

export interface ChatMessage {
  id: string
  session_id: string
  role: 'user' | 'assistant'
  content: string
  message_type: 'text' | 'image' | 'file'
  attachments?: Array<{
    type: string
    url: string
    name: string
    size: number
  }>
  timestamp: string
  tokens_used?: number
}

export interface AgentReview {
  id: string
  user_id: string
  user_name: string
  user_avatar: string
  agent_id: string
  rating: number
  content: string
  created_at: string
}

export const aiAgentApi = {
  // 获取智能体分类
  getCategories: () => {
    return http.get<AgentCategory[]>('/ai-agent/categories')
  },

  // 获取智能体列表
  getAgents: (params?: {
    category?: string
    search?: string
    sort?: 'popular' | 'rating' | 'newest' | 'price'
    is_free?: boolean
    page?: number
    limit?: number
  }) => {
    return http.get<{
      items: AIAgent[]
      total: number
      page: number
      limit: number
    }>('/ai-agent/list', params)
  },

  // 获取推荐智能体
  getFeaturedAgents: (limit = 10) => {
    return http.get<AIAgent[]>('/ai-agent/featured', { limit })
  },

  // 获取热门智能体
  getPopularAgents: (limit = 10) => {
    return http.get<AIAgent[]>('/ai-agent/popular', { limit })
  },

  // 获取智能体详情
  getAgentDetail: (id: string) => {
    return http.get<AIAgent>(`/ai-agent/${id}`)
  },

  // 搜索智能体
  searchAgents: (query: string, params?: {
    category?: string
    page?: number
    limit?: number
  }) => {
    return http.get<{
      items: AIAgent[]
      total: number
      page: number
      limit: number
    }>('/ai-agent/search', { query, ...params })
  },

  // 获取我的智能体
  getMyAgents: (params?: { page?: number; limit?: number }) => {
    return http.get<{
      items: AIAgent[]
      total: number
      page: number
      limit: number
    }>('/ai-agent/my', params)
  },

  // 创建智能体
  createAgent: (data: {
    name: string
    description: string
    category: string
    tags: string[]
    capabilities: string[]
    model_info: Record<string, any>
    avatar?: string
    is_free?: boolean
    price?: number
  }) => {
    return http.post<AIAgent>('/ai-agent/create', data)
  },

  // 更新智能体
  updateAgent: (id: string, data: Partial<AIAgent>) => {
    return http.put<AIAgent>(`/ai-agent/${id}`, data)
  },

  // 删除智能体
  deleteAgent: (id: string) => {
    return http.delete(`/ai-agent/${id}`)
  },

  // 上传智能体头像
  uploadAvatar: (filePath: string) => {
    return uploadFile({
      url: '/ai-agent/upload-avatar',
      filePath,
      name: 'avatar'
    })
  },

  // 获取对话会话列表
  getChatSessions: (params?: { page?: number; limit?: number }) => {
    return http.get<{
      items: ChatSession[]
      total: number
      page: number
      limit: number
    }>('/ai-agent/sessions', params)
  },

  // 创建对话会话
  createChatSession: (agentId: string, title?: string) => {
    return http.post<ChatSession>('/ai-agent/sessions', {
      agent_id: agentId,
      title: title || '新对话'
    })
  },

  // 获取会话消息
  getChatMessages: (sessionId: string, params?: { page?: number; limit?: number }) => {
    return http.get<{
      items: ChatMessage[]
      total: number
      page: number
      limit: number
    }>(`/ai-agent/sessions/${sessionId}/messages`, params)
  },

  // 发送消息
  sendMessage: (sessionId: string, content: string, messageType: 'text' | 'image' | 'file' = 'text') => {
    return http.post<ChatMessage>(`/ai-agent/sessions/${sessionId}/messages`, {
      content,
      message_type: messageType
    })
  },

  // 上传文件消息
  uploadFile: (sessionId: string, filePath: string, fileType: string) => {
    return uploadFile({
      url: `/ai-agent/sessions/${sessionId}/upload`,
      filePath,
      name: 'file',
      formData: {
        file_type: fileType
      }
    })
  },

  // 删除会话
  deleteChatSession: (sessionId: string) => {
    return http.delete(`/ai-agent/sessions/${sessionId}`)
  },

  // 清空会话消息
  clearChatMessages: (sessionId: string) => {
    return http.delete(`/ai-agent/sessions/${sessionId}/messages`)
  },

  // 获取智能体评价
  getAgentReviews: (agentId: string, params?: { page?: number; limit?: number }) => {
    return http.get<{
      items: AgentReview[]
      total: number
      page: number
      limit: number
      average_rating: number
    }>(`/ai-agent/${agentId}/reviews`, params)
  },

  // 添加评价
  addReview: (agentId: string, data: { rating: number; content: string }) => {
    return http.post<AgentReview>(`/ai-agent/${agentId}/reviews`, data)
  },

  // 收藏智能体
  favoriteAgent: (agentId: string) => {
    return http.post(`/ai-agent/${agentId}/favorite`)
  },

  // 取消收藏
  unfavoriteAgent: (agentId: string) => {
    return http.delete(`/ai-agent/${agentId}/favorite`)
  },

  // 获取收藏的智能体
  getFavoriteAgents: (params?: { page?: number; limit?: number }) => {
    return http.get<{
      items: AIAgent[]
      total: number
      page: number
      limit: number
    }>('/ai-agent/favorites', params)
  },

  // 购买智能体
  purchaseAgent: (agentId: string) => {
    return http.post(`/ai-agent/${agentId}/purchase`)
  },

  // 获取购买记录
  getPurchaseHistory: (params?: { page?: number; limit?: number }) => {
    return http.get<{
      items: Array<{
        id: string
        agent_id: string
        agent_name: string
        price: number
        purchase_time: string
        status: 'completed' | 'pending' | 'failed'
      }>
      total: number
      page: number
      limit: number
    }>('/ai-agent/purchases', params)
  },

  // 获取智能体统计信息
  getAgentStats: (agentId: string) => {
    return http.get<{
      total_sessions: number
      total_messages: number
      total_users: number
      average_rating: number
      revenue: number
    }>(`/ai-agent/${agentId}/stats`)
  },

  // 举报智能体
  reportAgent: (agentId: string, reason: string, description: string) => {
    return http.post(`/ai-agent/${agentId}/report`, {
      reason,
      description
    })
  }
}
