{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"home-page\">\n    <!-- 顶部轮播图 -->\n    <view class=\"banner-section\">\n      <swiper \n        class=\"banner-swiper\" \n        :indicator-dots=\"true\" \n        :autoplay=\"true\" \n        :interval=\"3000\"\n        :duration=\"500\"\n        indicator-color=\"rgba(255,255,255,0.5)\"\n        indicator-active-color=\"#2563EB\"\n      >\n        <swiper-item v-for=\"(banner, index) in banners\" :key=\"index\">\n          <view class=\"banner-item\" :style=\"{ backgroundImage: `url(${banner.image})` }\">\n            <view class=\"banner-content\">\n              <text class=\"banner-title\">{{ banner.title }}</text>\n              <text class=\"banner-desc\">{{ banner.description }}</text>\n              <u-button \n                type=\"primary\" \n                size=\"small\" \n                shape=\"round\"\n                @click=\"handleBannerClick(banner)\"\n              >\n                {{ banner.buttonText }}\n              </u-button>\n            </view>\n          </view>\n        </swiper-item>\n      </swiper>\n    </view>\n\n    <!-- 快捷功能入口 -->\n    <view class=\"quick-actions\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">快捷功能</text>\n        <text class=\"title-desc\">一键直达常用功能</text>\n      </view>\n      \n      <view class=\"actions-grid\">\n        <view \n          class=\"action-item\" \n          v-for=\"(action, index) in quickActions\" \n          :key=\"index\"\n          @click=\"navigateTo(action.path)\"\n        >\n          <view class=\"action-icon\" :style=\"{ backgroundColor: action.color }\">\n            <u-icon :name=\"action.icon\" color=\"#fff\" size=\"24\"></u-icon>\n          </view>\n          <text class=\"action-title\">{{ action.title }}</text>\n          <text class=\"action-desc\">{{ action.description }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 功能模块 -->\n    <view class=\"feature-modules\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">核心功能</text>\n        <text class=\"title-desc\">探索AI的无限可能</text>\n      </view>\n      \n      <view class=\"modules-list\">\n        <view \n          class=\"module-card\" \n          v-for=\"(module, index) in featureModules\" \n          :key=\"index\"\n          @click=\"navigateTo(module.path)\"\n        >\n          <view class=\"module-header\">\n            <view class=\"module-icon\">\n              <u-icon :name=\"module.icon\" :color=\"module.color\" size=\"28\"></u-icon>\n            </view>\n            <view class=\"module-badge\" v-if=\"module.badge\">\n              <text class=\"badge-text\">{{ module.badge }}</text>\n            </view>\n          </view>\n          \n          <view class=\"module-content\">\n            <text class=\"module-title\">{{ module.title }}</text>\n            <text class=\"module-desc\">{{ module.description }}</text>\n            \n            <view class=\"module-features\">\n              <text \n                class=\"feature-tag\" \n                v-for=\"(feature, idx) in module.features\" \n                :key=\"idx\"\n              >\n                {{ feature }}\n              </text>\n            </view>\n          </view>\n          \n          <view class=\"module-arrow\">\n            <u-icon name=\"arrow-right\" color=\"#9CA3AF\" size=\"16\"></u-icon>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 最近使用 -->\n    <view class=\"recent-section\" v-if=\"recentItems.length > 0\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">最近使用</text>\n        <text class=\"title-desc\">快速访问历史记录</text>\n      </view>\n      \n      <scroll-view class=\"recent-scroll\" scroll-x=\"true\" show-scrollbar=\"false\">\n        <view class=\"recent-list\">\n          <view \n            class=\"recent-item\" \n            v-for=\"(item, index) in recentItems\" \n            :key=\"index\"\n            @click=\"handleRecentClick(item)\"\n          >\n            <view class=\"recent-icon\">\n              <u-icon :name=\"item.icon\" color=\"#2563EB\" size=\"20\"></u-icon>\n            </view>\n            <text class=\"recent-title\">{{ item.title }}</text>\n            <text class=\"recent-time\">{{ formatTime(item.time) }}</text>\n          </view>\n        </view>\n      </scroll-view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted } from 'vue'\nimport { useUserStore } from '@/store/modules/user'\nimport dayjs from 'dayjs'\n\n// Store\nconst userStore = useUserStore()\n\n// 轮播图数据\nconst banners = ref([\n  {\n    id: 1,\n    title: 'AI翻译服务',\n    description: '支持200+语言，专业翻译',\n    image: '/static/images/banner1.jpg',\n    buttonText: '立即体验',\n    path: '/pages/translation/text/index'\n  },\n  {\n    id: 2,\n    title: '数字人对话',\n    description: '逼真AI数字人，智能交互',\n    image: '/static/images/banner2.jpg',\n    buttonText: '开始对话',\n    path: '/pages/digital-human/chat/index'\n  },\n  {\n    id: 3,\n    title: 'AI智能体',\n    description: '专业AI助手，提升效率',\n    image: '/static/images/banner3.jpg',\n    buttonText: '探索更多',\n    path: '/pages/ai-agent/market/index'\n  }\n])\n\n// 快捷功能\nconst quickActions = ref([\n  {\n    title: '文本翻译',\n    description: '快速翻译',\n    icon: 'edit-pen',\n    color: '#2563EB',\n    path: '/pages/translation/text/index'\n  },\n  {\n    title: '语音翻译',\n    description: '实时语音',\n    icon: 'mic',\n    color: '#10B981',\n    path: '/pages/translation/audio/index'\n  },\n  {\n    title: '数字人',\n    description: 'AI对话',\n    icon: 'account',\n    color: '#F59E0B',\n    path: '/pages/digital-human/chat/index'\n  },\n  {\n    title: '智能体',\n    description: 'AI助手',\n    icon: 'robot',\n    color: '#EF4444',\n    path: '/pages/ai-agent/market/index'\n  }\n])\n\n// 功能模块\nconst featureModules = ref([\n  {\n    title: '翻译服务',\n    description: '支持文本、文档、音频、视频等多种翻译方式',\n    icon: 'globe',\n    color: '#2563EB',\n    badge: '热门',\n    features: ['文本翻译', '文档翻译', '音频翻译', '视频翻译'],\n    path: '/pages/translation/text/index'\n  },\n  {\n    title: '数字人对话',\n    description: '与AI数字人进行自然对话，体验未来交互方式',\n    icon: 'account-circle',\n    color: '#10B981',\n    badge: '新功能',\n    features: ['智能对话', '表情生动', '多语言支持'],\n    path: '/pages/digital-human/chat/index'\n  },\n  {\n    title: 'AI智能体市场',\n    description: '丰富的AI智能体，满足各种专业需求',\n    icon: 'store',\n    color: '#F59E0B',\n    features: ['专业助手', '定制服务', '智能推荐'],\n    path: '/pages/ai-agent/market/index'\n  },\n  {\n    title: '实用工具',\n    description: '文档转换、音频处理、图片编辑等实用工具',\n    icon: 'wrench',\n    color: '#8B5CF6',\n    features: ['格式转换', '批量处理', '高效便捷'],\n    path: '/pages/utilities/conversion/document/index'\n  }\n])\n\n// 最近使用\nconst recentItems = ref([])\n\n// 页面加载\nonMounted(async () => {\n  await loadRecentItems()\n})\n\n// 加载最近使用记录\nconst loadRecentItems = async () => {\n  try {\n    // 这里从本地存储或API获取最近使用记录\n    const recent = uni.getStorageSync('recent_items') || []\n    recentItems.value = recent.slice(0, 10) // 只显示最近10条\n  } catch (error) {\n    uni.__f__('error','at pages/index/index.vue:249','加载最近使用记录失败:', error)\n  }\n}\n\n// 导航到指定页面\nconst navigateTo = (path: string) => {\n  if (!path) return\n  \n  uni.navigateTo({\n    url: path,\n    fail: (err) => {\n      uni.__f__('error','at pages/index/index.vue:260','页面跳转失败:', err)\n      uni.showToast({\n        title: '页面跳转失败',\n        icon: 'error'\n      })\n    }\n  })\n}\n\n// 处理轮播图点击\nconst handleBannerClick = (banner: any) => {\n  navigateTo(banner.path)\n}\n\n// 处理最近使用点击\nconst handleRecentClick = (item: any) => {\n  navigateTo(item.path)\n}\n\n// 格式化时间\nconst formatTime = (time: string) => {\n  return dayjs(time).fromNow()\n}\n\n// 下拉刷新\nconst onPullDownRefresh = async () => {\n  try {\n    await loadRecentItems()\n    uni.showToast({\n      title: '刷新成功',\n      icon: 'success'\n    })\n  } catch (error) {\n    uni.showToast({\n      title: '刷新失败',\n      icon: 'error'\n    })\n  } finally {\n    uni.stopPullDownRefresh()\n  }\n}\n\n// 导出方法供页面使用\ndefineExpose({\n  onPullDownRefresh\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.home-page {\n  min-height: 100vh;\n  background-color: $bg-secondary;\n}\n\n// 轮播图样式\n.banner-section {\n  height: 400rpx;\n  \n  .banner-swiper {\n    height: 100%;\n    \n    .banner-item {\n      height: 100%;\n      background-size: cover;\n      background-position: center;\n      position: relative;\n      display: flex;\n      align-items: center;\n      padding: 0 $spacing-6;\n      \n      &::before {\n        content: '';\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: linear-gradient(45deg, rgba(0,0,0,0.3), rgba(0,0,0,0.1));\n      }\n      \n      .banner-content {\n        position: relative;\n        z-index: 1;\n        color: white;\n        \n        .banner-title {\n          display: block;\n          font-size: $font-size-2xl;\n          font-weight: $font-weight-bold;\n          margin-bottom: $spacing-2;\n        }\n        \n        .banner-desc {\n          display: block;\n          font-size: $font-size-base;\n          opacity: 0.9;\n          margin-bottom: $spacing-4;\n        }\n      }\n    }\n  }\n}\n\n// 通用区块标题\n.section-title {\n  padding: $spacing-6 $spacing-4 $spacing-4;\n  \n  .title-text {\n    display: block;\n    font-size: $font-size-lg;\n    font-weight: $font-weight-semibold;\n    color: $text-primary;\n    margin-bottom: $spacing-1;\n  }\n  \n  .title-desc {\n    font-size: $font-size-sm;\n    color: $text-tertiary;\n  }\n}\n\n// 快捷功能样式\n.quick-actions {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n  \n  .actions-grid {\n    display: grid;\n    grid-template-columns: repeat(4, 1fr);\n    gap: $spacing-4;\n    padding: 0 $spacing-4 $spacing-6;\n    \n    .action-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      text-align: center;\n      \n      .action-icon {\n        width: 96rpx;\n        height: 96rpx;\n        border-radius: $radius-2xl;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-bottom: $spacing-2;\n      }\n      \n      .action-title {\n        font-size: $font-size-sm;\n        font-weight: $font-weight-medium;\n        color: $text-primary;\n        margin-bottom: $spacing-1;\n      }\n      \n      .action-desc {\n        font-size: $font-size-xs;\n        color: $text-tertiary;\n      }\n    }\n  }\n}\n\n// 功能模块样式\n.feature-modules {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n  \n  .modules-list {\n    padding: 0 $spacing-4 $spacing-6;\n    \n    .module-card {\n      background-color: $bg-primary;\n      border-radius: $radius-lg;\n      padding: $spacing-4;\n      margin-bottom: $spacing-3;\n      box-shadow: $shadow-sm;\n      border: 1px solid $border-primary;\n      display: flex;\n      align-items: flex-start;\n      \n      .module-header {\n        position: relative;\n        margin-right: $spacing-3;\n        \n        .module-icon {\n          width: 80rpx;\n          height: 80rpx;\n          background-color: $bg-tertiary;\n          border-radius: $radius-lg;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n        \n        .module-badge {\n          position: absolute;\n          top: -8rpx;\n          right: -8rpx;\n          background-color: $error;\n          border-radius: $radius-full;\n          padding: 4rpx 12rpx;\n          \n          .badge-text {\n            font-size: $font-size-xs;\n            color: white;\n          }\n        }\n      }\n      \n      .module-content {\n        flex: 1;\n        \n        .module-title {\n          display: block;\n          font-size: $font-size-md;\n          font-weight: $font-weight-semibold;\n          color: $text-primary;\n          margin-bottom: $spacing-1;\n        }\n        \n        .module-desc {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          line-height: $line-height-relaxed;\n          margin-bottom: $spacing-2;\n        }\n        \n        .module-features {\n          display: flex;\n          flex-wrap: wrap;\n          gap: $spacing-1;\n          \n          .feature-tag {\n            background-color: $bg-tertiary;\n            color: $text-tertiary;\n            font-size: $font-size-xs;\n            padding: 4rpx 12rpx;\n            border-radius: $radius-full;\n          }\n        }\n      }\n      \n      .module-arrow {\n        margin-left: $spacing-2;\n        display: flex;\n        align-items: center;\n      }\n    }\n  }\n}\n\n// 最近使用样式\n.recent-section {\n  background-color: $bg-primary;\n  \n  .recent-scroll {\n    white-space: nowrap;\n    \n    .recent-list {\n      display: flex;\n      padding: 0 $spacing-4 $spacing-6;\n      gap: $spacing-3;\n      \n      .recent-item {\n        flex-shrink: 0;\n        width: 200rpx;\n        background-color: $bg-tertiary;\n        border-radius: $radius-lg;\n        padding: $spacing-3;\n        text-align: center;\n        \n        .recent-icon {\n          width: 64rpx;\n          height: 64rpx;\n          background-color: $bg-primary;\n          border-radius: $radius-lg;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 auto $spacing-2;\n        }\n        \n        .recent-title {\n          display: block;\n          font-size: $font-size-sm;\n          font-weight: $font-weight-medium;\n          color: $text-primary;\n          margin-bottom: $spacing-1;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n        }\n        \n        .recent-time {\n          font-size: $font-size-xs;\n          color: $text-tertiary;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/AI_system/mobile-uniapp/src/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "onMounted", "uni", "dayjs"], "mappings": ";;;;;;;;;;;AAqIkBA,oCAAa;AAG/B,UAAM,UAAUC,cAAAA,IAAI;AAAA,MAClB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,MAAM;AAAA,MACR;AAAA,IAAA,CACD;AAGD,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACvB;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,IAAA,CACD;AAGD,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACzC,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU,CAAC,QAAQ,QAAQ,OAAO;AAAA,QAClC,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,QACP,UAAU,CAAC,QAAQ,QAAQ,MAAM;AAAA,QACjC,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,QACP,UAAU,CAAC,QAAQ,QAAQ,MAAM;AAAA,QACjC,MAAM;AAAA,MACR;AAAA,IAAA,CACD;AAGK,UAAA,cAAcA,kBAAI,CAAA,CAAE;AAG1BC,kBAAAA,UAAU,YAAY;AACpB,YAAM,gBAAgB;AAAA,IAAA,CACvB;AAGD,UAAM,kBAAkB,YAAY;AAC9B,UAAA;AAEF,cAAM,SAASC,cAAA,MAAI,eAAe,cAAc,KAAK,CAAA;AACrD,oBAAY,QAAQ,OAAO,MAAM,GAAG,EAAE;AAAA,eAC/B,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,gCAA+B,eAAe,KAAK;AAAA,MACvE;AAAA,IAAA;AAII,UAAA,aAAa,CAAC,SAAiB;AACnC,UAAI,CAAC;AAAM;AAEXA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAI,MAAM,SAAQ,gCAA+B,WAAW,GAAG;AAC/DA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;AAIG,UAAA,oBAAoB,CAAC,WAAgB;AACzC,iBAAW,OAAO,IAAI;AAAA,IAAA;AAIlB,UAAA,oBAAoB,CAAC,SAAc;AACvC,iBAAW,KAAK,IAAI;AAAA,IAAA;AAIhB,UAAA,aAAa,CAAC,SAAiB;AAC5B,aAAAC,oBAAM,IAAI,EAAE;IAAQ;AAI7B,UAAM,oBAAoB,YAAY;AAChC,UAAA;AACF,cAAM,gBAAgB;AACtBD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,eACM,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACAA,sBAAA,MAAI,oBAAoB;AAAA,MAC1B;AAAA,IAAA;AAIW,aAAA;AAAA,MACX;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/SD,GAAG,WAAW,eAAe;"}