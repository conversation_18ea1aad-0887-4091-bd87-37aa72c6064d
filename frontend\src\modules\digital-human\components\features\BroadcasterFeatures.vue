<template>
  <div class="broadcaster-features">
    <h4 class="features-title">主播讲解功能</h4>
    <div class="features-grid">
      <div class="feature-item" @click="sendAction('products')">
        <shopping-outlined />
        <span>产品展示</span>
      </div>
      <div class="feature-item" @click="sendAction('promotion')">
        <fire-outlined />
        <span>促销活动</span>
      </div>
      <div class="feature-item" @click="sendAction('recommend')">
        <star-outlined />
        <span>推荐榜单</span>
      </div>
      <div class="feature-item" @click="sendAction('schedule')">
        <calendar-outlined />
        <span>直播日程</span>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { ShoppingBag, Sunny, Star, Calendar } from '@element-plus/icons-vue';

export default defineComponent({
  name: 'BroadcasterFeatures',
  
  components: {
    VideoPlay,
    Microphone,
    Star,
    Calendar
  },
  
  props: {
    digitalHuman: {
      type: Object,
      required: true,
      default: () => ({
        name: '',
        type: '',
        avatar_url: '',
        description: '',
        apiId: '',
        digital_human_id: '',
        voice_id: '',
        welcomeText: ''
      })
    }
  },
  
  emits: ['send-message'],
  
  setup(props, { emit }) {
    const sendAction = (action) => {
      switch(action) {
        case 'products':
          emit('send-message', {
            type: 'sendMessage',
            content: '展示最新产品'
          });
          break;
        case 'promotion':
          emit('send-message', {
            type: 'showCard',
            title: '当前促销活动',
            content: '限时特惠: 全场满300减50<br>新品首发: 9折优惠<br>会员专享: 积分双倍<br>活动时间: 2023-11-20至2023-12-01',
            actions: [
              { text: '立即参与', primary: true },
              { text: '了解更多', primary: false }
            ]
          });
          break;
        case 'recommend':
          emit('send-message', {
            type: 'sendMessage',
            content: '有什么热门推荐产品？'
          });
          break;
        case 'schedule':
          emit('send-message', {
            type: 'showCard',
            title: '直播日程安排',
            content: '11月25日 19:00 - 新品发布<br>11月27日 20:00 - 使用教程<br>12月01日 19:30 - 年终大促',
            actions: [
              { text: '设置提醒', primary: true },
              { text: '查看详情', primary: false }
            ]
          });
          break;
      }
    };
    
    return {
      sendAction
    };
  }
});
</script>

<style scoped>
.broadcaster-features {
  width: 100%;
}

.features-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #ef4444;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.feature-item {
  background-color: rgba(239, 68, 68, 0.1);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.feature-item:hover {
  background-color: rgba(239, 68, 68, 0.2);
  transform: translateY(-2px);
}

.feature-item span {
  font-size: 14px;
  color: #f9fafb;
}

:deep(.light-theme) .feature-item {
  background-color: rgba(239, 68, 68, 0.05);
}

:deep(.light-theme) .feature-item span {
  color: #1f2937;
}

:deep(.light-theme) .feature-item:hover {
  background-color: rgba(239, 68, 68, 0.1);
}

:deep(.light-theme) .features-title {
  color: #b91c1c;
}
</style> 