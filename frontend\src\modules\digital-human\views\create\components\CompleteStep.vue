<template>
  <div class="complete-step">
    <div class="step-header">
      <h2 class="step-title">完成创建</h2>
      <p class="step-description">确认信息并生成您的专属数字人</p>
    </div>

    <div class="complete-content">
      <!-- 信息确认 -->
      <div class="info-summary">
        <h4>📋 信息确认</h4>
        <div class="summary-grid">
          <div class="summary-item">
            <label>数字人名称</label>
            <span>{{ formData.name || '未设置' }}</span>
          </div>
          
          <div class="summary-item">
            <label>数字人类型</label>
            <span>{{ getTypeText(formData.type) }}</span>
          </div>
          
          <div class="summary-item">
            <label>性别</label>
            <span>{{ getGenderText(formData.gender) }}</span>
          </div>
          
          <div class="summary-item">
            <label>外观设置</label>
            <span>{{ getAppearanceText() }}</span>
          </div>
          
          <div class="summary-item">
            <label>声音设置</label>
            <span>{{ getVoiceText() }}</span>
          </div>
          
          <div class="summary-item full-width">
            <label>个性描述</label>
            <span>{{ formData.description || '未设置' }}</span>
          </div>
          
          <div class="summary-item full-width">
            <label>欢迎语</label>
            <span>"{{ formData.welcomeText || '未设置' }}"</span>
          </div>
        </div>
      </div>

      <!-- 生成选项 -->
      <div class="generation-options">
        <h4>⚙️ 生成选项</h4>
        <div class="options-grid">
          <div class="option-item">
            <label>生成质量</label>
            <el-select v-model="generationOptions.quality" placeholder="选择质量">
              <el-option label="标准质量" value="standard" />
              <el-option label="高质量" value="high" />
              <el-option label="超高质量" value="ultra" />
            </el-select>
          </div>
          
          <div class="option-item">
            <label>输出格式</label>
            <el-select v-model="generationOptions.format" placeholder="选择格式">
              <el-option label="MP4视频" value="mp4" />
              <el-option label="WebM视频" value="webm" />
              <el-option label="GIF动图" value="gif" />
            </el-select>
          </div>
          
          <div class="option-item">
            <label>分辨率</label>
            <el-select v-model="generationOptions.resolution" placeholder="选择分辨率">
              <el-option label="720P" value="720p" />
              <el-option label="1080P" value="1080p" />
              <el-option label="4K" value="4k" />
            </el-select>
          </div>
          
          <div class="option-item">
            <label>帧率</label>
            <el-select v-model="generationOptions.fps" placeholder="选择帧率">
              <el-option label="24 FPS" value="24" />
              <el-option label="30 FPS" value="30" />
              <el-option label="60 FPS" value="60" />
            </el-select>
          </div>
        </div>
      </div>

      <!-- 高级设置 -->
      <div class="advanced-options">
        <h4>🔧 高级设置</h4>
        <div class="advanced-grid">
          <div class="advanced-item">
            <el-checkbox v-model="generationOptions.enableLipSync">
              启用唇形同步
            </el-checkbox>
            <span class="option-desc">让数字人的嘴部动作与语音同步</span>
          </div>
          
          <div class="advanced-item">
            <el-checkbox v-model="generationOptions.enableGestures">
              启用手势动作
            </el-checkbox>
            <span class="option-desc">添加自然的手势和肢体动作</span>
          </div>
          
          <div class="advanced-item">
            <el-checkbox v-model="generationOptions.enableEyeContact">
              启用眼神交流
            </el-checkbox>
            <span class="option-desc">让数字人的眼神更加自然生动</span>
          </div>
          
          <div class="advanced-item">
            <el-checkbox v-model="generationOptions.enableBackground">
              启用背景环境
            </el-checkbox>
            <span class="option-desc">添加适合的背景环境</span>
          </div>
        </div>
      </div>

      <!-- 创建提示 -->
      <div class="creation-tips">
        <h4>💡 创建提示</h4>
        <div class="tips-content">
          <div class="tip-item">
            <span class="tip-icon">⚡</span>
            <span class="tip-text">生成过程大约需要1-2分钟</span>
          </div>
          <div class="tip-item">
            <span class="tip-icon">🎯</span>
            <span class="tip-text">生成完成后可立即开始对话</span>
          </div>
          <div class="tip-item">
            <span class="tip-icon">💾</span>
            <span class="tip-text">数字人将自动保存到您的账户</span>
          </div>
        </div>
      </div>

      <!-- 创建按钮 -->
      <div class="creation-actions">
        <el-button 
          size="large" 
          @click="saveAsDraft"
          :loading="isSavingDraft"
        >
          <el-icon><Document /></el-icon>
          保存为草稿
        </el-button>
        
        <el-button 
          type="primary" 
          size="large" 
          @click="startCreation"
          :loading="isCreating"
          :disabled="!canCreate"
        >
          <el-icon><Star /></el-icon>
          开始生成数字人
        </el-button>
      </div>

      <!-- 调试状态信息 -->
      <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; font-size: 12px;">
        <div>isCreating: {{ isCreating }}</div>
        <div>generationResult: {{ !!generationResult }}</div>
        <div>generationProgress: {{ generationProgress }}</div>
        <div>currentProgressStep: {{ currentProgressStep }}</div>
        <div v-if="generationResult">digitalHumanId: {{ generationResult.digitalHumanId }}</div>
        <div v-if="generationResult">resultUrls: {{ Object.keys(generationResult.resultUrls || {}) }}</div>
      </div>

      <!-- 生成进度 -->
      <div v-if="(isCreating || generationProgress > 0) && !generationResult" class="generation-progress">
        <h4>🚀 正在生成数字人...</h4>
        <el-progress
          :percentage="generationProgress"
          :stroke-width="8"
          :color="getProgressColor(generationProgress)"
        >
          <template #default="{ percentage }">
            <span class="progress-text">{{ percentage }}%</span>
          </template>
        </el-progress>

        <div class="progress-steps">
          <div
            v-for="(step, index) in progressSteps"
            :key="index"
            class="progress-step"
            :class="{
              'active': currentProgressStep === index,
              'completed': currentProgressStep > index
            }"
          >
            <div class="step-icon">
              <el-icon v-if="currentProgressStep > index"><Check /></el-icon>
              <el-icon v-else-if="currentProgressStep === index" class="spinning"><Loading /></el-icon>
              <span v-else>{{ index + 1 }}</span>
            </div>
            <span class="step-text">{{ step }}</span>
          </div>
        </div>
      </div>

      <!-- 生成结果展示 -->
      <div v-if="generationResult" class="generation-result">
        <div class="result-header">
          <h3>🎉 数字人生成成功！</h3>
          <p>您的专属数字人已经准备就绪</p>
          <!-- 调试信息 -->
          <div style="font-size: 12px; color: #666; margin-top: 10px;">
            调试: generationResult存在, digitalHumanId: {{ generationResult?.digitalHumanId || 'null' }}
          </div>
        </div>

        <div class="result-content">
          <!-- 数字人预览 -->
          <div class="digital-human-preview">
            <div class="preview-container">
              <!-- 头像预览 -->
              <div class="avatar-preview">
                <h4>📸 数字人头像</h4>
                <div class="avatar-container">
                  <img
                    v-if="generationResult.resultUrls?.avatar_url"
                    :src="generationResult.resultUrls.avatar_url"
                    alt="数字人头像"
                    class="avatar-image"
                    @error="handleImageError"
                  />
                  <div v-else class="avatar-placeholder">
                    <el-icon><Picture /></el-icon>
                    <span>头像生成中...</span>
                  </div>
                </div>
                <div class="avatar-actions">
                  <el-button
                    size="small"
                    @click="downloadAvatar"
                    :disabled="!generationResult.resultUrls?.avatar_url"
                  >
                    <el-icon><Download /></el-icon>
                    下载头像
                  </el-button>
                </div>
              </div>

              <!-- 视频预览 -->
              <div class="video-preview">
                <h4>🎬 说话视频</h4>
                <div class="video-container">
                  <video
                    v-if="generationResult.resultUrls?.preview_url"
                    :src="generationResult.resultUrls.preview_url"
                    controls
                    autoplay
                    muted
                    loop
                    class="preview-video"
                    @error="handleVideoError"
                  >
                    您的浏览器不支持视频播放
                  </video>
                  <div v-else class="video-placeholder">
                    <el-icon><VideoPlay /></el-icon>
                    <span>视频生成中...</span>
                  </div>
                </div>
                <div class="video-actions">
                  <el-button
                    size="small"
                    @click="downloadVideo"
                    :disabled="!generationResult.resultUrls?.preview_url"
                  >
                    <el-icon><Download /></el-icon>
                    下载视频
                  </el-button>
                  <el-button
                    size="small"
                    type="primary"
                    @click="startChat"
                  >
                    <el-icon><ChatDotRound /></el-icon>
                    开始对话
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 数字人信息 -->
          <div class="digital-human-info">
            <h4>ℹ️ 数字人信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>数字人ID</label>
                <span>{{ generationResult.digitalHumanId }}</span>
              </div>
              <div class="info-item">
                <label>生成时间</label>
                <span>{{ formatDate(generationResult.createdAt) }}</span>
              </div>
              <div class="info-item">
                <label>文件大小</label>
                <span>{{ getFileSize() }}</span>
              </div>
              <div class="info-item">
                <label>视频时长</label>
                <span>{{ getVideoDuration() }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="result-actions">
            <el-button size="large" @click="createAnother">
              <el-icon><Plus /></el-icon>
              创建新的数字人
            </el-button>
            <el-button size="large" type="primary" @click="goToManagement">
              <el-icon><Setting /></el-icon>
              管理我的数字人
            </el-button>
            <el-button size="large" @click="shareDigitalHuman">
              <el-icon><Share /></el-icon>
              分享数字人
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Document,
  Star,
  Check,
  Loading,
  Picture,
  VideoPlay,
  Download,
  ChatDotRound,
  Plus,
  Setting,
  Share
} from '@element-plus/icons-vue';
import { DIGITAL_HUMAN_TYPES, GENDER_OPTIONS } from '../utils/constants.js';
import { DigitalHumanGenerationManager } from '../api/digitalHumanGeneration.js';

export default defineComponent({
  name: 'CompleteStep',
  
  components: {
    Document,
    Star,
    Check,
    Loading,
    Picture,
    VideoPlay,
    Download,
    ChatDotRound,
    Plus,
    Setting,
    Share
  },
  
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  
  emits: ['create'],
  
  setup(props, { emit }) {
    const isSavingDraft = ref(false);
    const isCreating = ref(false);
    const generationProgress = ref(0);
    const currentProgressStep = ref(0);
    const generationResult = ref(null);

    // 数字人生成管理器
    const digitalHumanGenerationManager = new DigitalHumanGenerationManager();
    
    // 生成选项
    const generationOptions = ref({
      quality: 'high',
      format: 'mp4',
      resolution: '1080p',
      fps: '30',
      enableLipSync: true,
      enableGestures: true,
      enableEyeContact: true,
      enableBackground: false
    });
    
    // 进度步骤
    const progressSteps = [
      '准备生成环境',
      '处理外观数据',
      '合成声音模型',
      '渲染数字人形象',
      '优化输出质量',
      '完成生成'
    ];
    
    // 是否可以创建
    const canCreate = computed(() => {
      return !!(props.formData.name && 
                props.formData.selectedTemplate && 
                props.formData.selectedVoice);
    });
    
    // 获取类型文本
    const getTypeText = (type) => {
      const typeItem = DIGITAL_HUMAN_TYPES.find(t => t.value === type);
      return typeItem?.label || type;
    };
    
    // 获取性别文本
    const getGenderText = (gender) => {
      const genderItem = GENDER_OPTIONS.find(g => g.value === gender);
      return genderItem?.label || gender;
    };
    
    // 获取外观文本
    const getAppearanceText = () => {
      if (props.formData.appearanceType === 'upload') {
        return '自定义上传';
      }
      return '预设模板';
    };
    
    // 获取声音文本
    const getVoiceText = () => {
      return '预设声音';
    };
    

    
    // 获取进度条颜色
    const getProgressColor = (percentage) => {
      if (percentage < 30) return '#f59e0b';
      if (percentage < 70) return '#3b82f6';
      return '#10b981';
    };
    
    // 保存草稿
    const saveAsDraft = async () => {
      isSavingDraft.value = true;
      
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        ElMessage.success('草稿保存成功');
      } catch (error) {
        ElMessage.error('保存失败，请重试');
      } finally {
        isSavingDraft.value = false;
      }
    };
    
    // 开始创建
    const startCreation = async () => {
      if (!canCreate.value) {
        ElMessage.warning('请完善必填信息');
        return;
      }
      
      try {
        await ElMessageBox.confirm(
          '确认开始生成数字人？此过程可能需要几分钟时间。',
          '确认生成',
          {
            confirmButtonText: '开始生成',
            cancelButtonText: '取消',
            type: 'info'
          }
        );

        // 直接触发父组件的创建方法，不在这里处理进度
        emit('create', props.formData);

      } catch (error) {
        if (error !== 'cancel') {
          console.log('用户取消创建');
        }
      }
    };
    
    // 结果处理方法
    const handleImageError = (event) => {
      console.error('头像加载失败:', event);
      ElMessage.error('头像加载失败');
    };

    const handleVideoError = (event) => {
      console.error('视频加载失败:', event);
      ElMessage.error('视频加载失败');
    };

    const downloadAvatar = () => {
      if (generationResult.value?.resultUrls?.avatar_url) {
        const link = document.createElement('a');
        link.href = generationResult.value.resultUrls.avatar_url;
        link.download = `digital_human_avatar_${generationResult.value.digitalHumanId}.jpg`;
        link.click();
      }
    };

    const downloadVideo = () => {
      if (generationResult.value?.resultUrls?.preview_url) {
        const link = document.createElement('a');
        link.href = generationResult.value.resultUrls.preview_url;
        link.download = `digital_human_video_${generationResult.value.digitalHumanId}.mp4`;
        link.click();
      }
    };

    const startChat = () => {
      // 跳转到数字人聊天页面
      const chatUrl = `${window.location.origin}/digital-human/chat/${generationResult.value.digitalHumanId}`;
      window.open(chatUrl, '_blank');
    };

    const createAnother = () => {
      // 重置状态，创建新的数字人
      generationResult.value = null;
      generationProgress.value = 0;
      currentProgressStep.value = 0;
      isCreating.value = false;
    };

    const goToManagement = () => {
      // 跳转到数字人应用中心
      const appUrl = `${window.location.origin}/digital-human/app`;
      window.open(appUrl, '_blank');
    };

    const shareDigitalHuman = () => {
      // 分享数字人
      const shareUrl = `${window.location.origin}/digital-human/view/${generationResult.value.digitalHumanId}`;
      navigator.clipboard.writeText(shareUrl).then(() => {
        ElMessage.success('分享链接已复制到剪贴板');
      }).catch(() => {
        ElMessage.error('复制失败，请手动复制链接');
      });
    };

    const formatDate = (date) => {
      if (!date) return '';
      return new Date(date).toLocaleString('zh-CN');
    };

    const getFileSize = () => {
      // 模拟文件大小计算
      return '约 2.1MB';
    };

    const getVideoDuration = () => {
      // 模拟视频时长
      return '约 5秒';
    };

    return {
      isSavingDraft,
      isCreating,
      generationProgress,
      currentProgressStep,
      generationResult,
      generationOptions,
      progressSteps,
      canCreate,
      getTypeText,
      getGenderText,
      getAppearanceText,
      getVoiceText,

      getProgressColor,
      saveAsDraft,
      startCreation,
      handleImageError,
      handleVideoError,
      downloadAvatar,
      downloadVideo,
      startChat,
      createAnother,
      goToManagement,
      shareDigitalHuman,
      formatDate,
      getFileSize,
      getVideoDuration
    };
  }
});
</script>

<style scoped>
.complete-step {
  padding: 32px;
  max-width: 800px;
}

.step-header {
  margin-bottom: 32px;
}

.step-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #f9fafb;
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.step-description {
  margin: 0;
  font-size: 16px;
  color: #9ca3af;
  line-height: 1.5;
}

.complete-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* 信息确认样式 */
.info-summary h4,
.generation-options h4,
.advanced-options h4,
.estimation-info h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #f9fafb;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.summary-item.full-width {
  grid-column: 1 / -1;
}

.summary-item label {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 500;
}

.summary-item span {
  font-size: 14px;
  color: #f9fafb;
  font-weight: 500;
}

/* 生成选项样式 */
.options-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.option-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-item label {
  font-size: 14px;
  font-weight: 500;
  color: #d1d5db;
}

/* 高级设置样式 */
.advanced-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.advanced-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  background: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
}

.option-desc {
  font-size: 12px;
  color: #9ca3af;
  margin-left: 24px;
}

/* 创建提示样式 */
.creation-tips {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.creation-tips h4 {
  margin: 0 0 16px 0;
  color: #60a5fa;
  font-size: 16px;
  font-weight: 600;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tip-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.tip-text {
  color: #f9fafb;
  font-size: 14px;
  line-height: 1.4;
}

/* 创建按钮样式 */
.creation-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* 生成进度样式 */
.generation-progress {
  padding: 24px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.generation-progress h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #3b82f6;
  text-align: center;
}

.progress-text {
  font-size: 14px;
  font-weight: 600;
  color: #f9fafb;
}

.progress-steps {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.progress-step {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.progress-step.active {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.progress-step.completed {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.step-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(107, 114, 128, 0.3);
  color: #9ca3af;
  font-size: 12px;
  font-weight: 600;
}

.progress-step.active .step-icon {
  background: rgba(59, 130, 246, 0.3);
  color: #3b82f6;
}

.progress-step.completed .step-icon {
  background: rgba(34, 197, 94, 0.3);
  color: #22c55e;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 生成结果样式 */
.generation-result {
  margin-top: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 16px;
  border: 1px solid #0ea5e9;
}

.result-header {
  text-align: center;
  margin-bottom: 32px;
}

.result-header h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #0369a1;
}

.result-header p {
  margin: 0;
  font-size: 16px;
  color: #64748b;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.digital-human-preview {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.preview-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.avatar-preview,
.video-preview {
  text-align: center;
}

.avatar-preview h4,
.video-preview h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.avatar-container,
.video-container {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
}

.preview-video {
  width: 100%;
  height: 200px;
  border-radius: 8px;
}

.avatar-placeholder,
.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #9ca3af;
}

.avatar-placeholder .el-icon,
.video-placeholder .el-icon {
  font-size: 48px;
}

.avatar-actions,
.video-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.digital-human-info {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.digital-human-info h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.info-item span {
  font-size: 16px;
  color: #374151;
}

.result-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .preview-container {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .result-actions {
    flex-direction: column;
  }
}

.step-text {
  font-size: 14px;
  color: #d1d5db;
}

.progress-step.active .step-text {
  color: #3b82f6;
  font-weight: 500;
}

.progress-step.completed .step-text {
  color: #22c55e;
}
</style>
