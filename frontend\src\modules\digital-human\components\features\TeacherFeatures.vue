<template>
  <div class="teacher-features">
    <h4 class="features-title">教育培训功能</h4>
    <div class="features-grid">
      <div class="feature-item" @click="sendAction('outline')">
        <book-outlined />
        <span>课程大纲</span>
      </div>
      <div class="feature-item" @click="sendAction('progress')">
        <line-chart-outlined />
        <span>学习进度</span>
      </div>
      <div class="feature-item" @click="sendAction('material')">
        <file-pdf-outlined />
        <span>学习资料</span>
      </div>
      <div class="feature-item" @click="sendAction('quiz')">
        <question-circle-outlined />
        <span>知识测验</span>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { Reading, TrendCharts, Document, QuestionFilled } from '@element-plus/icons-vue';

export default defineComponent({
  name: 'TeacherFeatures',
  
  components: {
    BookOutlined,
    LineChartOutlined,
    FilePdfOutlined,
    QuestionCircleOutlined
  },
  
  props: {
    digitalHuman: {
      type: Object,
      required: true,
      default: () => ({
        name: '',
        type: '',
        avatar_url: '',
        description: '',
        apiId: '',
        digital_human_id: '',
        voice_id: '',
        welcomeText: ''
      })
    }
  },
  
  emits: ['send-message'],
  
  setup(props, { emit }) {
    const sendAction = (action) => {
      switch(action) {
        case 'outline':
          emit('send-message', {
            type: 'showCard',
            title: '课程大纲',
            content: '<b>人工智能基础课程</b><br/><ol><li>人工智能概述</li><li>机器学习基础</li><li>神经网络入门</li><li>深度学习应用</li><li>强化学习理论</li></ol>',
            actions: [
              { text: '开始学习', primary: true },
              { text: '下载大纲', primary: false }
            ]
          });
          break;
        case 'progress':
          emit('send-message', {
            type: 'showCard',
            title: '学习进度',
            content: '当前进度: 3/5课程<br>完成度: 60%<br>最近学习: 神经网络入门<br>学习时长: 6小时45分钟',
            actions: [
              { text: '继续学习', primary: true },
              { text: '查看详情', primary: false }
            ]
          });
          break;
        case 'material':
          emit('send-message', {
            type: 'sendMessage',
            content: '我需要查看学习资料'
          });
          break;
        case 'quiz':
          emit('send-message', {
            type: 'sendMessage',
            content: '我想进行知识测验'
          });
          break;
      }
    };
    
    return {
      sendAction
    };
  }
});
</script>

<style scoped>
.teacher-features {
  width: 100%;
}

.features-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #f59e0b;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.feature-item {
  background-color: rgba(245, 158, 11, 0.1);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.feature-item:hover {
  background-color: rgba(245, 158, 11, 0.2);
  transform: translateY(-2px);
}

.feature-item span {
  font-size: 14px;
  color: #f9fafb;
}

:deep(.light-theme) .feature-item {
  background-color: rgba(245, 158, 11, 0.05);
}

:deep(.light-theme) .feature-item span {
  color: #1f2937;
}

:deep(.light-theme) .feature-item:hover {
  background-color: rgba(245, 158, 11, 0.1);
}

:deep(.light-theme) .features-title {
  color: #b45309;
}
</style> 