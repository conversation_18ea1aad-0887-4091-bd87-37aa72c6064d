<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'

onLaunch(() => {
  console.log('App Launch')
})

onShow(() => {
  console.log('App Show')
})

onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
@import '@/styles/variables.scss';
@import '@/styles/global.scss';

/* 全局样式 */
page {
  background-color: $bg-secondary;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
}

/* 通用类 */
.container {
  padding: 0;
  margin: 0;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.justify-center {
  justify-content: center;
}

.items-center {
  align-items: center;
}

.text-center {
  text-align: center;
}

/* 间距类 */
.p-1 { padding: $spacing-1; }
.p-2 { padding: $spacing-2; }
.p-3 { padding: $spacing-3; }
.p-4 { padding: $spacing-4; }

.m-1 { margin: $spacing-1; }
.m-2 { margin: $spacing-2; }
.m-3 { margin: $spacing-3; }
.m-4 { margin: $spacing-4; }

/* 文字颜色 */
.text-primary { color: $text-primary; }
.text-secondary { color: $text-secondary; }
.text-tertiary { color: $text-tertiary; }

/* 背景颜色 */
.bg-primary { background-color: $bg-primary; }
.bg-secondary { background-color: $bg-secondary; }
.bg-tertiary { background-color: $bg-tertiary; }

/* 圆角 */
.rounded { border-radius: $radius-md; }
.rounded-lg { border-radius: $radius-lg; }
.rounded-full { border-radius: $radius-full; }
</style>
