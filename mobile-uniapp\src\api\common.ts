/**
 * 通用API接口
 */
import request from '@/utils/request'

// 系统配置
export interface SystemConfig {
  appName: string
  appVersion: string
  apiVersion: string
  features: {
    translation: boolean
    chat: boolean
    document: boolean
    agent: boolean
    payment: boolean
  }
  limits: {
    maxFileSize: number
    maxDailyUsage: number
    maxConcurrentTasks: number
  }
  supportedLanguages: string[]
  supportedFormats: string[]
  maintenance: {
    enabled: boolean
    message?: string
    startTime?: string
    endTime?: string
  }
}

// 通知信息
export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  content: string
  link?: string
  read: boolean
  createdAt: string
  expiresAt?: string
}

// 反馈信息
export interface Feedback {
  id: string
  type: 'bug' | 'feature' | 'improvement' | 'other'
  title: string
  content: string
  contact?: string
  attachments?: string[]
  status: 'pending' | 'processing' | 'resolved' | 'closed'
  response?: string
  createdAt: string
  updatedAt: string
}

// 版本信息
export interface VersionInfo {
  version: string
  buildNumber: string
  releaseDate: string
  features: string[]
  bugFixes: string[]
  breaking: string[]
  downloadUrl?: string
  forceUpdate: boolean
  minVersion: string
}

/**
 * 获取系统配置
 */
export const getSystemConfig = (): Promise<SystemConfig> => {
  return request.get('/common/config')
}

/**
 * 获取版本信息
 */
export const getVersionInfo = (): Promise<VersionInfo> => {
  return request.get('/common/version')
}

/**
 * 检查更新
 */
export const checkUpdate = (currentVersion: string): Promise<{
  hasUpdate: boolean
  latestVersion: string
  updateInfo?: VersionInfo
  forceUpdate: boolean
}> => {
  return request.get('/common/check-update', {
    params: { version: currentVersion }
  })
}

/**
 * 获取通知列表
 */
export const getNotifications = (params: {
  page?: number
  limit?: number
  type?: string
  read?: boolean
}): Promise<{
  list: Notification[]
  total: number
  page: number
  limit: number
  unreadCount: number
}> => {
  return request.get('/common/notifications', { params })
}

/**
 * 标记通知为已读
 */
export const markNotificationRead = (notificationId: string): Promise<{ message: string }> => {
  return request.put(`/common/notifications/${notificationId}/read`)
}

/**
 * 批量标记通知为已读
 */
export const markAllNotificationsRead = (): Promise<{ message: string }> => {
  return request.put('/common/notifications/read-all')
}

/**
 * 删除通知
 */
export const deleteNotification = (notificationId: string): Promise<{ message: string }> => {
  return request.delete(`/common/notifications/${notificationId}`)
}

/**
 * 获取未读通知数量
 */
export const getUnreadNotificationCount = (): Promise<{ count: number }> => {
  return request.get('/common/notifications/unread-count')
}

/**
 * 提交反馈
 */
export const submitFeedback = (params: {
  type: 'bug' | 'feature' | 'improvement' | 'other'
  title: string
  content: string
  contact?: string
  attachments?: File[]
}): Promise<{ message: string; feedbackId: string }> => {
  const formData = new FormData()
  formData.append('type', params.type)
  formData.append('title', params.title)
  formData.append('content', params.content)
  if (params.contact) {
    formData.append('contact', params.contact)
  }
  if (params.attachments) {
    params.attachments.forEach((file, index) => {
      formData.append(`attachment_${index}`, file)
    })
  }
  
  return request.post('/common/feedback', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取反馈列表
 */
export const getFeedbackList = (params: {
  page?: number
  limit?: number
  type?: string
  status?: string
}): Promise<{
  list: Feedback[]
  total: number
  page: number
  limit: number
}> => {
  return request.get('/common/feedback', { params })
}

/**
 * 获取反馈详情
 */
export const getFeedbackDetail = (feedbackId: string): Promise<Feedback> => {
  return request.get(`/common/feedback/${feedbackId}`)
}

/**
 * 上传文件
 */
export const uploadFile = (file: File, params?: {
  type?: 'image' | 'document' | 'audio' | 'video' | 'other'
  category?: string
  compress?: boolean
}): Promise<{
  url: string
  name: string
  size: number
  type: string
  id: string
  thumbnailUrl?: string
}> => {
  const formData = new FormData()
  formData.append('file', file)
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      formData.append(key, String(value))
    })
  }
  
  return request.post('/common/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 批量上传文件
 */
export const uploadFiles = (files: File[], params?: {
  type?: string
  category?: string
  compress?: boolean
}): Promise<Array<{
  url: string
  name: string
  size: number
  type: string
  id: string
  thumbnailUrl?: string
}>> => {
  const formData = new FormData()
  files.forEach((file, index) => {
    formData.append(`file_${index}`, file)
  })
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      formData.append(key, String(value))
    })
  }
  
  return request.post('/common/upload/batch', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 删除文件
 */
export const deleteFile = (fileId: string): Promise<{ message: string }> => {
  return request.delete(`/common/file/${fileId}`)
}

/**
 * 获取文件信息
 */
export const getFileInfo = (fileId: string): Promise<{
  id: string
  name: string
  originalName: string
  size: number
  type: string
  url: string
  thumbnailUrl?: string
  createdAt: string
}> => {
  return request.get(`/common/file/${fileId}`)
}

/**
 * 生成二维码
 */
export const generateQRCode = (params: {
  content: string
  size?: number
  format?: 'png' | 'jpg' | 'svg'
  errorLevel?: 'L' | 'M' | 'Q' | 'H'
  margin?: number
  foregroundColor?: string
  backgroundColor?: string
  logo?: string
}): Promise<{
  qrCodeUrl: string
  content: string
  size: number
  format: string
}> => {
  return request.post('/common/qrcode', params)
}

/**
 * 解析二维码
 */
export const parseQRCode = (image: File | string): Promise<{
  content: string
  type: 'text' | 'url' | 'email' | 'phone' | 'wifi' | 'other'
  metadata?: Record<string, any>
}> => {
  if (typeof image === 'string') {
    return request.post('/common/qrcode/parse', { imageUrl: image })
  } else {
    const formData = new FormData()
    formData.append('image', image)
    return request.post('/common/qrcode/parse', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

/**
 * 获取地理位置信息
 */
export const getLocationInfo = (params?: {
  ip?: string
  coordinates?: {
    latitude: number
    longitude: number
  }
}): Promise<{
  country: string
  region: string
  city: string
  timezone: string
  currency: string
  language: string
  coordinates?: {
    latitude: number
    longitude: number
  }
}> => {
  return request.get('/common/location', { params })
}

/**
 * 发送短信验证码
 */
export const sendSMSCode = (params: {
  phone: string
  type: 'register' | 'login' | 'reset' | 'bind'
  template?: string
}): Promise<{ message: string; expiresIn: number }> => {
  return request.post('/common/sms/send', params)
}

/**
 * 验证短信验证码
 */
export const verifySMSCode = (params: {
  phone: string
  code: string
  type: string
}): Promise<{ valid: boolean; message: string }> => {
  return request.post('/common/sms/verify', params)
}

/**
 * 发送邮件验证码
 */
export const sendEmailCode = (params: {
  email: string
  type: 'register' | 'login' | 'reset' | 'bind'
  template?: string
}): Promise<{ message: string; expiresIn: number }> => {
  return request.post('/common/email/send', params)
}

/**
 * 验证邮件验证码
 */
export const verifyEmailCode = (params: {
  email: string
  code: string
  type: string
}): Promise<{ valid: boolean; message: string }> => {
  return request.post('/common/email/verify', params)
}

/**
 * 获取系统统计信息
 */
export const getSystemStats = (): Promise<{
  totalUsers: number
  activeUsers: number
  totalTranslations: number
  totalConversations: number
  totalDocuments: number
  systemLoad: number
  uptime: number
  version: string
}> => {
  return request.get('/common/stats')
}

/**
 * 健康检查
 */
export const healthCheck = (): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  services: Record<string, {
    status: 'up' | 'down'
    responseTime?: number
    error?: string
  }>
}> => {
  return request.get('/common/health')
}

// 默认导出
export default {
  getSystemConfig,
  getVersionInfo,
  checkUpdate,
  getNotifications,
  markNotificationRead,
  markAllNotificationsRead,
  deleteNotification,
  getUnreadNotificationCount,
  submitFeedback,
  getFeedbackList,
  getFeedbackDetail,
  uploadFile,
  uploadFiles,
  deleteFile,
  getFileInfo,
  generateQRCode,
  parseQRCode,
  getLocationInfo,
  sendSMSCode,
  verifySMSCode,
  sendEmailCode,
  verifyEmailCode,
  getSystemStats,
  healthCheck
}
