<template>
  <view class="document-conversion-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">文档转换</text>
      <text class="page-desc">支持多种文档格式互相转换</text>
    </view>

    <!-- 转换类型选择 -->
    <view class="conversion-types">
      <view class="section-title">
        <text class="title-text">选择转换类型</text>
      </view>
      
      <view class="types-grid">
        <view 
          class="type-item"
          v-for="type in conversionTypes"
          :key="type.id"
          :class="{ active: selectedType === type.id }"
          @click="selectType(type.id)"
        >
          <view class="type-icon" :style="{ backgroundColor: type.color }">
            <u-icon :name="type.icon" color="#fff" size="24"></u-icon>
          </view>
          <text class="type-name">{{ type.name }}</text>
          <text class="type-desc">{{ type.description }}</text>
        </view>
      </view>
    </view>

    <!-- 文件上传区域 -->
    <view class="upload-section">
      <view class="section-title">
        <text class="title-text">上传文件</text>
        <text class="title-desc">支持的格式：{{ getSupportedFormats() }}</text>
      </view>
      
      <view class="upload-area" @click="chooseFile">
        <view class="upload-content" v-if="!selectedFile">
          <u-icon name="cloud-upload" size="48" color="#2563EB"></u-icon>
          <text class="upload-text">点击选择文件</text>
          <text class="upload-desc">或拖拽文件到此处</text>
          <text class="upload-limit">文件大小限制：50MB</text>
        </view>
        
        <view class="file-info" v-else>
          <view class="file-icon">
            <u-icon :name="getFileIcon(selectedFile.type)" size="32" color="#2563EB"></u-icon>
          </view>
          <view class="file-details">
            <text class="file-name">{{ selectedFile.name }}</text>
            <text class="file-size">{{ formatFileSize(selectedFile.size) }}</text>
          </view>
          <view class="file-actions">
            <u-icon name="close-circle" size="20" color="#EF4444" @click.stop="removeFile"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 转换选项 -->
    <view class="conversion-options" v-if="selectedFile && selectedType">
      <view class="section-title">
        <text class="title-text">转换选项</text>
      </view>
      
      <view class="options-list">
        <!-- 输出格式选择 -->
        <view class="option-item">
          <text class="option-label">输出格式</text>
          <u-button 
            type="text" 
            :text="getOutputFormatText()" 
            @click="showFormatPicker = true"
            :custom-style="{ color: '#2563EB' }"
          >
            <u-icon name="arrow-down" size="12" color="#2563EB" margin-left="4"></u-icon>
          </u-button>
        </view>
        
        <!-- 质量设置 -->
        <view class="option-item" v-if="needsQualityOption()">
          <text class="option-label">转换质量</text>
          <view class="quality-options">
            <u-radio-group v-model="conversionOptions.quality">
              <u-radio name="high" label="高质量"></u-radio>
              <u-radio name="medium" label="标准"></u-radio>
              <u-radio name="low" label="快速"></u-radio>
            </u-radio-group>
          </view>
        </view>
        
        <!-- 页面范围 -->
        <view class="option-item" v-if="needsPageRange()">
          <text class="option-label">页面范围</text>
          <view class="page-range-options">
            <u-radio-group v-model="conversionOptions.pageRange">
              <u-radio name="all" label="全部页面"></u-radio>
              <u-radio name="custom" label="自定义"></u-radio>
            </u-radio-group>
            <input 
              v-if="conversionOptions.pageRange === 'custom'"
              class="page-input"
              v-model="conversionOptions.customPages"
              placeholder="例如：1-5,8,10-12"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 转换按钮 -->
    <view class="conversion-action" v-if="selectedFile && selectedType">
      <u-button 
        type="primary" 
        size="large"
        :loading="isConverting"
        :disabled="!canConvert"
        @click="startConversion"
        :custom-style="{ width: '100%' }"
      >
        {{ isConverting ? '转换中...' : '开始转换' }}
      </u-button>
    </view>

    <!-- 转换进度 -->
    <view class="conversion-progress" v-if="isConverting">
      <view class="progress-header">
        <text class="progress-title">正在转换文档</text>
        <text class="progress-desc">请稍候，转换可能需要几分钟时间</text>
      </view>
      
      <view class="progress-bar">
        <u-line-progress 
          :percent="conversionProgress" 
          :show-percent="true"
          active-color="#2563EB"
        ></u-line-progress>
      </view>
      
      <view class="progress-status">
        <text class="status-text">{{ progressStatus }}</text>
      </view>
    </view>

    <!-- 转换结果 -->
    <view class="conversion-result" v-if="conversionResult">
      <view class="result-header">
        <u-icon name="checkmark-circle" size="32" color="#10B981"></u-icon>
        <text class="result-title">转换完成</text>
      </view>
      
      <view class="result-file">
        <view class="result-icon">
          <u-icon :name="getFileIcon(conversionResult.format)" size="32" color="#2563EB"></u-icon>
        </view>
        <view class="result-details">
          <text class="result-name">{{ conversionResult.filename }}</text>
          <text class="result-size">{{ formatFileSize(conversionResult.size) }}</text>
        </view>
        <view class="result-actions">
          <u-button 
            type="primary" 
            size="small"
            @click="downloadResult"
          >
            下载
          </u-button>
          <u-button 
            type="text" 
            size="small"
            @click="shareResult"
            :custom-style="{ color: '#2563EB' }"
          >
            分享
          </u-button>
        </view>
      </view>
    </view>

    <!-- 转换历史 -->
    <view class="conversion-history" v-if="conversionHistory.length > 0">
      <view class="section-title">
        <text class="title-text">转换历史</text>
        <u-button 
          type="text" 
          text="清空" 
          size="small"
          @click="clearHistory"
          :custom-style="{ color: '#EF4444', fontSize: '24rpx' }"
        ></u-button>
      </view>
      
      <view class="history-list">
        <view 
          class="history-item"
          v-for="item in conversionHistory.slice(0, 5)"
          :key="item.id"
          @click="downloadHistoryItem(item)"
        >
          <view class="history-icon">
            <u-icon :name="getFileIcon(item.output_format)" size="20" color="#2563EB"></u-icon>
          </view>
          <view class="history-details">
            <text class="history-name">{{ item.filename }}</text>
            <text class="history-info">
              {{ item.input_format.toUpperCase() }} → {{ item.output_format.toUpperCase() }}
            </text>
            <text class="history-time">{{ formatTime(item.created_at) }}</text>
          </view>
          <view class="history-actions">
            <u-icon name="download" size="16" color="#6B7280"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 输出格式选择器 -->
    <u-picker
      v-model="showFormatPicker"
      :columns="outputFormatColumns"
      @confirm="onFormatConfirm"
      @cancel="showFormatPicker = false"
    ></u-picker>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import dayjs from 'dayjs'

// 响应式数据
const selectedType = ref<string>('')
const selectedFile = ref<any>(null)
const isConverting = ref<boolean>(false)
const conversionProgress = ref<number>(0)
const progressStatus = ref<string>('')
const conversionResult = ref<any>(null)
const conversionHistory = ref<any[]>([])
const showFormatPicker = ref<boolean>(false)
const conversionOptions = ref({
  outputFormat: '',
  quality: 'medium',
  pageRange: 'all',
  customPages: ''
})

// 转换类型
const conversionTypes = ref([
  {
    id: 'pdf',
    name: 'PDF转换',
    description: '转换为PDF格式',
    icon: 'file-pdf',
    color: '#EF4444',
    supportedInputs: ['doc', 'docx', 'txt', 'rtf', 'odt'],
    supportedOutputs: ['pdf']
  },
  {
    id: 'word',
    name: 'Word转换',
    description: '转换为Word格式',
    icon: 'file-word',
    color: '#2563EB',
    supportedInputs: ['pdf', 'txt', 'rtf', 'odt'],
    supportedOutputs: ['doc', 'docx']
  },
  {
    id: 'text',
    name: '文本转换',
    description: '转换为纯文本',
    icon: 'file-text',
    color: '#10B981',
    supportedInputs: ['pdf', 'doc', 'docx', 'rtf', 'odt'],
    supportedOutputs: ['txt']
  },
  {
    id: 'excel',
    name: 'Excel转换',
    description: '转换为Excel格式',
    icon: 'file-excel',
    color: '#059669',
    supportedInputs: ['csv', 'txt'],
    supportedOutputs: ['xls', 'xlsx']
  }
])

// 计算属性
const canConvert = computed(() => {
  return selectedFile.value && selectedType.value && conversionOptions.value.outputFormat
})

const outputFormatColumns = computed(() => {
  const selectedTypeData = conversionTypes.value.find(t => t.id === selectedType.value)
  if (!selectedTypeData) return [[]]

  return [selectedTypeData.supportedOutputs.map(format => ({
    text: format.toUpperCase(),
    value: format
  }))]
})

// 页面加载
onMounted(() => {
  loadConversionHistory()
})

// 选择转换类型
const selectType = (typeId: string) => {
  selectedType.value = typeId
  conversionOptions.value.outputFormat = ''

  // 自动选择第一个输出格式
  const typeData = conversionTypes.value.find(t => t.id === typeId)
  if (typeData && typeData.supportedOutputs.length > 0) {
    conversionOptions.value.outputFormat = typeData.supportedOutputs[0]
  }
}

// 选择文件
const chooseFile = () => {
  uni.chooseFile({
    count: 1,
    type: 'file',
    success: (res) => {
      const file = res.tempFiles[0]

      // 检查文件大小
      if (file.size > 50 * 1024 * 1024) {
        uni.showToast({
          title: '文件大小不能超过50MB',
          icon: 'error'
        })
        return
      }

      // 检查文件格式
      const fileExtension = getFileExtension(file.name)
      if (!isValidFileFormat(fileExtension)) {
        uni.showToast({
          title: '不支持的文件格式',
          icon: 'error'
        })
        return
      }

      selectedFile.value = {
        name: file.name,
        size: file.size,
        type: fileExtension,
        path: file.path
      }
    },
    fail: (error) => {
      console.error('选择文件失败:', error)
      uni.showToast({
        title: '选择文件失败',
        icon: 'error'
      })
    }
  })
}

// 移除文件
const removeFile = () => {
  selectedFile.value = null
  conversionResult.value = null
}

// 开始转换
const startConversion = async () => {
  if (!canConvert.value) return

  try {
    isConverting.value = true
    conversionProgress.value = 0
    progressStatus.value = '准备转换...'

    // 模拟转换进度
    const progressInterval = setInterval(() => {
      if (conversionProgress.value < 90) {
        conversionProgress.value += Math.random() * 10

        if (conversionProgress.value < 30) {
          progressStatus.value = '正在解析文档...'
        } else if (conversionProgress.value < 60) {
          progressStatus.value = '正在转换格式...'
        } else {
          progressStatus.value = '正在生成文件...'
        }
      }
    }, 500)

    // 模拟API调用
    setTimeout(() => {
      clearInterval(progressInterval)
      conversionProgress.value = 100
      progressStatus.value = '转换完成'

      // 模拟转换结果
      const outputExtension = conversionOptions.value.outputFormat
      const baseName = selectedFile.value.name.split('.')[0]

      conversionResult.value = {
        filename: `${baseName}.${outputExtension}`,
        format: outputExtension,
        size: Math.floor(selectedFile.value.size * (0.8 + Math.random() * 0.4)),
        downloadUrl: 'mock-download-url'
      }

      // 添加到历史记录
      const historyItem = {
        id: Date.now().toString(),
        filename: conversionResult.value.filename,
        input_format: selectedFile.value.type,
        output_format: outputExtension,
        size: conversionResult.value.size,
        created_at: new Date().toISOString(),
        download_url: conversionResult.value.downloadUrl
      }

      conversionHistory.value.unshift(historyItem)
      saveConversionHistory()

      isConverting.value = false

      uni.showToast({
        title: '转换成功',
        icon: 'success'
      })
    }, 3000)

  } catch (error) {
    console.error('转换失败:', error)
    isConverting.value = false
    uni.showToast({
      title: '转换失败',
      icon: 'error'
    })
  }
}

// 下载结果
const downloadResult = () => {
  if (!conversionResult.value) return

  // #ifdef H5
  // H5环境下模拟下载
  uni.showToast({
    title: '开始下载',
    icon: 'success'
  })
  // #endif

  // #ifdef APP-PLUS
  uni.downloadFile({
    url: conversionResult.value.downloadUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.saveFile({
          tempFilePath: res.tempFilePath,
          success: () => {
            uni.showToast({
              title: '下载成功',
              icon: 'success'
            })
          }
        })
      }
    },
    fail: () => {
      uni.showToast({
        title: '下载失败',
        icon: 'error'
      })
    }
  })
  // #endif
}

// 分享结果
const shareResult = () => {
  if (!conversionResult.value) return

  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 2,
    imageUrl: '/static/images/app-icon.png',
    title: '文档转换完成',
    summary: `已将文档转换为${conversionResult.value.format.toUpperCase()}格式`,
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '分享失败',
        icon: 'error'
      })
    }
  })
}

// 工具函数
const getFileExtension = (filename: string) => {
  return filename.split('.').pop()?.toLowerCase() || ''
}

const isValidFileFormat = (extension: string) => {
  const allSupportedFormats = conversionTypes.value.reduce((acc, type) => {
    return acc.concat(type.supportedInputs)
  }, [] as string[])

  return allSupportedFormats.includes(extension)
}

const getSupportedFormats = () => {
  if (!selectedType.value) return '请先选择转换类型'

  const typeData = conversionTypes.value.find(t => t.id === selectedType.value)
  return typeData ? typeData.supportedInputs.join(', ').toUpperCase() : ''
}

const getOutputFormatText = () => {
  return conversionOptions.value.outputFormat
    ? conversionOptions.value.outputFormat.toUpperCase()
    : '选择格式'
}

const needsQualityOption = () => {
  return ['pdf', 'word'].includes(selectedType.value)
}

const needsPageRange = () => {
  return selectedFile.value?.type === 'pdf' && selectedType.value !== 'pdf'
}

const getFileIcon = (fileType: string) => {
  const iconMap: Record<string, string> = {
    pdf: 'file-pdf',
    doc: 'file-word',
    docx: 'file-word',
    txt: 'file-text',
    rtf: 'file-text',
    odt: 'file-text',
    xls: 'file-excel',
    xlsx: 'file-excel',
    csv: 'file-excel'
  }

  return iconMap[fileType] || 'file'
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

// 格式选择确认
const onFormatConfirm = (value: any) => {
  conversionOptions.value.outputFormat = value[0].value
  showFormatPicker.value = false
}

// 下载历史项目
const downloadHistoryItem = (item: any) => {
  uni.showToast({
    title: '开始下载',
    icon: 'success'
  })
}

// 清空历史
const clearHistory = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有转换历史吗？',
    success: (res) => {
      if (res.confirm) {
        conversionHistory.value = []
        saveConversionHistory()
        uni.showToast({
          title: '清空成功',
          icon: 'success'
        })
      }
    }
  })
}

// 保存转换历史
const saveConversionHistory = () => {
  uni.setStorageSync('document_conversion_history', conversionHistory.value)
}

// 加载转换历史
const loadConversionHistory = () => {
  const history = uni.getStorageSync('document_conversion_history') || []
  conversionHistory.value = history
}

// 页面下拉刷新
const onPullDownRefresh = () => {
  loadConversionHistory()
  uni.stopPullDownRefresh()
}

// 导出方法供页面使用
defineExpose({
  onPullDownRefresh
})
</script>

<style lang="scss" scoped>
.document-conversion-page {
  min-height: 100vh;
  background-color: $bg-secondary;
}

// 页面头部
.page-header {
  background-color: $bg-primary;
  padding: $spacing-6 $spacing-4;
  text-align: center;
  border-bottom: 1px solid $border-primary;

  .page-title {
    display: block;
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: $spacing-2;
  }

  .page-desc {
    font-size: $font-size-base;
    color: $text-secondary;
  }
}

// 通用区块标题
.section-title {
  padding: $spacing-4 $spacing-4 $spacing-3;

  .title-text {
    display: block;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    margin-bottom: $spacing-1;
  }

  .title-desc {
    font-size: $font-size-sm;
    color: $text-tertiary;
  }
}

// 转换类型选择
.conversion-types {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .types-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-3;
    padding: 0 $spacing-4 $spacing-4;

    .type-item {
      background-color: $bg-tertiary;
      border-radius: $radius-lg;
      padding: $spacing-4;
      text-align: center;
      border: 2px solid transparent;

      &.active {
        border-color: $primary;
        background-color: $primary-50;
      }

      .type-icon {
        width: 96rpx;
        height: 96rpx;
        border-radius: $radius-2xl;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto $spacing-3;
      }

      .type-name {
        display: block;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-1;
      }

      .type-desc {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }
  }
}

// 文件上传区域
.upload-section {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .upload-area {
    margin: 0 $spacing-4 $spacing-4;
    border: 2px dashed $border-primary;
    border-radius: $radius-lg;
    padding: $spacing-6;

    .upload-content {
      text-align: center;

      .upload-text {
        display: block;
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin: $spacing-4 0 $spacing-2;
      }

      .upload-desc {
        display: block;
        font-size: $font-size-base;
        color: $text-secondary;
        margin-bottom: $spacing-2;
      }

      .upload-limit {
        font-size: $font-size-sm;
        color: $text-tertiary;
      }
    }

    .file-info {
      display: flex;
      align-items: center;
      gap: $spacing-3;

      .file-icon {
        width: 80rpx;
        height: 80rpx;
        background-color: $primary-50;
        border-radius: $radius-lg;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .file-details {
        flex: 1;

        .file-name {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-size {
          font-size: $font-size-sm;
          color: $text-secondary;
        }
      }

      .file-actions {
        padding: $spacing-2;
      }
    }
  }
}

// 转换选项
.conversion-options {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .options-list {
    padding: 0 $spacing-4 $spacing-4;

    .option-item {
      padding: $spacing-4 0;
      border-bottom: 1px solid $border-primary;

      &:last-child {
        border-bottom: none;
      }

      .option-label {
        display: block;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-3;
      }

      .quality-options,
      .page-range-options {
        .page-input {
          width: 100%;
          margin-top: $spacing-2;
          padding: $spacing-2 $spacing-3;
          border: 1px solid $border-primary;
          border-radius: $radius-md;
          font-size: $font-size-base;
          color: $text-primary;
          background-color: $bg-tertiary;

          &::placeholder {
            color: $text-quaternary;
          }
        }
      }
    }
  }
}

// 转换按钮
.conversion-action {
  padding: $spacing-4;
}

// 转换进度
.conversion-progress {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;
  padding: $spacing-4;

  .progress-header {
    text-align: center;
    margin-bottom: $spacing-4;

    .progress-title {
      display: block;
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-primary;
      margin-bottom: $spacing-1;
    }

    .progress-desc {
      font-size: $font-size-sm;
      color: $text-secondary;
    }
  }

  .progress-bar {
    margin-bottom: $spacing-4;
  }

  .progress-status {
    text-align: center;

    .status-text {
      font-size: $font-size-base;
      color: $text-secondary;
    }
  }
}

// 转换结果
.conversion-result {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;
  padding: $spacing-4;

  .result-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: $spacing-4;

    .result-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-primary;
      margin-top: $spacing-2;
    }
  }

  .result-file {
    display: flex;
    align-items: center;
    gap: $spacing-3;
    background-color: $bg-tertiary;
    border-radius: $radius-lg;
    padding: $spacing-4;

    .result-icon {
      width: 80rpx;
      height: 80rpx;
      background-color: $primary-50;
      border-radius: $radius-lg;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .result-details {
      flex: 1;

      .result-name {
        display: block;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .result-size {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }

    .result-actions {
      display: flex;
      gap: $spacing-2;
    }
  }
}

// 转换历史
.conversion-history {
  background-color: $bg-primary;

  .history-list {
    padding: 0 $spacing-4 $spacing-4;

    .history-item {
      display: flex;
      align-items: center;
      gap: $spacing-3;
      padding: $spacing-3;
      background-color: $bg-tertiary;
      border-radius: $radius-lg;
      margin-bottom: $spacing-2;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        background-color: $gray-200;
      }

      .history-icon {
        width: 64rpx;
        height: 64rpx;
        background-color: $primary-50;
        border-radius: $radius-lg;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .history-details {
        flex: 1;

        .history-name {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .history-info {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-1;
        }

        .history-time {
          font-size: $font-size-xs;
          color: $text-tertiary;
        }
      }

      .history-actions {
        padding: $spacing-2;
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 480px) {
  .types-grid {
    grid-template-columns: 1fr;
  }

  .page-header {
    padding: $spacing-4;
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .document-conversion-page {
    background-color: #1a1a1a;
  }

  .page-header,
  .conversion-types,
  .upload-section,
  .conversion-options,
  .conversion-progress,
  .conversion-result,
  .conversion-history {
    background-color: #2d2d2d;
    border-color: #404040;
  }

  .type-item,
  .upload-area,
  .result-file,
  .history-item {
    background-color: #404040;
    border-color: #555555;
  }

  .type-item.active {
    background-color: #1e3a8a;
    border-color: #3b82f6;
  }

  .page-input {
    background-color: #555555;
    border-color: #666666;
    color: #ffffff;

    &::placeholder {
      color: #888888;
    }
  }
}
</style>
