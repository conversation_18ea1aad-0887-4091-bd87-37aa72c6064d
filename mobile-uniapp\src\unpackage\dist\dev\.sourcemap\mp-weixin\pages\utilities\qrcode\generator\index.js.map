{"version": 3, "file": "index.js", "sources": ["pages/utilities/qrcode/generator/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXRpbGl0aWVzL3FyY29kZS9nZW5lcmF0b3IvaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"qrcode-generator-page\">\n    <!-- 页面标题 -->\n    <view class=\"page-header\">\n      <text class=\"page-title\">二维码生成器</text>\n      <text class=\"page-desc\">快速生成各种类型的二维码</text>\n    </view>\n\n    <!-- 二维码类型选择 -->\n    <view class=\"type-selector\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">选择二维码类型</text>\n      </view>\n      \n      <view class=\"type-tabs\">\n        <view \n          class=\"tab-item\"\n          v-for=\"type in qrcodeTypes\"\n          :key=\"type.id\"\n          :class=\"{ active: selectedType === type.id }\"\n          @click=\"selectType(type.id)\"\n        >\n          <u-icon :name=\"type.icon\" size=\"20\" :color=\"selectedType === type.id ? '#2563EB' : '#6B7280'\"></u-icon>\n          <text class=\"tab-text\">{{ type.name }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 内容输入区域 -->\n    <view class=\"content-input\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">{{ getInputTitle() }}</text>\n        <text class=\"title-desc\">{{ getInputDescription() }}</text>\n      </view>\n      \n      <view class=\"input-area\">\n        <!-- 文本输入 -->\n        <textarea \n          v-if=\"selectedType === 'text'\"\n          class=\"text-input\"\n          v-model=\"qrcodeContent.text\"\n          placeholder=\"请输入要生成二维码的文本内容\"\n          :maxlength=\"500\"\n        ></textarea>\n        \n        <!-- URL输入 -->\n        <input \n          v-if=\"selectedType === 'url'\"\n          class=\"url-input\"\n          v-model=\"qrcodeContent.url\"\n          placeholder=\"https://example.com\"\n          type=\"url\"\n        />\n        \n        <!-- WiFi信息输入 -->\n        <view v-if=\"selectedType === 'wifi'\" class=\"wifi-inputs\">\n          <input \n            class=\"wifi-input\"\n            v-model=\"qrcodeContent.wifi.ssid\"\n            placeholder=\"WiFi名称(SSID)\"\n          />\n          <input \n            class=\"wifi-input\"\n            v-model=\"qrcodeContent.wifi.password\"\n            placeholder=\"WiFi密码\"\n            type=\"password\"\n          />\n          <view class=\"wifi-security\">\n            <text class=\"security-label\">加密方式:</text>\n            <u-radio-group v-model=\"qrcodeContent.wifi.security\">\n              <u-radio name=\"WPA\" label=\"WPA/WPA2\"></u-radio>\n              <u-radio name=\"WEP\" label=\"WEP\"></u-radio>\n              <u-radio name=\"nopass\" label=\"无密码\"></u-radio>\n            </u-radio-group>\n          </view>\n        </view>\n        \n        <!-- 联系人信息输入 -->\n        <view v-if=\"selectedType === 'contact'\" class=\"contact-inputs\">\n          <input \n            class=\"contact-input\"\n            v-model=\"qrcodeContent.contact.name\"\n            placeholder=\"姓名\"\n          />\n          <input \n            class=\"contact-input\"\n            v-model=\"qrcodeContent.contact.phone\"\n            placeholder=\"电话号码\"\n            type=\"tel\"\n          />\n          <input \n            class=\"contact-input\"\n            v-model=\"qrcodeContent.contact.email\"\n            placeholder=\"邮箱地址\"\n            type=\"email\"\n          />\n          <input \n            class=\"contact-input\"\n            v-model=\"qrcodeContent.contact.organization\"\n            placeholder=\"公司/组织\"\n          />\n        </view>\n        \n        <!-- 短信输入 -->\n        <view v-if=\"selectedType === 'sms'\" class=\"sms-inputs\">\n          <input \n            class=\"sms-input\"\n            v-model=\"qrcodeContent.sms.phone\"\n            placeholder=\"手机号码\"\n            type=\"tel\"\n          />\n          <textarea \n            class=\"sms-input\"\n            v-model=\"qrcodeContent.sms.message\"\n            placeholder=\"短信内容\"\n            :maxlength=\"160\"\n          ></textarea>\n        </view>\n        \n        <!-- 邮件输入 -->\n        <view v-if=\"selectedType === 'email'\" class=\"email-inputs\">\n          <input \n            class=\"email-input\"\n            v-model=\"qrcodeContent.email.to\"\n            placeholder=\"收件人邮箱\"\n            type=\"email\"\n          />\n          <input \n            class=\"email-input\"\n            v-model=\"qrcodeContent.email.subject\"\n            placeholder=\"邮件主题\"\n          />\n          <textarea \n            class=\"email-input\"\n            v-model=\"qrcodeContent.email.body\"\n            placeholder=\"邮件内容\"\n            :maxlength=\"300\"\n          ></textarea>\n        </view>\n      </view>\n    </view>\n\n    <!-- 二维码样式设置 -->\n    <view class=\"style-settings\" v-if=\"hasContent()\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">样式设置</text>\n      </view>\n      \n      <view class=\"settings-content\">\n        <!-- 尺寸设置 -->\n        <view class=\"setting-item\">\n          <text class=\"setting-label\">二维码尺寸: {{ qrcodeStyle.size }}px</text>\n          <u-slider \n            v-model=\"qrcodeStyle.size\" \n            :min=\"200\" \n            :max=\"800\" \n            :step=\"50\"\n            active-color=\"#2563EB\"\n            @change=\"generateQRCode\"\n          ></u-slider>\n        </view>\n        \n        <!-- 颜色设置 -->\n        <view class=\"setting-item\">\n          <text class=\"setting-label\">前景色</text>\n          <view class=\"color-picker\">\n            <view \n              class=\"color-option\"\n              v-for=\"color in colorOptions\"\n              :key=\"color\"\n              :style=\"{ backgroundColor: color }\"\n              :class=\"{ active: qrcodeStyle.foreground === color }\"\n              @click=\"selectColor('foreground', color)\"\n            ></view>\n          </view>\n        </view>\n        \n        <view class=\"setting-item\">\n          <text class=\"setting-label\">背景色</text>\n          <view class=\"color-picker\">\n            <view \n              class=\"color-option\"\n              v-for=\"color in backgroundColors\"\n              :key=\"color\"\n              :style=\"{ backgroundColor: color }\"\n              :class=\"{ active: qrcodeStyle.background === color }\"\n              @click=\"selectColor('background', color)\"\n            ></view>\n          </view>\n        </view>\n        \n        <!-- 容错级别 -->\n        <view class=\"setting-item\">\n          <text class=\"setting-label\">容错级别</text>\n          <u-radio-group v-model=\"qrcodeStyle.errorLevel\" @change=\"generateQRCode\">\n            <u-radio name=\"L\" label=\"低 (7%)\"></u-radio>\n            <u-radio name=\"M\" label=\"中 (15%)\"></u-radio>\n            <u-radio name=\"Q\" label=\"较高 (25%)\"></u-radio>\n            <u-radio name=\"H\" label=\"高 (30%)\"></u-radio>\n          </u-radio-group>\n        </view>\n        \n        <!-- Logo设置 -->\n        <view class=\"setting-item\">\n          <text class=\"setting-label\">添加Logo</text>\n          <u-switch v-model=\"qrcodeStyle.withLogo\" @change=\"generateQRCode\"></u-switch>\n          \n          <view v-if=\"qrcodeStyle.withLogo\" class=\"logo-upload\">\n            <view class=\"upload-btn\" @click=\"chooseLogo\">\n              <u-icon name=\"image\" size=\"20\" color=\"#2563EB\"></u-icon>\n              <text class=\"upload-text\">选择Logo</text>\n            </view>\n            <image \n              v-if=\"qrcodeStyle.logoUrl\" \n              class=\"logo-preview\" \n              :src=\"qrcodeStyle.logoUrl\" \n              mode=\"aspectFit\"\n            ></image>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 二维码预览 -->\n    <view class=\"qrcode-preview\" v-if=\"generatedQRCode\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">二维码预览</text>\n        <view class=\"preview-actions\">\n          <u-icon name=\"download\" size=\"18\" color=\"#2563EB\" @click=\"downloadQRCode\"></u-icon>\n          <u-icon name=\"share\" size=\"18\" color=\"#2563EB\" margin-left=\"12\" @click=\"shareQRCode\"></u-icon>\n        </view>\n      </view>\n      \n      <view class=\"preview-content\">\n        <view class=\"qrcode-container\">\n          <canvas \n            class=\"qrcode-canvas\"\n            canvas-id=\"qrcode\"\n            :style=\"{ width: qrcodeStyle.size + 'rpx', height: qrcodeStyle.size + 'rpx' }\"\n          ></canvas>\n        </view>\n        \n        <view class=\"qrcode-info\">\n          <text class=\"info-item\">类型: {{ getTypeName(selectedType) }}</text>\n          <text class=\"info-item\">尺寸: {{ qrcodeStyle.size }}×{{ qrcodeStyle.size }}px</text>\n          <text class=\"info-item\">容错级别: {{ qrcodeStyle.errorLevel }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 生成历史 -->\n    <view class=\"generation-history\" v-if=\"qrcodeHistory.length > 0\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">生成历史</text>\n        <u-button \n          type=\"text\" \n          text=\"清空\" \n          size=\"small\"\n          @click=\"clearHistory\"\n          :custom-style=\"{ color: '#EF4444', fontSize: '24rpx' }\"\n        ></u-button>\n      </view>\n      \n      <view class=\"history-list\">\n        <view \n          class=\"history-item\"\n          v-for=\"item in qrcodeHistory.slice(0, 5)\"\n          :key=\"item.id\"\n          @click=\"loadHistoryItem(item)\"\n        >\n          <image class=\"history-qrcode\" :src=\"item.qrcodeUrl\" mode=\"aspectFit\"></image>\n          <view class=\"history-content\">\n            <text class=\"history-type\">{{ getTypeName(item.type) }}</text>\n            <text class=\"history-desc\">{{ getHistoryDescription(item) }}</text>\n            <text class=\"history-time\">{{ formatTime(item.createdAt) }}</text>\n          </view>\n          <view class=\"history-actions\">\n            <u-icon name=\"download\" size=\"16\" color=\"#6B7280\"></u-icon>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 生成按钮 -->\n    <view class=\"generate-button\" v-if=\"hasContent() && !generatedQRCode\">\n      <u-button \n        type=\"primary\" \n        size=\"large\"\n        :loading=\"isGenerating\"\n        @click=\"generateQRCode\"\n        :custom-style=\"{ width: '100%' }\"\n      >\n        {{ isGenerating ? '生成中...' : '生成二维码' }}\n      </u-button>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\nimport dayjs from 'dayjs'\n\n// 响应式数据\nconst selectedType = ref<string>('text')\nconst isGenerating = ref<boolean>(false)\nconst generatedQRCode = ref<string>('')\nconst qrcodeHistory = ref<any[]>([])\n\n// 二维码内容\nconst qrcodeContent = ref({\n  text: '',\n  url: '',\n  wifi: {\n    ssid: '',\n    password: '',\n    security: 'WPA'\n  },\n  contact: {\n    name: '',\n    phone: '',\n    email: '',\n    organization: ''\n  },\n  sms: {\n    phone: '',\n    message: ''\n  },\n  email: {\n    to: '',\n    subject: '',\n    body: ''\n  }\n})\n\n// 二维码样式\nconst qrcodeStyle = ref({\n  size: 400,\n  foreground: '#000000',\n  background: '#FFFFFF',\n  errorLevel: 'M',\n  withLogo: false,\n  logoUrl: ''\n})\n\n// 二维码类型\nconst qrcodeTypes = ref([\n  {\n    id: 'text',\n    name: '文本',\n    icon: 'file-text'\n  },\n  {\n    id: 'url',\n    name: '网址',\n    icon: 'link'\n  },\n  {\n    id: 'wifi',\n    name: 'WiFi',\n    icon: 'wifi'\n  },\n  {\n    id: 'contact',\n    name: '联系人',\n    icon: 'account'\n  },\n  {\n    id: 'sms',\n    name: '短信',\n    icon: 'message'\n  },\n  {\n    id: 'email',\n    name: '邮件',\n    icon: 'email'\n  }\n])\n\n// 颜色选项\nconst colorOptions = ref([\n  '#000000', '#2563EB', '#DC2626', '#059669',\n  '#D97706', '#7C3AED', '#DB2777', '#0891B2'\n])\n\nconst backgroundColors = ref([\n  '#FFFFFF', '#F3F4F6', '#FEF3C7', '#DBEAFE',\n  '#DCFCE7', '#FDE2E7', '#F3E8FF', '#E0F2FE'\n])\n\n// 页面加载\nonMounted(() => {\n  loadQRCodeHistory()\n})\n\n// 选择类型\nconst selectType = (typeId: string) => {\n  selectedType.value = typeId\n  generatedQRCode.value = ''\n}\n\n// 获取输入标题\nconst getInputTitle = () => {\n  const typeMap: Record<string, string> = {\n    text: '输入文本内容',\n    url: '输入网址',\n    wifi: '输入WiFi信息',\n    contact: '输入联系人信息',\n    sms: '输入短信信息',\n    email: '输入邮件信息'\n  }\n  return typeMap[selectedType.value] || '输入内容'\n}\n\n// 获取输入描述\nconst getInputDescription = () => {\n  const descMap: Record<string, string> = {\n    text: '支持中英文、数字、符号等',\n    url: '请输入完整的网址，包含http://或https://',\n    wifi: '生成WiFi连接二维码，扫码即可连接',\n    contact: '生成联系人信息，扫码即可保存到通讯录',\n    sms: '生成短信二维码，扫码即可发送短信',\n    email: '生成邮件二维码，扫码即可发送邮件'\n  }\n  return descMap[selectedType.value] || ''\n}\n\n// 检查是否有内容\nconst hasContent = () => {\n  switch (selectedType.value) {\n    case 'text':\n      return qrcodeContent.value.text.trim() !== ''\n    case 'url':\n      return qrcodeContent.value.url.trim() !== ''\n    case 'wifi':\n      return qrcodeContent.value.wifi.ssid.trim() !== ''\n    case 'contact':\n      return qrcodeContent.value.contact.name.trim() !== '' ||\n             qrcodeContent.value.contact.phone.trim() !== ''\n    case 'sms':\n      return qrcodeContent.value.sms.phone.trim() !== ''\n    case 'email':\n      return qrcodeContent.value.email.to.trim() !== ''\n    default:\n      return false\n  }\n}\n\n// 选择颜色\nconst selectColor = (type: 'foreground' | 'background', color: string) => {\n  qrcodeStyle.value[type] = color\n  if (generatedQRCode.value) {\n    generateQRCode()\n  }\n}\n\n// 选择Logo\nconst chooseLogo = () => {\n  uni.chooseImage({\n    count: 1,\n    sourceType: ['album'],\n    success: (res) => {\n      qrcodeStyle.value.logoUrl = res.tempFilePaths[0]\n      if (generatedQRCode.value) {\n        generateQRCode()\n      }\n    }\n  })\n}\n\n// 生成二维码内容字符串\nconst generateContentString = () => {\n  switch (selectedType.value) {\n    case 'text':\n      return qrcodeContent.value.text\n    case 'url':\n      return qrcodeContent.value.url\n    case 'wifi':\n      const wifi = qrcodeContent.value.wifi\n      return `WIFI:T:${wifi.security};S:${wifi.ssid};P:${wifi.password};;`\n    case 'contact':\n      const contact = qrcodeContent.value.contact\n      return `BEGIN:VCARD\\nVERSION:3.0\\nFN:${contact.name}\\nTEL:${contact.phone}\\nEMAIL:${contact.email}\\nORG:${contact.organization}\\nEND:VCARD`\n    case 'sms':\n      const sms = qrcodeContent.value.sms\n      return `SMSTO:${sms.phone}:${sms.message}`\n    case 'email':\n      const email = qrcodeContent.value.email\n      return `mailto:${email.to}?subject=${encodeURIComponent(email.subject)}&body=${encodeURIComponent(email.body)}`\n    default:\n      return ''\n  }\n}\n\n// 生成二维码\nconst generateQRCode = async () => {\n  if (!hasContent()) return\n\n  try {\n    isGenerating.value = true\n\n    const contentString = generateContentString()\n\n    // 模拟二维码生成\n    await new Promise(resolve => setTimeout(resolve, 1000))\n\n    // 这里应该使用真实的二维码生成库，比如 qrcode.js\n    // 现在使用模拟的二维码图片\n    const mockQRCodeUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='\n\n    generatedQRCode.value = mockQRCodeUrl\n\n    // 添加到历史记录\n    const historyItem = {\n      id: Date.now().toString(),\n      type: selectedType.value,\n      content: contentString,\n      qrcodeUrl: mockQRCodeUrl,\n      style: { ...qrcodeStyle.value },\n      createdAt: new Date().toISOString()\n    }\n\n    qrcodeHistory.value.unshift(historyItem)\n    saveQRCodeHistory()\n\n    uni.showToast({\n      title: '生成成功',\n      icon: 'success'\n    })\n\n  } catch (error) {\n    uni.__f__('error','at pages/utilities/qrcode/generator/index.vue:531','生成二维码失败:', error)\n    uni.showToast({\n      title: '生成失败',\n      icon: 'error'\n    })\n  } finally {\n    isGenerating.value = false\n  }\n}\n\n// 下载二维码\nconst downloadQRCode = () => {\n  if (!generatedQRCode.value) return\n\n  uni.showToast({\n    title: '开始下载',\n    icon: 'success'\n  })\n}\n\n// 分享二维码\nconst shareQRCode = () => {\n  if (!generatedQRCode.value) return\n\n  uni.share({\n    provider: 'weixin',\n    scene: 'WXSceneSession',\n    type: 1,\n    imageUrl: generatedQRCode.value,\n    success: () => {\n      uni.showToast({\n        title: '分享成功',\n        icon: 'success'\n      })\n    }\n  })\n}\n\n// 获取类型名称\nconst getTypeName = (typeId: string) => {\n  const type = qrcodeTypes.value.find(t => t.id === typeId)\n  return type?.name || '未知类型'\n}\n\n// 获取历史描述\nconst getHistoryDescription = (item: any) => {\n  switch (item.type) {\n    case 'text':\n      return item.content.substring(0, 20) + (item.content.length > 20 ? '...' : '')\n    case 'url':\n      return item.content\n    case 'wifi':\n      return `WiFi: ${item.content.match(/S:([^;]+)/)?.[1] || ''}`\n    case 'contact':\n      return `联系人: ${item.content.match(/FN:([^\\n]+)/)?.[1] || ''}`\n    case 'sms':\n      return `短信: ${item.content.match(/SMSTO:([^:]+)/)?.[1] || ''}`\n    case 'email':\n      return `邮件: ${item.content.match(/mailto:([^?]+)/)?.[1] || ''}`\n    default:\n      return item.content.substring(0, 20)\n  }\n}\n\n// 加载历史项目\nconst loadHistoryItem = (item: any) => {\n  selectedType.value = item.type\n\n  // 根据类型恢复内容\n  switch (item.type) {\n    case 'text':\n      qrcodeContent.value.text = item.content\n      break\n    case 'url':\n      qrcodeContent.value.url = item.content\n      break\n    // 其他类型的恢复逻辑...\n  }\n\n  // 恢复样式\n  qrcodeStyle.value = { ...item.style }\n  generatedQRCode.value = item.qrcodeUrl\n}\n\n// 工具函数\nconst formatTime = (time: string) => {\n  return dayjs(time).format('MM-DD HH:mm')\n}\n\n// 历史记录相关\nconst saveQRCodeHistory = () => {\n  uni.setStorageSync('qrcode_generation_history', qrcodeHistory.value)\n}\n\nconst loadQRCodeHistory = () => {\n  const history = uni.getStorageSync('qrcode_generation_history') || []\n  qrcodeHistory.value = history\n}\n\nconst clearHistory = () => {\n  uni.showModal({\n    title: '确认清空',\n    content: '确定要清空所有生成历史吗？',\n    success: (res) => {\n      if (res.confirm) {\n        qrcodeHistory.value = []\n        saveQRCodeHistory()\n        uni.showToast({\n          title: '清空成功',\n          icon: 'success'\n        })\n      }\n    }\n  })\n}\n\n// 页面下拉刷新\nconst onPullDownRefresh = () => {\n  loadQRCodeHistory()\n  uni.stopPullDownRefresh()\n}\n\n// 导出方法供页面使用\ndefineExpose({\n  onPullDownRefresh\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.qrcode-generator-page {\n  min-height: 100vh;\n  background-color: $bg-secondary;\n}\n\n// 页面头部\n.page-header {\n  background-color: $bg-primary;\n  padding: $spacing-6 $spacing-4;\n  text-align: center;\n  border-bottom: 1px solid $border-primary;\n\n  .page-title {\n    display: block;\n    font-size: $font-size-2xl;\n    font-weight: $font-weight-bold;\n    color: $text-primary;\n    margin-bottom: $spacing-2;\n  }\n\n  .page-desc {\n    font-size: $font-size-base;\n    color: $text-secondary;\n  }\n}\n\n// 通用区块标题\n.section-title {\n  padding: $spacing-4 $spacing-4 $spacing-3;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  .title-text {\n    font-size: $font-size-lg;\n    font-weight: $font-weight-semibold;\n    color: $text-primary;\n  }\n\n  .title-desc {\n    font-size: $font-size-sm;\n    color: $text-tertiary;\n  }\n\n  .preview-actions {\n    display: flex;\n    align-items: center;\n    gap: $spacing-2;\n  }\n}\n\n// 类型选择\n.type-selector {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .type-tabs {\n    display: flex;\n    padding: 0 $spacing-4 $spacing-4;\n    gap: $spacing-2;\n    overflow-x: auto;\n\n    .tab-item {\n      flex: 0 0 auto;\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      padding: $spacing-3;\n      background-color: $bg-tertiary;\n      border-radius: $radius-lg;\n      border: 2px solid transparent;\n      min-width: 120rpx;\n\n      &.active {\n        border-color: $primary;\n        background-color: $primary-50;\n      }\n\n      .tab-text {\n        font-size: $font-size-sm;\n        color: $text-primary;\n        margin-top: $spacing-1;\n      }\n    }\n  }\n}\n\n// 内容输入\n.content-input {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .input-area {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .text-input,\n    .url-input {\n      width: 100%;\n      min-height: 200rpx;\n      padding: $spacing-3;\n      border: 1px solid $border-primary;\n      border-radius: $radius-md;\n      font-size: $font-size-base;\n      color: $text-primary;\n      background-color: $bg-tertiary;\n\n      &::placeholder {\n        color: $text-quaternary;\n      }\n    }\n\n    .url-input {\n      min-height: 80rpx;\n    }\n\n    .wifi-inputs,\n    .contact-inputs,\n    .sms-inputs,\n    .email-inputs {\n      display: flex;\n      flex-direction: column;\n      gap: $spacing-3;\n\n      .wifi-input,\n      .contact-input,\n      .sms-input,\n      .email-input {\n        width: 100%;\n        padding: $spacing-3;\n        border: 1px solid $border-primary;\n        border-radius: $radius-md;\n        font-size: $font-size-base;\n        color: $text-primary;\n        background-color: $bg-tertiary;\n\n        &::placeholder {\n          color: $text-quaternary;\n        }\n      }\n\n      .wifi-security {\n        .security-label {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          margin-bottom: $spacing-2;\n        }\n      }\n    }\n  }\n}\n\n// 样式设置\n.style-settings {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .settings-content {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .setting-item {\n      margin-bottom: $spacing-6;\n\n      .setting-label {\n        display: block;\n        font-size: $font-size-base;\n        font-weight: $font-weight-medium;\n        color: $text-primary;\n        margin-bottom: $spacing-3;\n      }\n\n      .color-picker {\n        display: flex;\n        gap: $spacing-2;\n        flex-wrap: wrap;\n\n        .color-option {\n          width: 60rpx;\n          height: 60rpx;\n          border-radius: $radius-md;\n          border: 2px solid transparent;\n\n          &.active {\n            border-color: $primary;\n          }\n        }\n      }\n\n      .logo-upload {\n        margin-top: $spacing-3;\n\n        .upload-btn {\n          display: flex;\n          align-items: center;\n          gap: $spacing-2;\n          padding: $spacing-3;\n          background-color: $bg-tertiary;\n          border: 1px dashed $border-primary;\n          border-radius: $radius-md;\n\n          .upload-text {\n            font-size: $font-size-sm;\n            color: $text-secondary;\n          }\n        }\n\n        .logo-preview {\n          width: 120rpx;\n          height: 120rpx;\n          margin-top: $spacing-2;\n          border-radius: $radius-md;\n          background-color: $bg-tertiary;\n        }\n      }\n    }\n  }\n}\n\n// 二维码预览\n.qrcode-preview {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .preview-content {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .qrcode-container {\n      display: flex;\n      justify-content: center;\n      margin-bottom: $spacing-4;\n\n      .qrcode-canvas {\n        border: 1px solid $border-primary;\n        border-radius: $radius-md;\n        background-color: $bg-tertiary;\n      }\n    }\n\n    .qrcode-info {\n      background-color: $bg-tertiary;\n      border-radius: $radius-lg;\n      padding: $spacing-4;\n\n      .info-item {\n        display: block;\n        font-size: $font-size-sm;\n        color: $text-secondary;\n        margin-bottom: $spacing-1;\n\n        &:last-child {\n          margin-bottom: 0;\n        }\n      }\n    }\n  }\n}\n\n// 生成历史\n.generation-history {\n  background-color: $bg-primary;\n\n  .history-list {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .history-item {\n      display: flex;\n      align-items: center;\n      gap: $spacing-3;\n      padding: $spacing-3;\n      background-color: $bg-tertiary;\n      border-radius: $radius-lg;\n      margin-bottom: $spacing-2;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      &:active {\n        background-color: $gray-200;\n      }\n\n      .history-qrcode {\n        width: 80rpx;\n        height: 80rpx;\n        border-radius: $radius-md;\n        background-color: $bg-quaternary;\n      }\n\n      .history-content {\n        flex: 1;\n\n        .history-type {\n          display: block;\n          font-size: $font-size-base;\n          font-weight: $font-weight-medium;\n          color: $text-primary;\n          margin-bottom: $spacing-1;\n        }\n\n        .history-desc {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          margin-bottom: $spacing-1;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n\n        .history-time {\n          font-size: $font-size-xs;\n          color: $text-tertiary;\n        }\n      }\n\n      .history-actions {\n        padding: $spacing-2;\n      }\n    }\n  }\n}\n\n// 生成按钮\n.generate-button {\n  padding: $spacing-4;\n}\n\n// 响应式适配\n@media screen and (max-width: 480px) {\n  .type-tabs {\n    .tab-item {\n      min-width: 100rpx;\n      padding: $spacing-2;\n    }\n  }\n\n  .color-picker {\n    .color-option {\n      width: 48rpx;\n      height: 48rpx;\n    }\n  }\n\n  .qrcode-container {\n    .qrcode-canvas {\n      max-width: 100%;\n      height: auto;\n    }\n  }\n}\n\n// 暗色模式适配\n@media (prefers-color-scheme: dark) {\n  .qrcode-generator-page {\n    background-color: #1a1a1a;\n  }\n\n  .page-header,\n  .type-selector,\n  .content-input,\n  .style-settings,\n  .qrcode-preview,\n  .generation-history {\n    background-color: #2d2d2d;\n    border-color: #404040;\n  }\n\n  .tab-item,\n  .text-input,\n  .url-input,\n  .wifi-input,\n  .contact-input,\n  .sms-input,\n  .email-input,\n  .upload-btn,\n  .qrcode-info,\n  .history-item {\n    background-color: #404040;\n    border-color: #555555;\n  }\n\n  .tab-item.active {\n    background-color: #1e3a8a;\n    border-color: #3b82f6;\n  }\n\n  .text-input,\n  .url-input,\n  .wifi-input,\n  .contact-input,\n  .sms-input,\n  .email-input {\n    color: #ffffff;\n\n    &::placeholder {\n      color: #888888;\n    }\n  }\n\n  .qrcode-canvas,\n  .history-qrcode {\n    background-color: #555555;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/AI_system/mobile-uniapp/src/pages/utilities/qrcode/generator/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "dayjs"], "mappings": ";;;;;;;;;;;;;;AA+SM,UAAA,eAAeA,kBAAY,MAAM;AACjC,UAAA,eAAeA,kBAAa,KAAK;AACjC,UAAA,kBAAkBA,kBAAY,EAAE;AAChC,UAAA,gBAAgBA,kBAAW,CAAA,CAAE;AAGnC,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB,MAAM;AAAA,MACN,KAAK;AAAA,MACL,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,cAAc;AAAA,MAChB;AAAA,MACA,KAAK;AAAA,QACH,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,MACA,OAAO;AAAA,QACL,IAAI;AAAA,QACJ,SAAS;AAAA,QACT,MAAM;AAAA,MACR;AAAA,IAAA,CACD;AAGD,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,SAAS;AAAA,IAAA,CACV;AAGD,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IAAA,CACD;AAGD,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACvB;AAAA,MAAW;AAAA,MAAW;AAAA,MAAW;AAAA,MACjC;AAAA,MAAW;AAAA,MAAW;AAAA,MAAW;AAAA,IAAA,CAClC;AAED,UAAM,mBAAmBA,cAAAA,IAAI;AAAA,MAC3B;AAAA,MAAW;AAAA,MAAW;AAAA,MAAW;AAAA,MACjC;AAAA,MAAW;AAAA,MAAW;AAAA,MAAW;AAAA,IAAA,CAClC;AAGDC,kBAAAA,UAAU,MAAM;AACI;IAAA,CACnB;AAGK,UAAA,aAAa,CAAC,WAAmB;AACrC,mBAAa,QAAQ;AACrB,sBAAgB,QAAQ;AAAA,IAAA;AAI1B,UAAM,gBAAgB,MAAM;AAC1B,YAAM,UAAkC;AAAA,QACtC,MAAM;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,QACT,KAAK;AAAA,QACL,OAAO;AAAA,MAAA;AAEF,aAAA,QAAQ,aAAa,KAAK,KAAK;AAAA,IAAA;AAIxC,UAAM,sBAAsB,MAAM;AAChC,YAAM,UAAkC;AAAA,QACtC,MAAM;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,QACT,KAAK;AAAA,QACL,OAAO;AAAA,MAAA;AAEF,aAAA,QAAQ,aAAa,KAAK,KAAK;AAAA,IAAA;AAIxC,UAAM,aAAa,MAAM;AACvB,cAAQ,aAAa,OAAO;AAAA,QAC1B,KAAK;AACH,iBAAO,cAAc,MAAM,KAAK,KAAA,MAAW;AAAA,QAC7C,KAAK;AACH,iBAAO,cAAc,MAAM,IAAI,KAAA,MAAW;AAAA,QAC5C,KAAK;AACH,iBAAO,cAAc,MAAM,KAAK,KAAK,KAAW,MAAA;AAAA,QAClD,KAAK;AACH,iBAAO,cAAc,MAAM,QAAQ,KAAK,KAAK,MAAM,MAC5C,cAAc,MAAM,QAAQ,MAAM,KAAA,MAAW;AAAA,QACtD,KAAK;AACH,iBAAO,cAAc,MAAM,IAAI,MAAM,KAAW,MAAA;AAAA,QAClD,KAAK;AACH,iBAAO,cAAc,MAAM,MAAM,GAAG,KAAW,MAAA;AAAA,QACjD;AACS,iBAAA;AAAA,MACX;AAAA,IAAA;AAII,UAAA,cAAc,CAAC,MAAmC,UAAkB;AAC5D,kBAAA,MAAM,IAAI,IAAI;AAC1B,UAAI,gBAAgB,OAAO;AACV;MACjB;AAAA,IAAA;AAIF,UAAM,aAAa,MAAM;AACvBC,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,YAAY,CAAC,OAAO;AAAA,QACpB,SAAS,CAAC,QAAQ;AAChB,sBAAY,MAAM,UAAU,IAAI,cAAc,CAAC;AAC/C,cAAI,gBAAgB,OAAO;AACV;UACjB;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,wBAAwB,MAAM;AAClC,cAAQ,aAAa,OAAO;AAAA,QAC1B,KAAK;AACH,iBAAO,cAAc,MAAM;AAAA,QAC7B,KAAK;AACH,iBAAO,cAAc,MAAM;AAAA,QAC7B,KAAK;AACG,gBAAA,OAAO,cAAc,MAAM;AAC1B,iBAAA,UAAU,KAAK,QAAQ,MAAM,KAAK,IAAI,MAAM,KAAK,QAAQ;AAAA,QAClE,KAAK;AACG,gBAAA,UAAU,cAAc,MAAM;AAC7B,iBAAA;AAAA;AAAA,KAAgC,QAAQ,IAAI;AAAA,MAAS,QAAQ,KAAK;AAAA,QAAW,QAAQ,KAAK;AAAA,MAAS,QAAQ,YAAY;AAAA;AAAA,QAChI,KAAK;AACG,gBAAA,MAAM,cAAc,MAAM;AAChC,iBAAO,SAAS,IAAI,KAAK,IAAI,IAAI,OAAO;AAAA,QAC1C,KAAK;AACG,gBAAA,QAAQ,cAAc,MAAM;AAClC,iBAAO,UAAU,MAAM,EAAE,YAAY,mBAAmB,MAAM,OAAO,CAAC,SAAS,mBAAmB,MAAM,IAAI,CAAC;AAAA,QAC/G;AACS,iBAAA;AAAA,MACX;AAAA,IAAA;AAIF,UAAM,iBAAiB,YAAY;AACjC,UAAI,CAAC,WAAW;AAAG;AAEf,UAAA;AACF,qBAAa,QAAQ;AAErB,cAAM,gBAAgB;AAGtB,cAAM,IAAI,QAAQ,CAAA,YAAW,WAAW,SAAS,GAAI,CAAC;AAItD,cAAM,gBAAgB;AAEtB,wBAAgB,QAAQ;AAGxB,cAAM,cAAc;AAAA,UAClB,IAAI,KAAK,IAAI,EAAE,SAAS;AAAA,UACxB,MAAM,aAAa;AAAA,UACnB,SAAS;AAAA,UACT,WAAW;AAAA,UACX,OAAO,EAAE,GAAG,YAAY,MAAM;AAAA,UAC9B,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAAA;AAGtB,sBAAA,MAAM,QAAQ,WAAW;AACrB;AAElBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,eAEM,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,qDAAoD,YAAY,KAAK;AACvFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,qBAAa,QAAQ;AAAA,MACvB;AAAA,IAAA;AAIF,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,gBAAgB;AAAO;AAE5BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIH,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,gBAAgB;AAAO;AAE5BA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU,gBAAgB;AAAA,QAC1B,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;AAIG,UAAA,cAAc,CAAC,WAAmB;AACtC,YAAM,OAAO,YAAY,MAAM,KAAK,CAAK,MAAA,EAAE,OAAO,MAAM;AACxD,cAAO,6BAAM,SAAQ;AAAA,IAAA;AAIjB,UAAA,wBAAwB,CAAC,SAAc;;AAC3C,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AACI,iBAAA,KAAK,QAAQ,UAAU,GAAG,EAAE,KAAK,KAAK,QAAQ,SAAS,KAAK,QAAQ;AAAA,QAC7E,KAAK;AACH,iBAAO,KAAK;AAAA,QACd,KAAK;AACI,iBAAA,WAAS,UAAK,QAAQ,MAAM,WAAW,MAA9B,mBAAkC,OAAM,EAAE;AAAA,QAC5D,KAAK;AACI,iBAAA,UAAQ,UAAK,QAAQ,MAAM,aAAa,MAAhC,mBAAoC,OAAM,EAAE;AAAA,QAC7D,KAAK;AACI,iBAAA,SAAO,UAAK,QAAQ,MAAM,eAAe,MAAlC,mBAAsC,OAAM,EAAE;AAAA,QAC9D,KAAK;AACI,iBAAA,SAAO,UAAK,QAAQ,MAAM,gBAAgB,MAAnC,mBAAuC,OAAM,EAAE;AAAA,QAC/D;AACE,iBAAO,KAAK,QAAQ,UAAU,GAAG,EAAE;AAAA,MACvC;AAAA,IAAA;AAII,UAAA,kBAAkB,CAAC,SAAc;AACrC,mBAAa,QAAQ,KAAK;AAG1B,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AACW,wBAAA,MAAM,OAAO,KAAK;AAChC;AAAA,QACF,KAAK;AACW,wBAAA,MAAM,MAAM,KAAK;AAC/B;AAAA,MAEJ;AAGA,kBAAY,QAAQ,EAAE,GAAG,KAAK,MAAM;AACpC,sBAAgB,QAAQ,KAAK;AAAA,IAAA;AAIzB,UAAA,aAAa,CAAC,SAAiB;AACnC,aAAOC,cAAM,MAAA,IAAI,EAAE,OAAO,aAAa;AAAA,IAAA;AAIzC,UAAM,oBAAoB,MAAM;AAC1BD,oBAAAA,MAAA,eAAe,6BAA6B,cAAc,KAAK;AAAA,IAAA;AAGrE,UAAM,oBAAoB,MAAM;AAC9B,YAAM,UAAUA,cAAA,MAAI,eAAe,2BAA2B,KAAK,CAAA;AACnE,oBAAc,QAAQ;AAAA,IAAA;AAGxB,UAAM,eAAe,MAAM;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,0BAAc,QAAQ;AACJ;AAClBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAAA,CACP;AAAA,UACH;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,oBAAoB,MAAM;AACZ;AAClBA,oBAAA,MAAI,oBAAoB;AAAA,IAAA;AAIb,aAAA;AAAA,MACX;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9oBD,GAAG,WAAW,eAAe;"}