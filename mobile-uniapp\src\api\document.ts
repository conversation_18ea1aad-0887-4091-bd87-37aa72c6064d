/**
 * 文档处理相关API接口
 */
import request from '@/utils/request'

// 文档信息
export interface DocumentInfo {
  id: string
  name: string
  originalName: string
  type: string
  size: number
  pages?: number
  url: string
  thumbnailUrl?: string
  status: 'uploading' | 'processing' | 'completed' | 'failed'
  progress?: number
  error?: string
  metadata?: {
    author?: string
    title?: string
    subject?: string
    creator?: string
    producer?: string
    creationDate?: string
    modificationDate?: string
    keywords?: string[]
  }
  createdAt: string
  updatedAt: string
}

// 转换任务
export interface ConversionTask {
  id: string
  sourceDocumentId: string
  targetFormat: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  resultUrl?: string
  error?: string
  options: {
    quality?: 'fast' | 'standard' | 'high'
    preserveFormat?: boolean
    pageRange?: string
    watermark?: {
      text: string
      position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center'
      opacity: number
    }
  }
  createdAt: string
  updatedAt: string
  completedAt?: string
}

// 支持的格式
export interface SupportedFormat {
  extension: string
  mimeType: string
  name: string
  category: 'document' | 'spreadsheet' | 'presentation' | 'image' | 'text'
  canConvertTo: string[]
  canConvertFrom: string[]
  maxSize: number
  features: string[]
}

/**
 * 上传文档
 */
export const uploadDocument = (file: File, options?: {
  category?: string
  tags?: string[]
  description?: string
}): Promise<DocumentInfo> => {
  const formData = new FormData()
  formData.append('file', file)
  if (options) {
    formData.append('options', JSON.stringify(options))
  }
  
  return request.post('/document/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取文档列表
 */
export const getDocuments = (params: {
  page?: number
  limit?: number
  type?: string
  category?: string
  keyword?: string
  tags?: string[]
  status?: string
  sortBy?: 'name' | 'size' | 'created' | 'updated'
  sortOrder?: 'asc' | 'desc'
}): Promise<{
  list: DocumentInfo[]
  total: number
  page: number
  limit: number
  categories: string[]
  types: string[]
  tags: string[]
}> => {
  return request.get('/document/list', { params })
}

/**
 * 获取文档详情
 */
export const getDocument = (documentId: string): Promise<DocumentInfo> => {
  return request.get(`/document/${documentId}`)
}

/**
 * 更新文档信息
 */
export const updateDocument = (documentId: string, params: {
  name?: string
  category?: string
  tags?: string[]
  description?: string
}): Promise<DocumentInfo> => {
  return request.put(`/document/${documentId}`, params)
}

/**
 * 删除文档
 */
export const deleteDocument = (documentId: string): Promise<{ message: string }> => {
  return request.delete(`/document/${documentId}`)
}

/**
 * 批量删除文档
 */
export const batchDeleteDocuments = (documentIds: string[]): Promise<{ message: string }> => {
  return request.delete('/document/batch', { data: { ids: documentIds } })
}

/**
 * 转换文档格式
 */
export const convertDocument = (params: {
  documentId: string
  targetFormat: string
  options?: {
    quality?: 'fast' | 'standard' | 'high'
    preserveFormat?: boolean
    pageRange?: string
    watermark?: {
      text: string
      position: string
      opacity: number
    }
  }
}): Promise<ConversionTask> => {
  return request.post('/document/convert', params)
}

/**
 * 获取转换任务状态
 */
export const getConversionTask = (taskId: string): Promise<ConversionTask> => {
  return request.get(`/document/conversion/${taskId}`)
}

/**
 * 获取转换任务列表
 */
export const getConversionTasks = (params: {
  page?: number
  limit?: number
  status?: string
  documentId?: string
}): Promise<{
  list: ConversionTask[]
  total: number
  page: number
  limit: number
}> => {
  return request.get('/document/conversions', { params })
}

/**
 * 取消转换任务
 */
export const cancelConversionTask = (taskId: string): Promise<{ message: string }> => {
  return request.post(`/document/conversion/${taskId}/cancel`)
}

/**
 * 重试转换任务
 */
export const retryConversionTask = (taskId: string): Promise<ConversionTask> => {
  return request.post(`/document/conversion/${taskId}/retry`)
}

/**
 * 获取支持的格式列表
 */
export const getSupportedFormats = (): Promise<SupportedFormat[]> => {
  return request.get('/document/formats')
}

/**
 * 检查格式转换支持
 */
export const checkConversionSupport = (params: {
  fromFormat: string
  toFormat: string
}): Promise<{
  supported: boolean
  features: string[]
  limitations?: string[]
  estimatedTime?: number
}> => {
  return request.get('/document/conversion/check', { params })
}

/**
 * 预览文档
 */
export const previewDocument = (documentId: string, params?: {
  page?: number
  width?: number
  height?: number
  quality?: 'low' | 'medium' | 'high'
}): Promise<{
  previewUrl: string
  totalPages: number
  currentPage: number
  width: number
  height: number
}> => {
  return request.get(`/document/${documentId}/preview`, { params })
}

/**
 * 获取文档缩略图
 */
export const getDocumentThumbnail = (documentId: string, params?: {
  width?: number
  height?: number
  page?: number
}): Promise<{
  thumbnailUrl: string
  width: number
  height: number
}> => {
  return request.get(`/document/${documentId}/thumbnail`, { params })
}

/**
 * 提取文档文本
 */
export const extractDocumentText = (documentId: string, params?: {
  pageRange?: string
  format?: 'plain' | 'markdown' | 'html'
}): Promise<{
  text: string
  pages: Array<{
    page: number
    text: string
  }>
  wordCount: number
  characterCount: number
}> => {
  return request.get(`/document/${documentId}/text`, { params })
}

/**
 * 搜索文档内容
 */
export const searchDocumentContent = (documentId: string, params: {
  query: string
  caseSensitive?: boolean
  wholeWord?: boolean
  regex?: boolean
}): Promise<{
  results: Array<{
    page: number
    line: number
    text: string
    highlight: string
    context: string
  }>
  totalMatches: number
  totalPages: number
}> => {
  return request.get(`/document/${documentId}/search`, { params })
}

/**
 * 合并文档
 */
export const mergeDocuments = (params: {
  documentIds: string[]
  outputName: string
  outputFormat?: string
  options?: {
    addPageNumbers?: boolean
    addBookmarks?: boolean
    preserveFormatting?: boolean
  }
}): Promise<ConversionTask> => {
  return request.post('/document/merge', params)
}

/**
 * 拆分文档
 */
export const splitDocument = (params: {
  documentId: string
  splitType: 'pages' | 'bookmarks' | 'size'
  splitOptions: {
    pageRanges?: string[]
    maxPages?: number
    maxSize?: number
  }
  outputFormat?: string
}): Promise<ConversionTask> => {
  return request.post('/document/split', params)
}

/**
 * 压缩文档
 */
export const compressDocument = (params: {
  documentId: string
  quality: 'low' | 'medium' | 'high'
  options?: {
    optimizeImages?: boolean
    removeMetadata?: boolean
    compressText?: boolean
  }
}): Promise<ConversionTask> => {
  return request.post('/document/compress', params)
}

/**
 * 添加水印
 */
export const addWatermark = (params: {
  documentId: string
  watermark: {
    type: 'text' | 'image'
    content: string
    position: string
    opacity: number
    size?: number
    color?: string
    rotation?: number
  }
  pages?: string
}): Promise<ConversionTask> => {
  return request.post('/document/watermark', params)
}

/**
 * 获取文档统计
 */
export const getDocumentStats = (params?: {
  period?: 'day' | 'week' | 'month' | 'year'
  startDate?: string
  endDate?: string
}): Promise<{
  totalDocuments: number
  totalSize: number
  totalConversions: number
  byType: Array<{
    type: string
    count: number
    size: number
  }>
  byCategory: Array<{
    category: string
    count: number
    size: number
  }>
  recentActivity: Array<{
    date: string
    uploads: number
    conversions: number
    size: number
  }>
}> => {
  return request.get('/document/stats', { params })
}

// 默认导出
export default {
  uploadDocument,
  getDocuments,
  getDocument,
  updateDocument,
  deleteDocument,
  batchDeleteDocuments,
  convertDocument,
  getConversionTask,
  getConversionTasks,
  cancelConversionTask,
  retryConversionTask,
  getSupportedFormats,
  checkConversionSupport,
  previewDocument,
  getDocumentThumbnail,
  extractDocumentText,
  searchDocumentContent,
  mergeDocuments,
  splitDocument,
  compressDocument,
  addWatermark,
  getDocumentStats
}
