/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 导入自定义样式变量 */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
.chat-page.data-v-90568451 {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #F9FAFB;
}
.chat-header.data-v-90568451 {
  background-color: #FFFFFF;
  padding: 32rpx;
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.chat-header .digital-human-info.data-v-90568451 {
  display: flex;
  align-items: center;
}
.chat-header .digital-human-info .avatar.data-v-90568451 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 9999rpx;
  margin-right: 24rpx;
}
.chat-header .digital-human-info .info-content .name.data-v-90568451 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8rpx;
}
.chat-header .digital-human-info .info-content .status.data-v-90568451 {
  font-size: 20rpx;
  color: #10B981;
}
.chat-header .header-actions.data-v-90568451 {
  padding: 16rpx;
}
.message-list.data-v-90568451 {
  flex: 1;
  padding: 32rpx;
}
.message-list .load-more.data-v-90568451 {
  text-align: center;
  padding: 32rpx;
}
.message-list .load-more .load-text.data-v-90568451 {
  font-size: 24rpx;
  color: #6B7280;
  margin-left: 16rpx;
}
.message-item.data-v-90568451 {
  margin-bottom: 48rpx;
}
.message-item.user-message .message-content.data-v-90568451 {
  flex-direction: row-reverse;
}
.message-item.user-message .message-content .message-bubble.data-v-90568451 {
  margin-left: 96rpx;
  margin-right: 0;
}
.message-item.user-message .message-content .message-time.data-v-90568451 {
  text-align: right;
  margin-right: 16rpx;
}
.message-item.assistant-message .message-content.data-v-90568451 {
  flex-direction: row;
}
.message-item.assistant-message .message-content .message-bubble.data-v-90568451 {
  margin-right: 96rpx;
  margin-left: 16rpx;
}
.message-item.assistant-message .message-content .message-time.data-v-90568451 {
  text-align: left;
  margin-left: 88rpx;
}
.message-item .message-content.data-v-90568451 {
  display: flex;
  align-items: flex-start;
}
.message-item .message-content .message-avatar.data-v-90568451 {
  width: 64rpx;
  height: 64rpx;
  border-radius: 9999rpx;
  flex-shrink: 0;
}
.message-item .message-content .message-bubble.data-v-90568451 {
  max-width: 70%;
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  position: relative;
}
.message-item .message-content .message-bubble.user-bubble.data-v-90568451 {
  background-color: #667eea;
  color: white;
}
.message-item .message-content .message-bubble.user-bubble.data-v-90568451::after {
  content: "";
  position: absolute;
  right: -12rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border: 12rpx solid transparent;
  border-left-color: #667eea;
}
.message-item .message-content .message-bubble.assistant-bubble.data-v-90568451 {
  background-color: #FFFFFF;
  border: 1px solid #E5E7EB;
  color: #111827;
}
.message-item .message-content .message-bubble.assistant-bubble.data-v-90568451::after {
  content: "";
  position: absolute;
  left: -12rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border: 12rpx solid transparent;
  border-right-color: #FFFFFF;
}
.message-item .message-content .message-bubble .message-text.data-v-90568451 {
  font-size: 28rpx;
  line-height: 1.625;
  word-break: break-word;
}
.message-item .message-content .message-bubble .audio-message.data-v-90568451 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.message-item .message-content .message-bubble .audio-message .audio-duration.data-v-90568451 {
  font-size: 24rpx;
  min-width: 60rpx;
}
.message-item .message-content .message-bubble .video-message .message-video.data-v-90568451 {
  width: 100%;
  max-width: 400rpx;
  height: 300rpx;
  border-radius: 12rpx;
}
.message-item .message-content .message-bubble .message-actions.data-v-90568451 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1px solid #E5E7EB;
}
.message-item .message-content .message-time.data-v-90568451 {
  font-size: 20rpx;
  color: #9CA3AF;
  margin-top: 8rpx;
}
.generating-video.data-v-90568451 {
  text-align: center;
  padding: 48rpx;
}
.generating-video .generating-content.data-v-90568451 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}
.generating-video .generating-content .generating-text.data-v-90568451 {
  font-size: 24rpx;
  color: #374151;
}
.typing-indicator.data-v-90568451 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
}
.typing-indicator .typing-avatar.data-v-90568451 {
  width: 64rpx;
  height: 64rpx;
  border-radius: 9999rpx;
  margin-right: 16rpx;
}
.typing-indicator .typing-bubble.data-v-90568451 {
  background-color: #FFFFFF;
  border: 1px solid #E5E7EB;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  position: relative;
}
.typing-indicator .typing-bubble.data-v-90568451::after {
  content: "";
  position: absolute;
  left: -12rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border: 12rpx solid transparent;
  border-right-color: #FFFFFF;
}
.typing-indicator .typing-bubble .typing-dots.data-v-90568451 {
  display: flex;
  gap: 8rpx;
}
.typing-indicator .typing-bubble .typing-dots .dot.data-v-90568451 {
  width: 12rpx;
  height: 12rpx;
  background-color: #6B7280;
  border-radius: 9999rpx;
  animation: typing-90568451 1.4s infinite ease-in-out;
}
.typing-indicator .typing-bubble .typing-dots .dot.data-v-90568451:nth-child(1) {
  animation-delay: -0.32s;
}
.typing-indicator .typing-bubble .typing-dots .dot.data-v-90568451:nth-child(2) {
  animation-delay: -0.16s;
}
.typing-indicator .typing-bubble .typing-dots .dot.data-v-90568451:nth-child(3) {
  animation-delay: 0s;
}
@keyframes typing-90568451 {
0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
}
40% {
    transform: scale(1);
    opacity: 1;
}
}
.input-area.data-v-90568451 {
  background-color: #FFFFFF;
  border-top: 1px solid #E5E7EB;
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
}
.input-area .input-container.data-v-90568451 {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.input-area .input-container .text-input.data-v-90568451 {
  flex: 1;
  background-color: #F3F4F6;
  border: 1px solid #E5E7EB;
  border-radius: 9999rpx;
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  color: #111827;
}
.input-area .input-container .text-input.data-v-90568451::-webkit-input-placeholder {
  color: #9CA3AF;
}
.input-area .input-container .text-input.data-v-90568451::placeholder {
  color: #9CA3AF;
}
.input-area .input-container .text-input.data-v-90568451:focus {
  border-color: #667eea;
}
.input-area .input-container .input-actions.data-v-90568451 {
  display: flex;
  align-items: center;
}
.digital-human-list.data-v-90568451 {
  max-height: 600rpx;
}
.digital-human-list .digital-human-item.data-v-90568451 {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1px solid #E5E7EB;
}
.digital-human-list .digital-human-item.data-v-90568451:last-child {
  border-bottom: none;
}
.digital-human-list .digital-human-item.active.data-v-90568451 {
  background-color: #EFF6FF;
}
.digital-human-list .digital-human-item.data-v-90568451:active {
  background-color: #F3F4F6;
}
.digital-human-list .digital-human-item .item-avatar.data-v-90568451 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 9999rpx;
  margin-right: 24rpx;
}
.digital-human-list .digital-human-item .item-info.data-v-90568451 {
  flex: 1;
}
.digital-human-list .digital-human-item .item-info .item-name.data-v-90568451 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
}
.digital-human-list .digital-human-item .item-info .item-desc.data-v-90568451 {
  font-size: 24rpx;
  color: #374151;
  line-height: 1.375;
}
@media screen and (max-width: 480px) {
.message-item .message-bubble.data-v-90568451 {
    max-width: 80%;
}
.input-area.data-v-90568451 {
    padding: 24rpx;
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
}
}
@media (prefers-color-scheme: dark) {
.chat-page.data-v-90568451 {
    background-color: #1a1a1a;
}
.chat-header.data-v-90568451 {
    background-color: #2d2d2d;
    border-color: #404040;
}
.message-bubble.assistant-bubble.data-v-90568451 {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
}
.message-bubble.assistant-bubble.data-v-90568451::after {
    border-right-color: #2d2d2d;
}
.typing-bubble.data-v-90568451 {
    background-color: #2d2d2d;
    border-color: #404040;
}
.typing-bubble.data-v-90568451::after {
    border-right-color: #2d2d2d;
}
.input-area.data-v-90568451 {
    background-color: #2d2d2d;
    border-color: #404040;
}
.text-input.data-v-90568451 {
    background-color: #404040;
    border-color: #555555;
    color: #ffffff;
}
.text-input.data-v-90568451::-webkit-input-placeholder {
    color: #888888;
}
.text-input.data-v-90568451::placeholder {
    color: #888888;
}
}