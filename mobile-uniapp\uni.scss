/**
 * 全局 SCSS 变量和样式
 * 这里的样式会被自动注入到每个页面和组件中
 */

/* ==================== 颜色变量 ==================== */
// 主色调
$primary-color: #2563EB;
$primary-light: #3B82F6;
$primary-dark: #1D4ED8;

// 功能色
$success-color: #10B981;
$warning-color: #F59E0B;
$error-color: #EF4444;
$info-color: #06B6D4;

// 中性色
$text-color: #1F2937;
$text-color-light: #6B7280;
$text-color-lighter: #9CA3AF;
$border-color: #E5E7EB;
$border-color-light: #F3F4F6;
$background-color: #F9FAFB;
$background-color-dark: #F3F4F6;

// 白色和黑色
$white: #FFFFFF;
$black: #000000;

/* ==================== 尺寸变量 ==================== */
// 间距
$spacing-xs: 8rpx;
$spacing-sm: 16rpx;
$spacing-md: 24rpx;
$spacing-lg: 32rpx;
$spacing-xl: 48rpx;
$spacing-xxl: 64rpx;

// 圆角
$border-radius-sm: 8rpx;
$border-radius-md: 12rpx;
$border-radius-lg: 16rpx;
$border-radius-xl: 24rpx;
$border-radius-round: 50%;

// 字体大小
$font-size-xs: 20rpx;
$font-size-sm: 24rpx;
$font-size-md: 28rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;
$font-size-xxl: 40rpx;
$font-size-title: 48rpx;

// 行高
$line-height-sm: 1.2;
$line-height-md: 1.4;
$line-height-lg: 1.6;

// 字重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

/* ==================== 阴影变量 ==================== */
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
$shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
$shadow-lg: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
$shadow-xl: 0 16rpx 64rpx rgba(0, 0, 0, 0.16);

/* ==================== 动画变量 ==================== */
$transition-fast: 0.15s;
$transition-normal: 0.3s;
$transition-slow: 0.5s;

$ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
$ease-out: cubic-bezier(0, 0, 0.2, 1);
$ease-in: cubic-bezier(0.4, 0, 1, 1);

/* ==================== 布局变量 ==================== */
// 容器最大宽度
$container-max-width: 750rpx;

// 头部高度
$header-height: 88rpx;
$navbar-height: 44px;

// 底部导航高度
$tabbar-height: 100rpx;

// 安全区域
$safe-area-inset-top: env(safe-area-inset-top);
$safe-area-inset-bottom: env(safe-area-inset-bottom);
$safe-area-inset-left: env(safe-area-inset-left);
$safe-area-inset-right: env(safe-area-inset-right);

/* ==================== 响应式断点 ==================== */
$breakpoint-xs: 480px;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-xxl: 1600px;

/* ==================== Z-index 层级 ==================== */
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
$z-index-toast: 1080;

/* ==================== 自定义 uView 主题变量 ==================== */
// 覆盖 uView 的默认主题色
$u-primary: $primary-color;
$u-success: $success-color;
$u-warning: $warning-color;
$u-error: $error-color;
$u-info: $info-color;

// 覆盖 uView 的文字颜色
$u-main-color: $text-color;
$u-content-color: $text-color-light;
$u-tips-color: $text-color-lighter;

// 覆盖 uView 的边框颜色
$u-border-color: $border-color;

/* ==================== 平台特定样式 ==================== */
// H5 特定样式
/* #ifdef H5 */
$h5-header-height: 44px;
/* #endif */

// 小程序特定样式
/* #ifdef MP */
$mp-header-height: 32px;
/* #endif */

// App 特定样式
/* #ifdef APP-PLUS */
$app-status-bar-height: var(--status-bar-height);
/* #endif */
