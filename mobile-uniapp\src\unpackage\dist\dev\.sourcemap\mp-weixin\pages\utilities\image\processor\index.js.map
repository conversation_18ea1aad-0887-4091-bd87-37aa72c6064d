{"version": 3, "file": "index.js", "sources": ["pages/utilities/image/processor/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXRpbGl0aWVzL2ltYWdlL3Byb2Nlc3Nvci9pbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"image-processor-page\">\n    <!-- 页面标题 -->\n    <view class=\"page-header\">\n      <text class=\"page-title\">图片处理</text>\n      <text class=\"page-desc\">图片格式转换、压缩、裁剪、滤镜等功能</text>\n    </view>\n\n    <!-- 图片上传区域 -->\n    <view class=\"upload-section\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">选择图片</text>\n        <text class=\"title-desc\">支持JPG、PNG、GIF、WebP等格式，最大20MB</text>\n      </view>\n      \n      <view class=\"upload-area\" @click=\"chooseImage\">\n        <view class=\"upload-content\" v-if=\"!selectedImage\">\n          <u-icon name=\"image\" size=\"48\" color=\"#2563EB\"></u-icon>\n          <text class=\"upload-text\">选择图片</text>\n          <text class=\"upload-desc\">支持 JPG、PNG、GIF、WebP 格式</text>\n        </view>\n        \n        <view class=\"image-preview\" v-else>\n          <image \n            class=\"preview-image\" \n            :src=\"selectedImage.path\" \n            mode=\"aspectFit\"\n            @load=\"onImageLoad\"\n          ></image>\n          <view class=\"image-info\">\n            <text class=\"image-name\">{{ selectedImage.name }}</text>\n            <text class=\"image-size\">{{ formatFileSize(selectedImage.size) }}</text>\n            <text class=\"image-dimensions\" v-if=\"selectedImage.width && selectedImage.height\">\n              {{ selectedImage.width }} × {{ selectedImage.height }}\n            </text>\n          </view>\n          <view class=\"image-actions\">\n            <u-icon name=\"close-circle\" size=\"20\" color=\"#EF4444\" @click.stop=\"removeImage\"></u-icon>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 处理工具选择 -->\n    <view class=\"tools-section\" v-if=\"selectedImage\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">选择处理工具</text>\n      </view>\n      \n      <view class=\"tools-grid\">\n        <view \n          class=\"tool-item\"\n          v-for=\"tool in processingTools\"\n          :key=\"tool.id\"\n          :class=\"{ active: selectedTool === tool.id }\"\n          @click=\"selectTool(tool.id)\"\n        >\n          <view class=\"tool-icon\" :style=\"{ backgroundColor: tool.color }\">\n            <u-icon :name=\"tool.icon\" color=\"#fff\" size=\"20\"></u-icon>\n          </view>\n          <text class=\"tool-name\">{{ tool.name }}</text>\n          <text class=\"tool-desc\">{{ tool.description }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 工具选项 -->\n    <view class=\"tool-options\" v-if=\"selectedImage && selectedTool\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">{{ getToolName(selectedTool) }}选项</text>\n      </view>\n      \n      <view class=\"options-content\">\n        <!-- 格式转换选项 -->\n        <view class=\"option-group\" v-if=\"selectedTool === 'convert'\">\n          <text class=\"option-label\">输出格式</text>\n          <view class=\"format-options\">\n            <view \n              class=\"format-item\"\n              v-for=\"format in outputFormats\"\n              :key=\"format.value\"\n              :class=\"{ active: convertOptions.format === format.value }\"\n              @click=\"convertOptions.format = format.value\"\n            >\n              <text class=\"format-text\">{{ format.label }}</text>\n            </view>\n          </view>\n        </view>\n\n        <!-- 压缩选项 -->\n        <view class=\"option-group\" v-if=\"selectedTool === 'compress'\">\n          <text class=\"option-label\">压缩质量: {{ compressOptions.quality }}%</text>\n          <u-slider \n            v-model=\"compressOptions.quality\" \n            :min=\"10\" \n            :max=\"100\" \n            :step=\"10\"\n            active-color=\"#2563EB\"\n          ></u-slider>\n          \n          <text class=\"option-label\">最大尺寸</text>\n          <u-radio-group v-model=\"compressOptions.maxSize\">\n            <u-radio name=\"1024\" label=\"1024KB\"></u-radio>\n            <u-radio name=\"512\" label=\"512KB\"></u-radio>\n            <u-radio name=\"256\" label=\"256KB\"></u-radio>\n            <u-radio name=\"custom\" label=\"自定义\"></u-radio>\n          </u-radio-group>\n          \n          <input \n            v-if=\"compressOptions.maxSize === 'custom'\"\n            class=\"custom-input\"\n            v-model=\"compressOptions.customSize\"\n            placeholder=\"输入大小(KB)\"\n            type=\"number\"\n          />\n        </view>\n\n        <!-- 裁剪选项 -->\n        <view class=\"option-group\" v-if=\"selectedTool === 'crop'\">\n          <text class=\"option-label\">裁剪比例</text>\n          <u-radio-group v-model=\"cropOptions.ratio\">\n            <u-radio name=\"free\" label=\"自由裁剪\"></u-radio>\n            <u-radio name=\"1:1\" label=\"1:1 正方形\"></u-radio>\n            <u-radio name=\"4:3\" label=\"4:3\"></u-radio>\n            <u-radio name=\"16:9\" label=\"16:9\"></u-radio>\n          </u-radio-group>\n          \n          <text class=\"option-label\">输出尺寸</text>\n          <view class=\"size-inputs\">\n            <input \n              class=\"size-input\"\n              v-model=\"cropOptions.width\"\n              placeholder=\"宽度\"\n              type=\"number\"\n            />\n            <text class=\"size-separator\">×</text>\n            <input \n              class=\"size-input\"\n              v-model=\"cropOptions.height\"\n              placeholder=\"高度\"\n              type=\"number\"\n            />\n          </view>\n        </view>\n\n        <!-- 滤镜选项 -->\n        <view class=\"option-group\" v-if=\"selectedTool === 'filter'\">\n          <text class=\"option-label\">选择滤镜</text>\n          <view class=\"filter-options\">\n            <view \n              class=\"filter-item\"\n              v-for=\"filter in filterOptions\"\n              :key=\"filter.value\"\n              :class=\"{ active: selectedFilter === filter.value }\"\n              @click=\"selectedFilter = filter.value\"\n            >\n              <view class=\"filter-preview\" :style=\"{ filter: filter.css }\">\n                <image class=\"filter-image\" :src=\"selectedImage.path\" mode=\"aspectFill\"></image>\n              </view>\n              <text class=\"filter-name\">{{ filter.name }}</text>\n            </view>\n          </view>\n        </view>\n\n        <!-- 调整选项 -->\n        <view class=\"option-group\" v-if=\"selectedTool === 'adjust'\">\n          <view class=\"adjust-control\">\n            <text class=\"control-label\">亮度: {{ adjustOptions.brightness }}</text>\n            <u-slider \n              v-model=\"adjustOptions.brightness\" \n              :min=\"-100\" \n              :max=\"100\" \n              active-color=\"#2563EB\"\n            ></u-slider>\n          </view>\n          \n          <view class=\"adjust-control\">\n            <text class=\"control-label\">对比度: {{ adjustOptions.contrast }}</text>\n            <u-slider \n              v-model=\"adjustOptions.contrast\" \n              :min=\"-100\" \n              :max=\"100\" \n              active-color=\"#2563EB\"\n            ></u-slider>\n          </view>\n          \n          <view class=\"adjust-control\">\n            <text class=\"control-label\">饱和度: {{ adjustOptions.saturation }}</text>\n            <u-slider \n              v-model=\"adjustOptions.saturation\" \n              :min=\"-100\" \n              :max=\"100\" \n              active-color=\"#2563EB\"\n            ></u-slider>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"process-button\">\n        <u-button \n          type=\"primary\" \n          size=\"large\"\n          :loading=\"isProcessing\"\n          :disabled=\"!canProcess\"\n          @click=\"startProcessing\"\n          :custom-style=\"{ width: '100%' }\"\n        >\n          {{ isProcessing ? '处理中...' : '开始处理' }}\n        </u-button>\n      </view>\n    </view>\n\n    <!-- 处理进度 -->\n    <view class=\"processing-progress\" v-if=\"isProcessing\">\n      <view class=\"progress-header\">\n        <text class=\"progress-title\">正在处理图片</text>\n        <text class=\"progress-desc\">{{ progressStatus }}</text>\n      </view>\n      \n      <view class=\"progress-bar\">\n        <u-line-progress \n          :percent=\"processingProgress\" \n          :show-percent=\"true\"\n          active-color=\"#2563EB\"\n        ></u-line-progress>\n      </view>\n    </view>\n\n    <!-- 处理结果 -->\n    <view class=\"processing-result\" v-if=\"processingResult\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">处理完成</text>\n        <view class=\"result-actions\">\n          <u-icon name=\"download\" size=\"18\" color=\"#2563EB\" @click=\"downloadResult\"></u-icon>\n          <u-icon name=\"share\" size=\"18\" color=\"#2563EB\" margin-left=\"12\" @click=\"shareResult\"></u-icon>\n        </view>\n      </view>\n      \n      <view class=\"result-content\">\n        <view class=\"result-comparison\">\n          <view class=\"comparison-item\">\n            <text class=\"comparison-label\">原图</text>\n            <image class=\"comparison-image\" :src=\"selectedImage.path\" mode=\"aspectFit\"></image>\n            <text class=\"comparison-info\">{{ formatFileSize(selectedImage.size) }}</text>\n          </view>\n          \n          <view class=\"comparison-arrow\">\n            <u-icon name=\"arrow-right\" size=\"20\" color=\"#6B7280\"></u-icon>\n          </view>\n          \n          <view class=\"comparison-item\">\n            <text class=\"comparison-label\">处理后</text>\n            <image class=\"comparison-image\" :src=\"processingResult.imageUrl\" mode=\"aspectFit\"></image>\n            <text class=\"comparison-info\">{{ formatFileSize(processingResult.size) }}</text>\n          </view>\n        </view>\n        \n        <view class=\"result-stats\">\n          <text class=\"stat-item\">处理工具: {{ getToolName(selectedTool) }}</text>\n          <text class=\"stat-item\">文件大小: {{ formatFileSize(processingResult.size) }}</text>\n          <text class=\"stat-item\">压缩比: {{ calculateCompressionRatio() }}%</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 处理历史 -->\n    <view class=\"processing-history\" v-if=\"imageHistory.length > 0\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">处理历史</text>\n        <u-button \n          type=\"text\" \n          text=\"清空\" \n          size=\"small\"\n          @click=\"clearHistory\"\n          :custom-style=\"{ color: '#EF4444', fontSize: '24rpx' }\"\n        ></u-button>\n      </view>\n      \n      <view class=\"history-list\">\n        <view \n          class=\"history-item\"\n          v-for=\"item in imageHistory.slice(0, 5)\"\n          :key=\"item.id\"\n          @click=\"viewHistoryItem(item)\"\n        >\n          <image class=\"history-thumbnail\" :src=\"item.thumbnail\" mode=\"aspectFill\"></image>\n          <view class=\"history-content\">\n            <text class=\"history-title\">{{ item.filename }}</text>\n            <text class=\"history-desc\">{{ getToolName(item.tool) }} - {{ item.format }}</text>\n            <text class=\"history-time\">{{ formatTime(item.createdAt) }}</text>\n          </view>\n          <view class=\"history-actions\">\n            <u-icon name=\"download\" size=\"16\" color=\"#6B7280\"></u-icon>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\nimport dayjs from 'dayjs'\n\n// 响应式数据\nconst selectedImage = ref<any>(null)\nconst selectedTool = ref<string>('')\nconst selectedFilter = ref<string>('none')\nconst isProcessing = ref<boolean>(false)\nconst processingProgress = ref<number>(0)\nconst progressStatus = ref<string>('')\nconst processingResult = ref<any>(null)\nconst imageHistory = ref<any[]>([])\n\n// 处理选项\nconst convertOptions = ref({\n  format: 'jpg'\n})\n\nconst compressOptions = ref({\n  quality: 80,\n  maxSize: '1024',\n  customSize: ''\n})\n\nconst cropOptions = ref({\n  ratio: 'free',\n  width: '',\n  height: ''\n})\n\nconst adjustOptions = ref({\n  brightness: 0,\n  contrast: 0,\n  saturation: 0\n})\n\n// 处理工具\nconst processingTools = ref([\n  {\n    id: 'convert',\n    name: '格式转换',\n    description: '转换图片格式',\n    icon: 'refresh',\n    color: '#2563EB'\n  },\n  {\n    id: 'compress',\n    name: '图片压缩',\n    description: '减小文件大小',\n    icon: 'archive',\n    color: '#10B981'\n  },\n  {\n    id: 'crop',\n    name: '裁剪调整',\n    description: '裁剪和调整尺寸',\n    icon: 'crop',\n    color: '#F59E0B'\n  },\n  {\n    id: 'filter',\n    name: '滤镜效果',\n    description: '添加滤镜效果',\n    icon: 'color-filter',\n    color: '#8B5CF6'\n  },\n  {\n    id: 'adjust',\n    name: '色彩调整',\n    description: '调整亮度对比度',\n    icon: 'tune',\n    color: '#EF4444'\n  }\n])\n\n// 输出格式\nconst outputFormats = ref([\n  { label: 'JPG', value: 'jpg' },\n  { label: 'PNG', value: 'png' },\n  { label: 'WebP', value: 'webp' },\n  { label: 'GIF', value: 'gif' }\n])\n\n// 滤镜选项\nconst filterOptions = ref([\n  { name: '无滤镜', value: 'none', css: 'none' },\n  { name: '黑白', value: 'grayscale', css: 'grayscale(100%)' },\n  { name: '复古', value: 'sepia', css: 'sepia(100%)' },\n  { name: '模糊', value: 'blur', css: 'blur(2px)' },\n  { name: '高对比', value: 'contrast', css: 'contrast(150%)' },\n  { name: '高饱和', value: 'saturate', css: 'saturate(150%)' }\n])\n\n// 计算属性\nconst canProcess = computed(() => {\n  return selectedImage.value && selectedTool.value\n})\n\n// 页面加载\nonMounted(() => {\n  loadImageHistory()\n})\n\n// 选择图片\nconst chooseImage = () => {\n  uni.chooseImage({\n    count: 1,\n    sourceType: ['album', 'camera'],\n    success: (res) => {\n      const filePath = res.tempFilePaths[0]\n\n      // 获取图片信息\n      uni.getImageInfo({\n        src: filePath,\n        success: (info) => {\n          selectedImage.value = {\n            name: `image_${Date.now()}.jpg`,\n            path: filePath,\n            size: info.size || 0,\n            width: info.width,\n            height: info.height,\n            type: info.type || 'jpg'\n          }\n\n          // 重置选项\n          selectedTool.value = ''\n          processingResult.value = null\n        },\n        fail: (error) => {\n          uni.__f__('error','at pages/utilities/image/processor/index.vue:431','获取图片信息失败:', error)\n          uni.showToast({\n            title: '获取图片信息失败',\n            icon: 'error'\n          })\n        }\n      })\n    },\n    fail: (error) => {\n      uni.__f__('error','at pages/utilities/image/processor/index.vue:440','选择图片失败:', error)\n    }\n  })\n}\n\n// 图片加载完成\nconst onImageLoad = (e: any) => {\n  uni.__f__('log','at pages/utilities/image/processor/index.vue:447','图片加载完成:', e)\n}\n\n// 移除图片\nconst removeImage = () => {\n  selectedImage.value = null\n  selectedTool.value = ''\n  processingResult.value = null\n}\n\n// 选择工具\nconst selectTool = (toolId: string) => {\n  selectedTool.value = toolId\n  processingResult.value = null\n}\n\n// 开始处理\nconst startProcessing = async () => {\n  if (!canProcess.value) return\n\n  try {\n    isProcessing.value = true\n    processingProgress.value = 0\n    progressStatus.value = '正在处理图片...'\n\n    // 模拟处理进度\n    const progressInterval = setInterval(() => {\n      if (processingProgress.value < 90) {\n        processingProgress.value += Math.random() * 10\n\n        if (processingProgress.value < 30) {\n          progressStatus.value = '正在分析图片...'\n        } else if (processingProgress.value < 60) {\n          progressStatus.value = '正在应用处理...'\n        } else {\n          progressStatus.value = '正在生成结果...'\n        }\n      }\n    }, 200)\n\n    // 模拟处理时间\n    await new Promise(resolve => setTimeout(resolve, 3000))\n\n    clearInterval(progressInterval)\n    processingProgress.value = 100\n    progressStatus.value = '处理完成'\n\n    // 生成模拟结果\n    const mockResult = {\n      imageUrl: selectedImage.value.path, // 实际应该是处理后的图片URL\n      size: calculateProcessedSize(),\n      format: getOutputFormat(),\n      filename: generateResultFilename()\n    }\n\n    processingResult.value = mockResult\n\n    // 添加到历史记录\n    const historyItem = {\n      id: Date.now().toString(),\n      filename: selectedImage.value.name,\n      thumbnail: selectedImage.value.path,\n      tool: selectedTool.value,\n      format: mockResult.format,\n      originalSize: selectedImage.value.size,\n      processedSize: mockResult.size,\n      createdAt: new Date().toISOString()\n    }\n\n    imageHistory.value.unshift(historyItem)\n    saveImageHistory()\n\n    uni.showToast({\n      title: '处理完成',\n      icon: 'success'\n    })\n\n  } catch (error) {\n    uni.__f__('error','at pages/utilities/image/processor/index.vue:525','处理失败:', error)\n    uni.showToast({\n      title: '处理失败',\n      icon: 'error'\n    })\n  } finally {\n    isProcessing.value = false\n  }\n}\n\n// 计算处理后文件大小\nconst calculateProcessedSize = () => {\n  const originalSize = selectedImage.value.size\n\n  switch (selectedTool.value) {\n    case 'compress':\n      const quality = compressOptions.value.quality / 100\n      return Math.floor(originalSize * quality)\n    case 'convert':\n      // 不同格式的大小差异\n      const formatMultiplier = {\n        jpg: 0.8,\n        png: 1.2,\n        webp: 0.6,\n        gif: 1.0\n      }\n      return Math.floor(originalSize * (formatMultiplier[convertOptions.value.format as keyof typeof formatMultiplier] || 1))\n    default:\n      return originalSize\n  }\n}\n\n// 获取输出格式\nconst getOutputFormat = () => {\n  switch (selectedTool.value) {\n    case 'convert':\n      return convertOptions.value.format.toUpperCase()\n    default:\n      return selectedImage.value.type.toUpperCase()\n  }\n}\n\n// 生成结果文件名\nconst generateResultFilename = () => {\n  const baseName = selectedImage.value.name.split('.')[0]\n  const toolName = getToolName(selectedTool.value)\n  const format = getOutputFormat().toLowerCase()\n\n  return `${baseName}_${toolName}.${format}`\n}\n\n// 获取工具名称\nconst getToolName = (toolId: string) => {\n  const tool = processingTools.value.find(t => t.id === toolId)\n  return tool?.name || '未知工具'\n}\n\n// 计算压缩比\nconst calculateCompressionRatio = () => {\n  if (!processingResult.value || !selectedImage.value) return 0\n\n  const originalSize = selectedImage.value.size\n  const processedSize = processingResult.value.size\n\n  return Math.round(((originalSize - processedSize) / originalSize) * 100)\n}\n\n// 下载结果\nconst downloadResult = () => {\n  if (!processingResult.value) return\n\n  uni.showToast({\n    title: '开始下载',\n    icon: 'success'\n  })\n}\n\n// 分享结果\nconst shareResult = () => {\n  if (!processingResult.value) return\n\n  uni.share({\n    provider: 'weixin',\n    scene: 'WXSceneSession',\n    type: 1,\n    imageUrl: processingResult.value.imageUrl,\n    success: () => {\n      uni.showToast({\n        title: '分享成功',\n        icon: 'success'\n      })\n    }\n  })\n}\n\n// 工具函数\nconst formatFileSize = (bytes: number) => {\n  if (bytes === 0) return '0 B'\n  const k = 1024\n  const sizes = ['B', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\nconst formatTime = (time: string) => {\n  return dayjs(time).format('MM-DD HH:mm')\n}\n\n// 历史记录相关\nconst saveImageHistory = () => {\n  uni.setStorageSync('image_processing_history', imageHistory.value)\n}\n\nconst loadImageHistory = () => {\n  const history = uni.getStorageSync('image_processing_history') || []\n  imageHistory.value = history\n}\n\nconst clearHistory = () => {\n  uni.showModal({\n    title: '确认清空',\n    content: '确定要清空所有处理历史吗？',\n    success: (res) => {\n      if (res.confirm) {\n        imageHistory.value = []\n        saveImageHistory()\n        uni.showToast({\n          title: '清空成功',\n          icon: 'success'\n        })\n      }\n    }\n  })\n}\n\nconst viewHistoryItem = (item: any) => {\n  uni.showToast({\n    title: '查看历史记录',\n    icon: 'none'\n  })\n}\n\n// 页面下拉刷新\nconst onPullDownRefresh = () => {\n  loadImageHistory()\n  uni.stopPullDownRefresh()\n}\n\n// 导出方法供页面使用\ndefineExpose({\n  onPullDownRefresh\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.image-processor-page {\n  min-height: 100vh;\n  background-color: $bg-secondary;\n}\n\n// 页面头部\n.page-header {\n  background-color: $bg-primary;\n  padding: $spacing-6 $spacing-4;\n  text-align: center;\n  border-bottom: 1px solid $border-primary;\n\n  .page-title {\n    display: block;\n    font-size: $font-size-2xl;\n    font-weight: $font-weight-bold;\n    color: $text-primary;\n    margin-bottom: $spacing-2;\n  }\n\n  .page-desc {\n    font-size: $font-size-base;\n    color: $text-secondary;\n  }\n}\n\n// 通用区块标题\n.section-title {\n  padding: $spacing-4 $spacing-4 $spacing-3;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  .title-text {\n    font-size: $font-size-lg;\n    font-weight: $font-weight-semibold;\n    color: $text-primary;\n  }\n\n  .title-desc {\n    font-size: $font-size-sm;\n    color: $text-tertiary;\n  }\n\n  .result-actions {\n    display: flex;\n    align-items: center;\n    gap: $spacing-2;\n  }\n}\n\n// 图片上传区域\n.upload-section {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .upload-area {\n    margin: 0 $spacing-4 $spacing-4;\n    border: 2px dashed $border-primary;\n    border-radius: $radius-lg;\n    padding: $spacing-6;\n\n    .upload-content {\n      text-align: center;\n\n      .upload-text {\n        display: block;\n        font-size: $font-size-lg;\n        font-weight: $font-weight-medium;\n        color: $text-primary;\n        margin: $spacing-4 0 $spacing-2;\n      }\n\n      .upload-desc {\n        font-size: $font-size-base;\n        color: $text-secondary;\n      }\n    }\n\n    .image-preview {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: $spacing-3;\n\n      .preview-image {\n        width: 100%;\n        max-height: 400rpx;\n        border-radius: $radius-lg;\n      }\n\n      .image-info {\n        text-align: center;\n\n        .image-name {\n          display: block;\n          font-size: $font-size-base;\n          font-weight: $font-weight-medium;\n          color: $text-primary;\n          margin-bottom: $spacing-1;\n        }\n\n        .image-size,\n        .image-dimensions {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n        }\n      }\n\n      .image-actions {\n        display: flex;\n        justify-content: center;\n      }\n    }\n  }\n}\n\n// 工具选择区域\n.tools-section {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .tools-grid {\n    display: grid;\n    grid-template-columns: repeat(2, 1fr);\n    gap: $spacing-3;\n    padding: 0 $spacing-4 $spacing-4;\n\n    .tool-item {\n      background-color: $bg-tertiary;\n      border-radius: $radius-lg;\n      padding: $spacing-4;\n      text-align: center;\n      border: 2px solid transparent;\n\n      &.active {\n        border-color: $primary;\n        background-color: $primary-50;\n      }\n\n      .tool-icon {\n        width: 80rpx;\n        height: 80rpx;\n        border-radius: $radius-2xl;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin: 0 auto $spacing-3;\n      }\n\n      .tool-name {\n        display: block;\n        font-size: $font-size-base;\n        font-weight: $font-weight-medium;\n        color: $text-primary;\n        margin-bottom: $spacing-1;\n      }\n\n      .tool-desc {\n        font-size: $font-size-sm;\n        color: $text-secondary;\n      }\n    }\n  }\n}\n\n// 工具选项\n.tool-options {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .options-content {\n    padding: 0 $spacing-4;\n\n    .option-group {\n      margin-bottom: $spacing-6;\n\n      .option-label {\n        display: block;\n        font-size: $font-size-base;\n        font-weight: $font-weight-medium;\n        color: $text-primary;\n        margin-bottom: $spacing-3;\n      }\n\n      .format-options {\n        display: flex;\n        gap: $spacing-2;\n\n        .format-item {\n          flex: 1;\n          background-color: $bg-tertiary;\n          border: 1px solid $border-primary;\n          border-radius: $radius-md;\n          padding: $spacing-3;\n          text-align: center;\n\n          &.active {\n            border-color: $primary;\n            background-color: $primary-50;\n          }\n\n          .format-text {\n            font-size: $font-size-sm;\n            color: $text-primary;\n          }\n        }\n      }\n\n      .custom-input {\n        width: 100%;\n        margin-top: $spacing-2;\n        padding: $spacing-2 $spacing-3;\n        border: 1px solid $border-primary;\n        border-radius: $radius-md;\n        font-size: $font-size-base;\n        color: $text-primary;\n        background-color: $bg-tertiary;\n      }\n\n      .size-inputs {\n        display: flex;\n        align-items: center;\n        gap: $spacing-2;\n\n        .size-input {\n          flex: 1;\n          padding: $spacing-2 $spacing-3;\n          border: 1px solid $border-primary;\n          border-radius: $radius-md;\n          font-size: $font-size-base;\n          color: $text-primary;\n          background-color: $bg-tertiary;\n        }\n\n        .size-separator {\n          font-size: $font-size-base;\n          color: $text-tertiary;\n        }\n      }\n\n      .filter-options {\n        display: grid;\n        grid-template-columns: repeat(3, 1fr);\n        gap: $spacing-3;\n\n        .filter-item {\n          text-align: center;\n\n          &.active .filter-preview {\n            border-color: $primary;\n          }\n\n          .filter-preview {\n            width: 100%;\n            height: 120rpx;\n            border-radius: $radius-md;\n            border: 2px solid transparent;\n            overflow: hidden;\n            margin-bottom: $spacing-2;\n\n            .filter-image {\n              width: 100%;\n              height: 100%;\n            }\n          }\n\n          .filter-name {\n            font-size: $font-size-xs;\n            color: $text-secondary;\n          }\n        }\n      }\n\n      .adjust-control {\n        margin-bottom: $spacing-4;\n\n        .control-label {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          margin-bottom: $spacing-2;\n        }\n      }\n    }\n  }\n\n  .process-button {\n    padding: 0 $spacing-4 $spacing-4;\n  }\n}\n\n// 处理进度\n.processing-progress {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n  padding: $spacing-4;\n\n  .progress-header {\n    text-align: center;\n    margin-bottom: $spacing-4;\n\n    .progress-title {\n      display: block;\n      font-size: $font-size-lg;\n      font-weight: $font-weight-semibold;\n      color: $text-primary;\n      margin-bottom: $spacing-1;\n    }\n\n    .progress-desc {\n      font-size: $font-size-sm;\n      color: $text-secondary;\n    }\n  }\n}\n\n// 处理结果\n.processing-result {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .result-content {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .result-comparison {\n      display: flex;\n      align-items: center;\n      gap: $spacing-3;\n      margin-bottom: $spacing-4;\n\n      .comparison-item {\n        flex: 1;\n        text-align: center;\n\n        .comparison-label {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          margin-bottom: $spacing-2;\n        }\n\n        .comparison-image {\n          width: 100%;\n          height: 200rpx;\n          border-radius: $radius-md;\n          background-color: $bg-tertiary;\n          margin-bottom: $spacing-2;\n        }\n\n        .comparison-info {\n          font-size: $font-size-xs;\n          color: $text-tertiary;\n        }\n      }\n\n      .comparison-arrow {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n    }\n\n    .result-stats {\n      background-color: $bg-tertiary;\n      border-radius: $radius-lg;\n      padding: $spacing-4;\n\n      .stat-item {\n        display: block;\n        font-size: $font-size-sm;\n        color: $text-secondary;\n        margin-bottom: $spacing-1;\n\n        &:last-child {\n          margin-bottom: 0;\n        }\n      }\n    }\n  }\n}\n\n// 处理历史\n.processing-history {\n  background-color: $bg-primary;\n\n  .history-list {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .history-item {\n      display: flex;\n      align-items: center;\n      gap: $spacing-3;\n      padding: $spacing-3;\n      background-color: $bg-tertiary;\n      border-radius: $radius-lg;\n      margin-bottom: $spacing-2;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      &:active {\n        background-color: $gray-200;\n      }\n\n      .history-thumbnail {\n        width: 80rpx;\n        height: 80rpx;\n        border-radius: $radius-md;\n        background-color: $bg-quaternary;\n      }\n\n      .history-content {\n        flex: 1;\n\n        .history-title {\n          display: block;\n          font-size: $font-size-base;\n          font-weight: $font-weight-medium;\n          color: $text-primary;\n          margin-bottom: $spacing-1;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n\n        .history-desc {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          margin-bottom: $spacing-1;\n        }\n\n        .history-time {\n          font-size: $font-size-xs;\n          color: $text-tertiary;\n        }\n      }\n\n      .history-actions {\n        padding: $spacing-2;\n      }\n    }\n  }\n}\n\n// 响应式适配\n@media screen and (max-width: 480px) {\n  .tools-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .format-options {\n    flex-direction: column;\n  }\n\n  .filter-options {\n    grid-template-columns: repeat(2, 1fr);\n  }\n\n  .result-comparison {\n    flex-direction: column;\n\n    .comparison-arrow {\n      transform: rotate(90deg);\n    }\n  }\n}\n\n// 暗色模式适配\n@media (prefers-color-scheme: dark) {\n  .image-processor-page {\n    background-color: #1a1a1a;\n  }\n\n  .page-header,\n  .upload-section,\n  .tools-section,\n  .tool-options,\n  .processing-progress,\n  .processing-result,\n  .processing-history {\n    background-color: #2d2d2d;\n    border-color: #404040;\n  }\n\n  .upload-area,\n  .tool-item,\n  .format-item,\n  .result-stats,\n  .history-item {\n    background-color: #404040;\n    border-color: #555555;\n  }\n\n  .tool-item.active,\n  .format-item.active {\n    background-color: #1e3a8a;\n    border-color: #3b82f6;\n  }\n\n  .custom-input,\n  .size-input {\n    background-color: #555555;\n    border-color: #666666;\n    color: #ffffff;\n  }\n\n  .comparison-image,\n  .history-thumbnail {\n    background-color: #555555;\n  }\n\n  .filter-image {\n    opacity: 0.8;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/AI_system/mobile-uniapp/src/pages/utilities/image/processor/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "dayjs"], "mappings": ";;;;;;;;;;;;;;AAiTM,UAAA,gBAAgBA,kBAAS,IAAI;AAC7B,UAAA,eAAeA,kBAAY,EAAE;AAC7B,UAAA,iBAAiBA,kBAAY,MAAM;AACnC,UAAA,eAAeA,kBAAa,KAAK;AACjC,UAAA,qBAAqBA,kBAAY,CAAC;AAClC,UAAA,iBAAiBA,kBAAY,EAAE;AAC/B,UAAA,mBAAmBA,kBAAS,IAAI;AAChC,UAAA,eAAeA,kBAAW,CAAA,CAAE;AAGlC,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB,QAAQ;AAAA,IAAA,CACT;AAED,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,IAAA,CACb;AAED,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,IAAA,CACT;AAED,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,YAAY;AAAA,IAAA,CACb;AAGD,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IAAA,CACD;AAGD,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB,EAAE,OAAO,OAAO,OAAO,MAAM;AAAA,MAC7B,EAAE,OAAO,OAAO,OAAO,MAAM;AAAA,MAC7B,EAAE,OAAO,QAAQ,OAAO,OAAO;AAAA,MAC/B,EAAE,OAAO,OAAO,OAAO,MAAM;AAAA,IAAA,CAC9B;AAGD,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB,EAAE,MAAM,OAAO,OAAO,QAAQ,KAAK,OAAO;AAAA,MAC1C,EAAE,MAAM,MAAM,OAAO,aAAa,KAAK,kBAAkB;AAAA,MACzD,EAAE,MAAM,MAAM,OAAO,SAAS,KAAK,cAAc;AAAA,MACjD,EAAE,MAAM,MAAM,OAAO,QAAQ,KAAK,YAAY;AAAA,MAC9C,EAAE,MAAM,OAAO,OAAO,YAAY,KAAK,iBAAiB;AAAA,MACxD,EAAE,MAAM,OAAO,OAAO,YAAY,KAAK,iBAAiB;AAAA,IAAA,CACzD;AAGK,UAAA,aAAaC,cAAAA,SAAS,MAAM;AACzB,aAAA,cAAc,SAAS,aAAa;AAAA,IAAA,CAC5C;AAGDC,kBAAAA,UAAU,MAAM;AACG;IAAA,CAClB;AAGD,UAAM,cAAc,MAAM;AACxBC,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AACV,gBAAA,WAAW,IAAI,cAAc,CAAC;AAGpCA,wBAAAA,MAAI,aAAa;AAAA,YACf,KAAK;AAAA,YACL,SAAS,CAAC,SAAS;AACjB,4BAAc,QAAQ;AAAA,gBACpB,MAAM,SAAS,KAAK,IAAA,CAAK;AAAA,gBACzB,MAAM;AAAA,gBACN,MAAM,KAAK,QAAQ;AAAA,gBACnB,OAAO,KAAK;AAAA,gBACZ,QAAQ,KAAK;AAAA,gBACb,MAAM,KAAK,QAAQ;AAAA,cAAA;AAIrB,2BAAa,QAAQ;AACrB,+BAAiB,QAAQ;AAAA,YAC3B;AAAA,YACA,MAAM,CAAC,UAAU;AACfA,4BAAA,MAAI,MAAM,SAAQ,oDAAmD,aAAa,KAAK;AACvFA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACP;AAAA,YACH;AAAA,UAAA,CACD;AAAA,QACH;AAAA,QACA,MAAM,CAAC,UAAU;AACfA,wBAAA,MAAI,MAAM,SAAQ,oDAAmD,WAAW,KAAK;AAAA,QACvF;AAAA,MAAA,CACD;AAAA,IAAA;AAIG,UAAA,cAAc,CAAC,MAAW;AAC9BA,oBAAA,MAAI,MAAM,OAAM,oDAAmD,WAAW,CAAC;AAAA,IAAA;AAIjF,UAAM,cAAc,MAAM;AACxB,oBAAc,QAAQ;AACtB,mBAAa,QAAQ;AACrB,uBAAiB,QAAQ;AAAA,IAAA;AAIrB,UAAA,aAAa,CAAC,WAAmB;AACrC,mBAAa,QAAQ;AACrB,uBAAiB,QAAQ;AAAA,IAAA;AAI3B,UAAM,kBAAkB,YAAY;AAClC,UAAI,CAAC,WAAW;AAAO;AAEnB,UAAA;AACF,qBAAa,QAAQ;AACrB,2BAAmB,QAAQ;AAC3B,uBAAe,QAAQ;AAGjB,cAAA,mBAAmB,YAAY,MAAM;AACrC,cAAA,mBAAmB,QAAQ,IAAI;AACd,+BAAA,SAAS,KAAK,OAAA,IAAW;AAExC,gBAAA,mBAAmB,QAAQ,IAAI;AACjC,6BAAe,QAAQ;AAAA,YAAA,WACd,mBAAmB,QAAQ,IAAI;AACxC,6BAAe,QAAQ;AAAA,YAAA,OAClB;AACL,6BAAe,QAAQ;AAAA,YACzB;AAAA,UACF;AAAA,WACC,GAAG;AAGN,cAAM,IAAI,QAAQ,CAAA,YAAW,WAAW,SAAS,GAAI,CAAC;AAEtD,sBAAc,gBAAgB;AAC9B,2BAAmB,QAAQ;AAC3B,uBAAe,QAAQ;AAGvB,cAAM,aAAa;AAAA,UACjB,UAAU,cAAc,MAAM;AAAA;AAAA,UAC9B,MAAM,uBAAuB;AAAA,UAC7B,QAAQ,gBAAgB;AAAA,UACxB,UAAU,uBAAuB;AAAA,QAAA;AAGnC,yBAAiB,QAAQ;AAGzB,cAAM,cAAc;AAAA,UAClB,IAAI,KAAK,IAAI,EAAE,SAAS;AAAA,UACxB,UAAU,cAAc,MAAM;AAAA,UAC9B,WAAW,cAAc,MAAM;AAAA,UAC/B,MAAM,aAAa;AAAA,UACnB,QAAQ,WAAW;AAAA,UACnB,cAAc,cAAc,MAAM;AAAA,UAClC,eAAe,WAAW;AAAA,UAC1B,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAAA;AAGvB,qBAAA,MAAM,QAAQ,WAAW;AACrB;AAEjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,eAEM,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,oDAAmD,SAAS,KAAK;AACnFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,qBAAa,QAAQ;AAAA,MACvB;AAAA,IAAA;AAIF,UAAM,yBAAyB,MAAM;AAC7B,YAAA,eAAe,cAAc,MAAM;AAEzC,cAAQ,aAAa,OAAO;AAAA,QAC1B,KAAK;AACG,gBAAA,UAAU,gBAAgB,MAAM,UAAU;AACzC,iBAAA,KAAK,MAAM,eAAe,OAAO;AAAA,QAC1C,KAAK;AAEH,gBAAM,mBAAmB;AAAA,YACvB,KAAK;AAAA,YACL,KAAK;AAAA,YACL,MAAM;AAAA,YACN,KAAK;AAAA,UAAA;AAEA,iBAAA,KAAK,MAAM,gBAAgB,iBAAiB,eAAe,MAAM,MAAuC,KAAK,EAAE;AAAA,QACxH;AACS,iBAAA;AAAA,MACX;AAAA,IAAA;AAIF,UAAM,kBAAkB,MAAM;AAC5B,cAAQ,aAAa,OAAO;AAAA,QAC1B,KAAK;AACI,iBAAA,eAAe,MAAM,OAAO,YAAY;AAAA,QACjD;AACS,iBAAA,cAAc,MAAM,KAAK,YAAY;AAAA,MAChD;AAAA,IAAA;AAIF,UAAM,yBAAyB,MAAM;AACnC,YAAM,WAAW,cAAc,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC;AAChD,YAAA,WAAW,YAAY,aAAa,KAAK;AACzC,YAAA,SAAS,kBAAkB;AAEjC,aAAO,GAAG,QAAQ,IAAI,QAAQ,IAAI,MAAM;AAAA,IAAA;AAIpC,UAAA,cAAc,CAAC,WAAmB;AACtC,YAAM,OAAO,gBAAgB,MAAM,KAAK,CAAK,MAAA,EAAE,OAAO,MAAM;AAC5D,cAAO,6BAAM,SAAQ;AAAA,IAAA;AAIvB,UAAM,4BAA4B,MAAM;AACtC,UAAI,CAAC,iBAAiB,SAAS,CAAC,cAAc;AAAc,eAAA;AAEtD,YAAA,eAAe,cAAc,MAAM;AACnC,YAAA,gBAAgB,iBAAiB,MAAM;AAE7C,aAAO,KAAK,OAAQ,eAAe,iBAAiB,eAAgB,GAAG;AAAA,IAAA;AAIzE,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,iBAAiB;AAAO;AAE7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIH,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,iBAAiB;AAAO;AAE7BA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU,iBAAiB,MAAM;AAAA,QACjC,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;AAIG,UAAA,iBAAiB,CAAC,UAAkB;AACxC,UAAI,UAAU;AAAU,eAAA;AACxB,YAAM,IAAI;AACV,YAAM,QAAQ,CAAC,KAAK,MAAM,MAAM,IAAI;AAC9B,YAAA,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,CAAC;AAClD,aAAO,YAAY,QAAQ,KAAK,IAAI,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC;AAAA,IAAA;AAGlE,UAAA,aAAa,CAAC,SAAiB;AACnC,aAAOC,cAAM,MAAA,IAAI,EAAE,OAAO,aAAa;AAAA,IAAA;AAIzC,UAAM,mBAAmB,MAAM;AACzBD,oBAAAA,MAAA,eAAe,4BAA4B,aAAa,KAAK;AAAA,IAAA;AAGnE,UAAM,mBAAmB,MAAM;AAC7B,YAAM,UAAUA,cAAA,MAAI,eAAe,0BAA0B,KAAK,CAAA;AAClE,mBAAa,QAAQ;AAAA,IAAA;AAGvB,UAAM,eAAe,MAAM;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,yBAAa,QAAQ;AACJ;AACjBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAAA,CACP;AAAA,UACH;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IAAA;AAGG,UAAA,kBAAkB,CAAC,SAAc;AACrCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIH,UAAM,oBAAoB,MAAM;AACb;AACjBA,oBAAA,MAAI,oBAAoB;AAAA,IAAA;AAIb,aAAA;AAAA,MACX;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClqBD,GAAG,WAAW,eAAe;"}