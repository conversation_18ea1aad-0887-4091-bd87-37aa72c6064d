<template>
  <view class="home-page">
    <!-- 顶部轮播图 -->
    <view class="banner-section">
      <swiper 
        class="banner-swiper" 
        :indicator-dots="true" 
        :autoplay="true" 
        :interval="3000"
        :duration="500"
        indicator-color="rgba(255,255,255,0.5)"
        indicator-active-color="#2563EB"
      >
        <swiper-item v-for="(banner, index) in banners" :key="index">
          <view class="banner-item" :style="{ backgroundImage: `url(${banner.image})` }">
            <view class="banner-content">
              <text class="banner-title">{{ banner.title }}</text>
              <text class="banner-desc">{{ banner.description }}</text>
              <u-button 
                type="primary" 
                size="small" 
                shape="round"
                @click="handleBannerClick(banner)"
              >
                {{ banner.buttonText }}
              </u-button>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 快捷功能入口 -->
    <view class="quick-actions">
      <view class="section-title">
        <text class="title-text">快捷功能</text>
        <text class="title-desc">一键直达常用功能</text>
      </view>
      
      <view class="actions-grid">
        <view 
          class="action-item" 
          v-for="(action, index) in quickActions" 
          :key="index"
          @click="navigateTo(action.path)"
        >
          <view class="action-icon" :style="{ backgroundColor: action.color }">
            <u-icon :name="action.icon" color="#fff" size="24"></u-icon>
          </view>
          <text class="action-title">{{ action.title }}</text>
          <text class="action-desc">{{ action.description }}</text>
        </view>
      </view>
    </view>

    <!-- 功能模块 -->
    <view class="feature-modules">
      <view class="section-title">
        <text class="title-text">核心功能</text>
        <text class="title-desc">探索AI的无限可能</text>
      </view>
      
      <view class="modules-list">
        <view 
          class="module-card" 
          v-for="(module, index) in featureModules" 
          :key="index"
          @click="navigateTo(module.path)"
        >
          <view class="module-header">
            <view class="module-icon">
              <u-icon :name="module.icon" :color="module.color" size="28"></u-icon>
            </view>
            <view class="module-badge" v-if="module.badge">
              <text class="badge-text">{{ module.badge }}</text>
            </view>
          </view>
          
          <view class="module-content">
            <text class="module-title">{{ module.title }}</text>
            <text class="module-desc">{{ module.description }}</text>
            
            <view class="module-features">
              <text 
                class="feature-tag" 
                v-for="(feature, idx) in module.features" 
                :key="idx"
              >
                {{ feature }}
              </text>
            </view>
          </view>
          
          <view class="module-arrow">
            <u-icon name="arrow-right" color="#9CA3AF" size="16"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 最近使用 -->
    <view class="recent-section" v-if="recentItems.length > 0">
      <view class="section-title">
        <text class="title-text">最近使用</text>
        <text class="title-desc">快速访问历史记录</text>
      </view>
      
      <scroll-view class="recent-scroll" scroll-x="true" show-scrollbar="false">
        <view class="recent-list">
          <view 
            class="recent-item" 
            v-for="(item, index) in recentItems" 
            :key="index"
            @click="handleRecentClick(item)"
          >
            <view class="recent-icon">
              <u-icon :name="item.icon" color="#2563EB" size="20"></u-icon>
            </view>
            <text class="recent-title">{{ item.title }}</text>
            <text class="recent-time">{{ formatTime(item.time) }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/modules/user'
import dayjs from 'dayjs'

// Store
const userStore = useUserStore()

// 轮播图数据
const banners = ref([
  {
    id: 1,
    title: 'AI翻译服务',
    description: '支持200+语言，专业翻译',
    image: '/static/images/banner1.jpg',
    buttonText: '立即体验',
    path: '/pages/translation/text/index'
  },
  {
    id: 2,
    title: '数字人对话',
    description: '逼真AI数字人，智能交互',
    image: '/static/images/banner2.jpg',
    buttonText: '开始对话',
    path: '/pages/digital-human/chat/index'
  },
  {
    id: 3,
    title: 'AI智能体',
    description: '专业AI助手，提升效率',
    image: '/static/images/banner3.jpg',
    buttonText: '探索更多',
    path: '/pages/ai-agent/market/index'
  }
])

// 快捷功能
const quickActions = ref([
  {
    title: '文本翻译',
    description: '快速翻译',
    icon: 'edit-pen',
    color: '#2563EB',
    path: '/pages/translation/text/index'
  },
  {
    title: '语音翻译',
    description: '实时语音',
    icon: 'mic',
    color: '#10B981',
    path: '/pages/translation/audio/index'
  },
  {
    title: '数字人',
    description: 'AI对话',
    icon: 'account',
    color: '#F59E0B',
    path: '/pages/digital-human/chat/index'
  },
  {
    title: '智能体',
    description: 'AI助手',
    icon: 'robot',
    color: '#EF4444',
    path: '/pages/ai-agent/market/index'
  }
])

// 功能模块
const featureModules = ref([
  {
    title: '翻译服务',
    description: '支持文本、文档、音频、视频等多种翻译方式',
    icon: 'globe',
    color: '#2563EB',
    badge: '热门',
    features: ['文本翻译', '文档翻译', '音频翻译', '视频翻译'],
    path: '/pages/translation/text/index'
  },
  {
    title: '数字人对话',
    description: '与AI数字人进行自然对话，体验未来交互方式',
    icon: 'account-circle',
    color: '#10B981',
    badge: '新功能',
    features: ['智能对话', '表情生动', '多语言支持'],
    path: '/pages/digital-human/chat/index'
  },
  {
    title: 'AI智能体市场',
    description: '丰富的AI智能体，满足各种专业需求',
    icon: 'store',
    color: '#F59E0B',
    features: ['专业助手', '定制服务', '智能推荐'],
    path: '/pages/ai-agent/market/index'
  },
  {
    title: '实用工具',
    description: '文档转换、音频处理、图片编辑等实用工具',
    icon: 'wrench',
    color: '#8B5CF6',
    features: ['格式转换', '批量处理', '高效便捷'],
    path: '/pages/utilities/conversion/document/index'
  }
])

// 最近使用
const recentItems = ref([])

// 页面加载
onMounted(async () => {
  await loadRecentItems()
})

// 加载最近使用记录
const loadRecentItems = async () => {
  try {
    // 这里从本地存储或API获取最近使用记录
    const recent = uni.getStorageSync('recent_items') || []
    recentItems.value = recent.slice(0, 10) // 只显示最近10条
  } catch (error) {
    console.error('加载最近使用记录失败:', error)
  }
}

// 导航到指定页面
const navigateTo = (path: string) => {
  if (!path) return
  
  uni.navigateTo({
    url: path,
    fail: (err) => {
      console.error('页面跳转失败:', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'error'
      })
    }
  })
}

// 处理轮播图点击
const handleBannerClick = (banner: any) => {
  navigateTo(banner.path)
}

// 处理最近使用点击
const handleRecentClick = (item: any) => {
  navigateTo(item.path)
}

// 格式化时间
const formatTime = (time: string) => {
  return dayjs(time).fromNow()
}

// 下拉刷新
const onPullDownRefresh = async () => {
  try {
    await loadRecentItems()
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '刷新失败',
      icon: 'error'
    })
  } finally {
    uni.stopPullDownRefresh()
  }
}

// 导出方法供页面使用
defineExpose({
  onPullDownRefresh
})
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background-color: $bg-secondary;
}

// 轮播图样式
.banner-section {
  height: 400rpx;
  
  .banner-swiper {
    height: 100%;
    
    .banner-item {
      height: 100%;
      background-size: cover;
      background-position: center;
      position: relative;
      display: flex;
      align-items: center;
      padding: 0 $spacing-6;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(0,0,0,0.3), rgba(0,0,0,0.1));
      }
      
      .banner-content {
        position: relative;
        z-index: 1;
        color: white;
        
        .banner-title {
          display: block;
          font-size: $font-size-2xl;
          font-weight: $font-weight-bold;
          margin-bottom: $spacing-2;
        }
        
        .banner-desc {
          display: block;
          font-size: $font-size-base;
          opacity: 0.9;
          margin-bottom: $spacing-4;
        }
      }
    }
  }
}

// 通用区块标题
.section-title {
  padding: $spacing-6 $spacing-4 $spacing-4;
  
  .title-text {
    display: block;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    margin-bottom: $spacing-1;
  }
  
  .title-desc {
    font-size: $font-size-sm;
    color: $text-tertiary;
  }
}

// 快捷功能样式
.quick-actions {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;
  
  .actions-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: $spacing-4;
    padding: 0 $spacing-4 $spacing-6;
    
    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      
      .action-icon {
        width: 96rpx;
        height: 96rpx;
        border-radius: $radius-2xl;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: $spacing-2;
      }
      
      .action-title {
        font-size: $font-size-sm;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-1;
      }
      
      .action-desc {
        font-size: $font-size-xs;
        color: $text-tertiary;
      }
    }
  }
}

// 功能模块样式
.feature-modules {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;
  
  .modules-list {
    padding: 0 $spacing-4 $spacing-6;
    
    .module-card {
      background-color: $bg-primary;
      border-radius: $radius-lg;
      padding: $spacing-4;
      margin-bottom: $spacing-3;
      box-shadow: $shadow-sm;
      border: 1px solid $border-primary;
      display: flex;
      align-items: flex-start;
      
      .module-header {
        position: relative;
        margin-right: $spacing-3;
        
        .module-icon {
          width: 80rpx;
          height: 80rpx;
          background-color: $bg-tertiary;
          border-radius: $radius-lg;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .module-badge {
          position: absolute;
          top: -8rpx;
          right: -8rpx;
          background-color: $error;
          border-radius: $radius-full;
          padding: 4rpx 12rpx;
          
          .badge-text {
            font-size: $font-size-xs;
            color: white;
          }
        }
      }
      
      .module-content {
        flex: 1;
        
        .module-title {
          display: block;
          font-size: $font-size-md;
          font-weight: $font-weight-semibold;
          color: $text-primary;
          margin-bottom: $spacing-1;
        }
        
        .module-desc {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          line-height: $line-height-relaxed;
          margin-bottom: $spacing-2;
        }
        
        .module-features {
          display: flex;
          flex-wrap: wrap;
          gap: $spacing-1;
          
          .feature-tag {
            background-color: $bg-tertiary;
            color: $text-tertiary;
            font-size: $font-size-xs;
            padding: 4rpx 12rpx;
            border-radius: $radius-full;
          }
        }
      }
      
      .module-arrow {
        margin-left: $spacing-2;
        display: flex;
        align-items: center;
      }
    }
  }
}

// 最近使用样式
.recent-section {
  background-color: $bg-primary;
  
  .recent-scroll {
    white-space: nowrap;
    
    .recent-list {
      display: flex;
      padding: 0 $spacing-4 $spacing-6;
      gap: $spacing-3;
      
      .recent-item {
        flex-shrink: 0;
        width: 200rpx;
        background-color: $bg-tertiary;
        border-radius: $radius-lg;
        padding: $spacing-3;
        text-align: center;
        
        .recent-icon {
          width: 64rpx;
          height: 64rpx;
          background-color: $bg-primary;
          border-radius: $radius-lg;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto $spacing-2;
        }
        
        .recent-title {
          display: block;
          font-size: $font-size-sm;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .recent-time {
          font-size: $font-size-xs;
          color: $text-tertiary;
        }
      }
    }
  }
}
</style>
