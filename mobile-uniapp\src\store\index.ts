import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'

// 创建 pinia 实例
const pinia = createPinia()

// 持久化插件配置
pinia.use(
  createPersistedState({
    // 自定义存储方式，适配 uni-app
    storage: {
      getItem: (key: string) => {
        return uni.getStorageSync(key)
      },
      setItem: (key: string, value: string) => {
        uni.setStorageSync(key, value)
      },
      removeItem: (key: string) => {
        uni.removeStorageSync(key)
      }
    },
    // 默认持久化所有 store
    auto: true
  })
)

export default pinia

// 导出所有 store
export * from './modules/user'
export * from './modules/app'
export * from './modules/translation'
export * from './modules/digitalHuman'
export * from './modules/aiAgent'
