/**
 * 用户相关API接口
 */
import request from '@/utils/request'

// 用户信息接口类型定义
export interface UserInfo {
  id: string
  username: string
  nickname: string
  avatar: string
  email?: string
  phone?: string
  gender?: 'male' | 'female' | 'unknown'
  birthday?: string
  bio?: string
  level: 'free' | 'pro' | 'enterprise'
  vipExpireTime?: string
  createdAt: string
  updatedAt: string
}

// 登录请求参数
export interface LoginParams {
  username: string
  password: string
  captcha?: string
  rememberMe?: boolean
}

// 登录响应数据
export interface LoginResponse {
  token: string
  refreshToken: string
  userInfo: UserInfo
  expiresIn: number
}

// 注册请求参数
export interface RegisterParams {
  username: string
  password: string
  confirmPassword: string
  email?: string
  phone?: string
  captcha: string
  inviteCode?: string
}

// 用户统计信息
export interface UserStats {
  translationCount: number
  chatCount: number
  documentCount: number
  totalUsage: number
  monthlyUsage: number
  remainingQuota: number
}

// 用户设置
export interface UserSettings {
  theme: 'light' | 'dark' | 'auto'
  language: string
  notifications: {
    email: boolean
    push: boolean
    sms: boolean
  }
  privacy: {
    profileVisible: boolean
    activityVisible: boolean
  }
  preferences: {
    autoTranslate: boolean
    voiceInput: boolean
    smartRecommend: boolean
  }
}

/**
 * 用户登录
 */
export const login = (params: LoginParams): Promise<LoginResponse> => {
  return request.post('/auth/login', params)
}

/**
 * 用户注册
 */
export const register = (params: RegisterParams): Promise<{ message: string }> => {
  return request.post('/auth/register', params)
}

/**
 * 用户登出
 */
export const logout = (): Promise<{ message: string }> => {
  return request.post('/auth/logout')
}

/**
 * 刷新Token
 */
export const refreshToken = (refreshToken: string): Promise<{ token: string; expiresIn: number }> => {
  return request.post('/auth/refresh', { refreshToken })
}

/**
 * 获取用户信息
 */
export const getUserInfo = (): Promise<UserInfo> => {
  return request.get('/user/profile')
}

/**
 * 更新用户信息
 */
export const updateUserInfo = (params: Partial<UserInfo>): Promise<UserInfo> => {
  return request.put('/user/profile', params)
}

/**
 * 上传用户头像
 */
export const uploadAvatar = (file: File): Promise<{ avatar: string }> => {
  const formData = new FormData()
  formData.append('avatar', file)
  return request.post('/user/avatar', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 修改密码
 */
export const changePassword = (params: {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}): Promise<{ message: string }> => {
  return request.put('/user/password', params)
}

/**
 * 获取用户统计信息
 */
export const getUserStats = (): Promise<UserStats> => {
  return request.get('/user/stats')
}

/**
 * 获取用户设置
 */
export const getUserSettings = (): Promise<UserSettings> => {
  return request.get('/user/settings')
}

/**
 * 更新用户设置
 */
export const updateUserSettings = (settings: Partial<UserSettings>): Promise<UserSettings> => {
  return request.put('/user/settings', settings)
}

/**
 * 发送验证码
 */
export const sendVerificationCode = (params: {
  type: 'email' | 'phone'
  target: string
  purpose: 'register' | 'login' | 'reset' | 'bind'
}): Promise<{ message: string }> => {
  return request.post('/auth/verification-code', params)
}

/**
 * 验证验证码
 */
export const verifyCode = (params: {
  type: 'email' | 'phone'
  target: string
  code: string
}): Promise<{ valid: boolean }> => {
  return request.post('/auth/verify-code', params)
}

/**
 * 重置密码
 */
export const resetPassword = (params: {
  type: 'email' | 'phone'
  target: string
  code: string
  newPassword: string
  confirmPassword: string
}): Promise<{ message: string }> => {
  return request.post('/auth/reset-password', params)
}

/**
 * 绑定邮箱/手机
 */
export const bindContact = (params: {
  type: 'email' | 'phone'
  target: string
  code: string
}): Promise<{ message: string }> => {
  return request.post('/user/bind-contact', params)
}

/**
 * 解绑邮箱/手机
 */
export const unbindContact = (params: {
  type: 'email' | 'phone'
  password: string
}): Promise<{ message: string }> => {
  return request.post('/user/unbind-contact', params)
}

/**
 * 获取用户使用历史
 */
export const getUserHistory = (params: {
  type?: 'translation' | 'chat' | 'document' | 'all'
  page?: number
  limit?: number
  startDate?: string
  endDate?: string
}): Promise<{
  list: Array<{
    id: string
    type: string
    title: string
    content: string
    result?: string
    status: 'success' | 'failed' | 'processing'
    createdAt: string
  }>
  total: number
  page: number
  limit: number
}> => {
  return request.get('/user/history', { params })
}

/**
 * 删除使用历史
 */
export const deleteHistory = (ids: string[]): Promise<{ message: string }> => {
  return request.delete('/user/history', { data: { ids } })
}

/**
 * 清空使用历史
 */
export const clearHistory = (type?: string): Promise<{ message: string }> => {
  return request.delete('/user/history/clear', { data: { type } })
}

/**
 * 获取用户收藏
 */
export const getUserFavorites = (params: {
  type?: 'agent' | 'translation' | 'document'
  page?: number
  limit?: number
}): Promise<{
  list: Array<{
    id: string
    type: string
    title: string
    description: string
    thumbnail?: string
    createdAt: string
  }>
  total: number
  page: number
  limit: number
}> => {
  return request.get('/user/favorites', { params })
}

/**
 * 添加收藏
 */
export const addFavorite = (params: {
  type: string
  targetId: string
  title: string
  description?: string
  thumbnail?: string
}): Promise<{ message: string }> => {
  return request.post('/user/favorites', params)
}

/**
 * 取消收藏
 */
export const removeFavorite = (id: string): Promise<{ message: string }> => {
  return request.delete(`/user/favorites/${id}`)
}

/**
 * 检查是否已收藏
 */
export const checkFavorite = (targetId: string, type: string): Promise<{ isFavorite: boolean }> => {
  return request.get('/user/favorites/check', { params: { targetId, type } })
}

// 默认导出所有API
export default {
  login,
  register,
  logout,
  refreshToken,
  getUserInfo,
  updateUserInfo,
  uploadAvatar,
  changePassword,
  getUserStats,
  getUserSettings,
  updateUserSettings,
  sendVerificationCode,
  verifyCode,
  resetPassword,
  bindContact,
  unbindContact,
  getUserHistory,
  deleteHistory,
  clearHistory,
  getUserFavorites,
  addFavorite,
  removeFavorite,
  checkFavorite
}
