<template>
  <view id="app">
    <!-- 全局加载提示 -->
    <u-loading-page 
      :loading="globalLoading" 
      :text="loadingText"
      bg-color="rgba(255,255,255,0.9)"
    ></u-loading-page>
  </view>
</template>

<script setup lang="ts">
import { ref, onLaunch, onShow, onHide } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { useAppStore } from '@/store/modules/app'

// 全局状态
const globalLoading = ref(false)
const loadingText = ref('加载中...')

// Store
const userStore = useUserStore()
const appStore = useAppStore()

// 应用启动
onLaunch(async (options) => {
  console.log('App Launch', options)
  
  // 初始化应用
  await initApp()
})

// 应用显示
onShow((options) => {
  console.log('App Show', options)
  
  // 检查更新
  checkUpdate()
})

// 应用隐藏
onHide(() => {
  console.log('App Hide')
})

// 初始化应用
const initApp = async () => {
  try {
    globalLoading.value = true
    loadingText.value = '初始化应用...'
    
    // 获取系统信息
    const systemInfo = uni.getSystemInfoSync()
    appStore.setSystemInfo(systemInfo)
    
    // 检查网络状态
    const networkType = await getNetworkType()
    appStore.setNetworkType(networkType)
    
    // 尝试自动登录
    loadingText.value = '检查登录状态...'
    await userStore.checkLoginStatus()
    
    // 初始化完成
    globalLoading.value = false
    
  } catch (error) {
    console.error('应用初始化失败:', error)
    globalLoading.value = false
    
    uni.showToast({
      title: '初始化失败',
      icon: 'error'
    })
  }
}

// 获取网络类型
const getNetworkType = (): Promise<string> => {
  return new Promise((resolve) => {
    uni.getNetworkType({
      success: (res) => {
        resolve(res.networkType)
      },
      fail: () => {
        resolve('unknown')
      }
    })
  })
}

// 检查应用更新
const checkUpdate = () => {
  // #ifdef APP-PLUS
  const updateManager = plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
    console.log('当前版本:', widgetInfo.version)
    // 这里可以添加版本检查逻辑
  })
  // #endif
  
  // #ifdef MP-WEIXIN
  const updateManager = uni.getUpdateManager()
  
  updateManager.onCheckForUpdate((res) => {
    console.log('检查更新结果:', res.hasUpdate)
  })
  
  updateManager.onUpdateReady(() => {
    uni.showModal({
      title: '更新提示',
      content: '新版本已经准备好，是否重启应用？',
      success: (res) => {
        if (res.confirm) {
          updateManager.applyUpdate()
        }
      }
    })
  })
  
  updateManager.onUpdateFailed(() => {
    console.log('更新失败')
  })
  // #endif
}

// 监听网络状态变化
uni.onNetworkStatusChange((res) => {
  appStore.setNetworkType(res.networkType)
  appStore.setNetworkConnected(res.isConnected)
  
  if (!res.isConnected) {
    uni.showToast({
      title: '网络连接断开',
      icon: 'error'
    })
  }
})

// 监听内存警告
// #ifdef APP-PLUS
uni.onMemoryWarning(() => {
  console.warn('内存警告，建议清理缓存')
  // 可以在这里清理一些缓存数据
})
// #endif
</script>

<style lang="scss">
/* 全局样式 */
@import '@/styles/index.scss';

/* 应用基础样式 */
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 页面基础样式 */
page {
  background-color: #f5f5f5;
  font-size: 28rpx;
  line-height: 1.6;
}

/* 通用容器 */
.container {
  padding: 20rpx;
}

.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 安全区域适配 */
.safe-area-inset-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 通用工具类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* 边距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: 10rpx; }
.m-2 { margin: 20rpx; }
.m-3 { margin: 30rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 10rpx; }
.p-2 { padding: 20rpx; }
.p-3 { padding: 30rpx; }

/* 颜色工具类 */
.text-primary { color: #2563EB; }
.text-success { color: #10B981; }
.text-warning { color: #F59E0B; }
.text-error { color: #EF4444; }
.text-muted { color: #6B7280; }

.bg-primary { background-color: #2563EB; }
.bg-success { background-color: #10B981; }
.bg-warning { background-color: #F59E0B; }
.bg-error { background-color: #EF4444; }
.bg-white { background-color: #ffffff; }
</style>
