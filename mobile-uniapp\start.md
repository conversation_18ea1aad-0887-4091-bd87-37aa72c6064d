# 🚀 Uni-app 项目启动指南

## 📋 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0
- HBuilderX (可选，推荐用于可视化开发)

## 🛠 安装依赖

```bash
# 进入项目目录
cd mobile-uniapp

# 安装依赖
npm install

# 或使用 yarn
yarn install
```

## 🎯 开发运行

### H5 版本 (推荐先开发)
```bash
# 启动 H5 开发服务器
npm run dev:h5

# 浏览器访问: http://localhost:3000
```

### 微信小程序
```bash
# 编译微信小程序
npm run dev:mp-weixin

# 使用微信开发者工具打开 dist/dev/mp-weixin 目录
```

### 支付宝小程序
```bash
# 编译支付宝小程序
npm run dev:mp-alipay

# 使用支付宝小程序开发者工具打开 dist/dev/mp-alipay 目录
```

### Android App
```bash
# 编译 Android 应用
npm run dev:app-android

# 使用 HBuilderX 打开项目，连接真机或模拟器运行
```

### iOS App
```bash
# 编译 iOS 应用
npm run dev:app-ios

# 使用 HBuilderX 打开项目，连接 iOS 设备或模拟器运行
```

## 📦 构建发布

### H5 构建
```bash
# 构建 H5 生产版本
npm run build:h5

# 构建产物在 dist/build/h5 目录
```

### 小程序构建
```bash
# 构建微信小程序
npm run build:mp-weixin

# 构建支付宝小程序
npm run build:mp-alipay

# 构建产物在对应的 dist/build/ 目录下
```

### App 构建
```bash
# 构建 App
npm run build:app

# 使用 HBuilderX 进行云打包或本地打包
```

## 🔧 开发工具配置

### VSCode 推荐插件
- uni-app-schemas
- uni-app-snippets
- Vetur 或 Volar (Vue 3)
- TypeScript Importer
- SCSS IntelliSense

### HBuilderX 配置
1. 下载并安装 HBuilderX
2. 导入项目: 文件 -> 导入 -> 从本地目录导入
3. 选择项目根目录
4. 配置运行设备和调试环境

## 📱 真机调试

### Android 真机调试
1. 开启 Android 设备的开发者选项和 USB 调试
2. 连接设备到电脑
3. 在 HBuilderX 中选择设备运行

### iOS 真机调试
1. 使用数据线连接 iOS 设备
2. 信任开发者证书
3. 在 HBuilderX 中选择设备运行

## 🌐 API 配置

### 开发环境
- H5: 代理到 `http://localhost:8000/api`
- 小程序/App: 直接请求 `https://your-api-domain.com/api`

### 生产环境
修改 `src/utils/request.ts` 中的 `getBaseURL` 函数:

```typescript
const getBaseURL = () => {
  // 根据不同平台返回对应的 API 地址
  return 'https://your-production-api.com/api'
}
```

## 📋 开发检查清单

### 开发前准备
- [ ] 确认 Node.js 版本 >= 16
- [ ] 安装项目依赖
- [ ] 配置 API 地址
- [ ] 准备开发工具 (VSCode/HBuilderX)

### H5 开发
- [ ] 启动开发服务器
- [ ] 浏览器调试工具
- [ ] 移动端模拟器测试
- [ ] 响应式布局检查

### 小程序开发
- [ ] 下载对应开发者工具
- [ ] 申请小程序账号和 AppID
- [ ] 配置小程序信息
- [ ] 真机预览测试

### App 开发
- [ ] 安装 HBuilderX
- [ ] 配置证书和包名
- [ ] 真机调试环境
- [ ] 性能优化检查

## 🐛 常见问题

### 1. 依赖安装失败
```bash
# 清除缓存重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### 2. H5 开发服务器启动失败
- 检查端口 3000 是否被占用
- 确认 Node.js 版本是否符合要求
- 查看控制台错误信息

### 3. 小程序编译失败
- 检查 AppID 配置
- 确认开发者工具版本
- 查看编译错误日志

### 4. App 真机调试失败
- 确认设备连接状态
- 检查开发者选项设置
- 查看 HBuilderX 控制台日志

## 📞 技术支持

如遇到问题，可以：
1. 查看 [uni-app 官方文档](https://uniapp.dcloud.net.cn/)
2. 搜索 [DCloud 社区](https://ask.dcloud.net.cn/)
3. 查看项目 README 和相关文档

---

现在您可以开始 Uni-app 多端开发之旅了！🎉
