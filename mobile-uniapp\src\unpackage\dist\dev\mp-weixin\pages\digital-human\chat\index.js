"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_modules_digitalHuman = require("../../../store/modules/digitalHuman.js");
require("../../../store/modules/user.js");
if (!Array) {
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  const _component_u_loading_icon = common_vendor.resolveComponent("u-loading-icon");
  const _component_u_button = common_vendor.resolveComponent("u-button");
  const _component_u_modal = common_vendor.resolveComponent("u-modal");
  const _component_u_action_sheet = common_vendor.resolveComponent("u-action-sheet");
  (_component_u_icon + _component_u_loading_icon + _component_u_button + _component_u_modal + _component_u_action_sheet)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props, { expose: __expose }) {
    const digitalHumanStore = store_modules_digitalHuman.useDigitalHumanStore();
    const inputText = common_vendor.ref("");
    const scrollTop = common_vendor.ref(0);
    const playingAudio = common_vendor.ref("");
    const showDigitalHumanPicker = common_vendor.ref(false);
    const showInputOptions = common_vendor.ref(false);
    const showActionSheet = common_vendor.ref(false);
    const hasMoreMessages = common_vendor.ref(true);
    const isLoadingMore = common_vendor.ref(false);
    const {
      digitalHumans,
      currentDigitalHuman,
      currentSession,
      currentSessionMessages,
      isSending,
      isGeneratingVideo
    } = digitalHumanStore;
    const inputOptionActions = common_vendor.ref([
      { name: "拍照", value: "camera" },
      { name: "从相册选择", value: "album" },
      { name: "语音输入", value: "voice" }
    ]);
    const actionSheetActions = common_vendor.ref([
      { name: "切换数字人", value: "switch" },
      { name: "新建对话", value: "new" },
      { name: "对话历史", value: "history" },
      { name: "清空消息", value: "clear" },
      { name: "语音设置", value: "voice_settings" }
    ]);
    common_vendor.onMounted(async () => {
      await digitalHumanStore.loadDigitalHumans();
      if (!currentDigitalHuman.value && digitalHumans.value.length > 0) {
        showDigitalHumanPicker.value = true;
      } else if (currentDigitalHuman.value) {
        await digitalHumanStore.loadChatSessions();
        if (!currentSession.value) {
          await digitalHumanStore.createNewSession();
        } else {
          await digitalHumanStore.loadMessages();
        }
      }
      scrollToBottom();
    });
    common_vendor.watch(
      () => currentSessionMessages.value.length,
      () => {
        common_vendor.nextTick$1(() => {
          scrollToBottom();
        });
      }
    );
    const selectDigitalHuman = async (human) => {
      await digitalHumanStore.selectDigitalHuman(human);
      showDigitalHumanPicker.value = false;
      await digitalHumanStore.createNewSession();
      scrollToBottom();
    };
    const sendMessage = async () => {
      if (!inputText.value.trim() || isSending.value)
        return;
      const message = inputText.value.trim();
      inputText.value = "";
      try {
        await digitalHumanStore.sendTextMessage(message);
        scrollToBottom();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/digital-human/chat/index.vue:345", "发送消息失败:", error);
        common_vendor.index.showToast({
          title: error.message || "发送失败",
          icon: "error"
        });
        inputText.value = message;
      }
    };
    const startVoiceInput = () => {
      common_vendor.index.showToast({
        title: "当前平台暂不支持语音输入",
        icon: "none"
      });
    };
    const toggleAudioPlay = (message) => {
      if (!message.audio_url)
        return;
      if (playingAudio.value === message.id) {
        common_vendor.index.stopBackgroundAudio();
        playingAudio.value = "";
      } else {
        common_vendor.index.playBackgroundAudio({
          dataUrl: message.audio_url,
          title: "数字人语音",
          success: () => {
            playingAudio.value = message.id;
          },
          fail: (error) => {
            common_vendor.index.__f__("error", "at pages/digital-human/chat/index.vue:408", "播放音频失败:", error);
            common_vendor.index.showToast({
              title: "播放失败",
              icon: "error"
            });
          }
        });
      }
    };
    const playTextAudio = async (text) => {
      try {
        common_vendor.index.showToast({
          title: "正在生成语音...",
          icon: "loading"
        });
        setTimeout(() => {
          common_vendor.index.showToast({
            title: "语音播放功能开发中",
            icon: "none"
          });
        }, 1e3);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/digital-human/chat/index.vue:435", "播放文本语音失败:", error);
        common_vendor.index.showToast({
          title: "播放失败",
          icon: "error"
        });
      }
    };
    const generateVideoReply = async (messageId) => {
      try {
        common_vendor.index.showLoading({
          title: "生成视频中..."
        });
        const videoUrl = await digitalHumanStore.generateVideoReply(messageId);
        common_vendor.index.hideLoading();
        if (videoUrl) {
          common_vendor.index.showToast({
            title: "视频生成成功",
            icon: "success"
          });
          scrollToBottom();
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/digital-human/chat/index.vue:465", "生成视频失败:", error);
        common_vendor.index.showToast({
          title: error.message || "生成视频失败",
          icon: "error"
        });
      }
    };
    const onVideoPlay = () => {
      common_vendor.index.__f__("log", "at pages/digital-human/chat/index.vue:475", "视频开始播放");
    };
    const onVideoPause = () => {
      common_vendor.index.__f__("log", "at pages/digital-human/chat/index.vue:479", "视频暂停播放");
    };
    const loadMoreMessages = async () => {
      if (isLoadingMore.value || !hasMoreMessages.value)
        return;
      try {
        isLoadingMore.value = true;
        setTimeout(() => {
          hasMoreMessages.value = false;
          isLoadingMore.value = false;
        }, 1e3);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/digital-human/chat/index.vue:496", "加载更多消息失败:", error);
        isLoadingMore.value = false;
      }
    };
    const scrollToBottom = () => {
      common_vendor.nextTick$1(() => {
        scrollTop.value = 999999;
      });
    };
    const handleInputOption = (action) => {
      showInputOptions.value = false;
      switch (action.value) {
        case "camera":
          chooseImage("camera");
          break;
        case "album":
          chooseImage("album");
          break;
        case "voice":
          startVoiceInput();
          break;
      }
    };
    const chooseImage = (sourceType) => {
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: [sourceType],
        success: (res) => {
          res.tempFilePaths[0];
          common_vendor.index.showToast({
            title: "图片消息功能开发中",
            icon: "none"
          });
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/digital-human/chat/index.vue:539", "选择图片失败:", error);
        }
      });
    };
    const handleActionSheet = async (action) => {
      showActionSheet.value = false;
      switch (action.value) {
        case "switch":
          showDigitalHumanPicker.value = true;
          break;
        case "new":
          await createNewSession();
          break;
        case "history":
          navigateToHistory();
          break;
        case "clear":
          await clearMessages();
          break;
        case "voice_settings":
          navigateToVoiceSettings();
          break;
      }
    };
    const createNewSession = async () => {
      try {
        await digitalHumanStore.createNewSession();
        common_vendor.index.showToast({
          title: "新对话已创建",
          icon: "success"
        });
        scrollToBottom();
      } catch (error) {
        common_vendor.index.showToast({
          title: error.message || "创建失败",
          icon: "error"
        });
      }
    };
    const navigateToHistory = () => {
      common_vendor.index.navigateTo({
        url: "/pages/digital-human/history/index"
      });
    };
    const clearMessages = async () => {
      common_vendor.index.showModal({
        title: "确认清空",
        content: "确定要清空当前对话的所有消息吗？",
        success: async (res) => {
          if (res.confirm) {
            await digitalHumanStore.clearMessages();
          }
        }
      });
    };
    const navigateToVoiceSettings = () => {
      common_vendor.index.navigateTo({
        url: "/pages/digital-human/voice-settings/index"
      });
    };
    const formatTime = (timestamp) => {
      const now = common_vendor.dayjs();
      const time = common_vendor.dayjs(timestamp);
      if (now.diff(time, "day") === 0) {
        return time.format("HH:mm");
      } else if (now.diff(time, "day") === 1) {
        return "昨天 " + time.format("HH:mm");
      } else {
        return time.format("MM-DD HH:mm");
      }
    };
    const formatDuration = (duration) => {
      if (!duration)
        return "0:00";
      const minutes = Math.floor(duration / 60);
      const seconds = Math.floor(duration % 60);
      return `${minutes}:${seconds.toString().padStart(2, "0")}`;
    };
    const onPullDownRefresh = async () => {
      try {
        if (currentSession.value) {
          await digitalHumanStore.loadMessages();
        }
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "刷新失败",
          icon: "error"
        });
      } finally {
        common_vendor.index.stopPullDownRefresh();
      }
    };
    __expose({
      onPullDownRefresh
    });
    return (_ctx, _cache) => {
      var _a;
      return common_vendor.e({
        a: common_vendor.unref(currentDigitalHuman)
      }, common_vendor.unref(currentDigitalHuman) ? {
        b: common_vendor.unref(currentDigitalHuman).avatar || "/static/images/default-avatar.png",
        c: common_vendor.t(common_vendor.unref(currentDigitalHuman).name),
        d: common_vendor.o(($event) => showActionSheet.value = true),
        e: common_vendor.p({
          name: "more-dot-fill",
          size: "20",
          color: "#6B7280"
        })
      } : {}, {
        f: hasMoreMessages.value
      }, hasMoreMessages.value ? common_vendor.e({
        g: isLoadingMore.value
      }, isLoadingMore.value ? {} : {}, {
        h: common_vendor.t(isLoadingMore.value ? "加载中..." : "上拉加载更多")
      }) : {}, {
        i: common_vendor.f(common_vendor.unref(currentSessionMessages), (message, index, i0) => {
          var _a2, _b;
          return common_vendor.e({
            a: message.role === "user"
          }, message.role === "user" ? common_vendor.e({
            b: message.message_type === "text"
          }, message.message_type === "text" ? {
            c: common_vendor.t(message.content)
          } : {}, {
            d: message.message_type === "audio"
          }, message.message_type === "audio" ? {
            e: "90568451-2-" + i0,
            f: common_vendor.p({
              name: "mic-fill",
              size: "16",
              color: "#ffffff"
            }),
            g: common_vendor.t(formatDuration(message.duration)),
            h: common_vendor.o(($event) => toggleAudioPlay(message), message.id),
            i: "90568451-3-" + i0,
            j: common_vendor.p({
              name: playingAudio.value === message.id ? "pause" : "play",
              size: "16",
              color: "#ffffff"
            })
          } : {}, {
            k: common_vendor.t(formatTime(message.timestamp))
          }) : common_vendor.e({
            l: ((_a2 = common_vendor.unref(currentDigitalHuman)) == null ? void 0 : _a2.avatar) || "/static/images/default-avatar.png",
            m: message.message_type === "text"
          }, message.message_type === "text" ? {
            n: common_vendor.t(message.content)
          } : {}, {
            o: message.message_type === "audio"
          }, message.message_type === "audio" ? {
            p: common_vendor.o(($event) => toggleAudioPlay(message), message.id),
            q: "90568451-4-" + i0,
            r: common_vendor.p({
              name: playingAudio.value === message.id ? "pause-circle-fill" : "play-circle-fill",
              size: "32",
              color: "#2563EB"
            }),
            s: common_vendor.t(formatDuration(message.duration))
          } : {}, {
            t: message.message_type === "video"
          }, message.message_type === "video" ? {
            v: message.video_url,
            w: (_b = common_vendor.unref(currentDigitalHuman)) == null ? void 0 : _b.avatar,
            x: common_vendor.o(onVideoPlay, message.id),
            y: common_vendor.o(onVideoPause, message.id)
          } : {}, {
            z: message.message_type === "text"
          }, message.message_type === "text" ? {
            A: common_vendor.o(($event) => playTextAudio(message.content), message.id),
            B: "90568451-5-" + i0,
            C: common_vendor.p({
              name: "volume-up",
              size: "14",
              color: "#6B7280"
            })
          } : {}, {
            D: message.message_type === "text"
          }, message.message_type === "text" ? {
            E: common_vendor.o(($event) => generateVideoReply(message.id), message.id),
            F: "90568451-6-" + i0,
            G: common_vendor.p({
              name: "video",
              size: "14",
              color: "#6B7280",
              ["margin-left"]: "8"
            })
          } : {}, {
            H: common_vendor.t(formatTime(message.timestamp))
          }), {
            I: message.id,
            J: message.role === "user" ? 1 : "",
            K: message.role === "assistant" ? 1 : ""
          });
        }),
        j: common_vendor.unref(isGeneratingVideo)
      }, common_vendor.unref(isGeneratingVideo) ? {
        k: common_vendor.p({
          mode: "flower"
        })
      } : {}, {
        l: common_vendor.unref(isSending)
      }, common_vendor.unref(isSending) ? {
        m: ((_a = common_vendor.unref(currentDigitalHuman)) == null ? void 0 : _a.avatar) || "/static/images/default-avatar.png"
      } : {}, {
        n: scrollTop.value,
        o: common_vendor.o(loadMoreMessages),
        p: common_vendor.o(($event) => showInputOptions.value = true),
        q: common_vendor.p({
          name: "plus",
          size: "24",
          color: "#6B7280"
        }),
        r: common_vendor.unref(isSending),
        s: common_vendor.o(sendMessage),
        t: inputText.value,
        v: common_vendor.o(($event) => inputText.value = $event.detail.value),
        w: !inputText.value.trim()
      }, !inputText.value.trim() ? {
        x: common_vendor.o(startVoiceInput),
        y: common_vendor.p({
          name: "mic",
          size: "24",
          color: "#2563EB"
        })
      } : {
        z: common_vendor.o(sendMessage),
        A: common_vendor.p({
          type: "primary",
          size: "small",
          loading: common_vendor.unref(isSending),
          disabled: !inputText.value.trim()
        })
      }, {
        B: common_vendor.f(common_vendor.unref(digitalHumans), (human, k0, i0) => {
          var _a2, _b, _c;
          return common_vendor.e({
            a: human.avatar,
            b: common_vendor.t(human.name),
            c: common_vendor.t(human.description),
            d: ((_a2 = common_vendor.unref(currentDigitalHuman)) == null ? void 0 : _a2.id) === human.id
          }, ((_b = common_vendor.unref(currentDigitalHuman)) == null ? void 0 : _b.id) === human.id ? {
            e: "90568451-12-" + i0 + ",90568451-11",
            f: common_vendor.p({
              name: "checkmark",
              size: "16",
              color: "#2563EB"
            })
          } : {}, {
            g: human.id,
            h: ((_c = common_vendor.unref(currentDigitalHuman)) == null ? void 0 : _c.id) === human.id ? 1 : "",
            i: common_vendor.o(($event) => selectDigitalHuman(human), human.id)
          });
        }),
        C: common_vendor.o(($event) => showDigitalHumanPicker.value = false),
        D: common_vendor.o(($event) => showDigitalHumanPicker.value = $event),
        E: common_vendor.p({
          title: "选择数字人",
          ["show-cancel-button"]: false,
          ["confirm-text"]: "关闭",
          modelValue: showDigitalHumanPicker.value
        }),
        F: common_vendor.o(handleInputOption),
        G: common_vendor.o(($event) => showInputOptions.value = $event),
        H: common_vendor.p({
          actions: inputOptionActions.value,
          modelValue: showInputOptions.value
        }),
        I: common_vendor.o(handleActionSheet),
        J: common_vendor.o(($event) => showActionSheet.value = $event),
        K: common_vendor.p({
          actions: actionSheetActions.value,
          modelValue: showActionSheet.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-90568451"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/digital-human/chat/index.js.map
