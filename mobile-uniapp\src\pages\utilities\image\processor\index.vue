<template>
  <view class="image-processor-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">图片处理</text>
      <text class="page-desc">图片格式转换、压缩、裁剪、滤镜等功能</text>
    </view>

    <!-- 图片上传区域 -->
    <view class="upload-section">
      <view class="section-title">
        <text class="title-text">选择图片</text>
        <text class="title-desc">支持JPG、PNG、GIF、WebP等格式，最大20MB</text>
      </view>
      
      <view class="upload-area" @click="chooseImage">
        <view class="upload-content" v-if="!selectedImage">
          <u-icon name="image" size="48" color="#2563EB"></u-icon>
          <text class="upload-text">选择图片</text>
          <text class="upload-desc">支持 JPG、PNG、GIF、WebP 格式</text>
        </view>
        
        <view class="image-preview" v-else>
          <image 
            class="preview-image" 
            :src="selectedImage.path" 
            mode="aspectFit"
            @load="onImageLoad"
          ></image>
          <view class="image-info">
            <text class="image-name">{{ selectedImage.name }}</text>
            <text class="image-size">{{ formatFileSize(selectedImage.size) }}</text>
            <text class="image-dimensions" v-if="selectedImage.width && selectedImage.height">
              {{ selectedImage.width }} × {{ selectedImage.height }}
            </text>
          </view>
          <view class="image-actions">
            <u-icon name="close-circle" size="20" color="#EF4444" @click.stop="removeImage"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 处理工具选择 -->
    <view class="tools-section" v-if="selectedImage">
      <view class="section-title">
        <text class="title-text">选择处理工具</text>
      </view>
      
      <view class="tools-grid">
        <view 
          class="tool-item"
          v-for="tool in processingTools"
          :key="tool.id"
          :class="{ active: selectedTool === tool.id }"
          @click="selectTool(tool.id)"
        >
          <view class="tool-icon" :style="{ backgroundColor: tool.color }">
            <u-icon :name="tool.icon" color="#fff" size="20"></u-icon>
          </view>
          <text class="tool-name">{{ tool.name }}</text>
          <text class="tool-desc">{{ tool.description }}</text>
        </view>
      </view>
    </view>

    <!-- 工具选项 -->
    <view class="tool-options" v-if="selectedImage && selectedTool">
      <view class="section-title">
        <text class="title-text">{{ getToolName(selectedTool) }}选项</text>
      </view>
      
      <view class="options-content">
        <!-- 格式转换选项 -->
        <view class="option-group" v-if="selectedTool === 'convert'">
          <text class="option-label">输出格式</text>
          <view class="format-options">
            <view 
              class="format-item"
              v-for="format in outputFormats"
              :key="format.value"
              :class="{ active: convertOptions.format === format.value }"
              @click="convertOptions.format = format.value"
            >
              <text class="format-text">{{ format.label }}</text>
            </view>
          </view>
        </view>

        <!-- 压缩选项 -->
        <view class="option-group" v-if="selectedTool === 'compress'">
          <text class="option-label">压缩质量: {{ compressOptions.quality }}%</text>
          <u-slider 
            v-model="compressOptions.quality" 
            :min="10" 
            :max="100" 
            :step="10"
            active-color="#2563EB"
          ></u-slider>
          
          <text class="option-label">最大尺寸</text>
          <u-radio-group v-model="compressOptions.maxSize">
            <u-radio name="1024" label="1024KB"></u-radio>
            <u-radio name="512" label="512KB"></u-radio>
            <u-radio name="256" label="256KB"></u-radio>
            <u-radio name="custom" label="自定义"></u-radio>
          </u-radio-group>
          
          <input 
            v-if="compressOptions.maxSize === 'custom'"
            class="custom-input"
            v-model="compressOptions.customSize"
            placeholder="输入大小(KB)"
            type="number"
          />
        </view>

        <!-- 裁剪选项 -->
        <view class="option-group" v-if="selectedTool === 'crop'">
          <text class="option-label">裁剪比例</text>
          <u-radio-group v-model="cropOptions.ratio">
            <u-radio name="free" label="自由裁剪"></u-radio>
            <u-radio name="1:1" label="1:1 正方形"></u-radio>
            <u-radio name="4:3" label="4:3"></u-radio>
            <u-radio name="16:9" label="16:9"></u-radio>
          </u-radio-group>
          
          <text class="option-label">输出尺寸</text>
          <view class="size-inputs">
            <input 
              class="size-input"
              v-model="cropOptions.width"
              placeholder="宽度"
              type="number"
            />
            <text class="size-separator">×</text>
            <input 
              class="size-input"
              v-model="cropOptions.height"
              placeholder="高度"
              type="number"
            />
          </view>
        </view>

        <!-- 滤镜选项 -->
        <view class="option-group" v-if="selectedTool === 'filter'">
          <text class="option-label">选择滤镜</text>
          <view class="filter-options">
            <view 
              class="filter-item"
              v-for="filter in filterOptions"
              :key="filter.value"
              :class="{ active: selectedFilter === filter.value }"
              @click="selectedFilter = filter.value"
            >
              <view class="filter-preview" :style="{ filter: filter.css }">
                <image class="filter-image" :src="selectedImage.path" mode="aspectFill"></image>
              </view>
              <text class="filter-name">{{ filter.name }}</text>
            </view>
          </view>
        </view>

        <!-- 调整选项 -->
        <view class="option-group" v-if="selectedTool === 'adjust'">
          <view class="adjust-control">
            <text class="control-label">亮度: {{ adjustOptions.brightness }}</text>
            <u-slider 
              v-model="adjustOptions.brightness" 
              :min="-100" 
              :max="100" 
              active-color="#2563EB"
            ></u-slider>
          </view>
          
          <view class="adjust-control">
            <text class="control-label">对比度: {{ adjustOptions.contrast }}</text>
            <u-slider 
              v-model="adjustOptions.contrast" 
              :min="-100" 
              :max="100" 
              active-color="#2563EB"
            ></u-slider>
          </view>
          
          <view class="adjust-control">
            <text class="control-label">饱和度: {{ adjustOptions.saturation }}</text>
            <u-slider 
              v-model="adjustOptions.saturation" 
              :min="-100" 
              :max="100" 
              active-color="#2563EB"
            ></u-slider>
          </view>
        </view>
      </view>
      
      <view class="process-button">
        <u-button 
          type="primary" 
          size="large"
          :loading="isProcessing"
          :disabled="!canProcess"
          @click="startProcessing"
          :custom-style="{ width: '100%' }"
        >
          {{ isProcessing ? '处理中...' : '开始处理' }}
        </u-button>
      </view>
    </view>

    <!-- 处理进度 -->
    <view class="processing-progress" v-if="isProcessing">
      <view class="progress-header">
        <text class="progress-title">正在处理图片</text>
        <text class="progress-desc">{{ progressStatus }}</text>
      </view>
      
      <view class="progress-bar">
        <u-line-progress 
          :percent="processingProgress" 
          :show-percent="true"
          active-color="#2563EB"
        ></u-line-progress>
      </view>
    </view>

    <!-- 处理结果 -->
    <view class="processing-result" v-if="processingResult">
      <view class="section-title">
        <text class="title-text">处理完成</text>
        <view class="result-actions">
          <u-icon name="download" size="18" color="#2563EB" @click="downloadResult"></u-icon>
          <u-icon name="share" size="18" color="#2563EB" margin-left="12" @click="shareResult"></u-icon>
        </view>
      </view>
      
      <view class="result-content">
        <view class="result-comparison">
          <view class="comparison-item">
            <text class="comparison-label">原图</text>
            <image class="comparison-image" :src="selectedImage.path" mode="aspectFit"></image>
            <text class="comparison-info">{{ formatFileSize(selectedImage.size) }}</text>
          </view>
          
          <view class="comparison-arrow">
            <u-icon name="arrow-right" size="20" color="#6B7280"></u-icon>
          </view>
          
          <view class="comparison-item">
            <text class="comparison-label">处理后</text>
            <image class="comparison-image" :src="processingResult.imageUrl" mode="aspectFit"></image>
            <text class="comparison-info">{{ formatFileSize(processingResult.size) }}</text>
          </view>
        </view>
        
        <view class="result-stats">
          <text class="stat-item">处理工具: {{ getToolName(selectedTool) }}</text>
          <text class="stat-item">文件大小: {{ formatFileSize(processingResult.size) }}</text>
          <text class="stat-item">压缩比: {{ calculateCompressionRatio() }}%</text>
        </view>
      </view>
    </view>

    <!-- 处理历史 -->
    <view class="processing-history" v-if="imageHistory.length > 0">
      <view class="section-title">
        <text class="title-text">处理历史</text>
        <u-button 
          type="text" 
          text="清空" 
          size="small"
          @click="clearHistory"
          :custom-style="{ color: '#EF4444', fontSize: '24rpx' }"
        ></u-button>
      </view>
      
      <view class="history-list">
        <view 
          class="history-item"
          v-for="item in imageHistory.slice(0, 5)"
          :key="item.id"
          @click="viewHistoryItem(item)"
        >
          <image class="history-thumbnail" :src="item.thumbnail" mode="aspectFill"></image>
          <view class="history-content">
            <text class="history-title">{{ item.filename }}</text>
            <text class="history-desc">{{ getToolName(item.tool) }} - {{ item.format }}</text>
            <text class="history-time">{{ formatTime(item.createdAt) }}</text>
          </view>
          <view class="history-actions">
            <u-icon name="download" size="16" color="#6B7280"></u-icon>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import dayjs from 'dayjs'

// 响应式数据
const selectedImage = ref<any>(null)
const selectedTool = ref<string>('')
const selectedFilter = ref<string>('none')
const isProcessing = ref<boolean>(false)
const processingProgress = ref<number>(0)
const progressStatus = ref<string>('')
const processingResult = ref<any>(null)
const imageHistory = ref<any[]>([])

// 处理选项
const convertOptions = ref({
  format: 'jpg'
})

const compressOptions = ref({
  quality: 80,
  maxSize: '1024',
  customSize: ''
})

const cropOptions = ref({
  ratio: 'free',
  width: '',
  height: ''
})

const adjustOptions = ref({
  brightness: 0,
  contrast: 0,
  saturation: 0
})

// 处理工具
const processingTools = ref([
  {
    id: 'convert',
    name: '格式转换',
    description: '转换图片格式',
    icon: 'refresh',
    color: '#2563EB'
  },
  {
    id: 'compress',
    name: '图片压缩',
    description: '减小文件大小',
    icon: 'archive',
    color: '#10B981'
  },
  {
    id: 'crop',
    name: '裁剪调整',
    description: '裁剪和调整尺寸',
    icon: 'crop',
    color: '#F59E0B'
  },
  {
    id: 'filter',
    name: '滤镜效果',
    description: '添加滤镜效果',
    icon: 'color-filter',
    color: '#8B5CF6'
  },
  {
    id: 'adjust',
    name: '色彩调整',
    description: '调整亮度对比度',
    icon: 'tune',
    color: '#EF4444'
  }
])

// 输出格式
const outputFormats = ref([
  { label: 'JPG', value: 'jpg' },
  { label: 'PNG', value: 'png' },
  { label: 'WebP', value: 'webp' },
  { label: 'GIF', value: 'gif' }
])

// 滤镜选项
const filterOptions = ref([
  { name: '无滤镜', value: 'none', css: 'none' },
  { name: '黑白', value: 'grayscale', css: 'grayscale(100%)' },
  { name: '复古', value: 'sepia', css: 'sepia(100%)' },
  { name: '模糊', value: 'blur', css: 'blur(2px)' },
  { name: '高对比', value: 'contrast', css: 'contrast(150%)' },
  { name: '高饱和', value: 'saturate', css: 'saturate(150%)' }
])

// 计算属性
const canProcess = computed(() => {
  return selectedImage.value && selectedTool.value
})

// 页面加载
onMounted(() => {
  loadImageHistory()
})

// 选择图片
const chooseImage = () => {
  uni.chooseImage({
    count: 1,
    sourceType: ['album', 'camera'],
    success: (res) => {
      const filePath = res.tempFilePaths[0]

      // 获取图片信息
      uni.getImageInfo({
        src: filePath,
        success: (info) => {
          selectedImage.value = {
            name: `image_${Date.now()}.jpg`,
            path: filePath,
            size: info.size || 0,
            width: info.width,
            height: info.height,
            type: info.type || 'jpg'
          }

          // 重置选项
          selectedTool.value = ''
          processingResult.value = null
        },
        fail: (error) => {
          console.error('获取图片信息失败:', error)
          uni.showToast({
            title: '获取图片信息失败',
            icon: 'error'
          })
        }
      })
    },
    fail: (error) => {
      console.error('选择图片失败:', error)
    }
  })
}

// 图片加载完成
const onImageLoad = (e: any) => {
  console.log('图片加载完成:', e)
}

// 移除图片
const removeImage = () => {
  selectedImage.value = null
  selectedTool.value = ''
  processingResult.value = null
}

// 选择工具
const selectTool = (toolId: string) => {
  selectedTool.value = toolId
  processingResult.value = null
}

// 开始处理
const startProcessing = async () => {
  if (!canProcess.value) return

  try {
    isProcessing.value = true
    processingProgress.value = 0
    progressStatus.value = '正在处理图片...'

    // 模拟处理进度
    const progressInterval = setInterval(() => {
      if (processingProgress.value < 90) {
        processingProgress.value += Math.random() * 10

        if (processingProgress.value < 30) {
          progressStatus.value = '正在分析图片...'
        } else if (processingProgress.value < 60) {
          progressStatus.value = '正在应用处理...'
        } else {
          progressStatus.value = '正在生成结果...'
        }
      }
    }, 200)

    // 模拟处理时间
    await new Promise(resolve => setTimeout(resolve, 3000))

    clearInterval(progressInterval)
    processingProgress.value = 100
    progressStatus.value = '处理完成'

    // 生成模拟结果
    const mockResult = {
      imageUrl: selectedImage.value.path, // 实际应该是处理后的图片URL
      size: calculateProcessedSize(),
      format: getOutputFormat(),
      filename: generateResultFilename()
    }

    processingResult.value = mockResult

    // 添加到历史记录
    const historyItem = {
      id: Date.now().toString(),
      filename: selectedImage.value.name,
      thumbnail: selectedImage.value.path,
      tool: selectedTool.value,
      format: mockResult.format,
      originalSize: selectedImage.value.size,
      processedSize: mockResult.size,
      createdAt: new Date().toISOString()
    }

    imageHistory.value.unshift(historyItem)
    saveImageHistory()

    uni.showToast({
      title: '处理完成',
      icon: 'success'
    })

  } catch (error) {
    console.error('处理失败:', error)
    uni.showToast({
      title: '处理失败',
      icon: 'error'
    })
  } finally {
    isProcessing.value = false
  }
}

// 计算处理后文件大小
const calculateProcessedSize = () => {
  const originalSize = selectedImage.value.size

  switch (selectedTool.value) {
    case 'compress':
      const quality = compressOptions.value.quality / 100
      return Math.floor(originalSize * quality)
    case 'convert':
      // 不同格式的大小差异
      const formatMultiplier = {
        jpg: 0.8,
        png: 1.2,
        webp: 0.6,
        gif: 1.0
      }
      return Math.floor(originalSize * (formatMultiplier[convertOptions.value.format as keyof typeof formatMultiplier] || 1))
    default:
      return originalSize
  }
}

// 获取输出格式
const getOutputFormat = () => {
  switch (selectedTool.value) {
    case 'convert':
      return convertOptions.value.format.toUpperCase()
    default:
      return selectedImage.value.type.toUpperCase()
  }
}

// 生成结果文件名
const generateResultFilename = () => {
  const baseName = selectedImage.value.name.split('.')[0]
  const toolName = getToolName(selectedTool.value)
  const format = getOutputFormat().toLowerCase()

  return `${baseName}_${toolName}.${format}`
}

// 获取工具名称
const getToolName = (toolId: string) => {
  const tool = processingTools.value.find(t => t.id === toolId)
  return tool?.name || '未知工具'
}

// 计算压缩比
const calculateCompressionRatio = () => {
  if (!processingResult.value || !selectedImage.value) return 0

  const originalSize = selectedImage.value.size
  const processedSize = processingResult.value.size

  return Math.round(((originalSize - processedSize) / originalSize) * 100)
}

// 下载结果
const downloadResult = () => {
  if (!processingResult.value) return

  uni.showToast({
    title: '开始下载',
    icon: 'success'
  })
}

// 分享结果
const shareResult = () => {
  if (!processingResult.value) return

  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 1,
    imageUrl: processingResult.value.imageUrl,
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    }
  })
}

// 工具函数
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

// 历史记录相关
const saveImageHistory = () => {
  uni.setStorageSync('image_processing_history', imageHistory.value)
}

const loadImageHistory = () => {
  const history = uni.getStorageSync('image_processing_history') || []
  imageHistory.value = history
}

const clearHistory = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有处理历史吗？',
    success: (res) => {
      if (res.confirm) {
        imageHistory.value = []
        saveImageHistory()
        uni.showToast({
          title: '清空成功',
          icon: 'success'
        })
      }
    }
  })
}

const viewHistoryItem = (item: any) => {
  uni.showToast({
    title: '查看历史记录',
    icon: 'none'
  })
}

// 页面下拉刷新
const onPullDownRefresh = () => {
  loadImageHistory()
  uni.stopPullDownRefresh()
}

// 导出方法供页面使用
defineExpose({
  onPullDownRefresh
})
</script>

<style lang="scss" scoped>
.image-processor-page {
  min-height: 100vh;
  background-color: $bg-secondary;
}

// 页面头部
.page-header {
  background-color: $bg-primary;
  padding: $spacing-6 $spacing-4;
  text-align: center;
  border-bottom: 1px solid $border-primary;

  .page-title {
    display: block;
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: $spacing-2;
  }

  .page-desc {
    font-size: $font-size-base;
    color: $text-secondary;
  }
}

// 通用区块标题
.section-title {
  padding: $spacing-4 $spacing-4 $spacing-3;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title-text {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
  }

  .title-desc {
    font-size: $font-size-sm;
    color: $text-tertiary;
  }

  .result-actions {
    display: flex;
    align-items: center;
    gap: $spacing-2;
  }
}

// 图片上传区域
.upload-section {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .upload-area {
    margin: 0 $spacing-4 $spacing-4;
    border: 2px dashed $border-primary;
    border-radius: $radius-lg;
    padding: $spacing-6;

    .upload-content {
      text-align: center;

      .upload-text {
        display: block;
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin: $spacing-4 0 $spacing-2;
      }

      .upload-desc {
        font-size: $font-size-base;
        color: $text-secondary;
      }
    }

    .image-preview {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: $spacing-3;

      .preview-image {
        width: 100%;
        max-height: 400rpx;
        border-radius: $radius-lg;
      }

      .image-info {
        text-align: center;

        .image-name {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-1;
        }

        .image-size,
        .image-dimensions {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
        }
      }

      .image-actions {
        display: flex;
        justify-content: center;
      }
    }
  }
}

// 工具选择区域
.tools-section {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .tools-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-3;
    padding: 0 $spacing-4 $spacing-4;

    .tool-item {
      background-color: $bg-tertiary;
      border-radius: $radius-lg;
      padding: $spacing-4;
      text-align: center;
      border: 2px solid transparent;

      &.active {
        border-color: $primary;
        background-color: $primary-50;
      }

      .tool-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: $radius-2xl;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto $spacing-3;
      }

      .tool-name {
        display: block;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-1;
      }

      .tool-desc {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }
  }
}

// 工具选项
.tool-options {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .options-content {
    padding: 0 $spacing-4;

    .option-group {
      margin-bottom: $spacing-6;

      .option-label {
        display: block;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-3;
      }

      .format-options {
        display: flex;
        gap: $spacing-2;

        .format-item {
          flex: 1;
          background-color: $bg-tertiary;
          border: 1px solid $border-primary;
          border-radius: $radius-md;
          padding: $spacing-3;
          text-align: center;

          &.active {
            border-color: $primary;
            background-color: $primary-50;
          }

          .format-text {
            font-size: $font-size-sm;
            color: $text-primary;
          }
        }
      }

      .custom-input {
        width: 100%;
        margin-top: $spacing-2;
        padding: $spacing-2 $spacing-3;
        border: 1px solid $border-primary;
        border-radius: $radius-md;
        font-size: $font-size-base;
        color: $text-primary;
        background-color: $bg-tertiary;
      }

      .size-inputs {
        display: flex;
        align-items: center;
        gap: $spacing-2;

        .size-input {
          flex: 1;
          padding: $spacing-2 $spacing-3;
          border: 1px solid $border-primary;
          border-radius: $radius-md;
          font-size: $font-size-base;
          color: $text-primary;
          background-color: $bg-tertiary;
        }

        .size-separator {
          font-size: $font-size-base;
          color: $text-tertiary;
        }
      }

      .filter-options {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: $spacing-3;

        .filter-item {
          text-align: center;

          &.active .filter-preview {
            border-color: $primary;
          }

          .filter-preview {
            width: 100%;
            height: 120rpx;
            border-radius: $radius-md;
            border: 2px solid transparent;
            overflow: hidden;
            margin-bottom: $spacing-2;

            .filter-image {
              width: 100%;
              height: 100%;
            }
          }

          .filter-name {
            font-size: $font-size-xs;
            color: $text-secondary;
          }
        }
      }

      .adjust-control {
        margin-bottom: $spacing-4;

        .control-label {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-2;
        }
      }
    }
  }

  .process-button {
    padding: 0 $spacing-4 $spacing-4;
  }
}

// 处理进度
.processing-progress {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;
  padding: $spacing-4;

  .progress-header {
    text-align: center;
    margin-bottom: $spacing-4;

    .progress-title {
      display: block;
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-primary;
      margin-bottom: $spacing-1;
    }

    .progress-desc {
      font-size: $font-size-sm;
      color: $text-secondary;
    }
  }
}

// 处理结果
.processing-result {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .result-content {
    padding: 0 $spacing-4 $spacing-4;

    .result-comparison {
      display: flex;
      align-items: center;
      gap: $spacing-3;
      margin-bottom: $spacing-4;

      .comparison-item {
        flex: 1;
        text-align: center;

        .comparison-label {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-2;
        }

        .comparison-image {
          width: 100%;
          height: 200rpx;
          border-radius: $radius-md;
          background-color: $bg-tertiary;
          margin-bottom: $spacing-2;
        }

        .comparison-info {
          font-size: $font-size-xs;
          color: $text-tertiary;
        }
      }

      .comparison-arrow {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .result-stats {
      background-color: $bg-tertiary;
      border-radius: $radius-lg;
      padding: $spacing-4;

      .stat-item {
        display: block;
        font-size: $font-size-sm;
        color: $text-secondary;
        margin-bottom: $spacing-1;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 处理历史
.processing-history {
  background-color: $bg-primary;

  .history-list {
    padding: 0 $spacing-4 $spacing-4;

    .history-item {
      display: flex;
      align-items: center;
      gap: $spacing-3;
      padding: $spacing-3;
      background-color: $bg-tertiary;
      border-radius: $radius-lg;
      margin-bottom: $spacing-2;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        background-color: $gray-200;
      }

      .history-thumbnail {
        width: 80rpx;
        height: 80rpx;
        border-radius: $radius-md;
        background-color: $bg-quaternary;
      }

      .history-content {
        flex: 1;

        .history-title {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .history-desc {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-1;
        }

        .history-time {
          font-size: $font-size-xs;
          color: $text-tertiary;
        }
      }

      .history-actions {
        padding: $spacing-2;
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 480px) {
  .tools-grid {
    grid-template-columns: 1fr;
  }

  .format-options {
    flex-direction: column;
  }

  .filter-options {
    grid-template-columns: repeat(2, 1fr);
  }

  .result-comparison {
    flex-direction: column;

    .comparison-arrow {
      transform: rotate(90deg);
    }
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .image-processor-page {
    background-color: #1a1a1a;
  }

  .page-header,
  .upload-section,
  .tools-section,
  .tool-options,
  .processing-progress,
  .processing-result,
  .processing-history {
    background-color: #2d2d2d;
    border-color: #404040;
  }

  .upload-area,
  .tool-item,
  .format-item,
  .result-stats,
  .history-item {
    background-color: #404040;
    border-color: #555555;
  }

  .tool-item.active,
  .format-item.active {
    background-color: #1e3a8a;
    border-color: #3b82f6;
  }

  .custom-input,
  .size-input {
    background-color: #555555;
    border-color: #666666;
    color: #ffffff;
  }

  .comparison-image,
  .history-thumbnail {
    background-color: #555555;
  }

  .filter-image {
    opacity: 0.8;
  }
}
</style>
