<view class="translation-page data-v-0df80e11"><view class="language-bar data-v-0df80e11"><view class="language-selector data-v-0df80e11"><u-button wx:if="{{c}}" class="data-v-0df80e11" u-s="{{['d']}}" bindclick="{{b}}" u-i="0df80e11-0" bind:__l="__l" u-p="{{c}}"><u-icon wx:if="{{a}}" class="data-v-0df80e11" u-i="0df80e11-1,0df80e11-0" bind:__l="__l" u-p="{{a}}"></u-icon></u-button></view><view class="swap-button data-v-0df80e11" bindtap="{{e}}"><u-icon wx:if="{{d}}" class="data-v-0df80e11" u-i="0df80e11-2" bind:__l="__l" u-p="{{d}}"></u-icon></view><view class="language-selector data-v-0df80e11"><u-button wx:if="{{h}}" class="data-v-0df80e11" u-s="{{['d']}}" bindclick="{{g}}" u-i="0df80e11-3" bind:__l="__l" u-p="{{h}}"><u-icon wx:if="{{f}}" class="data-v-0df80e11" u-i="0df80e11-4,0df80e11-3" bind:__l="__l" u-p="{{f}}"></u-icon></u-button></view></view><view wx:if="{{i}}" class="quick-pairs data-v-0df80e11"><scroll-view class="pairs-scroll data-v-0df80e11" scroll-x="true" show-scrollbar="false"><view class="pairs-list data-v-0df80e11"><view wx:for="{{j}}" wx:for-item="pair" wx:key="c" class="pair-item data-v-0df80e11" bindtap="{{pair.d}}"><text class="pair-text data-v-0df80e11">{{pair.a}} → {{pair.b}}</text></view></view></scroll-view></view><view class="translation-container data-v-0df80e11"><view class="input-section data-v-0df80e11"><view class="section-header data-v-0df80e11"><text class="section-title data-v-0df80e11">{{k}}</text><view class="header-actions data-v-0df80e11"><u-icon wx:if="{{l}}" class="data-v-0df80e11" bindclick="{{m}}" u-i="0df80e11-5" bind:__l="__l" u-p="{{n}}"></u-icon><u-icon wx:if="{{p}}" class="data-v-0df80e11" bindclick="{{o}}" u-i="0df80e11-6" bind:__l="__l" u-p="{{p}}"></u-icon></view></view><block wx:if="{{r0}}"><textarea class="text-input data-v-0df80e11" placeholder="请输入要翻译的文本..." maxlength="{{5000}}" auto-height bindinput="{{q}}" bindfocus="{{r}}" bindblur="{{s}}" value="{{t}}"></textarea></block><view class="input-footer data-v-0df80e11"><text class="char-count data-v-0df80e11">{{v}}/5000</text><u-button wx:if="{{y}}" class="data-v-0df80e11" u-s="{{['d']}}" bindclick="{{x}}" u-i="0df80e11-7" bind:__l="__l" u-p="{{y}}">{{w}}</u-button></view></view><view wx:if="{{z}}" class="result-section data-v-0df80e11"><view class="section-header data-v-0df80e11"><text class="section-title data-v-0df80e11">{{A}}</text><view class="header-actions data-v-0df80e11"><u-icon wx:if="{{C}}" class="data-v-0df80e11" bindclick="{{B}}" u-i="0df80e11-8" bind:__l="__l" u-p="{{C}}"></u-icon><u-icon wx:if="{{E}}" class="data-v-0df80e11" bindclick="{{D}}" u-i="0df80e11-9" bind:__l="__l" u-p="{{E}}"></u-icon><u-icon wx:if="{{G}}" class="data-v-0df80e11" bindclick="{{F}}" u-i="0df80e11-10" bind:__l="__l" u-p="{{G}}"></u-icon></view></view><view class="result-content data-v-0df80e11"><text class="result-text data-v-0df80e11" selectable>{{H}}</text></view><view class="result-footer data-v-0df80e11"><text class="result-info data-v-0df80e11"> 置信度: {{I}}% | 字数: {{J}}</text></view></view></view><view wx:if="{{K}}" class="history-section data-v-0df80e11"><view class="section-header data-v-0df80e11"><text class="section-title data-v-0df80e11">翻译历史</text><u-button wx:if="{{M}}" class="data-v-0df80e11" bindclick="{{L}}" u-i="0df80e11-11" bind:__l="__l" u-p="{{M}}"></u-button></view><view class="history-list data-v-0df80e11"><view wx:for="{{N}}" wx:for-item="item" wx:key="f" class="history-item data-v-0df80e11" bindtap="{{item.g}}"><view class="history-content data-v-0df80e11"><text class="history-source data-v-0df80e11">{{item.a}}</text><text class="history-target data-v-0df80e11">{{item.b}}</text></view><view class="history-meta data-v-0df80e11"><text class="history-langs data-v-0df80e11">{{item.c}} → {{item.d}}</text><text class="history-time data-v-0df80e11">{{item.e}}</text></view></view></view></view><u-picker wx:if="{{R}}" class="data-v-0df80e11" bindconfirm="{{O}}" bindcancel="{{P}}" u-i="0df80e11-12" bind:__l="__l" bindupdateModelValue="{{Q}}" u-p="{{R}}"></u-picker><u-picker wx:if="{{V}}" class="data-v-0df80e11" bindconfirm="{{S}}" bindcancel="{{T}}" u-i="0df80e11-13" bind:__l="__l" bindupdateModelValue="{{U}}" u-p="{{V}}"></u-picker><u-modal wx:if="{{ab}}" class="data-v-0df80e11" u-s="{{['d']}}" bindcancel="{{Y}}" bindconfirm="{{Z}}" u-i="0df80e11-14" bind:__l="__l" bindupdateModelValue="{{aa}}" u-p="{{ab}}"><view class="history-modal data-v-0df80e11"><scroll-view class="history-scroll data-v-0df80e11" scroll-y="true" style="height:600rpx"><view wx:for="{{W}}" wx:for-item="item" wx:key="h" class="history-modal-item data-v-0df80e11" bindtap="{{item.i}}"><view class="modal-item-content data-v-0df80e11"><text class="modal-source data-v-0df80e11">{{item.a}}</text><text class="modal-target data-v-0df80e11">{{item.b}}</text><view class="modal-meta data-v-0df80e11"><text class="modal-langs data-v-0df80e11">{{item.c}} → {{item.d}}</text><text class="modal-time data-v-0df80e11">{{item.e}}</text></view></view><u-icon wx:if="{{X}}" class="data-v-0df80e11" catchclick="{{item.f}}" u-i="{{item.g}}" bind:__l="__l" u-p="{{X}}"></u-icon></view></scroll-view></view></u-modal></view>