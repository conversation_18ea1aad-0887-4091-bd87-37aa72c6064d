"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_modules_translation = require("../../../store/modules/translation.js");
if (!Array) {
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  const _component_u_button = common_vendor.resolveComponent("u-button");
  const _component_u_radio = common_vendor.resolveComponent("u-radio");
  const _component_u_radio_group = common_vendor.resolveComponent("u-radio-group");
  const _component_u_switch = common_vendor.resolveComponent("u-switch");
  const _component_u_line_progress = common_vendor.resolveComponent("u-line-progress");
  const _component_u_picker = common_vendor.resolveComponent("u-picker");
  (_component_u_icon + _component_u_button + _component_u_radio + _component_u_radio_group + _component_u_switch + _component_u_line_progress + _component_u_picker)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props, { expose: __expose }) {
    const translationStore = store_modules_translation.useTranslationStore();
    const selectedDocument = common_vendor.ref(null);
    const isTranslating = common_vendor.ref(false);
    const translationProgress = common_vendor.ref(0);
    const progressStatus = common_vendor.ref("");
    const processedPages = common_vendor.ref(0);
    const totalPages = common_vendor.ref(0);
    const estimatedTime = common_vendor.ref("--:--");
    const translationResult = common_vendor.ref(null);
    const documentHistory = common_vendor.ref([]);
    const showSourceLanguagePicker = common_vendor.ref(false);
    const showTargetLanguagePicker = common_vendor.ref(false);
    const translationMode = common_vendor.ref("full");
    const pageRange = common_vendor.ref("");
    const keepFormat = common_vendor.ref(true);
    const outputFormat = common_vendor.ref("same");
    const translationQuality = common_vendor.ref("balanced");
    const {
      languages,
      currentSourceLang,
      currentTargetLang,
      sourceLanguage,
      targetLanguage
    } = translationStore;
    const canTranslate = common_vendor.computed(() => {
      return selectedDocument.value && currentSourceLang.value && currentTargetLang.value;
    });
    const sourceLanguageColumns = common_vendor.computed(() => {
      const autoDetect = [{ text: "自动检测", value: "auto" }];
      const languageOptions = languages.map((lang) => ({
        text: lang.name,
        value: lang.code
      }));
      return [autoDetect.concat(languageOptions)];
    });
    const targetLanguageColumns = common_vendor.computed(() => {
      const languageOptions = languages.filter((lang) => lang.code !== "auto").map((lang) => ({
        text: lang.name,
        value: lang.code
      }));
      return [languageOptions];
    });
    common_vendor.onMounted(async () => {
      await translationStore.loadLanguages();
      loadDocumentHistory();
    });
    const chooseDocument = () => {
      common_vendor.index.chooseFile({
        count: 1,
        type: "file",
        extension: [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"],
        success: (res) => {
          const file = res.tempFiles[0];
          handleSelectedFile(file);
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/translation/document/index.vue:360", "选择文件失败:", error);
        }
      });
    };
    const handleSelectedFile = (file) => {
      if (file.size > 100 * 1024 * 1024) {
        common_vendor.index.showToast({
          title: "文件大小不能超过100MB",
          icon: "error"
        });
        return;
      }
      const fileExtension = getFileExtension(file.name);
      selectedDocument.value = {
        name: file.name,
        size: file.size,
        type: fileExtension,
        path: file.path || URL.createObjectURL(file),
        pages: getEstimatedPages(fileExtension, file.size)
      };
      translationResult.value = null;
    };
    const getFileExtension = (filename) => {
      var _a;
      return ((_a = filename.split(".").pop()) == null ? void 0 : _a.toLowerCase()) || "";
    };
    const getEstimatedPages = (fileType, fileSize) => {
      const avgPageSize = {
        pdf: 100 * 1024,
        // 100KB per page
        doc: 50 * 1024,
        // 50KB per page
        docx: 30 * 1024,
        // 30KB per page
        xls: 20 * 1024,
        // 20KB per page
        xlsx: 15 * 1024,
        // 15KB per page
        ppt: 200 * 1024,
        // 200KB per page
        pptx: 150 * 1024
        // 150KB per page
      };
      const avgSize = avgPageSize[fileType] || 50 * 1024;
      return Math.max(1, Math.ceil(fileSize / avgSize));
    };
    const getFileIcon = (fileType) => {
      const iconMap = {
        pdf: "file-pdf",
        doc: "file-word",
        docx: "file-word",
        xls: "file-excel",
        xlsx: "file-excel",
        ppt: "file-powerpoint",
        pptx: "file-powerpoint"
      };
      return iconMap[fileType] || "file-document";
    };
    const getFileColor = (fileType) => {
      const colorMap = {
        pdf: "#EF4444",
        doc: "#2563EB",
        docx: "#2563EB",
        xls: "#10B981",
        xlsx: "#10B981",
        ppt: "#F59E0B",
        pptx: "#F59E0B"
      };
      return colorMap[fileType] || "#6B7280";
    };
    const removeDocument = () => {
      selectedDocument.value = null;
      translationResult.value = null;
    };
    const previewDocument = () => {
      if (!selectedDocument.value)
        return;
      common_vendor.index.showToast({
        title: "预览功能开发中",
        icon: "none"
      });
    };
    const startTranslation = async () => {
      if (!canTranslate.value)
        return;
      try {
        isTranslating.value = true;
        translationProgress.value = 0;
        processedPages.value = 0;
        totalPages.value = selectedDocument.value.pages || 1;
        const totalSteps = totalPages.value;
        const stepDuration = 2e3;
        for (let i = 0; i < totalSteps; i++) {
          progressStatus.value = `正在翻译第 ${i + 1} 页...`;
          processedPages.value = i + 1;
          const remainingPages = totalSteps - (i + 1);
          const remainingSeconds = remainingPages * (stepDuration / 1e3);
          estimatedTime.value = formatDuration(remainingSeconds);
          await new Promise((resolve) => {
            const interval = setInterval(() => {
              translationProgress.value += 2;
              if (translationProgress.value >= (i + 1) / totalSteps * 100) {
                clearInterval(interval);
                resolve(true);
              }
            }, stepDuration / 50);
          });
        }
        const outputExt = outputFormat.value === "same" ? selectedDocument.value.type : outputFormat.value;
        const mockResult = {
          filename: `translated_${selectedDocument.value.name.split(".")[0]}.${outputExt}`,
          format: outputExt,
          size: selectedDocument.value.size * 1.1,
          processedPages: totalPages.value,
          wordCount: Math.floor(selectedDocument.value.size / 10),
          duration: formatDuration(totalSteps * 2),
          downloadUrl: "mock-download-url"
        };
        translationResult.value = mockResult;
        const historyItem = {
          id: Date.now().toString(),
          filename: selectedDocument.value.name,
          fileType: selectedDocument.value.type,
          sourceLang: currentSourceLang.value,
          targetLang: currentTargetLang.value,
          status: "completed",
          size: selectedDocument.value.size,
          pages: totalPages.value,
          createdAt: (/* @__PURE__ */ new Date()).toISOString()
        };
        documentHistory.value.unshift(historyItem);
        saveDocumentHistory();
        common_vendor.index.showToast({
          title: "翻译完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/translation/document/index.vue:530", "翻译失败:", error);
        common_vendor.index.showToast({
          title: "翻译失败",
          icon: "error"
        });
      } finally {
        isTranslating.value = false;
        estimatedTime.value = "00:00";
      }
    };
    const swapLanguages = () => {
      translationStore.swapLanguages();
    };
    const onSourceLanguageConfirm = (value) => {
      translationStore.setSourceLanguage(value[0].value);
      showSourceLanguagePicker.value = false;
    };
    const onTargetLanguageConfirm = (value) => {
      translationStore.setTargetLanguage(value[0].value);
      showTargetLanguagePicker.value = false;
    };
    const downloadResult = () => {
      if (!translationResult.value)
        return;
      common_vendor.index.showToast({
        title: "开始下载",
        icon: "success"
      });
    };
    const shareResult = () => {
      if (!translationResult.value)
        return;
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 0,
        summary: `文档翻译完成: ${translationResult.value.filename}`,
        success: () => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        }
      });
    };
    const previewResult = () => {
      if (!translationResult.value)
        return;
      common_vendor.index.showToast({
        title: "预览功能开发中",
        icon: "none"
      });
    };
    const formatFileSize = (bytes) => {
      if (bytes === 0)
        return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };
    const formatDuration = (seconds) => {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    };
    const formatTime = (time) => {
      return common_vendor.dayjs(time).format("MM-DD HH:mm");
    };
    const saveDocumentHistory = () => {
      common_vendor.index.setStorageSync("document_translation_history", documentHistory.value);
    };
    const loadDocumentHistory = () => {
      const history = common_vendor.index.getStorageSync("document_translation_history") || [];
      documentHistory.value = history;
    };
    const viewAllHistory = () => {
      common_vendor.index.navigateTo({
        url: "/pages/translation/document/history/index"
      });
    };
    const viewHistoryItem = (item) => {
      common_vendor.index.navigateTo({
        url: `/pages/translation/document/detail/index?id=${item.id}`
      });
    };
    const onPullDownRefresh = () => {
      loadDocumentHistory();
      common_vendor.index.stopPullDownRefresh();
    };
    __expose({
      onPullDownRefresh
    });
    return (_ctx, _cache) => {
      var _a, _b;
      return common_vendor.e({
        a: common_vendor.p({
          name: "arrow-down",
          size: "12",
          color: "#2563EB",
          ["margin-left"]: "4"
        }),
        b: common_vendor.o(($event) => showSourceLanguagePicker.value = true),
        c: common_vendor.p({
          type: "text",
          text: ((_a = common_vendor.unref(sourceLanguage)) == null ? void 0 : _a.name) || "自动检测",
          ["custom-style"]: {
            color: "#2563EB"
          }
        }),
        d: common_vendor.p({
          name: "arrow-left-right",
          size: "20",
          color: "#6B7280"
        }),
        e: common_vendor.o(swapLanguages),
        f: common_vendor.p({
          name: "arrow-down",
          size: "12",
          color: "#2563EB",
          ["margin-left"]: "4"
        }),
        g: common_vendor.o(($event) => showTargetLanguagePicker.value = true),
        h: common_vendor.p({
          type: "text",
          text: ((_b = common_vendor.unref(targetLanguage)) == null ? void 0 : _b.name) || "中文",
          ["custom-style"]: {
            color: "#2563EB"
          }
        }),
        i: !selectedDocument.value
      }, !selectedDocument.value ? {
        j: common_vendor.p({
          name: "file-document",
          size: "48",
          color: "#2563EB"
        })
      } : common_vendor.e({
        k: common_vendor.p({
          name: getFileIcon(selectedDocument.value.type),
          size: "32",
          color: getFileColor(selectedDocument.value.type)
        }),
        l: common_vendor.t(selectedDocument.value.name),
        m: common_vendor.t(formatFileSize(selectedDocument.value.size)),
        n: common_vendor.t(selectedDocument.value.type.toUpperCase()),
        o: selectedDocument.value.pages
      }, selectedDocument.value.pages ? {
        p: common_vendor.t(selectedDocument.value.pages)
      } : {}, {
        q: common_vendor.o(previewDocument),
        r: common_vendor.p({
          name: "eye",
          size: "20",
          color: "#2563EB"
        }),
        s: common_vendor.o(removeDocument),
        t: common_vendor.p({
          name: "close-circle",
          size: "20",
          color: "#EF4444"
        })
      }), {
        v: common_vendor.o(chooseDocument),
        w: selectedDocument.value
      }, selectedDocument.value ? common_vendor.e({
        x: common_vendor.p({
          name: "full",
          label: "完整翻译"
        }),
        y: common_vendor.p({
          name: "partial",
          label: "部分翻译"
        }),
        z: common_vendor.o(($event) => translationMode.value = $event),
        A: common_vendor.p({
          modelValue: translationMode.value
        }),
        B: translationMode.value === "partial" && selectedDocument.value.pages
      }, translationMode.value === "partial" && selectedDocument.value.pages ? {
        C: pageRange.value,
        D: common_vendor.o(($event) => pageRange.value = $event.detail.value)
      } : {}, {
        E: common_vendor.o(($event) => keepFormat.value = $event),
        F: common_vendor.p({
          ["active-color"]: "#2563EB",
          modelValue: keepFormat.value
        }),
        G: common_vendor.p({
          name: "same",
          label: "与原文档相同"
        }),
        H: common_vendor.p({
          name: "pdf",
          label: "PDF格式"
        }),
        I: common_vendor.p({
          name: "docx",
          label: "Word格式"
        }),
        J: common_vendor.o(($event) => outputFormat.value = $event),
        K: common_vendor.p({
          modelValue: outputFormat.value
        }),
        L: common_vendor.p({
          name: "fast",
          label: "快速翻译"
        }),
        M: common_vendor.p({
          name: "balanced",
          label: "平衡模式"
        }),
        N: common_vendor.p({
          name: "accurate",
          label: "精准翻译"
        }),
        O: common_vendor.o(($event) => translationQuality.value = $event),
        P: common_vendor.p({
          modelValue: translationQuality.value
        }),
        Q: common_vendor.t(isTranslating.value ? "翻译中..." : "开始翻译"),
        R: common_vendor.o(startTranslation),
        S: common_vendor.p({
          type: "primary",
          size: "large",
          loading: isTranslating.value,
          disabled: !canTranslate.value,
          ["custom-style"]: {
            width: "100%"
          }
        })
      }) : {}, {
        T: isTranslating.value
      }, isTranslating.value ? {
        U: common_vendor.t(progressStatus.value),
        V: common_vendor.p({
          percent: translationProgress.value,
          ["show-percent"]: true,
          ["active-color"]: "#2563EB"
        }),
        W: common_vendor.t(processedPages.value),
        X: common_vendor.t(totalPages.value),
        Y: common_vendor.t(estimatedTime.value)
      } : {}, {
        Z: translationResult.value
      }, translationResult.value ? {
        aa: common_vendor.o(downloadResult),
        ab: common_vendor.p({
          name: "download",
          size: "18",
          color: "#2563EB"
        }),
        ac: common_vendor.o(shareResult),
        ad: common_vendor.p({
          name: "share",
          size: "18",
          color: "#2563EB",
          ["margin-left"]: "12"
        }),
        ae: common_vendor.p({
          name: "checkmark-circle",
          size: "20",
          color: "#10B981"
        }),
        af: common_vendor.t(translationResult.value.processedPages),
        ag: common_vendor.t(translationResult.value.wordCount),
        ah: common_vendor.t(translationResult.value.duration),
        ai: common_vendor.p({
          name: getFileIcon(translationResult.value.format),
          size: "32",
          color: getFileColor(translationResult.value.format)
        }),
        aj: common_vendor.t(translationResult.value.filename),
        ak: common_vendor.t(formatFileSize(translationResult.value.size)),
        al: common_vendor.o(downloadResult),
        am: common_vendor.p({
          type: "primary",
          size: "small"
        }),
        an: common_vendor.o(previewResult),
        ao: common_vendor.p({
          type: "text",
          size: "small",
          ["custom-style"]: {
            color: "#2563EB"
          }
        })
      } : {}, {
        ap: documentHistory.value.length > 0
      }, documentHistory.value.length > 0 ? {
        aq: common_vendor.o(viewAllHistory),
        ar: common_vendor.p({
          type: "text",
          text: "查看全部",
          size: "small",
          ["custom-style"]: {
            color: "#2563EB",
            fontSize: "24rpx"
          }
        }),
        as: common_vendor.f(documentHistory.value.slice(0, 3), (item, k0, i0) => {
          return {
            a: "19b7412d-30-" + i0,
            b: common_vendor.p({
              name: getFileIcon(item.fileType),
              size: "20",
              color: getFileColor(item.fileType)
            }),
            c: common_vendor.t(item.filename),
            d: common_vendor.t(item.sourceLang),
            e: common_vendor.t(item.targetLang),
            f: common_vendor.t(formatTime(item.createdAt)),
            g: "19b7412d-31-" + i0,
            h: common_vendor.p({
              name: item.status === "completed" ? "checkmark-circle" : "time",
              size: "16",
              color: item.status === "completed" ? "#10B981" : "#F59E0B"
            }),
            i: item.id,
            j: common_vendor.o(($event) => viewHistoryItem(item), item.id)
          };
        })
      } : {}, {
        at: common_vendor.o(onSourceLanguageConfirm),
        av: common_vendor.o(($event) => showSourceLanguagePicker.value = false),
        aw: common_vendor.o(($event) => showSourceLanguagePicker.value = $event),
        ax: common_vendor.p({
          columns: sourceLanguageColumns.value,
          modelValue: showSourceLanguagePicker.value
        }),
        ay: common_vendor.o(onTargetLanguageConfirm),
        az: common_vendor.o(($event) => showTargetLanguagePicker.value = false),
        aA: common_vendor.o(($event) => showTargetLanguagePicker.value = $event),
        aB: common_vendor.p({
          columns: targetLanguageColumns.value,
          modelValue: showTargetLanguagePicker.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-19b7412d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/translation/document/index.js.map
