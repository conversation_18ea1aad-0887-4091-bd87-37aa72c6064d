/**
 * SCSS 变量定义
 */

/* ==================== 颜色系统 ==================== */
// 品牌色
$brand-primary: #2563EB;
$brand-secondary: #10B981;
$brand-accent: #F59E0B;

// 主要颜色变量 (用于兼容)
$primary: #667eea;
$primary-light: #764ba2;
$primary-dark: #5a67d8;

// 主色调变体
$primary-50: #EFF6FF;
$primary-100: #DBEAFE;
$primary-200: #BFDBFE;
$primary-300: #93C5FD;
$primary-400: #60A5FA;
$primary-500: #3B82F6;
$primary-600: #2563EB;
$primary-700: #1D4ED8;
$primary-800: #1E40AF;
$primary-900: #1E3A8A;

// 功能色
$success: #10B981;
$warning: #F59E0B;
$error: #EF4444;
$info: #06B6D4;

// 功能色变体
$success-50: #ECFDF5;
$success-100: #D1FAE5;
$success-200: #A7F3D0;
$success-300: #6EE7B7;
$success-400: #34D399;
$success-500: #10B981;
$success-600: #059669;
$success-700: #047857;
$success-800: #065F46;
$success-900: #064E3B;

$warning-50: #FFFBEB;
$warning-100: #FEF3C7;
$warning-200: #FDE68A;
$warning-300: #FCD34D;
$warning-400: #FBBF24;
$warning-500: #F59E0B;
$warning-600: #D97706;
$warning-700: #B45309;
$warning-800: #92400E;
$warning-900: #78350F;

$error-50: #FEF2F2;
$error-100: #FEE2E2;
$error-200: #FECACA;
$error-300: #FCA5A5;
$error-400: #F87171;
$error-500: #EF4444;
$error-600: #DC2626;
$error-700: #B91C1C;
$error-800: #991B1B;
$error-900: #7F1D1D;

$info-50: #F0F9FF;
$info-100: #E0F2FE;
$info-200: #BAE6FD;
$info-300: #7DD3FC;
$info-400: #38BDF8;
$info-500: #0EA5E9;
$info-600: #0284C7;
$info-700: #0369A1;
$info-800: #075985;
$info-900: #0C4A6E;

// 中性色
$gray-50: #F9FAFB;
$gray-100: #F3F4F6;
$gray-200: #E5E7EB;
$gray-300: #D1D5DB;
$gray-400: #9CA3AF;
$gray-500: #6B7280;
$gray-600: #4B5563;
$gray-700: #374151;
$gray-800: #1F2937;
$gray-900: #111827;

// 文字颜色
$text-primary: $gray-900;
$text-secondary: $gray-700;
$text-tertiary: $gray-500;
$text-quaternary: $gray-400;
$text-disabled: $gray-300;
$text-inverse: #FFFFFF;

// 背景色
$bg-primary: #FFFFFF;
$bg-secondary: $gray-50;
$bg-tertiary: $gray-100;
$bg-quaternary: $gray-200;
$bg-overlay: rgba(0, 0, 0, 0.5);

// 边框色
$border-primary: $gray-200;
$border-secondary: $gray-300;
$border-focus: $brand-primary;

/* ==================== 字体系统 ==================== */
// 字体族
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
$font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', 'Source Code Pro', Menlo, Consolas, 'Courier New', monospace;

// 字体大小 (rpx)
$font-size-xs: 20rpx;    // 10px
$font-size-sm: 24rpx;    // 12px
$font-size-base: 28rpx;  // 14px
$font-size-md: 32rpx;    // 16px
$font-size-lg: 36rpx;    // 18px
$font-size-xl: 40rpx;    // 20px
$font-size-2xl: 48rpx;   // 24px
$font-size-3xl: 60rpx;   // 30px
$font-size-4xl: 72rpx;   // 36px

// 字重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.25;
$line-height-snug: 1.375;
$line-height-normal: 1.5;
$line-height-relaxed: 1.625;
$line-height-loose: 2;

/* ==================== 间距系统 ==================== */
$spacing-0: 0;
$spacing-1: 8rpx;    // 4px
$spacing-2: 16rpx;   // 8px
$spacing-3: 24rpx;   // 12px
$spacing-4: 32rpx;   // 16px
$spacing-5: 40rpx;   // 20px
$spacing-6: 48rpx;   // 24px
$spacing-8: 64rpx;   // 32px
$spacing-10: 80rpx;  // 40px
$spacing-12: 96rpx;  // 48px
$spacing-16: 128rpx; // 64px
$spacing-20: 160rpx; // 80px
$spacing-24: 192rpx; // 96px
$spacing-32: 256rpx; // 128px

/* ==================== 圆角系统 ==================== */
$radius-none: 0;
$radius-sm: 4rpx;    // 2px
$radius-base: 8rpx;  // 4px
$radius-md: 12rpx;   // 6px
$radius-lg: 16rpx;   // 8px
$radius-xl: 24rpx;   // 12px
$radius-2xl: 32rpx;  // 16px
$radius-3xl: 48rpx;  // 24px
$radius-full: 9999rpx;

/* ==================== 阴影系统 ==================== */
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
$shadow-base: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
$shadow-md: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
$shadow-lg: 0 16rpx 64rpx rgba(0, 0, 0, 0.15);
$shadow-xl: 0 32rpx 128rpx rgba(0, 0, 0, 0.2);
$shadow-inner: inset 0 4rpx 8rpx rgba(0, 0, 0, 0.06);

/* ==================== 动画系统 ==================== */
// 持续时间
$duration-75: 75ms;
$duration-100: 100ms;
$duration-150: 150ms;
$duration-200: 200ms;
$duration-300: 300ms;
$duration-500: 500ms;
$duration-700: 700ms;
$duration-1000: 1000ms;

// 缓动函数
$ease-linear: linear;
$ease-in: cubic-bezier(0.4, 0, 1, 1);
$ease-out: cubic-bezier(0, 0, 0.2, 1);
$ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

/* ==================== 布局系统 ==================== */
// 容器宽度
$container-sm: 640rpx;
$container-md: 768rpx;
$container-lg: 1024rpx;
$container-xl: 1280rpx;
$container-2xl: 1536rpx;

// 组件高度
$height-xs: 48rpx;   // 24px
$height-sm: 64rpx;   // 32px
$height-base: 80rpx; // 40px
$height-md: 96rpx;   // 48px
$height-lg: 112rpx;  // 56px
$height-xl: 128rpx;  // 64px

/* ==================== Z-index 系统 ==================== */
$z-auto: auto;
$z-0: 0;
$z-10: 10;
$z-20: 20;
$z-30: 30;
$z-40: 40;
$z-50: 50;
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;
$z-toast: 1080;
