/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 导入自定义样式变量 */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
.profile-page.data-v-db22c714 {
  min-height: 100vh;
  background-color: #F9FAFB;
}
.profile-header.data-v-db22c714 {
  position: relative;
  background-color: #FFFFFF;
  padding-bottom: 48rpx;
}
.profile-header .header-bg.data-v-db22c714 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
}
.profile-header .user-info.data-v-db22c714 {
  position: relative;
  z-index: 1;
  padding: 48rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 32rpx;
}
.profile-header .user-info .avatar-container.data-v-db22c714 {
  position: relative;
}
.profile-header .user-info .avatar-container .user-avatar.data-v-db22c714 {
  width: 160rpx;
  height: 160rpx;
  border-radius: 9999rpx;
  border: 4px solid rgba(255, 255, 255, 0.2);
}
.profile-header .user-info .avatar-container .avatar-edit.data-v-db22c714 {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  background-color: #667eea;
  border-radius: 9999rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
}
.profile-header .user-info .user-details.data-v-db22c714 {
  flex: 1;
  color: white;
}
.profile-header .user-info .user-details .user-name.data-v-db22c714 {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
}
.profile-header .user-info .user-details .user-email.data-v-db22c714 {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 24rpx;
}
.profile-header .user-info .user-details .user-stats.data-v-db22c714 {
  display: flex;
  gap: 48rpx;
}
.profile-header .user-info .user-details .user-stats .stat-item.data-v-db22c714 {
  text-align: center;
}
.profile-header .user-info .user-details .user-stats .stat-item .stat-number.data-v-db22c714 {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  margin-bottom: 4rpx;
}
.profile-header .user-info .user-details .user-stats .stat-item .stat-label.data-v-db22c714 {
  font-size: 20rpx;
  opacity: 0.8;
}
.quick-actions.data-v-db22c714 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
  padding: 32rpx;
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.quick-actions .action-item.data-v-db22c714 {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
}
.quick-actions .action-item .action-icon.data-v-db22c714 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.quick-actions .action-item .action-title.data-v-db22c714 {
  font-size: 24rpx;
  color: #111827;
}
.quick-actions .action-item .action-badge.data-v-db22c714 {
  position: absolute;
  top: -8rpx;
  right: 16rpx;
  background-color: #EF4444;
  border-radius: 9999rpx;
  padding: 4rpx 12rpx;
  min-width: 32rpx;
}
.quick-actions .action-item .action-badge .badge-text.data-v-db22c714 {
  font-size: 20rpx;
  color: white;
  text-align: center;
}
.menu-sections .menu-section.data-v-db22c714 {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.menu-sections .menu-section .section-title.data-v-db22c714 {
  padding: 32rpx 32rpx 16rpx;
}
.menu-sections .menu-section .section-title .title-text.data-v-db22c714 {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
}
.menu-sections .menu-section .menu-list .menu-item.data-v-db22c714 {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1px solid #E5E7EB;
}
.menu-sections .menu-section .menu-list .menu-item.data-v-db22c714:last-child {
  border-bottom: none;
}
.menu-sections .menu-section .menu-list .menu-item.data-v-db22c714:active {
  background-color: #F3F4F6;
}
.menu-sections .menu-section .menu-list .menu-item .menu-icon.data-v-db22c714 {
  width: 64rpx;
  height: 64rpx;
  background-color: #F3F4F6;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.menu-sections .menu-section .menu-list .menu-item .menu-title.data-v-db22c714 {
  flex: 1;
  font-size: 28rpx;
  color: #111827;
}
.menu-sections .menu-section .menu-list .menu-item .menu-extra.data-v-db22c714 {
  margin-right: 16rpx;
}
.menu-sections .menu-section .menu-list .menu-item .menu-extra .extra-text.data-v-db22c714 {
  font-size: 24rpx;
  color: #6B7280;
}
.menu-sections .menu-section .menu-list .menu-item .menu-extra .badge-dot.data-v-db22c714 {
  width: 16rpx;
  height: 16rpx;
  background-color: #EF4444;
  border-radius: 9999rpx;
}
.logout-section.data-v-db22c714 {
  padding: 32rpx;
}
.version-info.data-v-db22c714 {
  text-align: center;
  padding: 32rpx;
}
.version-info .version-text.data-v-db22c714 {
  font-size: 24rpx;
  color: #9CA3AF;
}
@media screen and (max-width: 480px) {
.profile-header .user-info.data-v-db22c714 {
    flex-direction: column;
    text-align: center;
}
.profile-header .user-info .user-details .user-stats.data-v-db22c714 {
    justify-content: center;
}
.quick-actions.data-v-db22c714 {
    grid-template-columns: repeat(2, 1fr);
    gap: 48rpx;
}
.quick-actions .action-item .action-icon.data-v-db22c714 {
    width: 120rpx;
    height: 120rpx;
}
}
@media (prefers-color-scheme: dark) {
.profile-page.data-v-db22c714 {
    background-color: #1a1a1a;
}
.profile-header.data-v-db22c714 {
    background-color: #2d2d2d;
}
.profile-header .header-bg.data-v-db22c714 {
    background: linear-gradient(135deg, #1e40af, #3b82f6);
}
.quick-actions.data-v-db22c714,
.menu-section.data-v-db22c714 {
    background-color: #2d2d2d;
    border-color: #404040;
}
.menu-item.data-v-db22c714 {
    border-color: #404040;
}
.menu-item.data-v-db22c714:active {
    background-color: #404040;
}
.menu-icon.data-v-db22c714 {
    background-color: #404040;
}
.user-details.data-v-db22c714 {
    color: #ffffff;
}
.menu-title.data-v-db22c714 {
    color: #ffffff;
}
.extra-text.data-v-db22c714 {
    color: #cccccc;
}
.version-text.data-v-db22c714 {
    color: #888888;
}
}
.profile-header.data-v-db22c714 {
  padding-top: calc(48rpx + env(safe-area-inset-top));
}
.logout-section.data-v-db22c714 {
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
}