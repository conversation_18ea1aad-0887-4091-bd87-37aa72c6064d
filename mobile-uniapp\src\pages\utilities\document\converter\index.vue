<template>
  <view class="document-converter">
    <!-- 头部区域 -->
    <view class="header-section">
      <view class="header-content">
        <text class="page-title">文档转换</text>
        <text class="page-subtitle">支持多种格式互转，保持原有排版</text>
      </view>
    </view>
    
    <!-- 转换配置区域 -->
    <view class="converter-section">
      <view class="format-selector">
        <view class="format-item">
          <text class="format-label">源格式</text>
          <view class="format-picker" @click="showSourcePicker">
            <view class="format-display">
              <view class="format-icon" :style="{ background: sourceFormat.color }">
                <text class="iconfont" :class="sourceFormat.icon"></text>
              </view>
              <text class="format-name">{{ sourceFormat.name }}</text>
            </view>
            <u-icon name="arrow-down" size="16" color="#8E8E93"></u-icon>
          </view>
        </view>
        
        <view class="convert-arrow">
          <view class="arrow-icon" @click="swapFormats">
            <u-icon name="arrow-right" size="20" color="#667eea"></u-icon>
          </view>
        </view>
        
        <view class="format-item">
          <text class="format-label">目标格式</text>
          <view class="format-picker" @click="showTargetPicker">
            <view class="format-display">
              <view class="format-icon" :style="{ background: targetFormat.color }">
                <text class="iconfont" :class="targetFormat.icon"></text>
              </view>
              <text class="format-name">{{ targetFormat.name }}</text>
            </view>
            <u-icon name="arrow-down" size="16" color="#8E8E93"></u-icon>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 文件上传区域 -->
    <view class="upload-section">
      <view class="upload-area" @click="chooseFile">
        <view class="upload-content" v-if="!selectedFile">
          <view class="upload-icon">
            <u-icon name="cloud-upload" size="48" color="#667eea"></u-icon>
          </view>
          <text class="upload-title">选择文件或拖拽上传</text>
          <text class="upload-desc">支持 PDF、Word、Excel、PPT 等格式</text>
          <text class="upload-limit">文件大小不超过 50MB</text>
        </view>
        
        <view class="file-preview" v-else>
          <view class="file-info">
            <view class="file-icon" :style="{ background: getFileColor(selectedFile.type) }">
              <text class="iconfont" :class="getFileIcon(selectedFile.type)"></text>
            </view>
            <view class="file-details">
              <text class="file-name">{{ selectedFile.name }}</text>
              <text class="file-size">{{ formatFileSize(selectedFile.size) }}</text>
              <text class="file-status" :class="{ success: uploadSuccess, error: uploadError }">
                {{ getUploadStatus() }}
              </text>
            </view>
          </view>
          
          <view class="file-actions">
            <view class="action-btn remove" @click.stop="removeFile">
              <u-icon name="close" size="16" color="#EF4444"></u-icon>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 转换选项 -->
    <view class="options-section">
      <view class="section-title">转换选项</view>
      
      <view class="option-list">
        <view class="option-item">
          <view class="option-info">
            <text class="option-name">保持原有格式</text>
            <text class="option-desc">尽可能保持原文档的排版和样式</text>
          </view>
          <switch 
            :checked="options.keepFormat" 
            @change="toggleOption('keepFormat')"
            color="#667eea"
          />
        </view>
        
        <view class="option-item">
          <view class="option-info">
            <text class="option-name">高质量转换</text>
            <text class="option-desc">使用AI增强转换质量，处理时间较长</text>
          </view>
          <switch 
            :checked="options.highQuality" 
            @change="toggleOption('highQuality')"
            color="#667eea"
          />
        </view>
        
        <view class="option-item">
          <view class="option-info">
            <text class="option-name">批量处理</text>
            <text class="option-desc">同时转换多个文件</text>
          </view>
          <switch 
            :checked="options.batchMode" 
            @change="toggleOption('batchMode')"
            color="#667eea"
          />
        </view>
      </view>
    </view>
    
    <!-- 转换按钮 -->
    <view class="action-section">
      <view 
        class="convert-btn"
        :class="{ disabled: !canConvert, loading: isConverting }"
        @click="startConvert"
      >
        <view class="btn-content" v-if="!isConverting">
          <u-icon name="refresh" size="20" color="#FFFFFF"></u-icon>
          <text class="btn-text">开始转换</text>
        </view>
        <view class="btn-content" v-else>
          <view class="loading-icon">
            <u-icon name="loading" size="20" color="#FFFFFF"></u-icon>
          </view>
          <text class="btn-text">转换中... {{ convertProgress }}%</text>
        </view>
      </view>
      
      <text class="convert-tip">转换完成后将自动下载到您的设备</text>
    </view>
    
    <!-- 转换历史 -->
    <view class="history-section" v-if="convertHistory.length > 0">
      <view class="section-title">转换历史</view>
      
      <view class="history-list">
        <view 
          class="history-item"
          v-for="(item, index) in convertHistory"
          :key="index"
          @click="downloadFile(item)"
        >
          <view class="history-icon">
            <text class="iconfont" :class="getFileIcon(item.targetType)"></text>
          </view>
          
          <view class="history-content">
            <text class="history-name">{{ item.fileName }}</text>
            <text class="history-desc">
              {{ item.sourceType.toUpperCase() }} → {{ item.targetType.toUpperCase() }}
            </text>
            <text class="history-time">{{ formatTime(item.convertTime) }}</text>
          </view>
          
          <view class="history-action">
            <u-icon name="download" size="16" color="#667eea"></u-icon>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="bottom-safe-area"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const selectedFile = ref<any>(null)
const uploadSuccess = ref<boolean>(false)
const uploadError = ref<boolean>(false)
const isConverting = ref<boolean>(false)
const convertProgress = ref<number>(0)

const sourceFormat = ref({
  name: 'PDF',
  icon: 'icon-pdf',
  color: '#EF4444',
  type: 'pdf'
})

const targetFormat = ref({
  name: 'Word',
  icon: 'icon-document',
  color: '#2563EB',
  type: 'docx'
})

const options = ref({
  keepFormat: true,
  highQuality: false,
  batchMode: false
})

const supportedFormats = ref([
  { name: 'PDF', icon: 'icon-pdf', color: '#EF4444', type: 'pdf' },
  { name: 'Word', icon: 'icon-document', color: '#2563EB', type: 'docx' },
  { name: 'Excel', icon: 'icon-table', color: '#10B981', type: 'xlsx' },
  { name: 'PowerPoint', icon: 'icon-presentation', color: '#F59E0B', type: 'pptx' },
  { name: 'Text', icon: 'icon-text', color: '#8E8E93', type: 'txt' },
  { name: 'HTML', icon: 'icon-code', color: '#EC4899', type: 'html' }
])

const convertHistory = ref([
  {
    fileName: '商业计划书.docx',
    sourceType: 'pdf',
    targetType: 'docx',
    convertTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
    downloadUrl: '/downloads/business-plan.docx'
  },
  {
    fileName: '财务报表.xlsx',
    sourceType: 'pdf',
    targetType: 'xlsx',
    convertTime: new Date(Date.now() - 5 * 60 * 60 * 1000),
    downloadUrl: '/downloads/financial-report.xlsx'
  }
])

// 计算属性
const canConvert = computed(() => {
  return selectedFile.value && !isConverting.value && sourceFormat.value.type !== targetFormat.value.type
})

// 方法
const showSourcePicker = () => {
  const itemList = supportedFormats.value.map(format => format.name)
  uni.showActionSheet({
    itemList,
    success: (res) => {
      sourceFormat.value = supportedFormats.value[res.tapIndex]
    }
  })
}

const showTargetPicker = () => {
  const itemList = supportedFormats.value.map(format => format.name)
  uni.showActionSheet({
    itemList,
    success: (res) => {
      targetFormat.value = supportedFormats.value[res.tapIndex]
    }
  })
}

const swapFormats = () => {
  const temp = sourceFormat.value
  sourceFormat.value = targetFormat.value
  targetFormat.value = temp
}

const chooseFile = () => {
  uni.chooseFile({
    count: 1,
    type: 'file',
    success: (res) => {
      const file = res.tempFiles[0]
      selectedFile.value = {
        name: file.name,
        size: file.size,
        type: getFileTypeFromName(file.name),
        path: file.path
      }
      uploadSuccess.value = true
      uploadError.value = false
    },
    fail: (err) => {
      console.error('选择文件失败:', err)
      uploadError.value = true
    }
  })
}

const removeFile = () => {
  selectedFile.value = null
  uploadSuccess.value = false
  uploadError.value = false
}

const toggleOption = (key: string) => {
  options.value[key] = !options.value[key]
}

const startConvert = () => {
  if (!canConvert.value) return
  
  isConverting.value = true
  convertProgress.value = 0
  
  // 模拟转换进度
  const progressInterval = setInterval(() => {
    convertProgress.value += Math.random() * 15
    if (convertProgress.value >= 100) {
      convertProgress.value = 100
      clearInterval(progressInterval)
      
      setTimeout(() => {
        isConverting.value = false
        convertProgress.value = 0
        
        // 添加到转换历史
        convertHistory.value.unshift({
          fileName: selectedFile.value.name.replace(/\.[^/.]+$/, '') + '.' + targetFormat.value.type,
          sourceType: sourceFormat.value.type,
          targetType: targetFormat.value.type,
          convertTime: new Date(),
          downloadUrl: '/downloads/converted-file.' + targetFormat.value.type
        })
        
        uni.showToast({
          title: '转换完成',
          icon: 'success'
        })
        
        // 重置文件选择
        removeFile()
      }, 1000)
    }
  }, 200)
}

const downloadFile = (item: any) => {
  uni.showToast({
    title: '开始下载',
    icon: 'success'
  })
  console.log('下载文件:', item.fileName)
}

const getFileTypeFromName = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  return ext || 'unknown'
}

const getFileIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    pdf: 'icon-pdf',
    doc: 'icon-document',
    docx: 'icon-document',
    xls: 'icon-table',
    xlsx: 'icon-table',
    ppt: 'icon-presentation',
    pptx: 'icon-presentation',
    txt: 'icon-text',
    html: 'icon-code'
  }
  return iconMap[type] || 'icon-file'
}

const getFileColor = (type: string) => {
  const colorMap: Record<string, string> = {
    pdf: '#EF4444',
    doc: '#2563EB',
    docx: '#2563EB',
    xls: '#10B981',
    xlsx: '#10B981',
    ppt: '#F59E0B',
    pptx: '#F59E0B',
    txt: '#8E8E93',
    html: '#EC4899'
  }
  return colorMap[type] || '#6B7280'
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))
  
  if (hours < 1) return '刚刚'
  if (hours < 24) return `${hours}小时前`
  return `${Math.floor(hours / 24)}天前`
}

const getUploadStatus = () => {
  if (uploadError.value) return '上传失败'
  if (uploadSuccess.value) return '上传成功'
  return '准备上传'
}

// 生命周期
onMounted(() => {
  console.log('文档转换页面加载完成')
})
</script>

<style lang="scss" scoped>
.document-converter {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
  padding: 32rpx;
}

// 头部区域
.header-section {
  margin-bottom: 48rpx;
  
  .header-content {
    text-align: center;
    
    .page-title {
      display: block;
      font-size: 48rpx;
      font-weight: 700;
      color: #1D1D1F;
      margin-bottom: 16rpx;
    }
    
    .page-subtitle {
      font-size: 28rpx;
      color: #8E8E93;
      line-height: 1.4;
    }
  }
}

// 转换配置区域
.converter-section {
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .format-selector {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .format-item {
      flex: 1;
      
      .format-label {
        display: block;
        font-size: 24rpx;
        color: #8E8E93;
        margin-bottom: 16rpx;
      }
      
      .format-picker {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #F2F2F7;
        border-radius: 16rpx;
        padding: 24rpx;
        
        .format-display {
          display: flex;
          align-items: center;
          
          .format-icon {
            width: 48rpx;
            height: 48rpx;
            border-radius: 12rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16rpx;
            
            .iconfont {
              font-size: 24rpx;
              color: #FFFFFF;
            }
          }
          
          .format-name {
            font-size: 28rpx;
            font-weight: 500;
            color: #1D1D1F;
          }
        }
      }
    }
    
    .convert-arrow {
      margin: 0 32rpx;
      
      .arrow-icon {
        width: 64rpx;
        height: 64rpx;
        background: rgba(102, 126, 234, 0.1);
        border-radius: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        
        &:active {
          transform: scale(0.95);
        }
      }
    }
  }
}

// 文件上传区域
.upload-section {
  margin-bottom: 32rpx;
  
  .upload-area {
    background: #FFFFFF;
    border: 2rpx dashed #D1D5DB;
    border-radius: 24rpx;
    padding: 80rpx 40rpx;
    text-align: center;
    transition: all 0.3s ease;
    
    &:active {
      border-color: #667eea;
      background: rgba(102, 126, 234, 0.02);
    }
    
    .upload-content {
      .upload-icon {
        margin-bottom: 24rpx;
      }
      
      .upload-title {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #1D1D1F;
        margin-bottom: 12rpx;
      }
      
      .upload-desc {
        display: block;
        font-size: 26rpx;
        color: #8E8E93;
        margin-bottom: 8rpx;
      }
      
      .upload-limit {
        font-size: 22rpx;
        color: #C7C7CC;
      }
    }
    
    .file-preview {
      display: flex;
      align-items: center;
      justify-content: space-between;
      text-align: left;
      
      .file-info {
        display: flex;
        align-items: center;
        flex: 1;
        
        .file-icon {
          width: 80rpx;
          height: 80rpx;
          border-radius: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 24rpx;
          
          .iconfont {
            font-size: 32rpx;
            color: #FFFFFF;
          }
        }
        
        .file-details {
          .file-name {
            display: block;
            font-size: 28rpx;
            font-weight: 600;
            color: #1D1D1F;
            margin-bottom: 8rpx;
          }
          
          .file-size {
            display: block;
            font-size: 24rpx;
            color: #8E8E93;
            margin-bottom: 8rpx;
          }
          
          .file-status {
            font-size: 22rpx;
            color: #8E8E93;
            
            &.success {
              color: #10B981;
            }
            
            &.error {
              color: #EF4444;
            }
          }
        }
      }
      
      .file-actions {
        .action-btn {
          width: 56rpx;
          height: 56rpx;
          background: rgba(239, 68, 68, 0.1);
          border-radius: 28rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          
          &.remove {
            background: rgba(239, 68, 68, 0.1);
          }
        }
      }
    }
  }
}

// 转换选项
.options-section {
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #1D1D1F;
    margin-bottom: 32rpx;
  }
  
  .option-list {
    .option-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #F2F2F7;
      
      &:last-child {
        border-bottom: none;
      }
      
      .option-info {
        flex: 1;
        margin-right: 32rpx;
        
        .option-name {
          display: block;
          font-size: 28rpx;
          font-weight: 500;
          color: #1D1D1F;
          margin-bottom: 8rpx;
        }
        
        .option-desc {
          font-size: 24rpx;
          color: #8E8E93;
          line-height: 1.4;
        }
      }
    }
  }
}

// 转换按钮
.action-section {
  text-align: center;
  margin-bottom: 48rpx;
  
  .convert-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 28rpx;
    padding: 32rpx 64rpx;
    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    
    &:active:not(.disabled):not(.loading) {
      transform: scale(0.98);
    }
    
    &.disabled {
      opacity: 0.5;
      box-shadow: none;
    }
    
    &.loading {
      pointer-events: none;
    }
    
    .btn-content {
      display: flex;
      align-items: center;
      justify-content: center;
      
      .btn-text {
        font-size: 32rpx;
        font-weight: 600;
        color: #FFFFFF;
        margin-left: 16rpx;
      }
      
      .loading-icon {
        animation: spin 1s linear infinite;
      }
    }
  }
  
  .convert-tip {
    display: block;
    font-size: 24rpx;
    color: #8E8E93;
    margin-top: 24rpx;
  }
}

// 转换历史
.history-section {
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #1D1D1F;
    margin-bottom: 32rpx;
  }
  
  .history-list {
    .history-item {
      display: flex;
      align-items: center;
      background: #FFFFFF;
      border-radius: 20rpx;
      padding: 32rpx;
      margin-bottom: 16rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      
      &:active {
        transform: scale(0.98);
      }
      
      .history-icon {
        width: 64rpx;
        height: 64rpx;
        background: #F2F2F7;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
        
        .iconfont {
          font-size: 24rpx;
          color: #8E8E93;
        }
      }
      
      .history-content {
        flex: 1;
        
        .history-name {
          display: block;
          font-size: 28rpx;
          font-weight: 500;
          color: #1D1D1F;
          margin-bottom: 8rpx;
        }
        
        .history-desc {
          display: block;
          font-size: 24rpx;
          color: #8E8E93;
          margin-bottom: 8rpx;
        }
        
        .history-time {
          font-size: 22rpx;
          color: #C7C7CC;
        }
      }
      
      .history-action {
        padding: 16rpx;
      }
    }
  }
}

// 底部安全区域
.bottom-safe-area {
  height: calc(120rpx + env(safe-area-inset-bottom));
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .document-converter {
    background: linear-gradient(180deg, #1c1c1e 0%, #000000 100%);
  }
  
  .converter-section,
  .upload-area,
  .options-section,
  .history-item {
    background: #2c2c2e;
    border-color: #38383a;
  }
  
  .page-title,
  .section-title,
  .format-name,
  .option-name,
  .history-name {
    color: #ffffff !important;
  }
  
  .format-picker {
    background: #38383a !important;
  }
  
  .history-icon {
    background: #38383a;
  }
}
</style>
