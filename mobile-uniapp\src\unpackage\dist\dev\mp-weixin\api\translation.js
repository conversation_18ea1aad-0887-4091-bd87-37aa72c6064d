"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("../utils/request.js");
const translationApi = {
  // 获取支持的语言列表
  getLanguages: () => {
    return utils_request.http.get("/translation/languages");
  },
  // 文本翻译
  translateText: (data) => {
    return utils_request.http.post("/translation/text", data);
  },
  // 语言检测
  detectLanguage: (text) => {
    return utils_request.http.post("/translation/detect", { text });
  },
  // 获取翻译历史
  getHistory: (params) => {
    return utils_request.http.get("/translation/history", params);
  },
  // 删除翻译历史
  deleteHistory: (id) => {
    return utils_request.http.delete(`/translation/history/${id}`);
  },
  // 清空翻译历史
  clearHistory: () => {
    return utils_request.http.delete("/translation/history/clear");
  },
  // 收藏翻译
  favoriteTranslation: (id) => {
    return utils_request.http.post(`/translation/favorite/${id}`);
  },
  // 取消收藏
  unfavoriteTranslation: (id) => {
    return utils_request.http.delete(`/translation/favorite/${id}`);
  },
  // 获取收藏的翻译
  getFavorites: (params) => {
    return utils_request.http.get("/translation/favorites", params);
  },
  // 文档翻译
  translateDocument: (filePath, sourceLang, targetLang) => {
    return new Promise((resolve, reject) => {
      common_vendor.index.uploadFile({
        url: "/api/translation/document",
        filePath,
        name: "file",
        formData: {
          source_lang: sourceLang,
          target_lang: targetLang
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.success) {
              resolve(data);
            } else {
              reject(data);
            }
          } catch (error) {
            reject(error);
          }
        },
        fail: reject
      });
    });
  },
  // 音频翻译
  translateAudio: (filePath, sourceLang, targetLang) => {
    return new Promise((resolve, reject) => {
      common_vendor.index.uploadFile({
        url: "/api/translation/audio",
        filePath,
        name: "audio",
        formData: {
          source_lang: sourceLang,
          target_lang: targetLang
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.success) {
              resolve(data);
            } else {
              reject(data);
            }
          } catch (error) {
            reject(error);
          }
        },
        fail: reject
      });
    });
  },
  // 视频翻译
  translateVideo: (filePath, sourceLang, targetLang) => {
    return new Promise((resolve, reject) => {
      common_vendor.index.uploadFile({
        url: "/api/translation/video",
        filePath,
        name: "video",
        formData: {
          source_lang: sourceLang,
          target_lang: targetLang
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.success) {
              resolve(data);
            } else {
              reject(data);
            }
          } catch (error) {
            reject(error);
          }
        },
        fail: reject
      });
    });
  },
  // 获取翻译任务状态
  getTaskStatus: (taskId) => {
    return utils_request.http.get(`/translation/task/${taskId}`);
  },
  // 下载翻译结果
  downloadResult: (taskId) => {
    return utils_request.http.get(`/translation/download/${taskId}`, {}, { responseType: "blob" });
  }
};
exports.translationApi = translationApi;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/translation.js.map
