<template>
  <view class="translation-page">
    <!-- 语言选择栏 -->
    <view class="language-bar">
      <view class="language-selector">
        <u-button 
          type="text" 
          :text="sourceLanguage?.name || '自动检测'" 
          @click="showSourceLanguagePicker = true"
          :custom-style="{ color: '#2563EB', fontSize: '28rpx' }"
        >
          <u-icon name="arrow-down" size="12" color="#2563EB" margin-left="4"></u-icon>
        </u-button>
      </view>
      
      <view class="swap-button" @click="handleSwapLanguages">
        <u-icon name="arrow-left-right" size="20" color="#6B7280"></u-icon>
      </view>
      
      <view class="language-selector">
        <u-button 
          type="text" 
          :text="targetLanguage?.name || '中文'" 
          @click="showTargetLanguagePicker = true"
          :custom-style="{ color: '#2563EB', fontSize: '28rpx' }"
        >
          <u-icon name="arrow-down" size="12" color="#2563EB" margin-left="4"></u-icon>
        </u-button>
      </view>
    </view>

    <!-- 快速语言对 -->
    <view class="quick-pairs" v-if="recentLanguagePairs.length > 0">
      <scroll-view class="pairs-scroll" scroll-x="true" show-scrollbar="false">
        <view class="pairs-list">
          <view 
            class="pair-item" 
            v-for="(pair, index) in recentLanguagePairs" 
            :key="index"
            @click="useLanguagePair(pair.source, pair.target)"
          >
            <text class="pair-text">{{ getLanguageName(pair.source) }} → {{ getLanguageName(pair.target) }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 翻译输入区域 -->
    <view class="translation-container">
      <!-- 源文本输入 -->
      <view class="input-section">
        <view class="section-header">
          <text class="section-title">{{ sourceLanguage?.name || '自动检测' }}</text>
          <view class="header-actions">
            <u-icon 
              v-if="sourceText" 
              name="close-circle-fill" 
              size="18" 
              color="#9CA3AF" 
              @click="clearSourceText"
            ></u-icon>
            <u-icon 
              name="mic" 
              size="18" 
              color="#2563EB" 
              margin-left="12"
              @click="startVoiceInput"
            ></u-icon>
          </view>
        </view>
        
        <textarea 
          class="text-input"
          v-model="sourceText"
          placeholder="请输入要翻译的文本..."
          :maxlength="5000"
          auto-height
          @input="handleInputChange"
          @focus="inputFocused = true"
          @blur="inputFocused = false"
        ></textarea>
        
        <view class="input-footer">
          <text class="char-count">{{ sourceText.length }}/5000</text>
          <u-button 
            type="primary" 
            size="small"
            :loading="isTranslating"
            :disabled="!sourceText.trim()"
            @click="handleTranslate"
          >
            {{ isTranslating ? '翻译中...' : '翻译' }}
          </u-button>
        </view>
      </view>

      <!-- 翻译结果 -->
      <view class="result-section" v-if="translationResult">
        <view class="section-header">
          <text class="section-title">{{ targetLanguage?.name || '中文' }}</text>
          <view class="header-actions">
            <u-icon 
              name="copy" 
              size="18" 
              color="#2563EB" 
              @click="copyResult"
            ></u-icon>
            <u-icon 
              name="share" 
              size="18" 
              color="#2563EB" 
              margin-left="12"
              @click="shareResult"
            ></u-icon>
            <u-icon 
              :name="isResultFavorite ? 'heart-fill' : 'heart'" 
              size="18" 
              :color="isResultFavorite ? '#EF4444' : '#2563EB'"
              margin-left="12"
              @click="toggleResultFavorite"
            ></u-icon>
          </view>
        </view>
        
        <view class="result-content">
          <text class="result-text" selectable>{{ translationResult.translated_text }}</text>
        </view>
        
        <view class="result-footer">
          <text class="result-info">
            置信度: {{ Math.round(translationResult.confidence * 100) }}% | 
            字数: {{ translationResult.word_count }}
          </text>
        </view>
      </view>
    </view>

    <!-- 历史记录 -->
    <view class="history-section" v-if="translationHistory.length > 0">
      <view class="section-header">
        <text class="section-title">翻译历史</text>
        <u-button 
          type="text" 
          text="查看全部" 
          size="small"
          @click="showHistoryModal = true"
          :custom-style="{ color: '#2563EB', fontSize: '24rpx' }"
        ></u-button>
      </view>
      
      <view class="history-list">
        <view 
          class="history-item" 
          v-for="(item, index) in translationHistory.slice(0, 3)" 
          :key="item.id"
          @click="useHistoryItem(item)"
        >
          <view class="history-content">
            <text class="history-source">{{ item.source_text }}</text>
            <text class="history-target">{{ item.translated_text }}</text>
          </view>
          <view class="history-meta">
            <text class="history-langs">{{ getLanguageName(item.source_lang) }} → {{ getLanguageName(item.target_lang) }}</text>
            <text class="history-time">{{ formatTime(item.created_at) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 源语言选择器 -->
    <u-picker
      v-model="showSourceLanguagePicker"
      :columns="sourceLanguageColumns"
      @confirm="onSourceLanguageConfirm"
      @cancel="showSourceLanguagePicker = false"
    ></u-picker>

    <!-- 目标语言选择器 -->
    <u-picker
      v-model="showTargetLanguagePicker"
      :columns="targetLanguageColumns"
      @confirm="onTargetLanguageConfirm"
      @cancel="showTargetLanguagePicker = false"
    ></u-picker>

    <!-- 历史记录弹窗 -->
    <u-modal 
      v-model="showHistoryModal" 
      title="翻译历史"
      :show-cancel-button="true"
      cancel-text="清空"
      confirm-text="关闭"
      @cancel="handleClearHistory"
      @confirm="showHistoryModal = false"
    >
      <view class="history-modal">
        <scroll-view class="history-scroll" scroll-y="true" style="height: 600rpx;">
          <view 
            class="history-modal-item" 
            v-for="item in translationHistory" 
            :key="item.id"
            @click="useHistoryItem(item)"
          >
            <view class="modal-item-content">
              <text class="modal-source">{{ item.source_text }}</text>
              <text class="modal-target">{{ item.translated_text }}</text>
              <view class="modal-meta">
                <text class="modal-langs">{{ getLanguageName(item.source_lang) }} → {{ getLanguageName(item.target_lang) }}</text>
                <text class="modal-time">{{ formatTime(item.created_at) }}</text>
              </view>
            </view>
            <u-icon 
              name="delete" 
              size="16" 
              color="#EF4444"
              @click.stop="deleteHistoryItem(item.id)"
            ></u-icon>
          </view>
        </scroll-view>
      </view>
    </u-modal>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useTranslationStore } from '@/store/modules/translation'
import { type TranslationResponse, type TranslationHistory } from '@/api/translation'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.extend(relativeTime)

// Store
const translationStore = useTranslationStore()

// 响应式数据
const sourceText = ref<string>('')
const translationResult = ref<TranslationResponse | null>(null)
const inputFocused = ref<boolean>(false)
const showSourceLanguagePicker = ref<boolean>(false)
const showTargetLanguagePicker = ref<boolean>(false)
const showHistoryModal = ref<boolean>(false)
const isResultFavorite = ref<boolean>(false)

// 计算属性
const {
  languages,
  currentSourceLang,
  currentTargetLang,
  translationHistory,
  isTranslating,
  sourceLanguage,
  targetLanguage,
  recentLanguagePairs
} = translationStore

// 语言选择器数据
const sourceLanguageColumns = computed(() => {
  const autoDetect = [{ text: '自动检测', value: 'auto' }]
  const languageOptions = languages.map(lang => ({
    text: lang.name,
    value: lang.code
  }))
  return [autoDetect.concat(languageOptions)]
})

const targetLanguageColumns = computed(() => {
  const languageOptions = languages.filter(lang => lang.code !== 'auto').map(lang => ({
    text: lang.name,
    value: lang.code
  }))
  return [languageOptions]
})

// 页面加载
onMounted(async () => {
  // 加载语言列表
  await translationStore.loadLanguages()

  // 加载翻译历史
  await translationStore.loadHistory()

  // 如果有上次的翻译结果，显示它
  if (translationStore.lastTranslation) {
    sourceText.value = translationStore.lastTranslation.source
    translationResult.value = {
      translated_text: translationStore.lastTranslation.target,
      source_lang: translationStore.lastTranslation.sourceLang,
      target_lang: translationStore.lastTranslation.targetLang,
      confidence: 0.95,
      word_count: translationStore.lastTranslation.source.split(' ').length,
      char_count: translationStore.lastTranslation.source.length
    }
  }
})

// 监听输入变化，实现防抖翻译
let inputTimer: NodeJS.Timeout | null = null
const handleInputChange = () => {
  if (inputTimer) {
    clearTimeout(inputTimer)
  }

  // 清除之前的翻译结果
  if (translationResult.value) {
    translationResult.value = null
    isResultFavorite.value = false
  }

  // 如果开启了自动翻译且文本长度合适，则自动翻译
  if (sourceText.value.trim().length > 0 && sourceText.value.trim().length < 500) {
    inputTimer = setTimeout(() => {
      handleTranslate()
    }, 1000) // 1秒后自动翻译
  }
}

// 执行翻译
const handleTranslate = async () => {
  if (!sourceText.value.trim()) {
    uni.showToast({
      title: '请输入要翻译的文本',
      icon: 'none'
    })
    return
  }

  try {
    const result = await translationStore.translateText(sourceText.value.trim())
    translationResult.value = result
    isResultFavorite.value = false

    // 检查是否已收藏
    checkIfFavorite()

  } catch (error: any) {
    console.error('翻译失败:', error)
    uni.showToast({
      title: error.message || '翻译失败',
      icon: 'error'
    })
  }
}

// 检查是否已收藏
const checkIfFavorite = () => {
  if (!translationResult.value) return

  // 这里可以根据实际需求检查当前翻译是否已收藏
  // 暂时设为false
  isResultFavorite.value = false
}

// 清除源文本
const clearSourceText = () => {
  sourceText.value = ''
  translationResult.value = null
  isResultFavorite.value = false
}

// 开始语音输入
const startVoiceInput = () => {
  // #ifdef APP-PLUS
  plus.speech.startRecognize({
    engine: 'iFly',
    lang: currentSourceLang.value === 'auto' ? 'zh-cn' : currentSourceLang.value,
    onstart: () => {
      uni.showToast({
        title: '开始录音...',
        icon: 'none'
      })
    },
    onend: (result: string) => {
      if (result) {
        sourceText.value = result
        handleTranslate()
      }
    },
    onerror: (error: any) => {
      console.error('语音识别失败:', error)
      uni.showToast({
        title: '语音识别失败',
        icon: 'error'
      })
    }
  })
  // #endif

  // #ifdef H5
  uni.showToast({
    title: 'H5暂不支持语音输入',
    icon: 'none'
  })
  // #endif

  // #ifdef MP
  uni.showToast({
    title: '小程序暂不支持语音输入',
    icon: 'none'
  })
  // #endif
}

// 复制翻译结果
const copyResult = () => {
  if (!translationResult.value) return

  uni.setClipboardData({
    data: translationResult.value.translated_text,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'error'
      })
    }
  })
}

// 分享翻译结果
const shareResult = () => {
  if (!translationResult.value) return

  const shareContent = `原文: ${sourceText.value}\n译文: ${translationResult.value.translated_text}`

  // #ifdef APP-PLUS
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    summary: shareContent,
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    },
    fail: (error) => {
      console.error('分享失败:', error)
      uni.showToast({
        title: '分享失败',
        icon: 'error'
      })
    }
  })
  // #endif

  // #ifdef H5 || MP
  uni.setClipboardData({
    data: shareContent,
    success: () => {
      uni.showToast({
        title: '内容已复制，可分享到其他应用',
        icon: 'success'
      })
    }
  })
  // #endif
}

// 切换收藏状态
const toggleResultFavorite = async () => {
  if (!translationResult.value) return

  // 这里需要根据实际的收藏逻辑来实现
  // 暂时只是切换本地状态
  isResultFavorite.value = !isResultFavorite.value

  uni.showToast({
    title: isResultFavorite.value ? '收藏成功' : '取消收藏',
    icon: 'success'
  })
}

// 交换语言
const handleSwapLanguages = () => {
  translationStore.swapLanguages()

  // 如果有翻译结果，交换源文本和目标文本
  if (translationResult.value) {
    const temp = sourceText.value
    sourceText.value = translationResult.value.translated_text
    translationResult.value = null
    isResultFavorite.value = false
  }
}

// 语言选择确认
const onSourceLanguageConfirm = (value: any) => {
  translationStore.setSourceLanguage(value[0].value)
  showSourceLanguagePicker.value = false

  // 如果有文本，重新翻译
  if (sourceText.value.trim()) {
    handleTranslate()
  }
}

const onTargetLanguageConfirm = (value: any) => {
  translationStore.setTargetLanguage(value[0].value)
  showTargetLanguagePicker.value = false

  // 如果有文本，重新翻译
  if (sourceText.value.trim()) {
    handleTranslate()
  }
}

// 使用语言对
const useLanguagePair = (sourceLang: string, targetLang: string) => {
  translationStore.useLanguagePair(sourceLang, targetLang)

  // 如果有文本，重新翻译
  if (sourceText.value.trim()) {
    handleTranslate()
  }
}

// 使用历史记录项
const useHistoryItem = (item: TranslationHistory) => {
  sourceText.value = item.source_text
  translationResult.value = {
    translated_text: item.translated_text,
    source_lang: item.source_lang,
    target_lang: item.target_lang,
    confidence: 0.95,
    word_count: item.word_count || item.source_text.split(' ').length,
    char_count: item.source_text.length
  }

  // 设置语言
  translationStore.setSourceLanguage(item.source_lang)
  translationStore.setTargetLanguage(item.target_lang)

  // 关闭弹窗
  showHistoryModal.value = false

  // 检查收藏状态
  checkIfFavorite()
}

// 删除历史记录项
const deleteHistoryItem = async (id: string) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条翻译记录吗？',
    success: async (res) => {
      if (res.confirm) {
        await translationStore.deleteHistoryItem(id)
      }
    }
  })
}

// 清空历史记录
const handleClearHistory = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有翻译历史吗？此操作不可恢复。',
    success: async (res) => {
      if (res.confirm) {
        await translationStore.clearHistory()
        showHistoryModal.value = false
      }
    }
  })
}

// 获取语言名称
const getLanguageName = (langCode: string) => {
  if (langCode === 'auto') return '自动检测'
  const lang = languages.find(l => l.code === langCode)
  return lang?.name || langCode
}

// 格式化时间
const formatTime = (time: string) => {
  return dayjs(time).fromNow()
}

// 页面下拉刷新
const onPullDownRefresh = async () => {
  try {
    await translationStore.loadHistory()
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '刷新失败',
      icon: 'error'
    })
  } finally {
    uni.stopPullDownRefresh()
  }
}

// 导出方法供页面使用
defineExpose({
  onPullDownRefresh
})
</script>

<style lang="scss" scoped>
.translation-page {
  min-height: 100vh;
  background-color: $bg-secondary;
}

// 语言选择栏
.language-bar {
  background-color: $bg-primary;
  padding: $spacing-4;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid $border-primary;

  .language-selector {
    flex: 1;
    text-align: center;
  }

  .swap-button {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $bg-tertiary;
    border-radius: $radius-full;
    margin: 0 $spacing-4;

    &:active {
      background-color: $gray-200;
    }
  }
}

// 快速语言对
.quick-pairs {
  background-color: $bg-primary;
  padding: $spacing-2 0;
  border-bottom: 1px solid $border-primary;

  .pairs-scroll {
    white-space: nowrap;

    .pairs-list {
      display: flex;
      padding: 0 $spacing-4;
      gap: $spacing-2;

      .pair-item {
        flex-shrink: 0;
        background-color: $bg-tertiary;
        border-radius: $radius-full;
        padding: $spacing-1 $spacing-3;

        .pair-text {
          font-size: $font-size-xs;
          color: $text-secondary;
          white-space: nowrap;
        }

        &:active {
          background-color: $gray-200;
        }
      }
    }
  }
}

// 翻译容器
.translation-container {
  padding: $spacing-4;
  gap: $spacing-4;
  display: flex;
  flex-direction: column;
}

// 输入区域
.input-section {
  background-color: $bg-primary;
  border-radius: $radius-lg;
  overflow: hidden;
  box-shadow: $shadow-sm;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-3 $spacing-4;
    background-color: $bg-tertiary;
    border-bottom: 1px solid $border-primary;

    .section-title {
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      color: $text-secondary;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: $spacing-2;
    }
  }

  .text-input {
    width: 100%;
    min-height: 200rpx;
    padding: $spacing-4;
    font-size: $font-size-base;
    line-height: $line-height-relaxed;
    color: $text-primary;
    background-color: transparent;
    border: none;
    outline: none;
    resize: none;

    &::placeholder {
      color: $text-quaternary;
    }
  }

  .input-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-3 $spacing-4;
    background-color: $bg-tertiary;
    border-top: 1px solid $border-primary;

    .char-count {
      font-size: $font-size-xs;
      color: $text-tertiary;
    }
  }
}

// 结果区域
.result-section {
  background-color: $bg-primary;
  border-radius: $radius-lg;
  overflow: hidden;
  box-shadow: $shadow-sm;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-3 $spacing-4;
    background-color: $primary-50;
    border-bottom: 1px solid $primary-200;

    .section-title {
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      color: $primary-700;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: $spacing-2;
    }
  }

  .result-content {
    padding: $spacing-4;

    .result-text {
      font-size: $font-size-base;
      line-height: $line-height-relaxed;
      color: $text-primary;
      word-break: break-word;
    }
  }

  .result-footer {
    padding: $spacing-3 $spacing-4;
    background-color: $bg-tertiary;
    border-top: 1px solid $border-primary;

    .result-info {
      font-size: $font-size-xs;
      color: $text-tertiary;
    }
  }
}

// 历史记录区域
.history-section {
  background-color: $bg-primary;
  border-radius: $radius-lg;
  overflow: hidden;
  box-shadow: $shadow-sm;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-3 $spacing-4;
    background-color: $bg-tertiary;
    border-bottom: 1px solid $border-primary;

    .section-title {
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      color: $text-secondary;
    }
  }

  .history-list {
    .history-item {
      padding: $spacing-4;
      border-bottom: 1px solid $border-primary;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background-color: $bg-tertiary;
      }

      .history-content {
        margin-bottom: $spacing-2;

        .history-source {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .history-target {
          display: block;
          font-size: $font-size-base;
          color: $text-primary;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .history-meta {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .history-langs {
          font-size: $font-size-xs;
          color: $text-tertiary;
        }

        .history-time {
          font-size: $font-size-xs;
          color: $text-quaternary;
        }
      }
    }
  }
}

// 历史记录弹窗
.history-modal {
  .history-scroll {
    .history-modal-item {
      display: flex;
      align-items: flex-start;
      padding: $spacing-3;
      border-bottom: 1px solid $border-primary;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background-color: $bg-tertiary;
      }

      .modal-item-content {
        flex: 1;
        margin-right: $spacing-3;

        .modal-source {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-1;
          line-height: $line-height-snug;
        }

        .modal-target {
          display: block;
          font-size: $font-size-base;
          color: $text-primary;
          margin-bottom: $spacing-2;
          line-height: $line-height-snug;
        }

        .modal-meta {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .modal-langs {
            font-size: $font-size-xs;
            color: $text-tertiary;
          }

          .modal-time {
            font-size: $font-size-xs;
            color: $text-quaternary;
          }
        }
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 480px) {
  .translation-container {
    padding: $spacing-3;
  }

  .input-section .text-input {
    min-height: 160rpx;
  }

  .language-bar {
    padding: $spacing-3;
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .translation-page {
    background-color: #1a1a1a;
  }

  .input-section,
  .result-section,
  .history-section {
    background-color: #2d2d2d;
    border-color: #404040;
  }

  .section-header {
    background-color: #404040;
  }

  .text-input {
    color: #ffffff;

    &::placeholder {
      color: #888888;
    }
  }
}
</style>
