/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 导入自定义样式变量 */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
.qrcode-generator-page.data-v-af3ddd8d {
  min-height: 100vh;
  background-color: #F9FAFB;
}
.page-header.data-v-af3ddd8d {
  background-color: #FFFFFF;
  padding: 48rpx 32rpx;
  text-align: center;
  border-bottom: 1px solid #E5E7EB;
}
.page-header .page-title.data-v-af3ddd8d {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #111827;
  margin-bottom: 16rpx;
}
.page-header .page-desc.data-v-af3ddd8d {
  font-size: 28rpx;
  color: #374151;
}
.section-title.data-v-af3ddd8d {
  padding: 32rpx 32rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.section-title .title-text.data-v-af3ddd8d {
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
}
.section-title .title-desc.data-v-af3ddd8d {
  font-size: 24rpx;
  color: #6B7280;
}
.section-title .preview-actions.data-v-af3ddd8d {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.type-selector.data-v-af3ddd8d {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.type-selector .type-tabs.data-v-af3ddd8d {
  display: flex;
  padding: 0 32rpx 32rpx;
  gap: 16rpx;
  overflow-x: auto;
}
.type-selector .type-tabs .tab-item.data-v-af3ddd8d {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background-color: #F3F4F6;
  border-radius: 16rpx;
  border: 2px solid transparent;
  min-width: 120rpx;
}
.type-selector .type-tabs .tab-item.active.data-v-af3ddd8d {
  border-color: #667eea;
  background-color: #EFF6FF;
}
.type-selector .type-tabs .tab-item .tab-text.data-v-af3ddd8d {
  font-size: 24rpx;
  color: #111827;
  margin-top: 8rpx;
}
.content-input.data-v-af3ddd8d {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.content-input .input-area.data-v-af3ddd8d {
  padding: 0 32rpx 32rpx;
}
.content-input .input-area .text-input.data-v-af3ddd8d,
.content-input .input-area .url-input.data-v-af3ddd8d {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  border: 1px solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #111827;
  background-color: #F3F4F6;
}
.content-input .input-area .text-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .url-input.data-v-af3ddd8d::-webkit-input-placeholder {
  color: #9CA3AF;
}
.content-input .input-area .text-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .url-input.data-v-af3ddd8d::placeholder {
  color: #9CA3AF;
}
.content-input .input-area .url-input.data-v-af3ddd8d {
  min-height: 80rpx;
}
.content-input .input-area .wifi-inputs.data-v-af3ddd8d,
.content-input .input-area .contact-inputs.data-v-af3ddd8d,
.content-input .input-area .sms-inputs.data-v-af3ddd8d,
.content-input .input-area .email-inputs.data-v-af3ddd8d {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.content-input .input-area .wifi-inputs .wifi-input.data-v-af3ddd8d,
.content-input .input-area .wifi-inputs .contact-input.data-v-af3ddd8d,
.content-input .input-area .wifi-inputs .sms-input.data-v-af3ddd8d,
.content-input .input-area .wifi-inputs .email-input.data-v-af3ddd8d,
.content-input .input-area .contact-inputs .wifi-input.data-v-af3ddd8d,
.content-input .input-area .contact-inputs .contact-input.data-v-af3ddd8d,
.content-input .input-area .contact-inputs .sms-input.data-v-af3ddd8d,
.content-input .input-area .contact-inputs .email-input.data-v-af3ddd8d,
.content-input .input-area .sms-inputs .wifi-input.data-v-af3ddd8d,
.content-input .input-area .sms-inputs .contact-input.data-v-af3ddd8d,
.content-input .input-area .sms-inputs .sms-input.data-v-af3ddd8d,
.content-input .input-area .sms-inputs .email-input.data-v-af3ddd8d,
.content-input .input-area .email-inputs .wifi-input.data-v-af3ddd8d,
.content-input .input-area .email-inputs .contact-input.data-v-af3ddd8d,
.content-input .input-area .email-inputs .sms-input.data-v-af3ddd8d,
.content-input .input-area .email-inputs .email-input.data-v-af3ddd8d {
  width: 100%;
  padding: 24rpx;
  border: 1px solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #111827;
  background-color: #F3F4F6;
}
.content-input .input-area .wifi-inputs .wifi-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .wifi-inputs .contact-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .wifi-inputs .sms-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .wifi-inputs .email-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .contact-inputs .wifi-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .contact-inputs .contact-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .contact-inputs .sms-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .contact-inputs .email-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .sms-inputs .wifi-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .sms-inputs .contact-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .sms-inputs .sms-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .sms-inputs .email-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .email-inputs .wifi-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .email-inputs .contact-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .email-inputs .sms-input.data-v-af3ddd8d::-webkit-input-placeholder, .content-input .input-area .email-inputs .email-input.data-v-af3ddd8d::-webkit-input-placeholder {
  color: #9CA3AF;
}
.content-input .input-area .wifi-inputs .wifi-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .wifi-inputs .contact-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .wifi-inputs .sms-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .wifi-inputs .email-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .contact-inputs .wifi-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .contact-inputs .contact-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .contact-inputs .sms-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .contact-inputs .email-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .sms-inputs .wifi-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .sms-inputs .contact-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .sms-inputs .sms-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .sms-inputs .email-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .email-inputs .wifi-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .email-inputs .contact-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .email-inputs .sms-input.data-v-af3ddd8d::placeholder,
.content-input .input-area .email-inputs .email-input.data-v-af3ddd8d::placeholder {
  color: #9CA3AF;
}
.content-input .input-area .wifi-inputs .wifi-security .security-label.data-v-af3ddd8d,
.content-input .input-area .contact-inputs .wifi-security .security-label.data-v-af3ddd8d,
.content-input .input-area .sms-inputs .wifi-security .security-label.data-v-af3ddd8d,
.content-input .input-area .email-inputs .wifi-security .security-label.data-v-af3ddd8d {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 16rpx;
}
.style-settings.data-v-af3ddd8d {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.style-settings .settings-content.data-v-af3ddd8d {
  padding: 0 32rpx 32rpx;
}
.style-settings .settings-content .setting-item.data-v-af3ddd8d {
  margin-bottom: 48rpx;
}
.style-settings .settings-content .setting-item .setting-label.data-v-af3ddd8d {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 24rpx;
}
.style-settings .settings-content .setting-item .color-picker.data-v-af3ddd8d {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}
.style-settings .settings-content .setting-item .color-picker .color-option.data-v-af3ddd8d {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  border: 2px solid transparent;
}
.style-settings .settings-content .setting-item .color-picker .color-option.active.data-v-af3ddd8d {
  border-color: #667eea;
}
.style-settings .settings-content .setting-item .logo-upload.data-v-af3ddd8d {
  margin-top: 24rpx;
}
.style-settings .settings-content .setting-item .logo-upload .upload-btn.data-v-af3ddd8d {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background-color: #F3F4F6;
  border: 1px dashed #E5E7EB;
  border-radius: 12rpx;
}
.style-settings .settings-content .setting-item .logo-upload .upload-btn .upload-text.data-v-af3ddd8d {
  font-size: 24rpx;
  color: #374151;
}
.style-settings .settings-content .setting-item .logo-upload .logo-preview.data-v-af3ddd8d {
  width: 120rpx;
  height: 120rpx;
  margin-top: 16rpx;
  border-radius: 12rpx;
  background-color: #F3F4F6;
}
.qrcode-preview.data-v-af3ddd8d {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.qrcode-preview .preview-content.data-v-af3ddd8d {
  padding: 0 32rpx 32rpx;
}
.qrcode-preview .preview-content .qrcode-container.data-v-af3ddd8d {
  display: flex;
  justify-content: center;
  margin-bottom: 32rpx;
}
.qrcode-preview .preview-content .qrcode-container .qrcode-canvas.data-v-af3ddd8d {
  border: 1px solid #E5E7EB;
  border-radius: 12rpx;
  background-color: #F3F4F6;
}
.qrcode-preview .preview-content .qrcode-info.data-v-af3ddd8d {
  background-color: #F3F4F6;
  border-radius: 16rpx;
  padding: 32rpx;
}
.qrcode-preview .preview-content .qrcode-info .info-item.data-v-af3ddd8d {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 8rpx;
}
.qrcode-preview .preview-content .qrcode-info .info-item.data-v-af3ddd8d:last-child {
  margin-bottom: 0;
}
.generation-history.data-v-af3ddd8d {
  background-color: #FFFFFF;
}
.generation-history .history-list.data-v-af3ddd8d {
  padding: 0 32rpx 32rpx;
}
.generation-history .history-list .history-item.data-v-af3ddd8d {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  background-color: #F3F4F6;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}
.generation-history .history-list .history-item.data-v-af3ddd8d:last-child {
  margin-bottom: 0;
}
.generation-history .history-list .history-item.data-v-af3ddd8d:active {
  background-color: #E5E7EB;
}
.generation-history .history-list .history-item .history-qrcode.data-v-af3ddd8d {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  background-color: #E5E7EB;
}
.generation-history .history-list .history-item .history-content.data-v-af3ddd8d {
  flex: 1;
}
.generation-history .history-list .history-item .history-content .history-type.data-v-af3ddd8d {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
}
.generation-history .history-list .history-item .history-content .history-desc.data-v-af3ddd8d {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.generation-history .history-list .history-item .history-content .history-time.data-v-af3ddd8d {
  font-size: 20rpx;
  color: #6B7280;
}
.generation-history .history-list .history-item .history-actions.data-v-af3ddd8d {
  padding: 16rpx;
}
.generate-button.data-v-af3ddd8d {
  padding: 32rpx;
}
@media screen and (max-width: 480px) {
.type-tabs .tab-item.data-v-af3ddd8d {
    min-width: 100rpx;
    padding: 16rpx;
}
.color-picker .color-option.data-v-af3ddd8d {
    width: 48rpx;
    height: 48rpx;
}
.qrcode-container .qrcode-canvas.data-v-af3ddd8d {
    max-width: 100%;
    height: auto;
}
}
@media (prefers-color-scheme: dark) {
.qrcode-generator-page.data-v-af3ddd8d {
    background-color: #1a1a1a;
}
.page-header.data-v-af3ddd8d,
.type-selector.data-v-af3ddd8d,
.content-input.data-v-af3ddd8d,
.style-settings.data-v-af3ddd8d,
.qrcode-preview.data-v-af3ddd8d,
.generation-history.data-v-af3ddd8d {
    background-color: #2d2d2d;
    border-color: #404040;
}
.tab-item.data-v-af3ddd8d,
.text-input.data-v-af3ddd8d,
.url-input.data-v-af3ddd8d,
.wifi-input.data-v-af3ddd8d,
.contact-input.data-v-af3ddd8d,
.sms-input.data-v-af3ddd8d,
.email-input.data-v-af3ddd8d,
.upload-btn.data-v-af3ddd8d,
.qrcode-info.data-v-af3ddd8d,
.history-item.data-v-af3ddd8d {
    background-color: #404040;
    border-color: #555555;
}
.tab-item.active.data-v-af3ddd8d {
    background-color: #1e3a8a;
    border-color: #3b82f6;
}
.text-input.data-v-af3ddd8d,
.url-input.data-v-af3ddd8d,
.wifi-input.data-v-af3ddd8d,
.contact-input.data-v-af3ddd8d,
.sms-input.data-v-af3ddd8d,
.email-input.data-v-af3ddd8d {
    color: #ffffff;
}
.text-input.data-v-af3ddd8d::-webkit-input-placeholder, .url-input.data-v-af3ddd8d::-webkit-input-placeholder, .wifi-input.data-v-af3ddd8d::-webkit-input-placeholder, .contact-input.data-v-af3ddd8d::-webkit-input-placeholder, .sms-input.data-v-af3ddd8d::-webkit-input-placeholder, .email-input.data-v-af3ddd8d::-webkit-input-placeholder {
    color: #888888;
}
.text-input.data-v-af3ddd8d::placeholder,
.url-input.data-v-af3ddd8d::placeholder,
.wifi-input.data-v-af3ddd8d::placeholder,
.contact-input.data-v-af3ddd8d::placeholder,
.sms-input.data-v-af3ddd8d::placeholder,
.email-input.data-v-af3ddd8d::placeholder {
    color: #888888;
}
.qrcode-canvas.data-v-af3ddd8d,
.history-qrcode.data-v-af3ddd8d {
    background-color: #555555;
}
}