/**
 * HTTP请求工具
 */

import axios from 'axios';
import { ElMessage } from 'element-plus';

// 动态获取 API 基础 URL
const getApiBaseUrl = () => {
  // 优先使用环境变量配置
  if (import.meta.env.VITE_API_BASE_URL) {
    console.log('[API] 使用环境变量配置的API地址:', import.meta.env.VITE_API_BASE_URL);
    return import.meta.env.VITE_API_BASE_URL;
  }

  const { hostname, protocol } = window.location;

  // 如果是 localhost 访问，使用相对路径让 Vite 代理处理
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    console.log('[API] 使用 localhost，通过 Vite 代理');
    return '';
  } else {
    // IP 访问，直连后端
    const apiUrl = `${protocol}//${hostname}:8000`;
    console.log('[API] 使用动态IP地址:', apiUrl);
    return apiUrl;
  }
};

// 创建axios实例
const service = axios.create({
  baseURL: getApiBaseUrl(), // 动态后端API地址
  timeout: 30000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    console.log('发送请求:', config.method?.toUpperCase(), config.url, config.data || config.params);
    return config;
  },
  error => {
    // 对请求错误做些什么
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    console.log('收到响应:', response.status, response.config.url, response.data);
    
    const res = response.data;
    
    // 如果响应状态码不是200，则判断为错误
    if (response.status !== 200) {
      ElMessage.error(res.message || '请求失败');
      return Promise.reject(new Error(res.message || '请求失败'));
    }
    
    return res;
  },
  error => {
    // 对响应错误做点什么
    console.error('响应错误:', error);
    
    let message = '网络错误';
    
    if (error.response) {
      // 服务器返回了错误状态码
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          message = data.detail || '请求参数错误';
          break;
        case 401:
          message = '未授权，请重新登录';
          // 可以在这里处理token过期，跳转到登录页
          break;
        case 403:
          message = '拒绝访问';
          break;
        case 404:
          message = '请求的资源不存在';
          break;
        case 500:
          message = '服务器内部错误';
          break;
        default:
          message = data.detail || `请求失败 (${status})`;
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      message = '网络连接失败，请检查网络';
    } else {
      // 发生了一些问题，触发了请求错误
      message = error.message || '请求配置错误';
    }
    
    ElMessage.error(message);
    return Promise.reject(error);
  }
);

/**
 * 通用请求方法
 */
export function request(config) {
  return service(config);
}

/**
 * GET请求
 */
export function get(url, params = {}) {
  return service({
    method: 'get',
    url,
    params
  });
}

/**
 * POST请求
 */
export function post(url, data = {}) {
  return service({
    method: 'post',
    url,
    data
  });
}

/**
 * PUT请求
 */
export function put(url, data = {}) {
  return service({
    method: 'put',
    url,
    data
  });
}

/**
 * DELETE请求
 */
export function del(url, params = {}) {
  return service({
    method: 'delete',
    url,
    params
  });
}

/**
 * 上传文件
 */
export function upload(url, formData) {
  return service({
    method: 'post',
    url,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 下载文件
 */
export function download(url, params = {}) {
  return service({
    method: 'get',
    url,
    params,
    responseType: 'blob'
  });
}

export default service;
