{"version": 3, "file": "index.js", "sources": ["components/TabBar/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovd29ya3NwYWNlL0FJX3N5c3RlbS9tb2JpbGUtdW5pYXBwL3NyYy9jb21wb25lbnRzL1RhYkJhci9pbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"custom-tabbar\">\n    <view \n      class=\"tab-item\"\n      v-for=\"(item, index) in tabList\"\n      :key=\"index\"\n      :class=\"{ active: currentTab === index }\"\n      @click=\"switchTab(index)\"\n    >\n      <view class=\"tab-icon\">\n        <view class=\"icon-wrapper\" :class=\"{ active: currentTab === index }\">\n          <text class=\"iconfont\" :class=\"currentTab === index ? item.selectedIcon : item.icon\"></text>\n          <view class=\"icon-badge\" v-if=\"item.badge\">{{ item.badge }}</view>\n        </view>\n      </view>\n      <text class=\"tab-text\" :class=\"{ active: currentTab === index }\">{{ item.text }}</text>\n      <view class=\"tab-indicator\" v-if=\"currentTab === index\"></view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted } from 'vue'\n\ninterface TabItem {\n  text: string\n  icon: string\n  selectedIcon: string\n  pagePath: string\n  badge?: string | number\n}\n\nconst currentTab = ref<number>(0)\n\nconst tabList: TabItem[] = [\n  {\n    text: '首页',\n    icon: 'icon-home',\n    selectedIcon: 'icon-home-fill',\n    pagePath: '/pages/index/index'\n  },\n  {\n    text: '翻译',\n    icon: 'icon-translate',\n    selectedIcon: 'icon-translate-fill',\n    pagePath: '/pages/translation/text/index'\n  },\n  {\n    text: 'AI助手',\n    icon: 'icon-robot',\n    selectedIcon: 'icon-robot-fill',\n    pagePath: '/pages/digital-human/chat/index',\n    badge: 'New'\n  },\n  {\n    text: '智能体',\n    icon: 'icon-apps',\n    selectedIcon: 'icon-apps-fill',\n    pagePath: '/pages/ai-agents/market/index'\n  },\n  {\n    text: '我的',\n    icon: 'icon-user',\n    selectedIcon: 'icon-user-fill',\n    pagePath: '/pages/user/profile/index'\n  }\n]\n\nonMounted(() => {\n  // 获取当前页面路径，设置对应的tab\n  const pages = getCurrentPages()\n  if (pages.length > 0) {\n    const currentPage = pages[pages.length - 1]\n    const currentRoute = '/' + currentPage.route\n    const tabIndex = tabList.findIndex(item => item.pagePath === currentRoute)\n    if (tabIndex !== -1) {\n      currentTab.value = tabIndex\n    }\n  }\n})\n\nconst switchTab = (index: number) => {\n  if (currentTab.value === index) return\n  \n  currentTab.value = index\n  const targetPage = tabList[index].pagePath\n  \n  uni.switchTab({\n    url: targetPage,\n    fail: (err) => {\n      uni.__f__('error','at components/TabBar/index.vue:91','Tab switch failed:', err)\n      // 如果switchTab失败，尝试使用navigateTo\n      uni.reLaunch({\n        url: targetPage\n      })\n    }\n  })\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.custom-tabbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 120rpx;\n  background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 1) 100%);\n  backdrop-filter: blur(20rpx);\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\n  display: flex;\n  align-items: center;\n  padding: 0 20rpx 20rpx;\n  z-index: 1000;\n  \n  // 安全区域适配\n  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));\n  \n  .tab-item {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    position: relative;\n    padding: 8rpx 0;\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    \n    &:active {\n      transform: scale(0.95);\n    }\n    \n    .tab-icon {\n      position: relative;\n      margin-bottom: 4rpx;\n      \n      .icon-wrapper {\n        width: 48rpx;\n        height: 48rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        border-radius: 24rpx;\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        position: relative;\n        \n        &.active {\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          transform: translateY(-2rpx);\n          box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);\n        }\n        \n        .iconfont {\n          font-size: 24rpx;\n          color: #8E8E93;\n          transition: all 0.3s ease;\n        }\n        \n        &.active .iconfont {\n          color: #FFFFFF;\n          font-size: 26rpx;\n        }\n        \n        .icon-badge {\n          position: absolute;\n          top: -6rpx;\n          right: -8rpx;\n          background: linear-gradient(45deg, #FF6B6B, #FF8E53);\n          color: white;\n          font-size: 18rpx;\n          padding: 2rpx 6rpx;\n          border-radius: 10rpx;\n          min-width: 20rpx;\n          height: 20rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);\n          animation: pulse 2s infinite;\n        }\n      }\n    }\n    \n    .tab-text {\n      font-size: 20rpx;\n      color: #8E8E93;\n      font-weight: 500;\n      transition: all 0.3s ease;\n      \n      &.active {\n        color: #667eea;\n        font-weight: 600;\n        transform: scale(1.05);\n      }\n    }\n    \n    .tab-indicator {\n      position: absolute;\n      bottom: -2rpx;\n      width: 20rpx;\n      height: 4rpx;\n      background: linear-gradient(90deg, #667eea, #764ba2);\n      border-radius: 2rpx;\n      animation: slideIn 0.3s ease;\n    }\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n}\n\n@keyframes slideIn {\n  from {\n    width: 0;\n    opacity: 0;\n  }\n  to {\n    width: 20rpx;\n    opacity: 1;\n  }\n}\n\n// 暗色模式适配\n@media (prefers-color-scheme: dark) {\n  .custom-tabbar {\n    background: linear-gradient(180deg, rgba(28, 28, 30, 0.95) 0%, rgba(28, 28, 30, 1) 100%);\n    border-top-color: rgba(255, 255, 255, 0.1);\n    \n    .tab-item {\n      .tab-icon .icon-wrapper {\n        .iconfont {\n          color: #8E8E93;\n        }\n        \n        &.active .iconfont {\n          color: #FFFFFF;\n        }\n      }\n      \n      .tab-text {\n        color: #8E8E93;\n        \n        &.active {\n          color: #667eea;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import Component from 'E:/workspace/AI_system/mobile-uniapp/src/components/TabBar/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "onMounted", "uni"], "mappings": ";;;;;AAgCM,UAAA,aAAaA,kBAAY,CAAC;AAEhC,UAAM,UAAqB;AAAA,MACzB;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,cAAc;AAAA,QACd,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,cAAc;AAAA,QACd,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,cAAc;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,cAAc;AAAA,QACd,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,cAAc;AAAA,QACd,UAAU;AAAA,MACZ;AAAA,IAAA;AAGFC,kBAAAA,UAAU,MAAM;AAEd,YAAM,QAAQ;AACV,UAAA,MAAM,SAAS,GAAG;AACpB,cAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AACpC,cAAA,eAAe,MAAM,YAAY;AACvC,cAAM,WAAW,QAAQ,UAAU,CAAQ,SAAA,KAAK,aAAa,YAAY;AACzE,YAAI,aAAa,IAAI;AACnB,qBAAW,QAAQ;AAAA,QACrB;AAAA,MACF;AAAA,IAAA,CACD;AAEK,UAAA,YAAY,CAAC,UAAkB;AACnC,UAAI,WAAW,UAAU;AAAO;AAEhC,iBAAW,QAAQ;AACb,YAAA,aAAa,QAAQ,KAAK,EAAE;AAElCC,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,QACL,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAI,MAAM,SAAQ,qCAAoC,sBAAsB,GAAG;AAE/EA,wBAAAA,MAAI,SAAS;AAAA,YACX,KAAK;AAAA,UAAA,CACN;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;AC/FH,GAAG,gBAAgB,SAAS;"}