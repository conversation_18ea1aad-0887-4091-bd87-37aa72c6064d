import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { 
  digitalHumanApi, 
  type DigitalHuman, 
  type ChatSession, 
  type ChatMessage,
  type VoiceConfig 
} from '@/api/digital-human'

export const useDigitalHumanStore = defineStore('digitalHuman', () => {
  // 状态
  const digitalHumans = ref<DigitalHuman[]>([])
  const currentDigitalHuman = ref<DigitalHuman | null>(null)
  const chatSessions = ref<ChatSession[]>([])
  const currentSession = ref<ChatSession | null>(null)
  const messages = ref<ChatMessage[]>([])
  const isLoading = ref<boolean>(false)
  const isSending = ref<boolean>(false)
  const isGeneratingVideo = ref<boolean>(false)
  const voiceConfig = ref<VoiceConfig>({
    voice_id: 'default',
    speed: 1.0,
    pitch: 1.0,
    volume: 1.0,
    emotion: 'neutral'
  })

  // 计算属性
  const hasDigitalHumans = computed(() => digitalHumans.value.length > 0)
  
  const currentSessionMessages = computed(() => {
    if (!currentSession.value) return []
    return messages.value.filter(msg => msg.id.includes(currentSession.value!.id))
  })

  const recentSessions = computed(() => {
    return chatSessions.value
      .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
      .slice(0, 5)
  })

  // 加载数字人列表
  const loadDigitalHumans = async (page = 1, limit = 20) => {
    try {
      isLoading.value = true
      const response = await digitalHumanApi.getDigitalHumans({ page, limit })
      
      if (response.success) {
        if (page === 1) {
          digitalHumans.value = response.data.items
        } else {
          digitalHumans.value.push(...response.data.items)
        }
        return response.data
      }
    } catch (error) {
      console.error('加载数字人列表失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 选择数字人
  const selectDigitalHuman = async (digitalHuman: DigitalHuman) => {
    currentDigitalHuman.value = digitalHuman
    
    // 加载该数字人的对话会话
    await loadChatSessions()
  }

  // 加载对话会话
  const loadChatSessions = async (page = 1, limit = 20) => {
    if (!currentDigitalHuman.value) return
    
    try {
      const response = await digitalHumanApi.getChatSessions({ page, limit })
      
      if (response.success) {
        if (page === 1) {
          chatSessions.value = response.data.items
        } else {
          chatSessions.value.push(...response.data.items)
        }
        return response.data
      }
    } catch (error) {
      console.error('加载对话会话失败:', error)
      throw error
    }
  }

  // 创建新会话
  const createNewSession = async (title?: string) => {
    if (!currentDigitalHuman.value) {
      throw new Error('请先选择数字人')
    }

    try {
      const response = await digitalHumanApi.createChatSession(
        currentDigitalHuman.value.id,
        title
      )
      
      if (response.success) {
        const newSession = response.data
        chatSessions.value.unshift(newSession)
        currentSession.value = newSession
        messages.value = []
        
        return newSession
      } else {
        throw new Error(response.message || '创建会话失败')
      }
    } catch (error) {
      console.error('创建会话失败:', error)
      throw error
    }
  }

  // 选择会话
  const selectSession = async (session: ChatSession) => {
    currentSession.value = session
    await loadMessages()
  }

  // 加载消息
  const loadMessages = async (page = 1, limit = 50) => {
    if (!currentSession.value) return
    
    try {
      const response = await digitalHumanApi.getChatMessages(
        currentSession.value.id,
        { page, limit }
      )
      
      if (response.success) {
        if (page === 1) {
          messages.value = response.data.items.reverse() // 消息按时间正序显示
        } else {
          messages.value.unshift(...response.data.items.reverse())
        }
        return response.data
      }
    } catch (error) {
      console.error('加载消息失败:', error)
      throw error
    }
  }

  // 发送文本消息
  const sendTextMessage = async (content: string) => {
    if (!currentSession.value || !content.trim()) {
      throw new Error('会话或消息内容不能为空')
    }

    try {
      isSending.value = true
      
      // 添加用户消息到本地
      const userMessage: ChatMessage = {
        id: `temp_${Date.now()}`,
        role: 'user',
        content: content.trim(),
        timestamp: new Date().toISOString(),
        message_type: 'text'
      }
      messages.value.push(userMessage)

      // 发送消息到服务器
      const response = await digitalHumanApi.sendTextMessage(
        currentSession.value.id,
        content.trim()
      )
      
      if (response.success) {
        // 更新用户消息ID
        const messageIndex = messages.value.findIndex(msg => msg.id === userMessage.id)
        if (messageIndex !== -1) {
          messages.value[messageIndex] = response.data
        }

        // 获取数字人回复
        await getDigitalHumanReply(response.data.id)
        
        return response.data
      } else {
        throw new Error(response.message || '发送消息失败')
      }
    } catch (error) {
      console.error('发送消息失败:', error)
      
      // 移除失败的消息
      messages.value = messages.value.filter(msg => !msg.id.startsWith('temp_'))
      
      throw error
    } finally {
      isSending.value = false
    }
  }

  // 发送语音消息
  const sendAudioMessage = async (filePath: string) => {
    if (!currentSession.value) {
      throw new Error('请先选择会话')
    }

    try {
      isSending.value = true
      
      const response = await digitalHumanApi.sendAudioMessage(
        currentSession.value.id,
        filePath
      )
      
      if (response.success) {
        messages.value.push(response.data)
        
        // 获取数字人回复
        await getDigitalHumanReply(response.data.id)
        
        return response.data
      } else {
        throw new Error(response.message || '发送语音消息失败')
      }
    } catch (error) {
      console.error('发送语音消息失败:', error)
      throw error
    } finally {
      isSending.value = false
    }
  }

  // 获取数字人回复
  const getDigitalHumanReply = async (messageId: string, responseType: 'text' | 'audio' | 'video' = 'text') => {
    if (!currentSession.value) return

    try {
      const response = await digitalHumanApi.getDigitalHumanReply(
        currentSession.value.id,
        messageId,
        {
          voice_config: voiceConfig.value,
          response_type: responseType
        }
      )
      
      if (response.success) {
        messages.value.push(response.data)
        
        // 更新会话的最后消息时间
        if (currentSession.value) {
          currentSession.value.updated_at = new Date().toISOString()
          currentSession.value.last_message = response.data.content
        }
        
        return response.data
      }
    } catch (error) {
      console.error('获取数字人回复失败:', error)
      
      // 添加错误消息
      const errorMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        role: 'assistant',
        content: '抱歉，我现在无法回复您的消息，请稍后再试。',
        timestamp: new Date().toISOString(),
        message_type: 'text'
      }
      messages.value.push(errorMessage)
    }
  }

  // 生成视频回复
  const generateVideoReply = async (messageId: string) => {
    if (!currentSession.value) return

    try {
      isGeneratingVideo.value = true
      
      const response = await digitalHumanApi.generateVideoReply(
        currentSession.value.id,
        messageId,
        voiceConfig.value
      )
      
      if (response.success) {
        const taskId = response.data.task_id
        
        // 轮询任务状态
        return await pollVideoTaskStatus(taskId)
      } else {
        throw new Error(response.message || '生成视频失败')
      }
    } catch (error) {
      console.error('生成视频回复失败:', error)
      throw error
    } finally {
      isGeneratingVideo.value = false
    }
  }

  // 轮询视频任务状态
  const pollVideoTaskStatus = async (taskId: string): Promise<string | null> => {
    const maxAttempts = 30 // 最多轮询30次
    let attempts = 0
    
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++
          const response = await digitalHumanApi.getVideoTaskStatus(taskId)
          
          if (response.success) {
            const { status, video_url, error_message } = response.data
            
            if (status === 'completed' && video_url) {
              resolve(video_url)
            } else if (status === 'failed') {
              reject(new Error(error_message || '视频生成失败'))
            } else if (attempts >= maxAttempts) {
              reject(new Error('视频生成超时'))
            } else {
              // 继续轮询
              setTimeout(poll, 2000) // 2秒后再次检查
            }
          } else {
            reject(new Error('获取任务状态失败'))
          }
        } catch (error) {
          reject(error)
        }
      }
      
      poll()
    })
  }

  // 删除会话
  const deleteSession = async (sessionId: string) => {
    try {
      const response = await digitalHumanApi.deleteChatSession(sessionId)
      
      if (response.success) {
        chatSessions.value = chatSessions.value.filter(session => session.id !== sessionId)
        
        // 如果删除的是当前会话，清空当前会话
        if (currentSession.value?.id === sessionId) {
          currentSession.value = null
          messages.value = []
        }
        
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('删除会话失败:', error)
      uni.showToast({
        title: '删除失败',
        icon: 'error'
      })
    }
  }

  // 清空消息
  const clearMessages = async () => {
    if (!currentSession.value) return

    try {
      const response = await digitalHumanApi.clearChatMessages(currentSession.value.id)
      
      if (response.success) {
        messages.value = []
        uni.showToast({
          title: '清空成功',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('清空消息失败:', error)
      uni.showToast({
        title: '清空失败',
        icon: 'error'
      })
    }
  }

  // 更新语音配置
  const updateVoiceConfig = (config: Partial<VoiceConfig>) => {
    voiceConfig.value = { ...voiceConfig.value, ...config }
  }

  return {
    // 状态
    digitalHumans,
    currentDigitalHuman,
    chatSessions,
    currentSession,
    messages,
    isLoading,
    isSending,
    isGeneratingVideo,
    voiceConfig,

    // 计算属性
    hasDigitalHumans,
    currentSessionMessages,
    recentSessions,

    // 方法
    loadDigitalHumans,
    selectDigitalHuman,
    loadChatSessions,
    createNewSession,
    selectSession,
    loadMessages,
    sendTextMessage,
    sendAudioMessage,
    getDigitalHumanReply,
    generateVideoReply,
    deleteSession,
    clearMessages,
    updateVoiceConfig
  }
}, {
  persist: {
    key: 'digital-human-store',
    paths: ['currentDigitalHuman', 'voiceConfig']
  }
})
