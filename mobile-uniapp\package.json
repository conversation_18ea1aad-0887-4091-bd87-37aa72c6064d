{"name": "ai-system-mobile", "version": "1.0.0", "description": "AI系统移动端 - 基于Uni-app的多端应用", "main": "main.js", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "dev:h5": "cross-env UNI_PLATFORM=h5 vite --host 0.0.0.0 --port 3000", "dev:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin vite", "dev:mp-alipay": "cross-env UNI_PLATFORM=mp-alipay vite", "dev:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu vite", "dev:mp-toutiao": "cross-env UNI_PLATFORM=mp-toutiao vite", "dev:mp-qq": "cross-env UNI_PLATFORM=mp-qq vite", "dev:app": "cross-env UNI_PLATFORM=app vite", "dev:app-android": "cross-env UNI_PLATFORM=app-android vite", "dev:app-ios": "cross-env UNI_PLATFORM=app-ios vite", "dev:quickapp-webview": "cross-env UNI_PLATFORM=quickapp-webview vite", "serve": "http-server . -p 3000 -o", "build:h5": "cross-env UNI_PLATFORM=h5 vite build", "build:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin vite build", "build:mp-alipay": "cross-env UNI_PLATFORM=mp-alipay vite build", "build:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu vite build", "build:mp-toutiao": "cross-env UNI_PLATFORM=mp-toutiao vite build", "build:mp-qq": "cross-env UNI_PLATFORM=mp-qq vite build", "build:app": "cross-env UNI_PLATFORM=app vite build", "build:app-android": "cross-env UNI_PLATFORM=app-android vite build", "build:app-ios": "cross-env UNI_PLATFORM=app-ios vite build", "build:quickapp-webview": "cross-env UNI_PLATFORM=quickapp-webview vite build", "type-check": "vue-tsc --noEmit", "preview": "vite preview"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4020920240930001", "@dcloudio/uni-app-plus": "3.0.0-4020920240930001", "@dcloudio/uni-components": "3.0.0-4020920240930001", "@dcloudio/uni-h5": "3.0.0-4020920240930001", "@dcloudio/uni-mp-alipay": "3.0.0-4020920240930001", "@dcloudio/uni-mp-baidu": "3.0.0-4020920240930001", "@dcloudio/uni-mp-qq": "3.0.0-4020920240930001", "@dcloudio/uni-mp-toutiao": "3.0.0-4020920240930001", "@dcloudio/uni-mp-weixin": "3.0.0-4020920240930001", "@dcloudio/uni-quickapp-webview": "3.0.0-4020920240930001", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-i18n": "^9.9.1", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "uview-ui": "^2.0.36"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "^3.0.0-4010920231211001", "@dcloudio/uni-cli-shared": "^3.0.0-4010920231211001", "@dcloudio/vite-plugin-uni": "^3.0.0-4010920231211001", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@vue/tsconfig": "^0.5.1", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.21.1", "http-server": "^14.1.1", "sass": "^1.70.0", "typescript": "^5.3.3", "vite": "^5.1.4", "vue-tsc": "^1.8.27"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}