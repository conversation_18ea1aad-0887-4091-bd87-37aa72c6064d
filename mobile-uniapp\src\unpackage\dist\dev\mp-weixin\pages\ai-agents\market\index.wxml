<view class="ai-agents-market data-v-4fe86039"><view class="header-section data-v-4fe86039"><view class="search-container data-v-4fe86039"><view class="search-box data-v-4fe86039"><u-icon wx:if="{{a}}" class="data-v-4fe86039" u-i="4fe86039-0" bind:__l="__l" u-p="{{a}}"></u-icon><input class="search-input data-v-4fe86039" placeholder="搜索智能体..." bindinput="{{b}}" value="{{c}}"/><view class="filter-btn data-v-4fe86039" bindtap="{{e}}"><u-icon wx:if="{{d}}" class="data-v-4fe86039" u-i="4fe86039-1" bind:__l="__l" u-p="{{d}}"></u-icon></view></view></view><scroll-view class="category-scroll data-v-4fe86039" scroll-x="true"><view class="category-list data-v-4fe86039"><view wx:for="{{f}}" wx:for-item="category" wx:key="b" class="{{['category-item', 'data-v-4fe86039', category.c && 'active']}}" bindtap="{{category.d}}"><text class="category-text data-v-4fe86039">{{category.a}}</text></view></view></scroll-view></view><scroll-view class="agents-content data-v-4fe86039" scroll-y="true"><view wx:if="{{g}}" class="section data-v-4fe86039"><view class="section-header data-v-4fe86039"><text class="section-title data-v-4fe86039">🔥 热门推荐</text><text class="section-more data-v-4fe86039" bindtap="{{h}}">更多</text></view><view class="agents-grid data-v-4fe86039"><view wx:for="{{i}}" wx:for-item="agent" wx:key="o" class="agent-card featured data-v-4fe86039" bindtap="{{agent.p}}"><view class="card-header data-v-4fe86039"><image class="agent-avatar data-v-4fe86039" src="{{agent.a}}" mode="aspectFill"></image><view wx:if="{{agent.b}}" class="agent-badge data-v-4fe86039">PRO</view><view class="agent-rating data-v-4fe86039"><u-icon wx:if="{{j}}" class="data-v-4fe86039" u-i="{{agent.c}}" bind:__l="__l" u-p="{{j}}"></u-icon><text class="rating-text data-v-4fe86039">{{agent.d}}</text></view></view><view class="card-content data-v-4fe86039"><text class="agent-name data-v-4fe86039">{{agent.e}}</text><text class="agent-desc data-v-4fe86039">{{agent.f}}</text><view class="agent-tags data-v-4fe86039"><text wx:for="{{agent.g}}" wx:for-item="tag" wx:key="b" class="tag data-v-4fe86039">{{tag.a}}</text></view><view class="agent-stats data-v-4fe86039"><view class="stat-item data-v-4fe86039"><u-icon wx:if="{{k}}" class="data-v-4fe86039" u-i="{{agent.h}}" bind:__l="__l" u-p="{{k}}"></u-icon><text class="stat-text data-v-4fe86039">{{agent.i}}</text></view><view class="stat-item data-v-4fe86039"><u-icon wx:if="{{l}}" class="data-v-4fe86039" u-i="{{agent.j}}" bind:__l="__l" u-p="{{l}}"></u-icon><text class="stat-text data-v-4fe86039">{{agent.k}}</text></view></view></view><view class="card-action data-v-4fe86039"><view wx:if="{{agent.l}}" class="price-info data-v-4fe86039"><text class="price data-v-4fe86039">¥{{agent.m}}/月</text></view><view wx:else class="price-info data-v-4fe86039"><text class="price free data-v-4fe86039">免费</text></view><view class="action-btn data-v-4fe86039" catchtap="{{agent.n}}"><text class="btn-text data-v-4fe86039">立即使用</text></view></view></view></view></view><view class="section data-v-4fe86039"><view class="section-header data-v-4fe86039"><text class="section-title data-v-4fe86039">{{m}}</text><text class="section-count data-v-4fe86039">{{n}}个智能体</text></view><view class="agents-list data-v-4fe86039"><view wx:for="{{o}}" wx:for-item="agent" wx:key="k" class="agent-item data-v-4fe86039" bindtap="{{agent.l}}"><image class="item-avatar data-v-4fe86039" src="{{agent.a}}" mode="aspectFill"></image><view class="item-content data-v-4fe86039"><view class="item-header data-v-4fe86039"><text class="item-name data-v-4fe86039">{{agent.b}}</text><view class="item-rating data-v-4fe86039"><u-icon wx:if="{{p}}" class="data-v-4fe86039" u-i="{{agent.c}}" bind:__l="__l" u-p="{{p}}"></u-icon><text class="rating-text data-v-4fe86039">{{agent.d}}</text></view></view><text class="item-desc data-v-4fe86039">{{agent.e}}</text><view class="item-footer data-v-4fe86039"><view class="item-tags data-v-4fe86039"><text wx:for="{{agent.f}}" wx:for-item="tag" wx:key="b" class="mini-tag data-v-4fe86039">{{tag.a}}</text></view><view class="item-price data-v-4fe86039"><text wx:if="{{agent.g}}" class="price-text data-v-4fe86039">¥{{agent.h}}/月</text><text wx:else class="price-text free data-v-4fe86039">免费</text></view></view></view><view class="item-action data-v-4fe86039"><view class="action-btn mini data-v-4fe86039" catchtap="{{agent.j}}"><u-icon wx:if="{{q}}" class="data-v-4fe86039" u-i="{{agent.i}}" bind:__l="__l" u-p="{{q}}"></u-icon></view></view></view></view></view><view class="bottom-safe-area data-v-4fe86039"></view></scroll-view></view>