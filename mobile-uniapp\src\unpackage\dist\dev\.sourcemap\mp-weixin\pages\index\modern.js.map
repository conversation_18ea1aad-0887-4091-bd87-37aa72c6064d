{"version": 3, "file": "modern.js", "sources": ["pages/index/modern.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvbW9kZXJuLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"modern-home\">\n    <!-- 状态栏占位 -->\n    <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n    \n    <!-- 头部区域 -->\n    <view class=\"header-section\">\n      <view class=\"header-background\">\n        <view class=\"gradient-bg\"></view>\n        <view class=\"pattern-overlay\"></view>\n      </view>\n      \n      <view class=\"header-content\">\n        <!-- 用户信息区 -->\n        <view class=\"user-area\">\n          <view class=\"user-info\">\n            <view class=\"avatar-container\" @click=\"goToProfile\">\n              <image class=\"user-avatar\" :src=\"userInfo.avatar\" mode=\"aspectFill\"></image>\n              <view class=\"avatar-ring\"></view>\n              <view class=\"online-indicator\"></view>\n            </view>\n            <view class=\"user-details\">\n              <text class=\"greeting\">{{ getGreeting() }}</text>\n              <text class=\"username\">{{ userInfo.nickname || 'AI Explorer' }}</text>\n              <view class=\"user-badge\">\n                <text class=\"badge-text\">Pro</text>\n                <u-icon name=\"star-fill\" size=\"10\" color=\"#FFD700\"></u-icon>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"header-actions\">\n            <view class=\"action-item\" @click=\"showNotifications\">\n              <u-icon name=\"bell\" size=\"20\" color=\"#FFFFFF\"></u-icon>\n              <view class=\"notification-dot\" v-if=\"hasNotifications\"></view>\n            </view>\n            <view class=\"action-item\" @click=\"showQRCode\">\n              <u-icon name=\"qrcode-scan\" size=\"20\" color=\"#FFFFFF\"></u-icon>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 搜索区域 -->\n        <view class=\"search-container\">\n          <view class=\"search-box\" @click=\"goToSearch\">\n            <u-icon name=\"search\" size=\"16\" color=\"#8E8E93\"></u-icon>\n            <text class=\"search-text\">搜索AI功能、智能体...</text>\n            <view class=\"voice-search\" @click.stop=\"startVoiceSearch\">\n              <u-icon name=\"mic\" size=\"14\" color=\"#667eea\"></u-icon>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 主要内容区域 -->\n    <scroll-view class=\"main-content\" scroll-y=\"true\" :style=\"{ height: mainContentHeight + 'px' }\">\n      <!-- AI能力卡片 -->\n      <view class=\"ai-capabilities\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">AI能力中心</text>\n          <text class=\"section-subtitle\">探索强大的AI功能</text>\n        </view>\n        \n        <view class=\"capabilities-grid\">\n          <view \n            class=\"capability-card\"\n            v-for=\"(item, index) in aiCapabilities\"\n            :key=\"index\"\n            @click=\"navigateToFeature(item)\"\n          >\n            <view class=\"card-header\">\n              <view class=\"card-icon\" :style=\"{ background: item.gradient }\">\n                <text class=\"iconfont\" :class=\"item.icon\"></text>\n              </view>\n              <view class=\"card-badge\" v-if=\"item.isNew\">NEW</view>\n            </view>\n            <view class=\"card-content\">\n              <text class=\"card-title\">{{ item.title }}</text>\n              <text class=\"card-desc\">{{ item.description }}</text>\n              <view class=\"card-stats\">\n                <text class=\"stat-item\">{{ item.usage }}次使用</text>\n                <text class=\"stat-rating\">{{ item.rating }}⭐</text>\n              </view>\n            </view>\n            <view class=\"card-action\">\n              <text class=\"action-text\">立即体验</text>\n              <u-icon name=\"arrow-right\" size=\"12\" color=\"#667eea\"></u-icon>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 智能推荐 -->\n      <view class=\"smart-recommendations\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">为您推荐</text>\n          <text class=\"section-subtitle\">基于您的使用习惯</text>\n        </view>\n        \n        <scroll-view class=\"recommendations-scroll\" scroll-x=\"true\">\n          <view class=\"recommendations-list\">\n            <view \n              class=\"recommendation-card\"\n              v-for=\"(item, index) in recommendations\"\n              :key=\"index\"\n              @click=\"useRecommendation(item)\"\n            >\n              <view class=\"rec-image\">\n                <image :src=\"item.image\" mode=\"aspectFill\"></image>\n                <view class=\"rec-overlay\">\n                  <u-icon name=\"play-circle\" size=\"24\" color=\"#FFFFFF\"></u-icon>\n                </view>\n              </view>\n              <view class=\"rec-info\">\n                <text class=\"rec-title\">{{ item.title }}</text>\n                <text class=\"rec-desc\">{{ item.description }}</text>\n                <view class=\"rec-meta\">\n                  <text class=\"rec-category\">{{ item.category }}</text>\n                  <text class=\"rec-time\">{{ item.duration }}</text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n      \n      <!-- 最近使用 -->\n      <view class=\"recent-usage\" v-if=\"recentItems.length > 0\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">最近使用</text>\n          <text class=\"section-link\" @click=\"viewAllRecent\">查看全部</text>\n        </view>\n        \n        <view class=\"recent-list\">\n          <view \n            class=\"recent-item\"\n            v-for=\"(item, index) in recentItems\"\n            :key=\"index\"\n            @click=\"continueTask(item)\"\n          >\n            <view class=\"recent-icon\">\n              <text class=\"iconfont\" :class=\"item.icon\"></text>\n            </view>\n            <view class=\"recent-content\">\n              <text class=\"recent-title\">{{ item.title }}</text>\n              <text class=\"recent-desc\">{{ item.description }}</text>\n              <text class=\"recent-time\">{{ formatTime(item.lastUsed) }}</text>\n            </view>\n            <view class=\"recent-action\">\n              <u-icon name=\"more-dot-fill\" size=\"16\" color=\"#C7C7CC\"></u-icon>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 底部安全区域 -->\n      <view class=\"bottom-safe-area\"></view>\n    </scroll-view>\n    \n    <!-- 自定义底部导航 -->\n    <TabBar />\n    \n    <!-- 浮动操作按钮 -->\n    <view class=\"floating-action\" @click=\"showQuickActions\">\n      <view class=\"fab-button\">\n        <u-icon name=\"plus\" size=\"24\" color=\"#FFFFFF\"></u-icon>\n      </view>\n      <view class=\"fab-ripple\"></view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\nimport TabBar from '@/components/TabBar/index.vue'\n\n// 响应式数据\nconst statusBarHeight = ref<number>(0)\nconst mainContentHeight = ref<number>(0)\nconst hasNotifications = ref<boolean>(true)\n\nconst userInfo = ref({\n  nickname: 'Alex Chen',\n  avatar: '/static/images/avatar-default.png'\n})\n\nconst aiCapabilities = ref([\n  {\n    title: '智能翻译',\n    description: '支持100+语言实时翻译',\n    icon: 'icon-translate',\n    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    usage: '1.2万',\n    rating: '4.9',\n    isNew: false,\n    path: '/pages/translation/text/index'\n  },\n  {\n    title: 'AI对话',\n    description: '智能助手24小时在线',\n    icon: 'icon-chat',\n    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n    usage: '8.5千',\n    rating: '4.8',\n    isNew: true,\n    path: '/pages/digital-human/chat/index'\n  },\n  {\n    title: '文档处理',\n    description: '智能文档分析与转换',\n    icon: 'icon-document',\n    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n    usage: '5.3千',\n    rating: '4.7',\n    isNew: false,\n    path: '/pages/utilities/document/converter/index'\n  },\n  {\n    title: '图像识别',\n    description: '强大的视觉AI能力',\n    icon: 'icon-image',\n    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',\n    usage: '3.1千',\n    rating: '4.6',\n    isNew: true,\n    path: '/pages/utilities/image/processor/index'\n  }\n])\n\nconst recommendations = ref([\n  {\n    title: '语音转文字',\n    description: '高精度语音识别',\n    image: '/static/images/rec-voice.jpg',\n    category: '语音处理',\n    duration: '2分钟',\n    path: '/pages/translation/audio/index'\n  },\n  {\n    title: '智能摘要',\n    description: '长文档快速摘要',\n    image: '/static/images/rec-summary.jpg',\n    category: '文本处理',\n    duration: '30秒',\n    path: '/pages/utilities/text/processor/index'\n  }\n])\n\nconst recentItems = ref([\n  {\n    title: '英文翻译',\n    description: '商务邮件翻译',\n    icon: 'icon-translate',\n    lastUsed: new Date(Date.now() - 2 * 60 * 60 * 1000),\n    path: '/pages/translation/text/index'\n  },\n  {\n    title: 'PDF转换',\n    description: '合同文档转换',\n    icon: 'icon-pdf',\n    lastUsed: new Date(Date.now() - 5 * 60 * 60 * 1000),\n    path: '/pages/utilities/document/converter/index'\n  }\n])\n\n// 计算属性\nconst getGreeting = () => {\n  const hour = new Date().getHours()\n  if (hour < 12) return '早上好'\n  if (hour < 18) return '下午好'\n  return '晚上好'\n}\n\n// 生命周期\nonMounted(() => {\n  // 获取系统信息\n  uni.getSystemInfo({\n    success: (res) => {\n      statusBarHeight.value = res.statusBarHeight || 0\n      mainContentHeight.value = res.windowHeight - 120 - statusBarHeight.value\n    }\n  })\n})\n\n// 方法\nconst goToProfile = () => {\n  uni.navigateTo({ url: '/pages/user/profile/index' })\n}\n\nconst showNotifications = () => {\n  uni.showToast({ title: '通知中心', icon: 'none' })\n}\n\nconst showQRCode = () => {\n  uni.showToast({ title: '扫一扫', icon: 'none' })\n}\n\nconst goToSearch = () => {\n  uni.navigateTo({ url: '/pages/search/index' })\n}\n\nconst startVoiceSearch = () => {\n  uni.showToast({ title: '语音搜索', icon: 'none' })\n}\n\nconst navigateToFeature = (item: any) => {\n  uni.navigateTo({ url: item.path })\n}\n\nconst useRecommendation = (item: any) => {\n  uni.navigateTo({ url: item.path })\n}\n\nconst viewAllRecent = () => {\n  uni.navigateTo({ url: '/pages/recent/index' })\n}\n\nconst continueTask = (item: any) => {\n  uni.navigateTo({ url: item.path })\n}\n\nconst showQuickActions = () => {\n  uni.showActionSheet({\n    itemList: ['新建翻译', '语音输入', '扫描文档', '智能对话'],\n    success: (res) => {\n      uni.__f__('log','at pages/index/modern.vue:327','选择了第' + (res.tapIndex + 1) + '个按钮')\n    }\n  })\n}\n\nconst formatTime = (time: Date) => {\n  const now = new Date()\n  const diff = now.getTime() - time.getTime()\n  const hours = Math.floor(diff / (1000 * 60 * 60))\n  \n  if (hours < 1) return '刚刚'\n  if (hours < 24) return `${hours}小时前`\n  return `${Math.floor(hours / 24)}天前`\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.modern-home {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);\n  position: relative;\n}\n\n// 状态栏\n.status-bar {\n  background: transparent;\n}\n\n// 头部区域\n.header-section {\n  position: relative;\n  padding-bottom: 40rpx;\n\n  .header-background {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 100%;\n    overflow: hidden;\n\n    .gradient-bg {\n      width: 100%;\n      height: 100%;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    }\n\n    .pattern-overlay {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-image:\n        radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 0%, transparent 50%),\n        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),\n        radial-gradient(circle at 40% 80%, rgba(255,255,255,0.1) 0%, transparent 50%);\n    }\n  }\n\n  .header-content {\n    position: relative;\n    z-index: 2;\n    padding: 40rpx 32rpx 0;\n\n    .user-area {\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      margin-bottom: 40rpx;\n\n      .user-info {\n        display: flex;\n        align-items: center;\n\n        .avatar-container {\n          position: relative;\n          margin-right: 24rpx;\n\n          .user-avatar {\n            width: 96rpx;\n            height: 96rpx;\n            border-radius: 48rpx;\n            border: 4rpx solid rgba(255,255,255,0.3);\n          }\n\n          .avatar-ring {\n            position: absolute;\n            top: -4rpx;\n            left: -4rpx;\n            right: -4rpx;\n            bottom: -4rpx;\n            border: 2rpx solid rgba(255,255,255,0.5);\n            border-radius: 52rpx;\n            animation: pulse 2s infinite;\n          }\n\n          .online-indicator {\n            position: absolute;\n            bottom: 8rpx;\n            right: 8rpx;\n            width: 20rpx;\n            height: 20rpx;\n            background: #00D4AA;\n            border: 3rpx solid #FFFFFF;\n            border-radius: 50%;\n          }\n        }\n\n        .user-details {\n          .greeting {\n            display: block;\n            font-size: 28rpx;\n            color: rgba(255,255,255,0.8);\n            margin-bottom: 8rpx;\n          }\n\n          .username {\n            display: block;\n            font-size: 36rpx;\n            font-weight: 600;\n            color: #FFFFFF;\n            margin-bottom: 12rpx;\n          }\n\n          .user-badge {\n            display: flex;\n            align-items: center;\n            background: rgba(255,215,0,0.2);\n            padding: 6rpx 16rpx;\n            border-radius: 20rpx;\n            border: 1rpx solid rgba(255,215,0,0.3);\n\n            .badge-text {\n              font-size: 20rpx;\n              color: #FFD700;\n              margin-right: 6rpx;\n              font-weight: 600;\n            }\n          }\n        }\n      }\n\n      .header-actions {\n        display: flex;\n        gap: 20rpx;\n\n        .action-item {\n          position: relative;\n          width: 72rpx;\n          height: 72rpx;\n          background: rgba(255,255,255,0.15);\n          border-radius: 36rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          backdrop-filter: blur(10rpx);\n\n          &:active {\n            transform: scale(0.95);\n          }\n\n          .notification-dot {\n            position: absolute;\n            top: 16rpx;\n            right: 16rpx;\n            width: 16rpx;\n            height: 16rpx;\n            background: #FF3B30;\n            border-radius: 50%;\n            border: 2rpx solid #FFFFFF;\n          }\n        }\n      }\n    }\n\n    .search-container {\n      .search-box {\n        background: rgba(255,255,255,0.95);\n        border-radius: 28rpx;\n        padding: 24rpx 32rpx;\n        display: flex;\n        align-items: center;\n        backdrop-filter: blur(10rpx);\n        box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);\n\n        .search-text {\n          flex: 1;\n          font-size: 28rpx;\n          color: #8E8E93;\n          margin-left: 16rpx;\n        }\n\n        .voice-search {\n          width: 56rpx;\n          height: 56rpx;\n          background: linear-gradient(135deg, #667eea, #764ba2);\n          border-radius: 28rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n\n          &:active {\n            transform: scale(0.95);\n          }\n        }\n      }\n    }\n  }\n}\n\n// 主要内容区域\n.main-content {\n  background: #FFFFFF;\n  border-radius: 32rpx 32rpx 0 0;\n  margin-top: -32rpx;\n  position: relative;\n  z-index: 3;\n\n  .section-header {\n    padding: 48rpx 32rpx 32rpx;\n\n    .section-title {\n      display: block;\n      font-size: 40rpx;\n      font-weight: 700;\n      color: #1D1D1F;\n      margin-bottom: 8rpx;\n    }\n\n    .section-subtitle {\n      font-size: 28rpx;\n      color: #8E8E93;\n    }\n\n    .section-link {\n      font-size: 28rpx;\n      color: #667eea;\n      font-weight: 500;\n    }\n  }\n}\n\n// AI能力卡片\n.ai-capabilities {\n  .capabilities-grid {\n    padding: 0 32rpx;\n    display: grid;\n    grid-template-columns: repeat(2, 1fr);\n    gap: 24rpx;\n\n    .capability-card {\n      background: #FFFFFF;\n      border-radius: 24rpx;\n      padding: 32rpx;\n      box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);\n      border: 1rpx solid rgba(0,0,0,0.05);\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\n      &:active {\n        transform: scale(0.98);\n        box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.12);\n      }\n\n      .card-header {\n        position: relative;\n        margin-bottom: 24rpx;\n\n        .card-icon {\n          width: 80rpx;\n          height: 80rpx;\n          border-radius: 20rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n\n          .iconfont {\n            font-size: 32rpx;\n            color: #FFFFFF;\n          }\n        }\n\n        .card-badge {\n          position: absolute;\n          top: -8rpx;\n          right: -8rpx;\n          background: linear-gradient(45deg, #FF6B6B, #FF8E53);\n          color: #FFFFFF;\n          font-size: 18rpx;\n          font-weight: 600;\n          padding: 4rpx 12rpx;\n          border-radius: 12rpx;\n          box-shadow: 0 2rpx 8rpx rgba(255,107,107,0.4);\n        }\n      }\n\n      .card-content {\n        margin-bottom: 24rpx;\n\n        .card-title {\n          display: block;\n          font-size: 32rpx;\n          font-weight: 600;\n          color: #1D1D1F;\n          margin-bottom: 8rpx;\n        }\n\n        .card-desc {\n          display: block;\n          font-size: 24rpx;\n          color: #8E8E93;\n          line-height: 1.4;\n          margin-bottom: 16rpx;\n        }\n\n        .card-stats {\n          display: flex;\n          justify-content: space-between;\n\n          .stat-item,\n          .stat-rating {\n            font-size: 22rpx;\n            color: #C7C7CC;\n          }\n        }\n      }\n\n      .card-action {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n\n        .action-text {\n          font-size: 26rpx;\n          color: #667eea;\n          font-weight: 500;\n        }\n      }\n    }\n  }\n}\n\n// 智能推荐\n.smart-recommendations {\n  margin-top: 48rpx;\n\n  .recommendations-scroll {\n    white-space: nowrap;\n\n    .recommendations-list {\n      display: flex;\n      padding: 0 32rpx;\n      gap: 24rpx;\n\n      .recommendation-card {\n        flex-shrink: 0;\n        width: 320rpx;\n        background: #FFFFFF;\n        border-radius: 20rpx;\n        overflow: hidden;\n        box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);\n\n        .rec-image {\n          position: relative;\n          height: 180rpx;\n\n          image {\n            width: 100%;\n            height: 100%;\n          }\n\n          .rec-overlay {\n            position: absolute;\n            top: 50%;\n            left: 50%;\n            transform: translate(-50%, -50%);\n            width: 80rpx;\n            height: 80rpx;\n            background: rgba(0,0,0,0.6);\n            border-radius: 40rpx;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            backdrop-filter: blur(10rpx);\n          }\n        }\n\n        .rec-info {\n          padding: 24rpx;\n\n          .rec-title {\n            display: block;\n            font-size: 28rpx;\n            font-weight: 600;\n            color: #1D1D1F;\n            margin-bottom: 8rpx;\n          }\n\n          .rec-desc {\n            display: block;\n            font-size: 24rpx;\n            color: #8E8E93;\n            margin-bottom: 16rpx;\n            line-height: 1.4;\n          }\n\n          .rec-meta {\n            display: flex;\n            justify-content: space-between;\n\n            .rec-category,\n            .rec-time {\n              font-size: 22rpx;\n              color: #C7C7CC;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\n// 最近使用\n.recent-usage {\n  margin-top: 48rpx;\n\n  .section-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n  }\n\n  .recent-list {\n    padding: 0 32rpx;\n\n    .recent-item {\n      display: flex;\n      align-items: center;\n      padding: 24rpx 0;\n      border-bottom: 1rpx solid #F2F2F7;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &:active {\n        background: #F2F2F7;\n        margin: 0 -32rpx;\n        padding: 24rpx 32rpx;\n        border-radius: 16rpx;\n      }\n\n      .recent-icon {\n        width: 80rpx;\n        height: 80rpx;\n        background: #F2F2F7;\n        border-radius: 20rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 24rpx;\n\n        .iconfont {\n          font-size: 28rpx;\n          color: #8E8E93;\n        }\n      }\n\n      .recent-content {\n        flex: 1;\n\n        .recent-title {\n          display: block;\n          font-size: 28rpx;\n          font-weight: 500;\n          color: #1D1D1F;\n          margin-bottom: 6rpx;\n        }\n\n        .recent-desc {\n          display: block;\n          font-size: 24rpx;\n          color: #8E8E93;\n          margin-bottom: 8rpx;\n        }\n\n        .recent-time {\n          font-size: 22rpx;\n          color: #C7C7CC;\n        }\n      }\n\n      .recent-action {\n        padding: 16rpx;\n      }\n    }\n  }\n}\n\n// 浮动操作按钮\n.floating-action {\n  position: fixed;\n  bottom: 200rpx;\n  right: 32rpx;\n  z-index: 999;\n\n  .fab-button {\n    width: 112rpx;\n    height: 112rpx;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-radius: 56rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\n    &:active {\n      transform: scale(0.95);\n    }\n  }\n\n  .fab-ripple {\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    width: 112rpx;\n    height: 112rpx;\n    border: 2rpx solid rgba(102, 126, 234, 0.3);\n    border-radius: 56rpx;\n    animation: ripple 2s infinite;\n  }\n}\n\n// 底部安全区域\n.bottom-safe-area {\n  height: calc(120rpx + env(safe-area-inset-bottom));\n}\n\n// 动画\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.8;\n    transform: scale(1.05);\n  }\n}\n\n@keyframes ripple {\n  0% {\n    opacity: 1;\n    transform: translate(-50%, -50%) scale(1);\n  }\n  100% {\n    opacity: 0;\n    transform: translate(-50%, -50%) scale(1.5);\n  }\n}\n\n// 暗色模式适配\n@media (prefers-color-scheme: dark) {\n  .modern-home {\n    background: linear-gradient(180deg, #1c1c1e 0%, #000000 100%);\n  }\n\n  .main-content {\n    background: #1c1c1e;\n  }\n\n  .capability-card,\n  .recommendation-card {\n    background: #2c2c2e;\n    border-color: #38383a;\n  }\n\n  .section-title {\n    color: #ffffff !important;\n  }\n\n  .card-title,\n  .recent-title {\n    color: #ffffff !important;\n  }\n\n  .recent-item {\n    border-bottom-color: #38383a;\n\n    &:active {\n      background: #38383a;\n    }\n  }\n\n  .recent-icon {\n    background: #38383a;\n  }\n}\n\n// 响应式适配\n@media screen and (max-width: 480px) {\n  .capabilities-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .user-area {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 24rpx;\n  }\n\n  .header-actions {\n    align-self: flex-end;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/AI_system/mobile-uniapp/src/pages/index/modern.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni"], "mappings": ";;;;;;;;;AA+KA,MAAA,SAAmB,MAAA;;;;AAGb,UAAA,kBAAkBA,kBAAY,CAAC;AAC/B,UAAA,oBAAoBA,kBAAY,CAAC;AACjC,UAAA,mBAAmBA,kBAAa,IAAI;AAE1C,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,UAAU;AAAA,MACV,QAAQ;AAAA,IAAA,CACT;AAED,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,IAAA,CACD;AAED,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,MAAM;AAAA,MACR;AAAA,IAAA,CACD;AAED,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU,IAAI,KAAK,KAAK,IAAQ,IAAA,IAAI,KAAK,KAAK,GAAI;AAAA,QAClD,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU,IAAI,KAAK,KAAK,IAAQ,IAAA,IAAI,KAAK,KAAK,GAAI;AAAA,QAClD,MAAM;AAAA,MACR;AAAA,IAAA,CACD;AAGD,UAAM,cAAc,MAAM;AACxB,YAAM,QAAO,oBAAI,KAAK,GAAE,SAAS;AACjC,UAAI,OAAO;AAAW,eAAA;AACtB,UAAI,OAAO;AAAW,eAAA;AACf,aAAA;AAAA,IAAA;AAITC,kBAAAA,UAAU,MAAM;AAEdC,oBAAAA,MAAI,cAAc;AAAA,QAChB,SAAS,CAAC,QAAQ;AACA,0BAAA,QAAQ,IAAI,mBAAmB;AAC/C,4BAAkB,QAAQ,IAAI,eAAe,MAAM,gBAAgB;AAAA,QACrE;AAAA,MAAA,CACD;AAAA,IAAA,CACF;AAGD,UAAM,cAAc,MAAM;AACxBA,oBAAAA,MAAI,WAAW,EAAE,KAAK,4BAA6B,CAAA;AAAA,IAAA;AAGrD,UAAM,oBAAoB,MAAM;AAC9BA,oBAAA,MAAI,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,IAAA;AAG/C,UAAM,aAAa,MAAM;AACvBA,oBAAA,MAAI,UAAU,EAAE,OAAO,OAAO,MAAM,QAAQ;AAAA,IAAA;AAG9C,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAI,WAAW,EAAE,KAAK,sBAAuB,CAAA;AAAA,IAAA;AAG/C,UAAM,mBAAmB,MAAM;AAC7BA,oBAAA,MAAI,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,IAAA;AAGzC,UAAA,oBAAoB,CAAC,SAAc;AACvCA,oBAAA,MAAI,WAAW,EAAE,KAAK,KAAK,KAAM,CAAA;AAAA,IAAA;AAG7B,UAAA,oBAAoB,CAAC,SAAc;AACvCA,oBAAA,MAAI,WAAW,EAAE,KAAK,KAAK,KAAM,CAAA;AAAA,IAAA;AAGnC,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,WAAW,EAAE,KAAK,sBAAuB,CAAA;AAAA,IAAA;AAGzC,UAAA,eAAe,CAAC,SAAc;AAClCA,oBAAA,MAAI,WAAW,EAAE,KAAK,KAAK,KAAM,CAAA;AAAA,IAAA;AAGnC,UAAM,mBAAmB,MAAM;AAC7BA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACzC,SAAS,CAAC,QAAQ;AAChBA,8BAAI,MAAM,OAAM,iCAAgC,UAAU,IAAI,WAAW,KAAK,KAAK;AAAA,QACrF;AAAA,MAAA,CACD;AAAA,IAAA;AAGG,UAAA,aAAa,CAAC,SAAe;AAC3B,YAAA,0BAAU;AAChB,YAAM,OAAO,IAAI,QAAQ,IAAI,KAAK,QAAQ;AAC1C,YAAM,QAAQ,KAAK,MAAM,QAAQ,MAAO,KAAK,GAAG;AAEhD,UAAI,QAAQ;AAAU,eAAA;AACtB,UAAI,QAAQ;AAAI,eAAO,GAAG,KAAK;AAC/B,aAAO,GAAG,KAAK,MAAM,QAAQ,EAAE,CAAC;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjVlC,GAAG,WAAW,eAAe;"}