import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { 
  aiAgentApi, 
  type AIAgent, 
  type AgentCategory, 
  type ChatSession, 
  type ChatMessage 
} from '@/api/ai-agent'

export const useAIAgentStore = defineStore('aiAgent', () => {
  // 状态
  const categories = ref<AgentCategory[]>([])
  const agents = ref<AIAgent[]>([])
  const featuredAgents = ref<AIAgent[]>([])
  const popularAgents = ref<AIAgent[]>([])
  const myAgents = ref<AIAgent[]>([])
  const favoriteAgents = ref<AIAgent[]>([])
  const currentAgent = ref<AIAgent | null>(null)
  const chatSessions = ref<ChatSession[]>([])
  const currentSession = ref<ChatSession | null>(null)
  const messages = ref<ChatMessage[]>([])
  const isLoading = ref<boolean>(false)
  const isSending = ref<boolean>(false)
  const searchQuery = ref<string>('')
  const selectedCategory = ref<string>('')
  const sortBy = ref<'popular' | 'rating' | 'newest' | 'price'>('popular')

  // 计算属性
  const hasAgents = computed(() => agents.value.length > 0)
  
  const filteredAgents = computed(() => {
    let filtered = agents.value
    
    if (selectedCategory.value) {
      filtered = filtered.filter(agent => agent.category === selectedCategory.value)
    }
    
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(agent => 
        agent.name.toLowerCase().includes(query) ||
        agent.description.toLowerCase().includes(query) ||
        agent.tags.some(tag => tag.toLowerCase().includes(query))
      )
    }
    
    return filtered
  })

  const currentSessionMessages = computed(() => {
    if (!currentSession.value) return []
    return messages.value.filter(msg => msg.session_id === currentSession.value!.id)
  })

  const recentSessions = computed(() => {
    return chatSessions.value
      .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
      .slice(0, 5)
  })

  // 加载分类
  const loadCategories = async () => {
    try {
      const response = await aiAgentApi.getCategories()
      if (response.success) {
        categories.value = response.data
      }
    } catch (error) {
      console.error('加载分类失败:', error)
    }
  }

  // 加载智能体列表
  const loadAgents = async (params?: {
    category?: string
    search?: string
    sort?: 'popular' | 'rating' | 'newest' | 'price'
    is_free?: boolean
    page?: number
    limit?: number
  }) => {
    try {
      isLoading.value = true
      const response = await aiAgentApi.getAgents(params)
      
      if (response.success) {
        if (params?.page === 1 || !params?.page) {
          agents.value = response.data.items
        } else {
          agents.value.push(...response.data.items)
        }
        return response.data
      }
    } catch (error) {
      console.error('加载智能体列表失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 加载推荐智能体
  const loadFeaturedAgents = async () => {
    try {
      const response = await aiAgentApi.getFeaturedAgents()
      if (response.success) {
        featuredAgents.value = response.data
      }
    } catch (error) {
      console.error('加载推荐智能体失败:', error)
    }
  }

  // 加载热门智能体
  const loadPopularAgents = async () => {
    try {
      const response = await aiAgentApi.getPopularAgents()
      if (response.success) {
        popularAgents.value = response.data
      }
    } catch (error) {
      console.error('加载热门智能体失败:', error)
    }
  }

  // 搜索智能体
  const searchAgents = async (query: string, category?: string) => {
    try {
      isLoading.value = true
      searchQuery.value = query
      
      const response = await aiAgentApi.searchAgents(query, { category })
      
      if (response.success) {
        agents.value = response.data.items
        return response.data
      }
    } catch (error) {
      console.error('搜索智能体失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 获取智能体详情
  const getAgentDetail = async (id: string) => {
    try {
      const response = await aiAgentApi.getAgentDetail(id)
      if (response.success) {
        currentAgent.value = response.data
        return response.data
      }
    } catch (error) {
      console.error('获取智能体详情失败:', error)
      throw error
    }
  }

  // 选择智能体
  const selectAgent = async (agent: AIAgent) => {
    currentAgent.value = agent
    
    // 加载该智能体的对话会话
    await loadChatSessions()
  }

  // 加载对话会话
  const loadChatSessions = async (page = 1, limit = 20) => {
    try {
      const response = await aiAgentApi.getChatSessions({ page, limit })
      
      if (response.success) {
        if (page === 1) {
          chatSessions.value = response.data.items
        } else {
          chatSessions.value.push(...response.data.items)
        }
        return response.data
      }
    } catch (error) {
      console.error('加载对话会话失败:', error)
      throw error
    }
  }

  // 创建新会话
  const createNewSession = async (agentId: string, title?: string) => {
    try {
      const response = await aiAgentApi.createChatSession(agentId, title)
      
      if (response.success) {
        const newSession = response.data
        chatSessions.value.unshift(newSession)
        currentSession.value = newSession
        messages.value = []
        
        return newSession
      } else {
        throw new Error(response.message || '创建会话失败')
      }
    } catch (error) {
      console.error('创建会话失败:', error)
      throw error
    }
  }

  // 选择会话
  const selectSession = async (session: ChatSession) => {
    currentSession.value = session
    await loadMessages()
  }

  // 加载消息
  const loadMessages = async (page = 1, limit = 50) => {
    if (!currentSession.value) return
    
    try {
      const response = await aiAgentApi.getChatMessages(
        currentSession.value.id,
        { page, limit }
      )
      
      if (response.success) {
        if (page === 1) {
          messages.value = response.data.items.reverse()
        } else {
          messages.value.unshift(...response.data.items.reverse())
        }
        return response.data
      }
    } catch (error) {
      console.error('加载消息失败:', error)
      throw error
    }
  }

  // 发送消息
  const sendMessage = async (content: string, messageType: 'text' | 'image' | 'file' = 'text') => {
    if (!currentSession.value || !content.trim()) {
      throw new Error('会话或消息内容不能为空')
    }

    try {
      isSending.value = true
      
      // 添加用户消息到本地
      const userMessage: ChatMessage = {
        id: `temp_${Date.now()}`,
        session_id: currentSession.value.id,
        role: 'user',
        content: content.trim(),
        message_type: messageType,
        timestamp: new Date().toISOString()
      }
      messages.value.push(userMessage)

      // 发送消息到服务器
      const response = await aiAgentApi.sendMessage(
        currentSession.value.id,
        content.trim(),
        messageType
      )
      
      if (response.success) {
        // 更新用户消息
        const messageIndex = messages.value.findIndex(msg => msg.id === userMessage.id)
        if (messageIndex !== -1) {
          messages.value[messageIndex] = response.data
        }

        // 模拟AI回复（实际应该从服务器获取）
        setTimeout(() => {
          const aiMessage: ChatMessage = {
            id: `ai_${Date.now()}`,
            session_id: currentSession.value!.id,
            role: 'assistant',
            content: '这是AI智能体的回复消息。',
            message_type: 'text',
            timestamp: new Date().toISOString()
          }
          messages.value.push(aiMessage)
          
          // 更新会话信息
          if (currentSession.value) {
            currentSession.value.updated_at = new Date().toISOString()
            currentSession.value.last_message = aiMessage.content
            currentSession.value.message_count += 2
          }
        }, 1000)
        
        return response.data
      } else {
        throw new Error(response.message || '发送消息失败')
      }
    } catch (error) {
      console.error('发送消息失败:', error)
      
      // 移除失败的消息
      messages.value = messages.value.filter(msg => !msg.id.startsWith('temp_'))
      
      throw error
    } finally {
      isSending.value = false
    }
  }

  // 收藏/取消收藏智能体
  const toggleFavorite = async (agentId: string, isFavorite: boolean) => {
    try {
      const response = isFavorite 
        ? await aiAgentApi.unfavoriteAgent(agentId)
        : await aiAgentApi.favoriteAgent(agentId)
      
      if (response.success) {
        // 更新本地状态
        const agent = agents.value.find(a => a.id === agentId)
        if (agent) {
          (agent as any).is_favorite = !isFavorite
        }

        if (currentAgent.value?.id === agentId) {
          (currentAgent.value as any).is_favorite = !isFavorite
        }

        uni.showToast({
          title: isFavorite ? '取消收藏' : '收藏成功',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('操作收藏失败:', error)
      uni.showToast({
        title: '操作失败',
        icon: 'error'
      })
    }
  }

  // 加载收藏的智能体
  const loadFavoriteAgents = async (page = 1, limit = 20) => {
    try {
      const response = await aiAgentApi.getFavoriteAgents({ page, limit })
      
      if (response.success) {
        if (page === 1) {
          favoriteAgents.value = response.data.items
        } else {
          favoriteAgents.value.push(...response.data.items)
        }
        return response.data
      }
    } catch (error) {
      console.error('加载收藏列表失败:', error)
      throw error
    }
  }

  // 删除会话
  const deleteSession = async (sessionId: string) => {
    try {
      const response = await aiAgentApi.deleteChatSession(sessionId)
      
      if (response.success) {
        chatSessions.value = chatSessions.value.filter(session => session.id !== sessionId)
        
        if (currentSession.value?.id === sessionId) {
          currentSession.value = null
          messages.value = []
        }
        
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('删除会话失败:', error)
      uni.showToast({
        title: '删除失败',
        icon: 'error'
      })
    }
  }

  // 设置筛选条件
  const setCategory = (category: string) => {
    selectedCategory.value = category
  }

  const setSortBy = (sort: 'popular' | 'rating' | 'newest' | 'price') => {
    sortBy.value = sort
  }

  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }

  return {
    // 状态
    categories,
    agents,
    featuredAgents,
    popularAgents,
    myAgents,
    favoriteAgents,
    currentAgent,
    chatSessions,
    currentSession,
    messages,
    isLoading,
    isSending,
    searchQuery,
    selectedCategory,
    sortBy,

    // 计算属性
    hasAgents,
    filteredAgents,
    currentSessionMessages,
    recentSessions,

    // 方法
    loadCategories,
    loadAgents,
    loadFeaturedAgents,
    loadPopularAgents,
    searchAgents,
    getAgentDetail,
    selectAgent,
    loadChatSessions,
    createNewSession,
    selectSession,
    loadMessages,
    sendMessage,
    toggleFavorite,
    loadFavoriteAgents,
    deleteSession,
    setCategory,
    setSortBy,
    setSearchQuery
  }
}, {
  persist: {
    key: 'ai-agent-store',
    paths: ['currentAgent', 'selectedCategory', 'sortBy']
  }
})
