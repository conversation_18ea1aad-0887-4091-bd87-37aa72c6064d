"use strict";
const common_vendor = require("../../common/vendor.js");
const api_user = require("../../api/user.js");
const useUserStore = common_vendor.defineStore("user", () => {
  const token = common_vendor.ref("");
  const userInfo = common_vendor.ref(null);
  const isLoggedIn = common_vendor.ref(false);
  const loginLoading = common_vendor.ref(false);
  const isAuthenticated = common_vendor.computed(() => {
    return !!token.value && !!userInfo.value;
  });
  const userDisplayName = common_vendor.computed(() => {
    if (!userInfo.value)
      return "";
    return userInfo.value.nickname || userInfo.value.username || "用户";
  });
  const login = async (credentials) => {
    try {
      loginLoading.value = true;
      const response = await api_user.userApi.login(credentials);
      if (response.success) {
        token.value = response.data.token;
        userInfo.value = response.data.user;
        isLoggedIn.value = true;
        common_vendor.index.setStorageSync("token", token.value);
        common_vendor.index.setStorageSync("userInfo", userInfo.value);
        return { success: true };
      } else {
        throw new Error(response.message || "登录失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.ts:54", "登录失败:", error);
      return {
        success: false,
        message: error.message || "登录失败，请检查网络连接"
      };
    } finally {
      loginLoading.value = false;
    }
  };
  const logout = async () => {
    try {
      await api_user.userApi.logout();
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.ts:70", "退出登录API调用失败:", error);
    } finally {
      token.value = "";
      userInfo.value = null;
      isLoggedIn.value = false;
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync("userInfo");
      common_vendor.index.reLaunch({
        url: "/pages/user/login/index"
      });
    }
  };
  const checkLoginStatus = async () => {
    try {
      const localToken = common_vendor.index.getStorageSync("token");
      const localUserInfo = common_vendor.index.getStorageSync("userInfo");
      if (localToken && localUserInfo) {
        token.value = localToken;
        userInfo.value = localUserInfo;
        isLoggedIn.value = true;
        try {
          const response = await api_user.userApi.getUserInfo();
          if (response.success) {
            userInfo.value = response.data;
          } else {
            await logout();
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/user.ts:110", "验证token失败:", error);
        }
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.ts:115", "检查登录状态失败:", error);
    }
  };
  const updateUserInfo = async (updateData) => {
    try {
      const response = await api_user.userApi.updateUserInfo(updateData);
      if (response.success) {
        userInfo.value = { ...userInfo.value, ...response.data };
        common_vendor.index.setStorageSync("userInfo", userInfo.value);
        return { success: true };
      } else {
        throw new Error(response.message || "更新失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.ts:135", "更新用户信息失败:", error);
      return {
        success: false,
        message: error.message || "更新失败"
      };
    }
  };
  const changePassword = async (passwordData) => {
    try {
      const response = await api_user.userApi.changePassword(passwordData);
      if (response.success) {
        common_vendor.index.showToast({
          title: "密码修改成功",
          icon: "success"
        });
        return { success: true };
      } else {
        throw new Error(response.message || "密码修改失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.ts:161", "修改密码失败:", error);
      return {
        success: false,
        message: error.message || "密码修改失败"
      };
    }
  };
  const uploadAvatar = async (filePath) => {
    try {
      const response = await api_user.userApi.uploadAvatar(filePath);
      if (response.success) {
        if (userInfo.value) {
          userInfo.value.avatar = response.data.avatar;
          common_vendor.index.setStorageSync("userInfo", userInfo.value);
        }
        common_vendor.index.showToast({
          title: "头像上传成功",
          icon: "success"
        });
        return { success: true, avatar: response.data.avatar };
      } else {
        throw new Error(response.message || "头像上传失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.ts:190", "上传头像失败:", error);
      return {
        success: false,
        message: error.message || "头像上传失败"
      };
    }
  };
  const getUserStats = async () => {
    try {
      const response = await api_user.userApi.getUserStats();
      return response.success ? response.data : null;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.ts:204", "获取用户统计失败:", error);
      return null;
    }
  };
  return {
    // 状态
    token,
    userInfo,
    isLoggedIn,
    loginLoading,
    // 计算属性
    isAuthenticated,
    userDisplayName,
    // 方法
    login,
    logout,
    checkLoginStatus,
    updateUserInfo,
    changePassword,
    uploadAvatar,
    getUserStats
  };
}, {
  // 持久化配置
  persist: {
    key: "user-store",
    paths: ["token", "userInfo", "isLoggedIn"]
  }
});
exports.useUserStore = useUserStore;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/store/modules/user.js.map
