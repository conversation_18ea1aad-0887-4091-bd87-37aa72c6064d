{"version": 3, "file": "index.js", "sources": ["pages/ai-agents/market/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWktYWdlbnRzL21hcmtldC9pbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"ai-agents-market\">\n    <!-- 头部搜索区域 -->\n    <view class=\"header-section\">\n      <view class=\"search-container\">\n        <view class=\"search-box\">\n          <u-icon name=\"search\" size=\"18\" color=\"#8E8E93\"></u-icon>\n          <input \n            class=\"search-input\" \n            placeholder=\"搜索智能体...\" \n            v-model=\"searchKeyword\"\n            @input=\"handleSearch\"\n          />\n          <view class=\"filter-btn\" @click=\"showFilter\">\n            <u-icon name=\"filter\" size=\"16\" color=\"#667eea\"></u-icon>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 分类标签 -->\n      <scroll-view class=\"category-scroll\" scroll-x=\"true\">\n        <view class=\"category-list\">\n          <view \n            class=\"category-item\"\n            v-for=\"(category, index) in categories\"\n            :key=\"index\"\n            :class=\"{ active: currentCategory === index }\"\n            @click=\"switchCategory(index)\"\n          >\n            <text class=\"category-text\">{{ category.name }}</text>\n          </view>\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 智能体列表 -->\n    <scroll-view class=\"agents-content\" scroll-y=\"true\">\n      <!-- 推荐智能体 -->\n      <view class=\"section\" v-if=\"currentCategory === 0\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">🔥 热门推荐</text>\n          <text class=\"section-more\" @click=\"viewMore('hot')\">更多</text>\n        </view>\n        \n        <view class=\"agents-grid\">\n          <view \n            class=\"agent-card featured\"\n            v-for=\"(agent, index) in featuredAgents\"\n            :key=\"index\"\n            @click=\"viewAgent(agent)\"\n          >\n            <view class=\"card-header\">\n              <image class=\"agent-avatar\" :src=\"agent.avatar\" mode=\"aspectFill\"></image>\n              <view class=\"agent-badge\" v-if=\"agent.isPro\">PRO</view>\n              <view class=\"agent-rating\">\n                <u-icon name=\"star-fill\" size=\"12\" color=\"#FFD700\"></u-icon>\n                <text class=\"rating-text\">{{ agent.rating }}</text>\n              </view>\n            </view>\n            \n            <view class=\"card-content\">\n              <text class=\"agent-name\">{{ agent.name }}</text>\n              <text class=\"agent-desc\">{{ agent.description }}</text>\n              \n              <view class=\"agent-tags\">\n                <text \n                  class=\"tag\"\n                  v-for=\"(tag, tagIndex) in agent.tags.slice(0, 2)\"\n                  :key=\"tagIndex\"\n                >\n                  {{ tag }}\n                </text>\n              </view>\n              \n              <view class=\"agent-stats\">\n                <view class=\"stat-item\">\n                  <u-icon name=\"user\" size=\"12\" color=\"#8E8E93\"></u-icon>\n                  <text class=\"stat-text\">{{ agent.users }}</text>\n                </view>\n                <view class=\"stat-item\">\n                  <u-icon name=\"heart\" size=\"12\" color=\"#8E8E93\"></u-icon>\n                  <text class=\"stat-text\">{{ agent.likes }}</text>\n                </view>\n              </view>\n            </view>\n            \n            <view class=\"card-action\">\n              <view class=\"price-info\" v-if=\"agent.price > 0\">\n                <text class=\"price\">¥{{ agent.price }}/月</text>\n              </view>\n              <view class=\"price-info\" v-else>\n                <text class=\"price free\">免费</text>\n              </view>\n              <view class=\"action-btn\" @click.stop=\"useAgent(agent)\">\n                <text class=\"btn-text\">立即使用</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 普通智能体列表 -->\n      <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">{{ categories[currentCategory].name }}</text>\n          <text class=\"section-count\">{{ filteredAgents.length }}个智能体</text>\n        </view>\n        \n        <view class=\"agents-list\">\n          <view \n            class=\"agent-item\"\n            v-for=\"(agent, index) in filteredAgents\"\n            :key=\"index\"\n            @click=\"viewAgent(agent)\"\n          >\n            <image class=\"item-avatar\" :src=\"agent.avatar\" mode=\"aspectFill\"></image>\n            \n            <view class=\"item-content\">\n              <view class=\"item-header\">\n                <text class=\"item-name\">{{ agent.name }}</text>\n                <view class=\"item-rating\">\n                  <u-icon name=\"star-fill\" size=\"10\" color=\"#FFD700\"></u-icon>\n                  <text class=\"rating-text\">{{ agent.rating }}</text>\n                </view>\n              </view>\n              \n              <text class=\"item-desc\">{{ agent.description }}</text>\n              \n              <view class=\"item-footer\">\n                <view class=\"item-tags\">\n                  <text \n                    class=\"mini-tag\"\n                    v-for=\"(tag, tagIndex) in agent.tags.slice(0, 3)\"\n                    :key=\"tagIndex\"\n                  >\n                    {{ tag }}\n                  </text>\n                </view>\n                \n                <view class=\"item-price\">\n                  <text class=\"price-text\" v-if=\"agent.price > 0\">¥{{ agent.price }}/月</text>\n                  <text class=\"price-text free\" v-else>免费</text>\n                </view>\n              </view>\n            </view>\n            \n            <view class=\"item-action\">\n              <view class=\"action-btn mini\" @click.stop=\"useAgent(agent)\">\n                <u-icon name=\"plus\" size=\"14\" color=\"#667eea\"></u-icon>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 底部安全区域 -->\n      <view class=\"bottom-safe-area\"></view>\n    </scroll-view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\n\n// 响应式数据\nconst searchKeyword = ref<string>('')\nconst currentCategory = ref<number>(0)\n\nconst categories = ref([\n  { name: '全部', icon: 'apps' },\n  { name: '写作助手', icon: 'edit' },\n  { name: '编程助手', icon: 'code' },\n  { name: '翻译助手', icon: 'translate' },\n  { name: '设计助手', icon: 'palette' },\n  { name: '商务助手', icon: 'briefcase' },\n  { name: '学习助手', icon: 'book' },\n  { name: '生活助手', icon: 'home' }\n])\n\nconst featuredAgents = ref([\n  {\n    id: 1,\n    name: 'GPT-4 写作大师',\n    description: '专业的写作助手，帮您创作高质量文章',\n    avatar: '/static/images/agent-writer.png',\n    rating: 4.9,\n    users: '10万+',\n    likes: '8.5万',\n    tags: ['写作', '创意', '文案'],\n    price: 29,\n    isPro: true,\n    category: 'writing'\n  },\n  {\n    id: 2,\n    name: '代码助手Pro',\n    description: '智能编程助手，支持多种编程语言',\n    avatar: '/static/images/agent-coder.png',\n    rating: 4.8,\n    users: '8万+',\n    likes: '6.2万',\n    tags: ['编程', 'AI', '调试'],\n    price: 0,\n    isPro: false,\n    category: 'coding'\n  },\n  {\n    id: 3,\n    name: '多语言翻译专家',\n    description: '支持100+语言的专业翻译助手',\n    avatar: '/static/images/agent-translator.png',\n    rating: 4.7,\n    users: '15万+',\n    likes: '12万',\n    tags: ['翻译', '多语言', '商务'],\n    price: 19,\n    isPro: true,\n    category: 'translation'\n  },\n  {\n    id: 4,\n    name: 'UI设计师',\n    description: '专业的UI/UX设计建议和灵感助手',\n    avatar: '/static/images/agent-designer.png',\n    rating: 4.6,\n    users: '5万+',\n    likes: '4.1万',\n    tags: ['设计', 'UI', 'UX'],\n    price: 39,\n    isPro: true,\n    category: 'design'\n  }\n])\n\nconst allAgents = ref([\n  ...featuredAgents.value,\n  {\n    id: 5,\n    name: '商务邮件助手',\n    description: '帮您撰写专业的商务邮件和文档',\n    avatar: '/static/images/agent-business.png',\n    rating: 4.5,\n    users: '3万+',\n    likes: '2.8万',\n    tags: ['商务', '邮件', '文档'],\n    price: 15,\n    isPro: false,\n    category: 'business'\n  },\n  {\n    id: 6,\n    name: '学习规划师',\n    description: '个性化学习计划制定和进度跟踪',\n    avatar: '/static/images/agent-study.png',\n    rating: 4.4,\n    users: '6万+',\n    likes: '5.2万',\n    tags: ['学习', '规划', '教育'],\n    price: 0,\n    isPro: false,\n    category: 'study'\n  }\n])\n\n// 计算属性\nconst filteredAgents = computed(() => {\n  let agents = allAgents.value\n  \n  // 分类筛选\n  if (currentCategory.value > 0) {\n    const categoryName = categories.value[currentCategory.value].name\n    agents = agents.filter(agent => {\n      return agent.tags.some(tag => tag.includes(categoryName.replace('助手', '')))\n    })\n  }\n  \n  // 搜索筛选\n  if (searchKeyword.value) {\n    agents = agents.filter(agent => \n      agent.name.includes(searchKeyword.value) || \n      agent.description.includes(searchKeyword.value) ||\n      agent.tags.some(tag => tag.includes(searchKeyword.value))\n    )\n  }\n  \n  return agents\n})\n\n// 方法\nconst handleSearch = () => {\n  // 搜索逻辑\n  uni.__f__('log','at pages/ai-agents/market/index.vue:292','搜索:', searchKeyword.value)\n}\n\nconst showFilter = () => {\n  uni.showActionSheet({\n    itemList: ['按评分排序', '按使用量排序', '按价格排序', '只看免费'],\n    success: (res) => {\n      uni.__f__('log','at pages/ai-agents/market/index.vue:299','筛选选项:', res.tapIndex)\n    }\n  })\n}\n\nconst switchCategory = (index: number) => {\n  currentCategory.value = index\n}\n\nconst viewAgent = (agent: any) => {\n  uni.navigateTo({\n    url: `/pages/ai-agents/detail/index?id=${agent.id}`\n  })\n}\n\nconst useAgent = (agent: any) => {\n  if (agent.price > 0) {\n    uni.showModal({\n      title: '付费智能体',\n      content: `${agent.name} 需要付费使用，价格：¥${agent.price}/月`,\n      confirmText: '立即购买',\n      success: (res) => {\n        if (res.confirm) {\n          // 跳转到支付页面\n          uni.__f__('log','at pages/ai-agents/market/index.vue:323','购买智能体:', agent.name)\n        }\n      }\n    })\n  } else {\n    // 免费智能体直接使用\n    uni.navigateTo({\n      url: `/pages/digital-human/chat/index?agentId=${agent.id}`\n    })\n  }\n}\n\nconst viewMore = (type: string) => {\n  uni.__f__('log','at pages/ai-agents/market/index.vue:336','查看更多:', type)\n}\n\n// 生命周期\nonMounted(() => {\n  uni.__f__('log','at pages/ai-agents/market/index.vue:341','AI智能体市场页面加载完成')\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.ai-agents-market {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);\n}\n\n// 头部区域\n.header-section {\n  background: #FFFFFF;\n  padding: 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n  \n  .search-container {\n    margin-bottom: 32rpx;\n    \n    .search-box {\n      display: flex;\n      align-items: center;\n      background: #F2F2F7;\n      border-radius: 20rpx;\n      padding: 24rpx 32rpx;\n      \n      .search-input {\n        flex: 1;\n        font-size: 28rpx;\n        color: #1D1D1F;\n        margin-left: 16rpx;\n        \n        &::placeholder {\n          color: #8E8E93;\n        }\n      }\n      \n      .filter-btn {\n        width: 56rpx;\n        height: 56rpx;\n        background: linear-gradient(135deg, #667eea, #764ba2);\n        border-radius: 28rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-left: 16rpx;\n      }\n    }\n  }\n  \n  .category-scroll {\n    white-space: nowrap;\n    \n    .category-list {\n      display: flex;\n      gap: 16rpx;\n      \n      .category-item {\n        flex-shrink: 0;\n        padding: 16rpx 32rpx;\n        background: #F2F2F7;\n        border-radius: 40rpx;\n        transition: all 0.3s ease;\n        \n        &.active {\n          background: linear-gradient(135deg, #667eea, #764ba2);\n          \n          .category-text {\n            color: #FFFFFF;\n          }\n        }\n        \n        .category-text {\n          font-size: 26rpx;\n          color: #8E8E93;\n          font-weight: 500;\n        }\n      }\n    }\n  }\n}\n\n// 内容区域\n.agents-content {\n  flex: 1;\n  padding: 32rpx;\n}\n\n.section {\n  margin-bottom: 48rpx;\n  \n  .section-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 32rpx;\n    \n    .section-title {\n      font-size: 36rpx;\n      font-weight: 700;\n      color: #1D1D1F;\n    }\n    \n    .section-more,\n    .section-count {\n      font-size: 26rpx;\n      color: #667eea;\n    }\n  }\n}\n\n// 推荐智能体网格\n.agents-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 24rpx;\n  \n  .agent-card {\n    background: #FFFFFF;\n    border-radius: 24rpx;\n    padding: 32rpx;\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n    transition: all 0.3s ease;\n    \n    &:active {\n      transform: scale(0.98);\n    }\n    \n    &.featured {\n      border: 2rpx solid rgba(102, 126, 234, 0.2);\n    }\n    \n    .card-header {\n      position: relative;\n      margin-bottom: 24rpx;\n      \n      .agent-avatar {\n        width: 80rpx;\n        height: 80rpx;\n        border-radius: 20rpx;\n      }\n      \n      .agent-badge {\n        position: absolute;\n        top: -8rpx;\n        right: -8rpx;\n        background: linear-gradient(45deg, #FFD700, #FFA500);\n        color: #FFFFFF;\n        font-size: 18rpx;\n        font-weight: 600;\n        padding: 4rpx 12rpx;\n        border-radius: 12rpx;\n      }\n      \n      .agent-rating {\n        position: absolute;\n        bottom: -8rpx;\n        right: 0;\n        display: flex;\n        align-items: center;\n        background: rgba(255, 215, 0, 0.1);\n        padding: 6rpx 12rpx;\n        border-radius: 16rpx;\n        \n        .rating-text {\n          font-size: 20rpx;\n          color: #FFD700;\n          margin-left: 4rpx;\n          font-weight: 600;\n        }\n      }\n    }\n    \n    .card-content {\n      margin-bottom: 24rpx;\n      \n      .agent-name {\n        display: block;\n        font-size: 28rpx;\n        font-weight: 600;\n        color: #1D1D1F;\n        margin-bottom: 8rpx;\n      }\n      \n      .agent-desc {\n        display: block;\n        font-size: 24rpx;\n        color: #8E8E93;\n        line-height: 1.4;\n        margin-bottom: 16rpx;\n      }\n      \n      .agent-tags {\n        display: flex;\n        gap: 8rpx;\n        margin-bottom: 16rpx;\n        \n        .tag {\n          font-size: 20rpx;\n          color: #667eea;\n          background: rgba(102, 126, 234, 0.1);\n          padding: 4rpx 12rpx;\n          border-radius: 12rpx;\n        }\n      }\n      \n      .agent-stats {\n        display: flex;\n        gap: 16rpx;\n        \n        .stat-item {\n          display: flex;\n          align-items: center;\n          \n          .stat-text {\n            font-size: 20rpx;\n            color: #8E8E93;\n            margin-left: 4rpx;\n          }\n        }\n      }\n    }\n    \n    .card-action {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      \n      .price-info {\n        .price {\n          font-size: 24rpx;\n          font-weight: 600;\n          color: #1D1D1F;\n          \n          &.free {\n            color: #10B981;\n          }\n        }\n      }\n      \n      .action-btn {\n        background: linear-gradient(135deg, #667eea, #764ba2);\n        color: #FFFFFF;\n        font-size: 22rpx;\n        font-weight: 500;\n        padding: 12rpx 24rpx;\n        border-radius: 20rpx;\n        \n        .btn-text {\n          color: #FFFFFF;\n        }\n      }\n    }\n  }\n}\n\n// 智能体列表\n.agents-list {\n  .agent-item {\n    display: flex;\n    align-items: center;\n    background: #FFFFFF;\n    border-radius: 20rpx;\n    padding: 32rpx;\n    margin-bottom: 16rpx;\n    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);\n    transition: all 0.3s ease;\n    \n    &:active {\n      transform: scale(0.98);\n    }\n    \n    .item-avatar {\n      width: 96rpx;\n      height: 96rpx;\n      border-radius: 24rpx;\n      margin-right: 24rpx;\n    }\n    \n    .item-content {\n      flex: 1;\n      \n      .item-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 8rpx;\n        \n        .item-name {\n          font-size: 28rpx;\n          font-weight: 600;\n          color: #1D1D1F;\n        }\n        \n        .item-rating {\n          display: flex;\n          align-items: center;\n          \n          .rating-text {\n            font-size: 20rpx;\n            color: #FFD700;\n            margin-left: 4rpx;\n          }\n        }\n      }\n      \n      .item-desc {\n        display: block;\n        font-size: 24rpx;\n        color: #8E8E93;\n        line-height: 1.4;\n        margin-bottom: 16rpx;\n      }\n      \n      .item-footer {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        \n        .item-tags {\n          display: flex;\n          gap: 8rpx;\n          \n          .mini-tag {\n            font-size: 18rpx;\n            color: #8E8E93;\n            background: #F2F2F7;\n            padding: 4rpx 8rpx;\n            border-radius: 8rpx;\n          }\n        }\n        \n        .item-price {\n          .price-text {\n            font-size: 22rpx;\n            font-weight: 600;\n            color: #1D1D1F;\n            \n            &.free {\n              color: #10B981;\n            }\n          }\n        }\n      }\n    }\n    \n    .item-action {\n      .action-btn {\n        width: 64rpx;\n        height: 64rpx;\n        background: rgba(102, 126, 234, 0.1);\n        border-radius: 32rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        \n        &.mini {\n          width: 56rpx;\n          height: 56rpx;\n          border-radius: 28rpx;\n        }\n      }\n    }\n  }\n}\n\n// 底部安全区域\n.bottom-safe-area {\n  height: calc(120rpx + env(safe-area-inset-bottom));\n}\n\n// 暗色模式适配\n@media (prefers-color-scheme: dark) {\n  .ai-agents-market {\n    background: linear-gradient(180deg, #1c1c1e 0%, #000000 100%);\n  }\n  \n  .header-section {\n    background: #1c1c1e;\n    border-bottom-color: #38383a;\n  }\n  \n  .agent-card,\n  .agent-item {\n    background: #2c2c2e;\n    border-color: #38383a;\n  }\n  \n  .agent-name,\n  .item-name {\n    color: #ffffff !important;\n  }\n  \n  .search-box {\n    background: #38383a !important;\n  }\n  \n  .category-item {\n    background: #38383a;\n    \n    &:not(.active) .category-text {\n      color: #8e8e93;\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/AI_system/mobile-uniapp/src/pages/ai-agents/market/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "onMounted"], "mappings": ";;;;;;;;;AAqKM,UAAA,gBAAgBA,kBAAY,EAAE;AAC9B,UAAA,kBAAkBA,kBAAY,CAAC;AAErC,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,EAAE,MAAM,MAAM,MAAM,OAAO;AAAA,MAC3B,EAAE,MAAM,QAAQ,MAAM,OAAO;AAAA,MAC7B,EAAE,MAAM,QAAQ,MAAM,OAAO;AAAA,MAC7B,EAAE,MAAM,QAAQ,MAAM,YAAY;AAAA,MAClC,EAAE,MAAM,QAAQ,MAAM,UAAU;AAAA,MAChC,EAAE,MAAM,QAAQ,MAAM,YAAY;AAAA,MAClC,EAAE,MAAM,QAAQ,MAAM,OAAO;AAAA,MAC7B,EAAE,MAAM,QAAQ,MAAM,OAAO;AAAA,IAAA,CAC9B;AAED,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,QACvB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,QACvB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM,CAAC,MAAM,OAAO,IAAI;AAAA,QACxB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,QACvB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,IAAA,CACD;AAED,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB,GAAG,eAAe;AAAA,MAClB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,QACvB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,QACvB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,IAAA,CACD;AAGK,UAAA,iBAAiBC,cAAAA,SAAS,MAAM;AACpC,UAAI,SAAS,UAAU;AAGnB,UAAA,gBAAgB,QAAQ,GAAG;AAC7B,cAAM,eAAe,WAAW,MAAM,gBAAgB,KAAK,EAAE;AACpD,iBAAA,OAAO,OAAO,CAAS,UAAA;AACvB,iBAAA,MAAM,KAAK,KAAK,CAAO,QAAA,IAAI,SAAS,aAAa,QAAQ,MAAM,EAAE,CAAC,CAAC;AAAA,QAAA,CAC3E;AAAA,MACH;AAGA,UAAI,cAAc,OAAO;AACvB,iBAAS,OAAO;AAAA,UAAO,CAAA,UACrB,MAAM,KAAK,SAAS,cAAc,KAAK,KACvC,MAAM,YAAY,SAAS,cAAc,KAAK,KAC9C,MAAM,KAAK,KAAK,SAAO,IAAI,SAAS,cAAc,KAAK,CAAC;AAAA,QAAA;AAAA,MAE5D;AAEO,aAAA;AAAA,IAAA,CACR;AAGD,UAAM,eAAe,MAAM;AAEzBC,oBAAA,MAAI,MAAM,OAAM,2CAA0C,OAAO,cAAc,KAAK;AAAA,IAAA;AAGtF,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,UAAU,SAAS,MAAM;AAAA,QAC7C,SAAS,CAAC,QAAQ;AAChBA,wBAAA,MAAI,MAAM,OAAM,2CAA0C,SAAS,IAAI,QAAQ;AAAA,QACjF;AAAA,MAAA,CACD;AAAA,IAAA;AAGG,UAAA,iBAAiB,CAAC,UAAkB;AACxC,sBAAgB,QAAQ;AAAA,IAAA;AAGpB,UAAA,YAAY,CAAC,UAAe;AAChCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,oCAAoC,MAAM,EAAE;AAAA,MAAA,CAClD;AAAA,IAAA;AAGG,UAAA,WAAW,CAAC,UAAe;AAC3B,UAAA,MAAM,QAAQ,GAAG;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS,GAAG,MAAM,IAAI,eAAe,MAAM,KAAK;AAAA,UAChD,aAAa;AAAA,UACb,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AAEfA,4BAAA,MAAI,MAAM,OAAM,2CAA0C,UAAU,MAAM,IAAI;AAAA,YAChF;AAAA,UACF;AAAA,QAAA,CACD;AAAA,MAAA,OACI;AAELA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,2CAA2C,MAAM,EAAE;AAAA,QAAA,CACzD;AAAA,MACH;AAAA,IAAA;AAGI,UAAA,WAAW,CAAC,SAAiB;AACjCA,oBAAA,MAAI,MAAM,OAAM,2CAA0C,SAAS,IAAI;AAAA,IAAA;AAIzEC,kBAAAA,UAAU,MAAM;AACVD,oBAAAA,MAAA,MAAM,OAAM,2CAA0C,eAAe;AAAA,IAAA,CAC1E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpVD,GAAG,WAAW,eAAe;"}