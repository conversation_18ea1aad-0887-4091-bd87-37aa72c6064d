"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_modules_translation = require("../../../store/modules/translation.js");
if (!Array) {
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  const _component_u_button = common_vendor.resolveComponent("u-button");
  const _component_u_radio = common_vendor.resolveComponent("u-radio");
  const _component_u_radio_group = common_vendor.resolveComponent("u-radio-group");
  const _component_u_slider = common_vendor.resolveComponent("u-slider");
  const _component_u_line_progress = common_vendor.resolveComponent("u-line-progress");
  const _component_u_picker = common_vendor.resolveComponent("u-picker");
  (_component_u_icon + _component_u_button + _component_u_radio + _component_u_radio_group + _component_u_slider + _component_u_line_progress + _component_u_picker)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props, { expose: __expose }) {
    const translationStore = store_modules_translation.useTranslationStore();
    const selectedVideoFile = common_vendor.ref(null);
    const isTranslating = common_vendor.ref(false);
    const translationProgress = common_vendor.ref(0);
    const progressStatus = common_vendor.ref("");
    const currentStep = common_vendor.ref(0);
    const translationResult = common_vendor.ref(null);
    const videoHistory = common_vendor.ref([]);
    const showSourceLanguagePicker = common_vendor.ref(false);
    const showTargetLanguagePicker = common_vendor.ref(false);
    const extractionMode = common_vendor.ref("auto");
    const outputFormat = common_vendor.ref("srt");
    const subtitleConfig = common_vendor.ref({
      fontSize: 18,
      color: "#FFFFFF",
      backgroundColor: "rgba(0,0,0,0.7)",
      position: "bottom"
    });
    const progressSteps = common_vendor.ref([
      { icon: "video", text: "视频解析" },
      { icon: "mic", text: "语音识别" },
      { icon: "translate", text: "文本翻译" },
      { icon: "subtitles", text: "字幕生成" },
      { icon: "checkmark", text: "完成处理" }
    ]);
    const colorOptions = common_vendor.ref([
      "#FFFFFF",
      "#000000",
      "#FF0000",
      "#00FF00",
      "#0000FF",
      "#FFFF00",
      "#FF00FF",
      "#00FFFF"
    ]);
    const {
      languages,
      currentSourceLang,
      currentTargetLang,
      sourceLanguage,
      targetLanguage
    } = translationStore;
    const canTranslate = common_vendor.computed(() => {
      return selectedVideoFile.value && currentSourceLang.value && currentTargetLang.value;
    });
    const sourceLanguageColumns = common_vendor.computed(() => {
      const autoDetect = [{ text: "自动检测", value: "auto" }];
      const languageOptions = languages.map((lang) => ({
        text: lang.name,
        value: lang.code
      }));
      return [autoDetect.concat(languageOptions)];
    });
    const targetLanguageColumns = common_vendor.computed(() => {
      const languageOptions = languages.filter((lang) => lang.code !== "auto").map((lang) => ({
        text: lang.name,
        value: lang.code
      }));
      return [languageOptions];
    });
    const subtitleStyle = common_vendor.computed(() => {
      return {
        fontSize: subtitleConfig.value.fontSize + "px",
        color: subtitleConfig.value.color,
        backgroundColor: subtitleConfig.value.backgroundColor,
        padding: "8px 12px",
        borderRadius: "4px",
        display: "inline-block"
      };
    });
    common_vendor.onMounted(async () => {
      await translationStore.loadLanguages();
      loadVideoHistory();
    });
    const chooseVideoFile = () => {
      common_vendor.index.chooseVideo({
        sourceType: ["album", "camera"],
        maxDuration: 300,
        // 5分钟
        success: (res) => {
          handleSelectedFile({
            name: res.tempFilePath.split("/").pop(),
            size: res.size,
            path: res.tempFilePath,
            duration: res.duration,
            width: res.width,
            height: res.height
          });
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/translation/video/index.vue:422", "选择视频失败:", error);
        }
      });
    };
    const handleSelectedFile = (file) => {
      if (file.size > 500 * 1024 * 1024) {
        common_vendor.index.showToast({
          title: "文件大小不能超过500MB",
          icon: "error"
        });
        return;
      }
      selectedVideoFile.value = {
        name: file.name,
        size: file.size,
        path: file.path || URL.createObjectURL(file),
        duration: file.duration,
        resolution: file.width && file.height ? `${file.width}x${file.height}` : null,
        thumbnail: null
      };
      generateThumbnail(selectedVideoFile.value.path);
    };
    const generateThumbnail = (videoPath) => {
    };
    const onVideoLoaded = (e) => {
      if (selectedVideoFile.value && !selectedVideoFile.value.duration) {
        selectedVideoFile.value.duration = e.target.duration;
      }
    };
    const removeVideoFile = () => {
      selectedVideoFile.value = null;
      translationResult.value = null;
    };
    const startTranslation = async () => {
      if (!canTranslate.value)
        return;
      try {
        isTranslating.value = true;
        translationProgress.value = 0;
        currentStep.value = 0;
        const steps = [
          { status: "正在解析视频文件...", duration: 2e3 },
          { status: "正在识别语音内容...", duration: 3e3 },
          { status: "正在翻译文本...", duration: 2e3 },
          { status: "正在生成字幕...", duration: 2e3 },
          { status: "正在合成视频...", duration: 3e3 }
        ];
        for (let i = 0; i < steps.length; i++) {
          currentStep.value = i;
          progressStatus.value = steps[i].status;
          const stepProgress = 100 / steps.length;
          const startProgress = i * stepProgress;
          await new Promise((resolve) => {
            const interval = setInterval(() => {
              translationProgress.value += 2;
              if (translationProgress.value >= (i + 1) * stepProgress) {
                clearInterval(interval);
                resolve(true);
              }
            }, steps[i].duration / 50);
          });
        }
        const mockResult = outputFormat.value === "video" ? {
          type: "video",
          filename: `translated_${selectedVideoFile.value.name}`,
          videoUrl: selectedVideoFile.value.path,
          thumbnail: selectedVideoFile.value.thumbnail,
          size: selectedVideoFile.value.size * 1.2,
          duration: selectedVideoFile.value.duration
        } : {
          type: "subtitle",
          filename: `${selectedVideoFile.value.name.split(".")[0]}.${outputFormat.value}`,
          subtitles: [
            {
              startTime: 0,
              endTime: 3,
              originalText: "这是第一句原文",
              translatedText: "This is the first original sentence"
            },
            {
              startTime: 3,
              endTime: 6,
              originalText: "这是第二句原文",
              translatedText: "This is the second original sentence"
            },
            {
              startTime: 6,
              endTime: 9,
              originalText: "这是第三句原文",
              translatedText: "This is the third original sentence"
            }
          ]
        };
        translationResult.value = mockResult;
        const historyItem = {
          id: Date.now().toString(),
          filename: selectedVideoFile.value.name,
          thumbnail: selectedVideoFile.value.thumbnail,
          sourceLang: currentSourceLang.value,
          targetLang: currentTargetLang.value,
          outputFormat: outputFormat.value,
          size: selectedVideoFile.value.size,
          duration: selectedVideoFile.value.duration,
          createdAt: (/* @__PURE__ */ new Date()).toISOString()
        };
        videoHistory.value.unshift(historyItem);
        saveVideoHistory();
        common_vendor.index.showToast({
          title: "翻译完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/translation/video/index.vue:579", "翻译失败:", error);
        common_vendor.index.showToast({
          title: "翻译失败",
          icon: "error"
        });
      } finally {
        isTranslating.value = false;
        currentStep.value = progressSteps.value.length;
      }
    };
    const swapLanguages = () => {
      translationStore.swapLanguages();
    };
    const onSourceLanguageConfirm = (value) => {
      translationStore.setSourceLanguage(value[0].value);
      showSourceLanguagePicker.value = false;
    };
    const onTargetLanguageConfirm = (value) => {
      translationStore.setTargetLanguage(value[0].value);
      showTargetLanguagePicker.value = false;
    };
    const updateSubtitleStyle = () => {
    };
    const selectColor = (color) => {
      subtitleConfig.value.color = color;
    };
    const downloadResult = () => {
      if (!translationResult.value)
        return;
      common_vendor.index.showToast({
        title: "开始下载",
        icon: "success"
      });
    };
    const shareResult = () => {
      if (!translationResult.value)
        return;
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 2,
        imageUrl: translationResult.value.thumbnail,
        title: "视频翻译完成",
        summary: `已完成视频翻译: ${translationResult.value.filename}`,
        success: () => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        }
      });
    };
    const downloadSubtitle = () => {
      var _a;
      if (!((_a = translationResult.value) == null ? void 0 : _a.subtitles))
        return;
      if (outputFormat.value === "srt") {
        translationResult.value.subtitles.map((sub, index) => {
          return `${index + 1}
${formatSRTTime(sub.startTime)} --> ${formatSRTTime(sub.endTime)}
${sub.translatedText}
`;
        }).join("\n");
      }
      common_vendor.index.showToast({
        title: "字幕文件已生成",
        icon: "success"
      });
    };
    const formatTime = (seconds) => {
      if (typeof seconds === "string") {
        return common_vendor.dayjs(seconds).format("MM-DD HH:mm");
      }
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    };
    const formatDuration = (seconds) => {
      if (!seconds)
        return "00:00";
      return formatTime(seconds);
    };
    const formatSRTTime = (seconds) => {
      const hours = Math.floor(seconds / 3600);
      const mins = Math.floor(seconds % 3600 / 60);
      const secs = Math.floor(seconds % 60);
      const ms = Math.floor(seconds % 1 * 1e3);
      return `${hours.toString().padStart(2, "0")}:${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")},${ms.toString().padStart(3, "0")}`;
    };
    const formatFileSize = (bytes) => {
      if (bytes === 0)
        return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };
    const saveVideoHistory = () => {
      common_vendor.index.setStorageSync("video_translation_history", videoHistory.value);
    };
    const loadVideoHistory = () => {
      const history = common_vendor.index.getStorageSync("video_translation_history") || [];
      videoHistory.value = history;
    };
    const viewAllHistory = () => {
      common_vendor.index.navigateTo({
        url: "/pages/translation/video/history/index"
      });
    };
    const viewHistoryItem = (item) => {
      common_vendor.index.navigateTo({
        url: `/pages/translation/video/detail/index?id=${item.id}`
      });
    };
    const onPullDownRefresh = () => {
      loadVideoHistory();
      common_vendor.index.stopPullDownRefresh();
    };
    __expose({
      onPullDownRefresh
    });
    return (_ctx, _cache) => {
      var _a, _b;
      return common_vendor.e({
        a: common_vendor.p({
          name: "arrow-down",
          size: "12",
          color: "#2563EB",
          ["margin-left"]: "4"
        }),
        b: common_vendor.o(($event) => showSourceLanguagePicker.value = true),
        c: common_vendor.p({
          type: "text",
          text: ((_a = common_vendor.unref(sourceLanguage)) == null ? void 0 : _a.name) || "自动检测",
          ["custom-style"]: {
            color: "#2563EB"
          }
        }),
        d: common_vendor.p({
          name: "arrow-left-right",
          size: "20",
          color: "#6B7280"
        }),
        e: common_vendor.o(swapLanguages),
        f: common_vendor.p({
          name: "arrow-down",
          size: "12",
          color: "#2563EB",
          ["margin-left"]: "4"
        }),
        g: common_vendor.o(($event) => showTargetLanguagePicker.value = true),
        h: common_vendor.p({
          type: "text",
          text: ((_b = common_vendor.unref(targetLanguage)) == null ? void 0 : _b.name) || "中文",
          ["custom-style"]: {
            color: "#2563EB"
          }
        }),
        i: !selectedVideoFile.value
      }, !selectedVideoFile.value ? {
        j: common_vendor.p({
          name: "video",
          size: "48",
          color: "#2563EB"
        })
      } : common_vendor.e({
        k: selectedVideoFile.value.path,
        l: selectedVideoFile.value.thumbnail,
        m: common_vendor.o(onVideoLoaded),
        n: common_vendor.t(selectedVideoFile.value.name),
        o: common_vendor.t(formatFileSize(selectedVideoFile.value.size)),
        p: selectedVideoFile.value.duration
      }, selectedVideoFile.value.duration ? {
        q: common_vendor.t(formatDuration(selectedVideoFile.value.duration))
      } : {}, {
        r: selectedVideoFile.value.resolution
      }, selectedVideoFile.value.resolution ? {
        s: common_vendor.t(selectedVideoFile.value.resolution)
      } : {}, {
        t: common_vendor.o(removeVideoFile),
        v: common_vendor.p({
          name: "close-circle",
          size: "24",
          color: "#EF4444"
        })
      }), {
        w: common_vendor.o(chooseVideoFile),
        x: selectedVideoFile.value
      }, selectedVideoFile.value ? common_vendor.e({
        y: common_vendor.p({
          name: "auto",
          label: "自动识别"
        }),
        z: common_vendor.p({
          name: "existing",
          label: "现有字幕"
        }),
        A: common_vendor.o(($event) => extractionMode.value = $event),
        B: common_vendor.p({
          modelValue: extractionMode.value
        }),
        C: common_vendor.p({
          name: "srt",
          label: "SRT字幕"
        }),
        D: common_vendor.p({
          name: "vtt",
          label: "VTT字幕"
        }),
        E: common_vendor.p({
          name: "ass",
          label: "ASS字幕"
        }),
        F: common_vendor.p({
          name: "video",
          label: "嵌入视频"
        }),
        G: common_vendor.o(($event) => outputFormat.value = $event),
        H: common_vendor.p({
          modelValue: outputFormat.value
        }),
        I: outputFormat.value === "video"
      }, outputFormat.value === "video" ? {
        J: common_vendor.s(subtitleStyle.value),
        K: common_vendor.o(updateSubtitleStyle),
        L: common_vendor.o(($event) => subtitleConfig.value.fontSize = $event),
        M: common_vendor.p({
          min: 12,
          max: 32,
          modelValue: subtitleConfig.value.fontSize
        }),
        N: common_vendor.f(colorOptions.value, (color, k0, i0) => {
          return {
            a: color,
            b: color,
            c: subtitleConfig.value.color === color ? 1 : "",
            d: common_vendor.o(($event) => selectColor(color), color)
          };
        })
      } : {}, {
        O: common_vendor.t(isTranslating.value ? "处理中..." : "开始翻译"),
        P: common_vendor.o(startTranslation),
        Q: common_vendor.p({
          type: "primary",
          size: "large",
          loading: isTranslating.value,
          disabled: !canTranslate.value,
          ["custom-style"]: {
            width: "100%"
          }
        })
      }) : {}, {
        R: isTranslating.value
      }, isTranslating.value ? {
        S: common_vendor.t(progressStatus.value),
        T: common_vendor.p({
          percent: translationProgress.value,
          ["show-percent"]: true,
          ["active-color"]: "#2563EB"
        }),
        U: common_vendor.f(progressSteps.value, (step, index, i0) => {
          return {
            a: "2bac73e5-18-" + i0,
            b: common_vendor.p({
              name: currentStep.value > index ? "checkmark" : step.icon,
              size: "16",
              color: currentStep.value >= index ? "#2563EB" : "#9CA3AF"
            }),
            c: common_vendor.t(step.text),
            d: index,
            e: currentStep.value === index ? 1 : "",
            f: currentStep.value > index ? 1 : ""
          };
        })
      } : {}, {
        V: translationResult.value
      }, translationResult.value ? common_vendor.e({
        W: common_vendor.o(downloadResult),
        X: common_vendor.p({
          name: "download",
          size: "18",
          color: "#2563EB"
        }),
        Y: common_vendor.o(shareResult),
        Z: common_vendor.p({
          name: "share",
          size: "18",
          color: "#2563EB",
          ["margin-left"]: "12"
        }),
        aa: translationResult.value.type === "video"
      }, translationResult.value.type === "video" ? {
        ab: translationResult.value.videoUrl,
        ac: translationResult.value.thumbnail,
        ad: common_vendor.t(translationResult.value.filename),
        ae: common_vendor.t(formatFileSize(translationResult.value.size))
      } : {}, {
        af: translationResult.value.type === "subtitle"
      }, translationResult.value.type === "subtitle" ? common_vendor.e({
        ag: common_vendor.f(translationResult.value.subtitles.slice(0, 5), (subtitle, index, i0) => {
          return {
            a: common_vendor.t(formatTime(subtitle.startTime)),
            b: common_vendor.t(formatTime(subtitle.endTime)),
            c: common_vendor.t(subtitle.originalText),
            d: common_vendor.t(subtitle.translatedText),
            e: index
          };
        }),
        ah: translationResult.value.subtitles.length > 5
      }, translationResult.value.subtitles.length > 5 ? {
        ai: common_vendor.t(translationResult.value.subtitles.length - 5)
      } : {}, {
        aj: common_vendor.o(downloadSubtitle),
        ak: common_vendor.p({
          type: "primary",
          size: "small"
        })
      }) : {}) : {}, {
        al: videoHistory.value.length > 0
      }, videoHistory.value.length > 0 ? {
        am: common_vendor.o(viewAllHistory),
        an: common_vendor.p({
          type: "text",
          text: "查看全部",
          size: "small",
          ["custom-style"]: {
            color: "#2563EB",
            fontSize: "24rpx"
          }
        }),
        ao: common_vendor.f(videoHistory.value.slice(0, 3), (item, k0, i0) => {
          return {
            a: item.thumbnail,
            b: "2bac73e5-23-" + i0,
            c: common_vendor.t(item.filename),
            d: common_vendor.t(item.sourceLang),
            e: common_vendor.t(item.targetLang),
            f: common_vendor.t(formatTime(item.createdAt)),
            g: "2bac73e5-24-" + i0,
            h: item.id,
            i: common_vendor.o(($event) => viewHistoryItem(item), item.id)
          };
        }),
        ap: common_vendor.p({
          name: "play",
          size: "16",
          color: "#fff"
        }),
        aq: common_vendor.p({
          name: "download",
          size: "16",
          color: "#6B7280"
        })
      } : {}, {
        ar: common_vendor.o(onSourceLanguageConfirm),
        as: common_vendor.o(($event) => showSourceLanguagePicker.value = false),
        at: common_vendor.o(($event) => showSourceLanguagePicker.value = $event),
        av: common_vendor.p({
          columns: sourceLanguageColumns.value,
          modelValue: showSourceLanguagePicker.value
        }),
        aw: common_vendor.o(onTargetLanguageConfirm),
        ax: common_vendor.o(($event) => showTargetLanguagePicker.value = false),
        ay: common_vendor.o(($event) => showTargetLanguagePicker.value = $event),
        az: common_vendor.p({
          columns: targetLanguageColumns.value,
          modelValue: showTargetLanguagePicker.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2bac73e5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/translation/video/index.js.map
