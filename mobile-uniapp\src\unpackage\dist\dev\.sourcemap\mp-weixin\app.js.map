{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script setup lang=\"ts\">\nimport { onLaunch, onShow, onHide } from '@dcloudio/uni-app'\n\nonLaunch(() => {\n  uni.__f__('log','at App.vue:5','App Launch')\n})\n\nonShow(() => {\n  uni.__f__('log','at App.vue:9','App Show')\n})\n\nonHide(() => {\n  uni.__f__('log','at App.vue:13','App Hide')\n})\n</script>\n\n<style lang=\"scss\">\n@import '@/styles/variables.scss';\n@import '@/styles/global.scss';\n\n/* 全局样式 */\npage {\n  background-color: $bg-secondary;\n  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;\n}\n\n/* 通用类 */\n.container {\n  padding: 0;\n  margin: 0;\n}\n\n.flex {\n  display: flex;\n}\n\n.flex-1 {\n  flex: 1;\n}\n\n.justify-center {\n  justify-content: center;\n}\n\n.items-center {\n  align-items: center;\n}\n\n.text-center {\n  text-align: center;\n}\n\n/* 间距类 */\n.p-1 { padding: $spacing-1; }\n.p-2 { padding: $spacing-2; }\n.p-3 { padding: $spacing-3; }\n.p-4 { padding: $spacing-4; }\n\n.m-1 { margin: $spacing-1; }\n.m-2 { margin: $spacing-2; }\n.m-3 { margin: $spacing-3; }\n.m-4 { margin: $spacing-4; }\n\n/* 文字颜色 */\n.text-primary { color: $text-primary; }\n.text-secondary { color: $text-secondary; }\n.text-tertiary { color: $text-tertiary; }\n\n/* 背景颜色 */\n.bg-primary { background-color: $bg-primary; }\n.bg-secondary { background-color: $bg-secondary; }\n.bg-tertiary { background-color: $bg-tertiary; }\n\n/* 圆角 */\n.rounded { border-radius: $radius-md; }\n.rounded-lg { border-radius: $radius-lg; }\n.rounded-full { border-radius: $radius-full; }\n</style>\n", "import { createSSRApp } from 'vue'\nimport { createPinia } from 'pinia'\nimport App from './App.vue'\n\nexport function createApp() {\n  const app = createSSRApp(App)\n  const pinia = createPinia()\n  \n  app.use(pinia)\n  \n  return {\n    app,\n    pinia\n  }\n}\n"], "names": ["onLaunch", "uni", "onShow", "onHide", "createSSRApp", "App", "createPinia"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAGAA,kBAAAA,SAAS,MAAM;AACTC,oBAAAA,MAAA,MAAM,OAAM,gBAAe,YAAY;AAAA,IAAA,CAC5C;AAEDC,kBAAAA,OAAO,MAAM;AACPD,oBAAAA,MAAA,MAAM,OAAM,gBAAe,UAAU;AAAA,IAAA,CAC1C;AAEDE,kBAAAA,OAAO,MAAM;AACPF,oBAAAA,MAAA,MAAM,OAAM,iBAAgB,UAAU;AAAA,IAAA,CAC3C;;;;;ACTM,SAAS,YAAY;AAC1B,QAAM,MAAMG,cAAY,aAACC,SAAG;AAC5B,QAAM,QAAQC,cAAAA,YAAa;AAE3B,MAAI,IAAI,KAAK;AAEb,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACD;AACH;AAEA,YAAY,IAAI,MAAM,MAAM;;"}