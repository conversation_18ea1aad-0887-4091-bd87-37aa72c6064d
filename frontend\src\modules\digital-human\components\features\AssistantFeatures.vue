<template>
  <div class="assistant-features">
    <h4 class="features-title">智能助手功能</h4>
    <div class="features-grid">
      <div class="feature-item" @click="sendAction('calculator')">
        <calculator-outlined />
        <span>计算器</span>
      </div>
      <div class="feature-item" @click="sendAction('weather')">
        <cloud-outlined />
        <span>天气查询</span>
      </div>
      <div class="feature-item" @click="sendAction('knowledge')">
        <database-outlined />
        <span>知识库</span>
      </div>
      <div class="feature-item" @click="sendAction('reminder')">
        <clock-circle-outlined />
        <span>提醒事项</span>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { Calculator, CloudyNight, Database, Clock } from '@element-plus/icons-vue';

export default defineComponent({
  name: 'AssistantFeatures',
  
  components: {
    CalculatorOutlined,
    CloudOutlined,
    DatabaseOutlined,
    ClockCircleOutlined
  },
  
  props: {
    digitalHuman: {
      type: Object,
      required: true,
      default: () => ({
        name: '',
        type: '',
        avatar_url: '',
        description: '',
        apiId: '',
        digital_human_id: '',
        voice_id: '',
        welcomeText: ''
      })
    }
  },
  
  emits: ['send-message'],
  
  setup(props, { emit }) {
    const sendAction = (action) => {
      switch(action) {
        case 'calculator':
          emit('send-message', {
            type: 'sendMessage',
            content: '我需要进行计算'
          });
          break;
        case 'weather':
          emit('send-message', {
            type: 'sendMessage',
            content: '查询今天的天气'
          });
          break;
        case 'knowledge':
          emit('send-message', {
            type: 'sendMessage',
            content: '请给我介绍一下人工智能的知识'
          });
          break;
        case 'reminder':
          emit('send-message', {
            type: 'sendMessage',
            content: '我需要设置一个提醒'
          });
          break;
      }
    };
    
    return {
      sendAction
    };
  }
});
</script>

<style scoped>
.assistant-features {
  width: 100%;
}

.features-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #3b82f6;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.feature-item {
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.feature-item:hover {
  background-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

.feature-item span {
  font-size: 14px;
  color: #f9fafb;
}

:deep(.light-theme) .feature-item {
  background-color: rgba(59, 130, 246, 0.05);
}

:deep(.light-theme) .feature-item span {
  color: #1f2937;
}

:deep(.light-theme) .feature-item:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

:deep(.light-theme) .features-title {
  color: #2563eb;
}
</style> 