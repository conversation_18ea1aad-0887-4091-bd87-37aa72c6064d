/**
 * 网络请求工具
 * 基于 uni.request 封装，支持拦截器、错误处理等
 */

import { useUserStore } from '@/store/modules/user'

// 请求配置接口
interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  data?: any
  header?: Record<string, string>
  timeout?: number
  dataType?: string
  responseType?: string
  sslVerify?: boolean
  withCredentials?: boolean
  firstIpv4?: boolean
}

// 响应数据接口
interface ResponseData<T = any> {
  code: number
  message: string
  data: T
  success: boolean
  timestamp?: number
}

// API基础URL配置
const getBaseURL = () => {
  // #ifdef H5
  return '/api'
  // #endif
  
  // #ifdef MP
  return 'https://your-api-domain.com/api'
  // #endif
  
  // #ifdef APP-PLUS
  return 'https://your-api-domain.com/api'
  // #endif
  
  return 'http://localhost:8000/api'
}

const BASE_URL = getBaseURL()

// 请求超时时间
const TIMEOUT = 10000

// 请求拦截器
const requestInterceptor = (config: RequestConfig) => {
  // 添加基础URL
  if (!config.url.startsWith('http')) {
    config.url = BASE_URL + config.url
  }
  
  // 设置默认请求头
  config.header = {
    'Content-Type': 'application/json',
    ...config.header
  }
  
  // 添加认证token
  const userStore = useUserStore()
  if (userStore.token) {
    config.header.Authorization = `Bearer ${userStore.token}`
  }
  
  // 设置超时时间
  config.timeout = config.timeout || TIMEOUT
  
  // 打印请求日志
  console.log(`[REQUEST] ${config.method || 'GET'} ${config.url}`, config.data)
  
  return config
}

// 响应拦截器
const responseInterceptor = (response: any, config: RequestConfig) => {
  console.log(`[RESPONSE] ${config.url}`, response)
  
  const { statusCode, data } = response
  
  // HTTP状态码检查
  if (statusCode >= 200 && statusCode < 300) {
    // 业务状态码检查
    if (data.code === 0 || data.success === true) {
      return Promise.resolve(data)
    } else {
      // 业务错误处理
      handleBusinessError(data, config)
      return Promise.reject(data)
    }
  } else {
    // HTTP错误处理
    handleHttpError(statusCode, config)
    return Promise.reject(response)
  }
}

// 错误拦截器
const errorInterceptor = (error: any, config: RequestConfig) => {
  console.error(`[REQUEST ERROR] ${config.url}`, error)
  
  // 网络错误处理
  if (error.errMsg) {
    if (error.errMsg.includes('timeout')) {
      showError('请求超时，请检查网络连接')
    } else if (error.errMsg.includes('fail')) {
      showError('网络连接失败，请检查网络设置')
    } else {
      showError('网络请求失败')
    }
  }
  
  return Promise.reject(error)
}

// 业务错误处理
const handleBusinessError = (data: ResponseData, config: RequestConfig) => {
  const { code, message } = data
  
  switch (code) {
    case 401:
      // 未授权，跳转登录
      handleUnauthorized()
      break
    case 403:
      showError('没有权限访问')
      break
    case 404:
      showError('请求的资源不存在')
      break
    case 500:
      showError('服务器内部错误')
      break
    default:
      if (message) {
        showError(message)
      }
  }
}

// HTTP错误处理
const handleHttpError = (statusCode: number, config: RequestConfig) => {
  switch (statusCode) {
    case 400:
      showError('请求参数错误')
      break
    case 401:
      handleUnauthorized()
      break
    case 403:
      showError('禁止访问')
      break
    case 404:
      showError('请求地址不存在')
      break
    case 500:
      showError('服务器内部错误')
      break
    case 502:
      showError('网关错误')
      break
    case 503:
      showError('服务不可用')
      break
    case 504:
      showError('网关超时')
      break
    default:
      showError(`请求失败 (${statusCode})`)
  }
}

// 处理未授权
const handleUnauthorized = () => {
  const userStore = useUserStore()
  
  // 清除登录状态
  userStore.logout()
  
  showError('登录已过期，请重新登录')
  
  // 跳转到登录页
  uni.reLaunch({
    url: '/pages/user/login/index'
  })
}

// 显示错误提示
const showError = (message: string) => {
  uni.showToast({
    title: message,
    icon: 'error',
    duration: 2000
  })
}

// 主要请求函数
const request = <T = any>(config: RequestConfig): Promise<ResponseData<T>> => {
  return new Promise((resolve, reject) => {
    // 请求拦截
    const finalConfig = requestInterceptor(config)
    
    uni.request({
      ...finalConfig,
      success: (response) => {
        responseInterceptor(response, finalConfig)
          .then(resolve)
          .catch(reject)
      },
      fail: (error) => {
        errorInterceptor(error, finalConfig)
          .then(resolve)
          .catch(reject)
      }
    })
  })
}

// 便捷方法
export const http = {
  get: <T = any>(url: string, params?: any, config?: Partial<RequestConfig>) => {
    return request<T>({
      url: params ? `${url}?${new URLSearchParams(params).toString()}` : url,
      method: 'GET',
      ...config
    })
  },
  
  post: <T = any>(url: string, data?: any, config?: Partial<RequestConfig>) => {
    return request<T>({
      url,
      method: 'POST',
      data,
      ...config
    })
  },
  
  put: <T = any>(url: string, data?: any, config?: Partial<RequestConfig>) => {
    return request<T>({
      url,
      method: 'PUT',
      data,
      ...config
    })
  },
  
  delete: <T = any>(url: string, config?: Partial<RequestConfig>) => {
    return request<T>({
      url,
      method: 'DELETE',
      ...config
    })
  },
  
  patch: <T = any>(url: string, data?: any, config?: Partial<RequestConfig>) => {
    return request<T>({
      url,
      method: 'PATCH',
      data,
      ...config
    })
  }
}

// 文件上传
export const uploadFile = (config: {
  url: string
  filePath: string
  name: string
  formData?: Record<string, any>
  header?: Record<string, string>
}) => {
  return new Promise((resolve, reject) => {
    const userStore = useUserStore()
    
    const uploadConfig = {
      url: config.url.startsWith('http') ? config.url : BASE_URL + config.url,
      filePath: config.filePath,
      name: config.name,
      formData: config.formData,
      header: {
        ...config.header,
        Authorization: userStore.token ? `Bearer ${userStore.token}` : ''
      },
      success: (response: any) => {
        console.log('[UPLOAD SUCCESS]', response)
        
        try {
          const data = typeof response.data === 'string' 
            ? JSON.parse(response.data) 
            : response.data
          
          if (data.success || data.code === 0) {
            resolve(data)
          } else {
            showError(data.message || '上传失败')
            reject(data)
          }
        } catch (error) {
          console.error('解析上传响应失败:', error)
          reject(error)
        }
      },
      fail: (error: any) => {
        console.error('[UPLOAD ERROR]', error)
        showError('文件上传失败')
        reject(error)
      }
    }
    
    uni.uploadFile(uploadConfig)
  })
}

export default request
