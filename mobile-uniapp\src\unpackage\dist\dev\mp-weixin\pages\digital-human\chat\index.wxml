<view class="chat-page data-v-90568451"><view wx:if="{{a}}" class="chat-header data-v-90568451"><view class="digital-human-info data-v-90568451"><image class="avatar data-v-90568451" src="{{b}}" mode="aspectFill"></image><view class="info-content data-v-90568451"><text class="name data-v-90568451">{{c}}</text><text class="status data-v-90568451">在线</text></view></view><view class="header-actions data-v-90568451"><u-icon wx:if="{{e}}" class="data-v-90568451" bindclick="{{d}}" u-i="90568451-0" bind:__l="__l" u-p="{{e}}"></u-icon></view></view><scroll-view class="message-list data-v-90568451" scroll-y="true" scroll-top="{{n}}" scroll-with-animation="{{true}}" bindscrolltoupper="{{o}}"><view wx:if="{{f}}" class="load-more data-v-90568451"><u-loading-icon wx:if="{{g}}" class="data-v-90568451" u-i="90568451-1" bind:__l="__l"></u-loading-icon><text class="load-text data-v-90568451">{{h}}</text></view><view wx:for="{{i}}" wx:for-item="message" wx:key="I" class="{{['message-item', 'data-v-90568451', message.J && 'user-message', message.K && 'assistant-message']}}"><view wx:if="{{message.a}}" class="message-content data-v-90568451"><view class="message-bubble user-bubble data-v-90568451"><text wx:if="{{message.b}}" class="message-text data-v-90568451">{{message.c}}</text><view wx:if="{{message.d}}" class="audio-message data-v-90568451"><u-icon wx:if="{{message.f}}" class="data-v-90568451" u-i="{{message.e}}" bind:__l="__l" u-p="{{message.f}}"></u-icon><text class="audio-duration data-v-90568451">{{message.g}}</text><u-icon wx:if="{{message.j}}" class="data-v-90568451" bindclick="{{message.h}}" u-i="{{message.i}}" bind:__l="__l" u-p="{{message.j}}"></u-icon></view></view><text class="message-time data-v-90568451">{{message.k}}</text></view><view wx:else class="message-content data-v-90568451"><image class="message-avatar data-v-90568451" src="{{message.l}}" mode="aspectFill"></image><view class="message-bubble assistant-bubble data-v-90568451"><text wx:if="{{message.m}}" class="message-text data-v-90568451">{{message.n}}</text><view wx:if="{{message.o}}" class="audio-message data-v-90568451"><u-icon wx:if="{{message.r}}" class="data-v-90568451" bindclick="{{message.p}}" u-i="{{message.q}}" bind:__l="__l" u-p="{{message.r}}"></u-icon><text class="audio-duration data-v-90568451">{{message.s}}</text></view><view wx:if="{{message.t}}" class="video-message data-v-90568451"><video class="message-video data-v-90568451" src="{{message.v}}" poster="{{message.w}}" controls bindplay="{{message.x}}" bindpause="{{message.y}}"></video></view><view class="message-actions data-v-90568451"><u-icon wx:if="{{message.z}}" class="data-v-90568451" bindclick="{{message.A}}" u-i="{{message.B}}" bind:__l="__l" u-p="{{message.C}}"></u-icon><u-icon wx:if="{{message.D}}" class="data-v-90568451" bindclick="{{message.E}}" u-i="{{message.F}}" bind:__l="__l" u-p="{{message.G}}"></u-icon></view></view><text class="message-time data-v-90568451">{{message.H}}</text></view></view><view wx:if="{{j}}" class="generating-video data-v-90568451"><view class="generating-content data-v-90568451"><u-loading-icon wx:if="{{k}}" class="data-v-90568451" u-i="90568451-7" bind:__l="__l" u-p="{{k}}"></u-loading-icon><text class="generating-text data-v-90568451">正在生成数字人视频回复...</text></view></view><view wx:if="{{l}}" class="typing-indicator data-v-90568451"><image class="typing-avatar data-v-90568451" src="{{m}}" mode="aspectFill"></image><view class="typing-bubble data-v-90568451"><view class="typing-dots data-v-90568451"><view class="dot data-v-90568451"></view><view class="dot data-v-90568451"></view><view class="dot data-v-90568451"></view></view></view></view></scroll-view><view class="input-area data-v-90568451"><view class="input-container data-v-90568451"><u-icon wx:if="{{q}}" class="data-v-90568451" bindclick="{{p}}" u-i="90568451-8" bind:__l="__l" u-p="{{q}}"></u-icon><input class="text-input data-v-90568451" placeholder="输入消息..." disabled="{{r}}" bindconfirm="{{s}}" confirm-type="send" value="{{t}}" bindinput="{{v}}"/><view class="input-actions data-v-90568451"><u-icon wx:if="{{w}}" class="data-v-90568451" bindclick="{{x}}" u-i="90568451-9" bind:__l="__l" u-p="{{y}}"></u-icon><u-button wx:else class="data-v-90568451" u-s="{{['d']}}" bindclick="{{z}}" u-i="90568451-10" bind:__l="__l" u-p="{{A||''}}"> 发送 </u-button></view></view></view><u-modal wx:if="{{E}}" class="data-v-90568451" u-s="{{['d']}}" bindconfirm="{{C}}" u-i="90568451-11" bind:__l="__l" bindupdateModelValue="{{D}}" u-p="{{E}}"><view class="digital-human-list data-v-90568451"><view wx:for="{{B}}" wx:for-item="human" wx:key="g" class="{{['digital-human-item', 'data-v-90568451', human.h && 'active']}}" bindtap="{{human.i}}"><image class="item-avatar data-v-90568451" src="{{human.a}}" mode="aspectFill"></image><view class="item-info data-v-90568451"><text class="item-name data-v-90568451">{{human.b}}</text><text class="item-desc data-v-90568451">{{human.c}}</text></view><u-icon wx:if="{{human.d}}" class="data-v-90568451" u-i="{{human.e}}" bind:__l="__l" u-p="{{human.f}}"></u-icon></view></view></u-modal><u-action-sheet wx:if="{{H}}" class="data-v-90568451" bindclick="{{F}}" u-i="90568451-13" bind:__l="__l" bindupdateModelValue="{{G}}" u-p="{{H}}"></u-action-sheet><u-action-sheet wx:if="{{K}}" class="data-v-90568451" bindclick="{{I}}" u-i="90568451-14" bind:__l="__l" bindupdateModelValue="{{J}}" u-p="{{K}}"></u-action-sheet></view>