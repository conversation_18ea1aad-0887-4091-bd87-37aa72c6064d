/**
 * 数字人生成API
 */

import { request } from '@/utils/request';

/**
 * 创建数字人
 * @param {Object} formData - 表单数据
 * @returns {Promise} 创建结果
 */
export async function createDigitalHuman(formData) {
  try {
    console.log('创建数字人请求数据:', formData);

    // 使用 fetch 直接调用，通过 Vite 代理
    const response = await fetch('/api/v1/digital-human-generation/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 添加认证头
        ...(localStorage.getItem('token') && {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        })
      },
      body: JSON.stringify(formData)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('创建数字人响应:', result);
    return result;
  } catch (error) {
    console.error('创建数字人失败:', error);
    throw error;
  }
}

/**
 * 获取生成状态
 * @param {string} taskId - 任务ID
 * @returns {Promise} 状态信息
 */
export async function getGenerationStatus(taskId) {
  try {
    console.log('查询数字人生成状态:', taskId);

    // 使用 fetch 直接调用，通过 Vite 代理
    const response = await fetch(`/api/v1/digital-human-generation/status/${taskId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // 添加认证头
        ...(localStorage.getItem('token') && {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        })
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('状态查询响应:', result);
    return result;
  } catch (error) {
    console.error('查询数字人生成状态失败:', error);
    throw error;
  }
}

/**
 * 获取数字人列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 数字人列表
 */
export function getDigitalHumanList(params = {}) {
  return request({
    url: '/api/v1/digital-human-generation/list',
    method: 'get',
    params
  });
}

/**
 * 获取数字人详情
 * @param {number} digitalHumanId - 数字人ID
 * @returns {Promise} 数字人详情
 */
export function getDigitalHumanDetail(digitalHumanId) {
  return request({
    url: `/api/v1/digital-human-generation/${digitalHumanId}`,
    method: 'get'
  });
}

/**
 * 删除数字人
 * @param {number} digitalHumanId - 数字人ID
 * @returns {Promise} 删除结果
 */
export function deleteDigitalHuman(digitalHumanId) {
  return request({
    url: `/api/v1/digital-human-generation/${digitalHumanId}`,
    method: 'delete'
  });
}

/**
 * 数字人生成状态枚举
 */
export const GENERATION_STATUS = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed'
};

/**
 * 状态显示文本映射
 */
export const STATUS_TEXT = {
  [GENERATION_STATUS.PENDING]: '等待开始',
  [GENERATION_STATUS.RUNNING]: '生成中',
  [GENERATION_STATUS.COMPLETED]: '生成完成',
  [GENERATION_STATUS.FAILED]: '生成失败'
};

/**
 * 状态颜色映射
 */
export const STATUS_COLOR = {
  [GENERATION_STATUS.PENDING]: '#909399',
  [GENERATION_STATUS.RUNNING]: '#409EFF',
  [GENERATION_STATUS.COMPLETED]: '#67C23A',
  [GENERATION_STATUS.FAILED]: '#F56C6C'
};

/**
 * 数字人生成管理器
 */
export class DigitalHumanGenerationManager {
  constructor() {
    this.currentTask = null;
    this.statusCheckInterval = null;
    this.callbacks = {
      onProgress: null,
      onComplete: null,
      onError: null
    };
  }

  /**
   * 开始生成数字人
   */
  async startGeneration(formData, callbacks = {}) {
    try {
      this.callbacks = { ...this.callbacks, ...callbacks };
      
      // 调用创建API
      const result = await createDigitalHuman(formData);
      
      if (result.success) {
        this.currentTask = {
          digitalHumanId: result.digital_human_id,
          taskId: result.task_id,
          status: result.status,
          progress: result.progress
        };
        
        // 开始状态检查
        this.startStatusCheck();
        
        return result;
      } else {
        throw new Error(result.message || '创建失败');
      }
    } catch (error) {
      if (this.callbacks.onError) {
        this.callbacks.onError(error);
      }
      throw error;
    }
  }

  /**
   * 开始状态检查
   */
  startStatusCheck() {
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval);
    }

    this.statusCheckInterval = setInterval(async () => {
      try {
        await this.checkStatus();
      } catch (error) {
        console.error('状态检查失败:', error);
        this.stopStatusCheck();
        if (this.callbacks.onError) {
          this.callbacks.onError(error);
        }
      }
    }, 2000); // 每2秒检查一次
  }

  /**
   * 检查生成状态
   */
  async checkStatus() {
    if (!this.currentTask) return;

    const status = await getGenerationStatus(this.currentTask.taskId);
    
    if (status.success) {
      this.currentTask.status = status.status;
      this.currentTask.progress = status.progress;
      
      // 触发进度回调
      if (this.callbacks.onProgress) {
        this.callbacks.onProgress({
          status: status.status,
          progress: status.progress,
          message: status.message
        });
      }
      
      // 检查是否完成
      if (status.status === GENERATION_STATUS.COMPLETED) {
        this.stopStatusCheck();
        if (this.callbacks.onComplete) {
          this.callbacks.onComplete({
            digitalHumanId: status.digital_human_id,
            resultUrls: status.result_urls,
            status: status
          });
        }
      } else if (status.status === GENERATION_STATUS.FAILED) {
        this.stopStatusCheck();
        if (this.callbacks.onError) {
          this.callbacks.onError(new Error(status.error_message || '生成失败'));
        }
      }
    }
  }

  /**
   * 停止状态检查
   */
  stopStatusCheck() {
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval);
      this.statusCheckInterval = null;
    }
  }

  /**
   * 获取当前任务信息
   */
  getCurrentTask() {
    return this.currentTask;
  }

  /**
   * 重置管理器
   */
  reset() {
    this.stopStatusCheck();
    this.currentTask = null;
    this.callbacks = {
      onProgress: null,
      onComplete: null,
      onError: null
    };
  }
}

// 创建全局实例
export const digitalHumanGenerationManager = new DigitalHumanGenerationManager();

/**
 * 格式化进度文本
 */
export function formatProgressText(status, progress) {
  const statusText = STATUS_TEXT[status] || status;
  
  if (status === GENERATION_STATUS.RUNNING && progress > 0) {
    return `${statusText} (${progress}%)`;
  }
  
  return statusText;
}

/**
 * 获取状态图标
 */
export function getStatusIcon(status) {
  const icons = {
    [GENERATION_STATUS.PENDING]: 'Clock',
    [GENERATION_STATUS.RUNNING]: 'Loading',
    [GENERATION_STATUS.COMPLETED]: 'Check',
    [GENERATION_STATUS.FAILED]: 'Close'
  };
  
  return icons[status] || 'QuestionFilled';
}

/**
 * 验证表单数据
 */
export function validateFormData(formData) {
  const errors = [];
  
  // 基本信息验证
  if (!formData.name || formData.name.trim() === '') {
    errors.push('请输入数字人名称');
  }
  
  if (!formData.type) {
    errors.push('请选择数字人类型');
  }
  
  if (!formData.gender) {
    errors.push('请选择性别');
  }
  
  // 外观验证
  if (formData.appearance_type === 'template' && !formData.selected_template) {
    if (!formData.type || !formData.gender) {
      errors.push('请选择外观模板或确保已填写基本信息以使用推荐外观');
    }
  }
  
  if (formData.appearance_type === 'upload' && !formData.uploaded_image_url) {
    errors.push('请上传外观图片');
  }
  
  // 声音验证
  if (formData.voice_type === 'standard' && !formData.selected_voice) {
    errors.push('请选择声音');
  }
  
  if (formData.voice_type === 'custom' && !formData.custom_voice_url) {
    errors.push('请上传自定义声音');
  }
  
  return errors;
}
