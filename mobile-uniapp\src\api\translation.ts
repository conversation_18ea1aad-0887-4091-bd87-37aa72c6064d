import { http } from '@/utils/request'

// 翻译相关接口
export interface TranslationRequest {
  text: string
  source_lang: string
  target_lang: string
  domain?: string
  style?: string
}

export interface TranslationResponse {
  translated_text: string
  source_lang: string
  target_lang: string
  confidence: number
  word_count: number
  char_count: number
}

export interface LanguageInfo {
  code: string
  name: string
  native_name: string
  flag?: string
}

export interface TranslationHistory {
  id: string
  source_text: string
  translated_text: string
  source_lang: string
  target_lang: string
  created_at: string
  word_count: number
}

export const translationApi = {
  // 获取支持的语言列表
  getLanguages: () => {
    return http.get<LanguageInfo[]>('/translation/languages')
  },

  // 文本翻译
  translateText: (data: TranslationRequest) => {
    return http.post<TranslationResponse>('/translation/text', data)
  },

  // 语言检测
  detectLanguage: (text: string) => {
    return http.post<{ language: string; confidence: number }>('/translation/detect', { text })
  },

  // 获取翻译历史
  getHistory: (params?: { page?: number; limit?: number }) => {
    return http.get<{
      items: TranslationHistory[]
      total: number
      page: number
      limit: number
    }>('/translation/history', params)
  },

  // 删除翻译历史
  deleteHistory: (id: string) => {
    return http.delete(`/translation/history/${id}`)
  },

  // 清空翻译历史
  clearHistory: () => {
    return http.delete('/translation/history/clear')
  },

  // 收藏翻译
  favoriteTranslation: (id: string) => {
    return http.post(`/translation/favorite/${id}`)
  },

  // 取消收藏
  unfavoriteTranslation: (id: string) => {
    return http.delete(`/translation/favorite/${id}`)
  },

  // 获取收藏的翻译
  getFavorites: (params?: { page?: number; limit?: number }) => {
    return http.get<{
      items: TranslationHistory[]
      total: number
      page: number
      limit: number
    }>('/translation/favorites', params)
  },

  // 文档翻译
  translateDocument: (filePath: string, sourceLang: string, targetLang: string) => {
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: '/api/translation/document',
        filePath,
        name: 'file',
        formData: {
          source_lang: sourceLang,
          target_lang: targetLang
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (data.success) {
              resolve(data)
            } else {
              reject(data)
            }
          } catch (error) {
            reject(error)
          }
        },
        fail: reject
      })
    })
  },

  // 音频翻译
  translateAudio: (filePath: string, sourceLang: string, targetLang: string) => {
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: '/api/translation/audio',
        filePath,
        name: 'audio',
        formData: {
          source_lang: sourceLang,
          target_lang: targetLang
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (data.success) {
              resolve(data)
            } else {
              reject(data)
            }
          } catch (error) {
            reject(error)
          }
        },
        fail: reject
      })
    })
  },

  // 视频翻译
  translateVideo: (filePath: string, sourceLang: string, targetLang: string) => {
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: '/api/translation/video',
        filePath,
        name: 'video',
        formData: {
          source_lang: sourceLang,
          target_lang: targetLang
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (data.success) {
              resolve(data)
            } else {
              reject(data)
            }
          } catch (error) {
            reject(error)
          }
        },
        fail: reject
      })
    })
  },

  // 获取翻译任务状态
  getTaskStatus: (taskId: string) => {
    return http.get(`/translation/task/${taskId}`)
  },

  // 下载翻译结果
  downloadResult: (taskId: string) => {
    return http.get(`/translation/download/${taskId}`, {}, { responseType: 'blob' })
  }
}
