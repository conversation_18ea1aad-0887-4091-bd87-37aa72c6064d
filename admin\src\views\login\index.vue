<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <img src="/logo.png" alt="Logo" class="logo-image" />
        </div>
        <h1 class="title">AI专业平台管理系统</h1>
        <p class="subtitle">智能化管理，专业化服务</p>
      </div>
      
      <a-form
        :model="loginForm"
        :rules="rules"
        ref="loginFormRef"
        @submit.prevent="handleSubmit"
      >
        <a-form-item name="username">
          <a-input
            v-model:value="loginForm.username"
            placeholder="用户名"
            size="large"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-form-item>
        
        <a-form-item name="password">
          <a-input-password
            v-model:value="loginForm.password"
            placeholder="密码"
            size="large"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>
        
        <div class="remember-forgot">
          <a-checkbox v-model:checked="rememberMe">记住我</a-checkbox>
          <a href="javascript:;" class="forgot-link">忘记密码？</a>
        </div>
        
        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            :loading="loading"
            size="large"
            block
          >
            登录
          </a-button>
        </a-form-item>
      </a-form>
      
      <div class="login-footer">
        <p>测试账号: admin / 密码: admin123</p>
        <p>Copyright © {{ new Date().getFullYear() }} 后台管理系统</p>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { UserOutlined, LockOutlined, HomeOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

export default defineComponent({
  name: 'Login',
  components: {
    UserOutlined,
    LockOutlined
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    
    const loginFormRef = ref(null);
    const loading = ref(false);
    const rememberMe = ref(false);
    
    // 登录表单数据
    const loginForm = reactive({
      username: '',
      password: ''
    });
    
    // 表单验证规则
    const rules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度应在3-20个字符之间', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度应在6-20个字符之间', trigger: 'blur' }
      ]
    };
    
    // 处理表单提交
    const handleSubmit = async () => {
      try {
        await loginFormRef.value.validate();
        loading.value = true;

        // 尝试真实的登录API
        try {
          const response = await fetch('/api/v1/admin/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              username: loginForm.username,
              password: loginForm.password
            })
          });

          if (response.ok) {
            const result = await response.json();
            if (result.success) {
              // 保存token到本地存储
              localStorage.setItem('token', result.data.token);

              // 保存用户信息
              localStorage.setItem('userInfo', JSON.stringify(result.data.user));

              if (rememberMe.value) {
                localStorage.setItem('rememberedUser', loginForm.username);
              } else {
                localStorage.removeItem('rememberedUser');
              }

              message.success('登录成功');

              // 获取重定向地址
              const redirect = route.query.redirect || '/';
              router.push(redirect);
              return;
            }
          }
        } catch (apiError) {
          console.warn('API登录失败，使用模拟登录:', apiError);
        }

        // 如果API登录失败，使用模拟登录
        if (loginForm.username === 'admin' && loginForm.password === 'admin123') {
          // 保存token到本地存储
          localStorage.setItem('token', 'demo-token-abc123');

          // 保存用户信息
          localStorage.setItem('userInfo', JSON.stringify({
            id: 1,
            username: loginForm.username,
            name: '管理员',
            avatar: '',
            roles: ['admin']
          }));

          if (rememberMe.value) {
            localStorage.setItem('rememberedUser', loginForm.username);
          } else {
            localStorage.removeItem('rememberedUser');
          }

          message.success('登录成功');

          // 获取重定向地址
          const redirect = route.query.redirect || '/';
          router.push(redirect);
        } else {
          message.error('用户名或密码错误');
        }
      } catch (error) {
        console.error('登录过程中出错:', error);
        message.error('登录失败，请稍后重试');
      } finally {
        loading.value = false;
      }
    };
    };
    
    // 组件挂载时，如果有记住的用户，则自动填充用户名
    onMounted(() => {
      const rememberedUser = localStorage.getItem('rememberedUser');
      if (rememberedUser) {
        loginForm.username = rememberedUser;
        rememberMe.value = true;
      }
    });
    
    return {
      loginForm,
      loginFormRef,
      rules,
      loading,
      rememberMe,
      handleSubmit
    };
  }
});
</script>

<style lang="less" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  position: relative;
  overflow: hidden;

  // 添加动态背景效果
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: float 20s ease-in-out infinite;
  }

  .login-card {
    width: 420px;
    padding: 48px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 1;

    .login-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 40px;

      .logo {
        width: 80px;
        height: 80px;
        margin-bottom: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 20px;
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);

        .logo-image {
          width: 50px;
          height: 50px;
          object-fit: contain;
        }
      }

      .title {
        font-size: 28px;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 0 8px 0;
        text-align: center;
      }

      .subtitle {
        font-size: 14px;
        color: #666;
        margin: 0;
        text-align: center;
      }
    }

    // 美化表单项
    :deep(.ant-form-item) {
      margin-bottom: 24px;

      .ant-input, .ant-input-password {
        height: 48px;
        border-radius: 12px;
        border: 2px solid #f0f0f0;
        transition: all 0.3s ease;

        &:hover, &:focus {
          border-color: #667eea;
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
      }

      .ant-input-prefix {
        color: #667eea;
      }
    }

    :deep(.ant-btn-primary) {
      height: 48px;
      border-radius: 12px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      border: none;
      font-weight: 600;
      font-size: 16px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
      }
    }

    .remember-forgot {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;

      .forgot-link {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;

        &:hover {
          color: #764ba2;
          text-decoration: underline;
        }
      }
    }

    .login-footer {
      margin-top: 24px;
      text-align: center;
      color: #999;
      font-size: 13px;

      p {
        margin: 8px 0;

        &:first-child {
          color: #667eea;
          font-weight: 500;
        }
      }
    }
  }
}

// 动画效果
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-container {
    padding: 20px;

    .login-card {
      width: 100%;
      max-width: 380px;
      padding: 32px 24px;
    }
  }
}
</style> 