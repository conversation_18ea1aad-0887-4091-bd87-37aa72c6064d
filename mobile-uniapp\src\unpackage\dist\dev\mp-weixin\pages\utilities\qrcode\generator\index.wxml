<view class="qrcode-generator-page data-v-af3ddd8d"><view class="page-header data-v-af3ddd8d"><text class="page-title data-v-af3ddd8d">二维码生成器</text><text class="page-desc data-v-af3ddd8d">快速生成各种类型的二维码</text></view><view class="type-selector data-v-af3ddd8d"><view class="section-title data-v-af3ddd8d"><text class="title-text data-v-af3ddd8d">选择二维码类型</text></view><view class="type-tabs data-v-af3ddd8d"><view wx:for="{{a}}" wx:for-item="type" wx:key="d" class="{{['tab-item', 'data-v-af3ddd8d', type.e && 'active']}}" bindtap="{{type.f}}"><u-icon wx:if="{{type.b}}" class="data-v-af3ddd8d" u-i="{{type.a}}" bind:__l="__l" u-p="{{type.b}}"></u-icon><text class="tab-text data-v-af3ddd8d">{{type.c}}</text></view></view></view><view class="content-input data-v-af3ddd8d"><view class="section-title data-v-af3ddd8d"><text class="title-text data-v-af3ddd8d">{{b}}</text><text class="title-desc data-v-af3ddd8d">{{c}}</text></view><view class="input-area data-v-af3ddd8d"><textarea wx:if="{{d}}" class="text-input data-v-af3ddd8d" placeholder="请输入要生成二维码的文本内容" maxlength="{{500}}" value="{{e}}" bindinput="{{f}}"></textarea><input wx:if="{{g}}" class="url-input data-v-af3ddd8d" placeholder="https://example.com" type="url" value="{{h}}" bindinput="{{i}}"/><view wx:if="{{j}}" class="wifi-inputs data-v-af3ddd8d"><input class="wifi-input data-v-af3ddd8d" placeholder="WiFi名称(SSID)" value="{{k}}" bindinput="{{l}}"/><input class="wifi-input data-v-af3ddd8d" placeholder="WiFi密码" type="password" value="{{m}}" bindinput="{{n}}"/><view class="wifi-security data-v-af3ddd8d"><text class="security-label data-v-af3ddd8d">加密方式:</text><u-radio-group wx:if="{{s}}" class="data-v-af3ddd8d" u-s="{{['d']}}" u-i="af3ddd8d-1" bind:__l="__l" bindupdateModelValue="{{r}}" u-p="{{s}}"><u-radio wx:if="{{o}}" class="data-v-af3ddd8d" u-i="af3ddd8d-2,af3ddd8d-1" bind:__l="__l" u-p="{{o}}"></u-radio><u-radio wx:if="{{p}}" class="data-v-af3ddd8d" u-i="af3ddd8d-3,af3ddd8d-1" bind:__l="__l" u-p="{{p}}"></u-radio><u-radio wx:if="{{q}}" class="data-v-af3ddd8d" u-i="af3ddd8d-4,af3ddd8d-1" bind:__l="__l" u-p="{{q}}"></u-radio></u-radio-group></view></view><view wx:if="{{t}}" class="contact-inputs data-v-af3ddd8d"><input class="contact-input data-v-af3ddd8d" placeholder="姓名" value="{{v}}" bindinput="{{w}}"/><input class="contact-input data-v-af3ddd8d" placeholder="电话号码" type="tel" value="{{x}}" bindinput="{{y}}"/><input class="contact-input data-v-af3ddd8d" placeholder="邮箱地址" type="email" value="{{z}}" bindinput="{{A}}"/><input class="contact-input data-v-af3ddd8d" placeholder="公司/组织" value="{{B}}" bindinput="{{C}}"/></view><view wx:if="{{D}}" class="sms-inputs data-v-af3ddd8d"><input class="sms-input data-v-af3ddd8d" placeholder="手机号码" type="tel" value="{{E}}" bindinput="{{F}}"/><block wx:if="{{r0}}"><textarea class="sms-input data-v-af3ddd8d" placeholder="短信内容" maxlength="{{160}}" value="{{G}}" bindinput="{{H}}"></textarea></block></view><view wx:if="{{I}}" class="email-inputs data-v-af3ddd8d"><input class="email-input data-v-af3ddd8d" placeholder="收件人邮箱" type="email" value="{{J}}" bindinput="{{K}}"/><input class="email-input data-v-af3ddd8d" placeholder="邮件主题" value="{{L}}" bindinput="{{M}}"/><block wx:if="{{r0}}"><textarea class="email-input data-v-af3ddd8d" placeholder="邮件内容" maxlength="{{300}}" value="{{N}}" bindinput="{{O}}"></textarea></block></view></view></view><view wx:if="{{P}}" class="style-settings data-v-af3ddd8d"><view class="section-title data-v-af3ddd8d"><text class="title-text data-v-af3ddd8d">样式设置</text></view><view class="settings-content data-v-af3ddd8d"><view class="setting-item data-v-af3ddd8d"><text class="setting-label data-v-af3ddd8d">二维码尺寸: {{Q}}px</text><u-slider wx:if="{{T}}" class="data-v-af3ddd8d" bindchange="{{R}}" u-i="af3ddd8d-5" bind:__l="__l" bindupdateModelValue="{{S}}" u-p="{{T}}"></u-slider></view><view class="setting-item data-v-af3ddd8d"><text class="setting-label data-v-af3ddd8d">前景色</text><view class="color-picker data-v-af3ddd8d"><view wx:for="{{U}}" wx:for-item="color" wx:key="a" style="{{'background-color:' + color.b}}" class="{{['color-option', 'data-v-af3ddd8d', color.c && 'active']}}" bindtap="{{color.d}}"></view></view></view><view class="setting-item data-v-af3ddd8d"><text class="setting-label data-v-af3ddd8d">背景色</text><view class="color-picker data-v-af3ddd8d"><view wx:for="{{V}}" wx:for-item="color" wx:key="a" style="{{'background-color:' + color.b}}" class="{{['color-option', 'data-v-af3ddd8d', color.c && 'active']}}" bindtap="{{color.d}}"></view></view></view><view class="setting-item data-v-af3ddd8d"><text class="setting-label data-v-af3ddd8d">容错级别</text><u-radio-group wx:if="{{ac}}" class="data-v-af3ddd8d" u-s="{{['d']}}" bindchange="{{aa}}" u-i="af3ddd8d-6" bind:__l="__l" bindupdateModelValue="{{ab}}" u-p="{{ac}}"><u-radio wx:if="{{W}}" class="data-v-af3ddd8d" u-i="af3ddd8d-7,af3ddd8d-6" bind:__l="__l" u-p="{{W}}"></u-radio><u-radio wx:if="{{X}}" class="data-v-af3ddd8d" u-i="af3ddd8d-8,af3ddd8d-6" bind:__l="__l" u-p="{{X}}"></u-radio><u-radio wx:if="{{Y}}" class="data-v-af3ddd8d" u-i="af3ddd8d-9,af3ddd8d-6" bind:__l="__l" u-p="{{Y}}"></u-radio><u-radio wx:if="{{Z}}" class="data-v-af3ddd8d" u-i="af3ddd8d-10,af3ddd8d-6" bind:__l="__l" u-p="{{Z}}"></u-radio></u-radio-group></view><view class="setting-item data-v-af3ddd8d"><text class="setting-label data-v-af3ddd8d">添加Logo</text><u-switch wx:if="{{af}}" class="data-v-af3ddd8d" bindchange="{{ad}}" u-i="af3ddd8d-11" bind:__l="__l" bindupdateModelValue="{{ae}}" u-p="{{af}}"></u-switch><view wx:if="{{ag}}" class="logo-upload data-v-af3ddd8d"><view class="upload-btn data-v-af3ddd8d" bindtap="{{ai}}"><u-icon wx:if="{{ah}}" class="data-v-af3ddd8d" u-i="af3ddd8d-12" bind:__l="__l" u-p="{{ah}}"></u-icon><text class="upload-text data-v-af3ddd8d">选择Logo</text></view><image wx:if="{{aj}}" class="logo-preview data-v-af3ddd8d" src="{{ak}}" mode="aspectFit"></image></view></view></view></view><view wx:if="{{al}}" class="qrcode-preview data-v-af3ddd8d"><view class="section-title data-v-af3ddd8d"><text class="title-text data-v-af3ddd8d">二维码预览</text><view class="preview-actions data-v-af3ddd8d"><u-icon wx:if="{{an}}" class="data-v-af3ddd8d" bindclick="{{am}}" u-i="af3ddd8d-13" bind:__l="__l" u-p="{{an}}"></u-icon><u-icon wx:if="{{ap}}" class="data-v-af3ddd8d" bindclick="{{ao}}" u-i="af3ddd8d-14" bind:__l="__l" u-p="{{ap}}"></u-icon></view></view><view class="preview-content data-v-af3ddd8d"><view class="qrcode-container data-v-af3ddd8d"><canvas class="qrcode-canvas data-v-af3ddd8d" canvas-id="qrcode" style="{{'width:' + aq + ';' + ('height:' + ar)}}"></canvas></view><view class="qrcode-info data-v-af3ddd8d"><text class="info-item data-v-af3ddd8d">类型: {{as}}</text><text class="info-item data-v-af3ddd8d">尺寸: {{at}}×{{av}}px</text><text class="info-item data-v-af3ddd8d">容错级别: {{aw}}</text></view></view></view><view wx:if="{{ax}}" class="generation-history data-v-af3ddd8d"><view class="section-title data-v-af3ddd8d"><text class="title-text data-v-af3ddd8d">生成历史</text><u-button wx:if="{{az}}" class="data-v-af3ddd8d" bindclick="{{ay}}" u-i="af3ddd8d-15" bind:__l="__l" u-p="{{az}}"></u-button></view><view class="history-list data-v-af3ddd8d"><view wx:for="{{aA}}" wx:for-item="item" wx:key="f" class="history-item data-v-af3ddd8d" bindtap="{{item.g}}"><image class="history-qrcode data-v-af3ddd8d" src="{{item.a}}" mode="aspectFit"></image><view class="history-content data-v-af3ddd8d"><text class="history-type data-v-af3ddd8d">{{item.b}}</text><text class="history-desc data-v-af3ddd8d">{{item.c}}</text><text class="history-time data-v-af3ddd8d">{{item.d}}</text></view><view class="history-actions data-v-af3ddd8d"><u-icon wx:if="{{aB}}" class="data-v-af3ddd8d" u-i="{{item.e}}" bind:__l="__l" u-p="{{aB}}"></u-icon></view></view></view></view><view wx:if="{{aC}}" class="generate-button data-v-af3ddd8d"><u-button wx:if="{{aF}}" class="data-v-af3ddd8d" u-s="{{['d']}}" bindclick="{{aE}}" u-i="af3ddd8d-17" bind:__l="__l" u-p="{{aF}}">{{aD}}</u-button></view></view>