{"version": 3, "file": "index.js", "sources": ["pages/utilities/text/processor/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXRpbGl0aWVzL3RleHQvcHJvY2Vzc29yL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"text-processor-page\">\n    <!-- 页面标题 -->\n    <view class=\"page-header\">\n      <text class=\"page-title\">文本处理工具</text>\n      <text class=\"page-desc\">文本格式化、统计分析、转换等功能</text>\n    </view>\n\n    <!-- 工具选择 -->\n    <view class=\"tools-selector\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">选择处理工具</text>\n      </view>\n      \n      <view class=\"tools-tabs\">\n        <view \n          class=\"tab-item\"\n          v-for=\"tool in textTools\"\n          :key=\"tool.id\"\n          :class=\"{ active: selectedTool === tool.id }\"\n          @click=\"selectTool(tool.id)\"\n        >\n          <u-icon :name=\"tool.icon\" size=\"18\" :color=\"selectedTool === tool.id ? '#2563EB' : '#6B7280'\"></u-icon>\n          <text class=\"tab-text\">{{ tool.name }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 文本输入区域 -->\n    <view class=\"text-input-section\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">输入文本</text>\n        <view class=\"input-actions\">\n          <u-icon name=\"file-text\" size=\"16\" color=\"#2563EB\" @click=\"importFromFile\"></u-icon>\n          <u-icon name=\"clipboard\" size=\"16\" color=\"#2563EB\" margin-left=\"8\" @click=\"pasteFromClipboard\"></u-icon>\n          <u-icon name=\"close-circle\" size=\"16\" color=\"#EF4444\" margin-left=\"8\" @click=\"clearInput\"></u-icon>\n        </view>\n      </view>\n      \n      <view class=\"input-area\">\n        <textarea \n          class=\"text-input\"\n          v-model=\"inputText\"\n          placeholder=\"请输入或粘贴要处理的文本内容...\"\n          :maxlength=\"10000\"\n          @input=\"onTextInput\"\n        ></textarea>\n        \n        <view class=\"input-stats\">\n          <text class=\"stat-item\">字符: {{ textStats.characters }}</text>\n          <text class=\"stat-item\">单词: {{ textStats.words }}</text>\n          <text class=\"stat-item\">行数: {{ textStats.lines }}</text>\n          <text class=\"stat-item\">段落: {{ textStats.paragraphs }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 工具选项 -->\n    <view class=\"tool-options\" v-if=\"inputText && selectedTool\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">{{ getToolName(selectedTool) }}选项</text>\n      </view>\n      \n      <view class=\"options-content\">\n        <!-- 格式化选项 -->\n        <view class=\"option-group\" v-if=\"selectedTool === 'format'\">\n          <view class=\"option-item\">\n            <text class=\"option-label\">格式化类型</text>\n            <u-radio-group v-model=\"formatOptions.type\">\n              <u-radio name=\"capitalize\" label=\"首字母大写\"></u-radio>\n              <u-radio name=\"uppercase\" label=\"全部大写\"></u-radio>\n              <u-radio name=\"lowercase\" label=\"全部小写\"></u-radio>\n              <u-radio name=\"title\" label=\"标题格式\"></u-radio>\n            </u-radio-group>\n          </view>\n          \n          <view class=\"option-item\">\n            <text class=\"option-label\">其他选项</text>\n            <view class=\"checkbox-group\">\n              <u-checkbox v-model=\"formatOptions.removeExtraSpaces\" label=\"移除多余空格\"></u-checkbox>\n              <u-checkbox v-model=\"formatOptions.removeEmptyLines\" label=\"移除空行\"></u-checkbox>\n              <u-checkbox v-model=\"formatOptions.trimLines\" label=\"去除行首尾空格\"></u-checkbox>\n            </view>\n          </view>\n        </view>\n\n        <!-- 统计选项 -->\n        <view class=\"option-group\" v-if=\"selectedTool === 'stats'\">\n          <view class=\"stats-display\">\n            <view class=\"stat-card\">\n              <text class=\"stat-number\">{{ detailedStats.characters }}</text>\n              <text class=\"stat-label\">总字符数</text>\n            </view>\n            <view class=\"stat-card\">\n              <text class=\"stat-number\">{{ detailedStats.charactersNoSpaces }}</text>\n              <text class=\"stat-label\">不含空格</text>\n            </view>\n            <view class=\"stat-card\">\n              <text class=\"stat-number\">{{ detailedStats.words }}</text>\n              <text class=\"stat-label\">单词数</text>\n            </view>\n            <view class=\"stat-card\">\n              <text class=\"stat-number\">{{ detailedStats.sentences }}</text>\n              <text class=\"stat-label\">句子数</text>\n            </view>\n            <view class=\"stat-card\">\n              <text class=\"stat-number\">{{ detailedStats.paragraphs }}</text>\n              <text class=\"stat-label\">段落数</text>\n            </view>\n            <view class=\"stat-card\">\n              <text class=\"stat-number\">{{ detailedStats.readingTime }}</text>\n              <text class=\"stat-label\">阅读时间(分钟)</text>\n            </view>\n          </view>\n        </view>\n\n        <!-- 转换选项 -->\n        <view class=\"option-group\" v-if=\"selectedTool === 'convert'\">\n          <view class=\"option-item\">\n            <text class=\"option-label\">转换类型</text>\n            <u-radio-group v-model=\"convertOptions.type\">\n              <u-radio name=\"markdown\" label=\"转换为Markdown\"></u-radio>\n              <u-radio name=\"html\" label=\"转换为HTML\"></u-radio>\n              <u-radio name=\"json\" label=\"转换为JSON\"></u-radio>\n              <u-radio name=\"csv\" label=\"转换为CSV\"></u-radio>\n            </u-radio-group>\n          </view>\n        </view>\n\n        <!-- 查找替换选项 -->\n        <view class=\"option-group\" v-if=\"selectedTool === 'replace'\">\n          <view class=\"option-item\">\n            <text class=\"option-label\">查找内容</text>\n            <input \n              class=\"option-input\"\n              v-model=\"replaceOptions.findText\"\n              placeholder=\"输入要查找的文本\"\n            />\n          </view>\n          \n          <view class=\"option-item\">\n            <text class=\"option-label\">替换为</text>\n            <input \n              class=\"option-input\"\n              v-model=\"replaceOptions.replaceText\"\n              placeholder=\"输入替换后的文本\"\n            />\n          </view>\n          \n          <view class=\"option-item\">\n            <text class=\"option-label\">选项</text>\n            <view class=\"checkbox-group\">\n              <u-checkbox v-model=\"replaceOptions.caseSensitive\" label=\"区分大小写\"></u-checkbox>\n              <u-checkbox v-model=\"replaceOptions.wholeWord\" label=\"全词匹配\"></u-checkbox>\n              <u-checkbox v-model=\"replaceOptions.useRegex\" label=\"使用正则表达式\"></u-checkbox>\n            </view>\n          </view>\n          \n          <view class=\"replace-preview\" v-if=\"replaceOptions.findText\">\n            <text class=\"preview-label\">找到 {{ findMatches }} 处匹配</text>\n          </view>\n        </view>\n\n        <!-- 编码选项 -->\n        <view class=\"option-group\" v-if=\"selectedTool === 'encode'\">\n          <view class=\"option-item\">\n            <text class=\"option-label\">编码类型</text>\n            <u-radio-group v-model=\"encodeOptions.type\">\n              <u-radio name=\"base64\" label=\"Base64编码\"></u-radio>\n              <u-radio name=\"url\" label=\"URL编码\"></u-radio>\n              <u-radio name=\"html\" label=\"HTML实体编码\"></u-radio>\n              <u-radio name=\"unicode\" label=\"Unicode编码\"></u-radio>\n            </u-radio-group>\n          </view>\n          \n          <view class=\"option-item\">\n            <text class=\"option-label\">操作</text>\n            <u-radio-group v-model=\"encodeOptions.operation\">\n              <u-radio name=\"encode\" label=\"编码\"></u-radio>\n              <u-radio name=\"decode\" label=\"解码\"></u-radio>\n            </u-radio-group>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"process-button\" v-if=\"selectedTool !== 'stats'\">\n        <u-button \n          type=\"primary\" \n          size=\"large\"\n          :loading=\"isProcessing\"\n          @click=\"processText\"\n          :custom-style=\"{ width: '100%' }\"\n        >\n          {{ isProcessing ? '处理中...' : '开始处理' }}\n        </u-button>\n      </view>\n    </view>\n\n    <!-- 处理结果 -->\n    <view class=\"processing-result\" v-if=\"processedText\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">处理结果</text>\n        <view class=\"result-actions\">\n          <u-icon name=\"copy\" size=\"18\" color=\"#2563EB\" @click=\"copyResult\"></u-icon>\n          <u-icon name=\"download\" size=\"18\" color=\"#2563EB\" margin-left=\"12\" @click=\"downloadResult\"></u-icon>\n          <u-icon name=\"share\" size=\"18\" color=\"#2563EB\" margin-left=\"12\" @click=\"shareResult\"></u-icon>\n        </view>\n      </view>\n      \n      <view class=\"result-content\">\n        <textarea \n          class=\"result-text\"\n          :value=\"processedText\"\n          readonly\n          :style=\"{ height: getResultHeight() }\"\n        ></textarea>\n        \n        <view class=\"result-stats\">\n          <text class=\"stat-item\">原文: {{ textStats.characters }} 字符</text>\n          <text class=\"stat-item\">结果: {{ getProcessedStats().characters }} 字符</text>\n          <text class=\"stat-item\">变化: {{ getChangePercent() }}%</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 处理历史 -->\n    <view class=\"processing-history\" v-if=\"textHistory.length > 0\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">处理历史</text>\n        <u-button \n          type=\"text\" \n          text=\"清空\" \n          size=\"small\"\n          @click=\"clearHistory\"\n          :custom-style=\"{ color: '#EF4444', fontSize: '24rpx' }\"\n        ></u-button>\n      </view>\n      \n      <view class=\"history-list\">\n        <view \n          class=\"history-item\"\n          v-for=\"item in textHistory.slice(0, 5)\"\n          :key=\"item.id\"\n          @click=\"loadHistoryItem(item)\"\n        >\n          <view class=\"history-icon\">\n            <u-icon :name=\"getToolIcon(item.tool)\" size=\"20\" color=\"#2563EB\"></u-icon>\n          </view>\n          <view class=\"history-content\">\n            <text class=\"history-title\">{{ getToolName(item.tool) }}</text>\n            <text class=\"history-desc\">{{ item.preview }}</text>\n            <text class=\"history-time\">{{ formatTime(item.createdAt) }}</text>\n          </view>\n          <view class=\"history-actions\">\n            <u-icon name=\"refresh\" size=\"16\" color=\"#6B7280\"></u-icon>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted, watch } from 'vue'\nimport dayjs from 'dayjs'\n\n// 响应式数据\nconst selectedTool = ref<string>('format')\nconst inputText = ref<string>('')\nconst processedText = ref<string>('')\nconst isProcessing = ref<boolean>(false)\nconst textHistory = ref<any[]>([])\n\n// 处理选项\nconst formatOptions = ref({\n  type: 'capitalize',\n  removeExtraSpaces: true,\n  removeEmptyLines: false,\n  trimLines: true\n})\n\nconst convertOptions = ref({\n  type: 'markdown'\n})\n\nconst replaceOptions = ref({\n  findText: '',\n  replaceText: '',\n  caseSensitive: false,\n  wholeWord: false,\n  useRegex: false\n})\n\nconst encodeOptions = ref({\n  type: 'base64',\n  operation: 'encode'\n})\n\n// 文本工具\nconst textTools = ref([\n  {\n    id: 'format',\n    name: '格式化',\n    icon: 'text-format'\n  },\n  {\n    id: 'stats',\n    name: '统计分析',\n    icon: 'chart-bar'\n  },\n  {\n    id: 'convert',\n    name: '格式转换',\n    icon: 'swap'\n  },\n  {\n    id: 'replace',\n    name: '查找替换',\n    icon: 'find-replace'\n  },\n  {\n    id: 'encode',\n    name: '编码转换',\n    icon: 'code'\n  }\n])\n\n// 计算属性\nconst textStats = computed(() => {\n  if (!inputText.value) {\n    return { characters: 0, words: 0, lines: 0, paragraphs: 0 }\n  }\n\n  const text = inputText.value\n  const characters = text.length\n  const words = text.trim() ? text.trim().split(/\\s+/).length : 0\n  const lines = text.split('\\n').length\n  const paragraphs = text.split(/\\n\\s*\\n/).filter(p => p.trim()).length\n\n  return { characters, words, lines, paragraphs }\n})\n\nconst detailedStats = computed(() => {\n  if (!inputText.value) {\n    return {\n      characters: 0,\n      charactersNoSpaces: 0,\n      words: 0,\n      sentences: 0,\n      paragraphs: 0,\n      readingTime: 0\n    }\n  }\n\n  const text = inputText.value\n  const characters = text.length\n  const charactersNoSpaces = text.replace(/\\s/g, '').length\n  const words = text.trim() ? text.trim().split(/\\s+/).length : 0\n  const sentences = text.split(/[.!?]+/).filter(s => s.trim()).length\n  const paragraphs = text.split(/\\n\\s*\\n/).filter(p => p.trim()).length\n  const readingTime = Math.ceil(words / 200) // 假设每分钟200词\n\n  return {\n    characters,\n    charactersNoSpaces,\n    words,\n    sentences,\n    paragraphs,\n    readingTime\n  }\n})\n\nconst findMatches = computed(() => {\n  if (!inputText.value || !replaceOptions.value.findText) return 0\n\n  try {\n    let searchText = replaceOptions.value.findText\n    let flags = 'g'\n\n    if (!replaceOptions.value.caseSensitive) {\n      flags += 'i'\n    }\n\n    if (replaceOptions.value.useRegex) {\n      const regex = new RegExp(searchText, flags)\n      const matches = inputText.value.match(regex)\n      return matches ? matches.length : 0\n    } else {\n      if (replaceOptions.value.wholeWord) {\n        searchText = `\\\\b${searchText}\\\\b`\n      }\n      const regex = new RegExp(searchText, flags)\n      const matches = inputText.value.match(regex)\n      return matches ? matches.length : 0\n    }\n  } catch (error) {\n    return 0\n  }\n})\n\n// 页面加载\nonMounted(() => {\n  loadTextHistory()\n})\n\n// 监听文本变化\nwatch(inputText, () => {\n  if (selectedTool.value === 'stats') {\n    // 统计工具实时更新\n  }\n})\n\n// 选择工具\nconst selectTool = (toolId: string) => {\n  selectedTool.value = toolId\n  processedText.value = ''\n}\n\n// 文本输入事件\nconst onTextInput = () => {\n  processedText.value = ''\n}\n\n// 从文件导入\nconst importFromFile = () => {\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  uni.chooseFile({\n    count: 1,\n    type: 'file',\n    extension: ['.txt', '.md', '.json', '.csv'],\n    success: (res) => {\n      // 读取文件内容\n      uni.showToast({\n        title: '文件导入功能开发中',\n        icon: 'none'\n      })\n    }\n  })\n\n}\n\n// 从剪贴板粘贴\nconst pasteFromClipboard = () => {\n  uni.getClipboardData({\n    success: (res) => {\n      inputText.value = res.data\n      uni.showToast({\n        title: '粘贴成功',\n        icon: 'success'\n      })\n    },\n    fail: () => {\n      uni.showToast({\n        title: '粘贴失败',\n        icon: 'error'\n      })\n    }\n  })\n}\n\n// 清空输入\nconst clearInput = () => {\n  inputText.value = ''\n  processedText.value = ''\n}\n\n// 处理文本\nconst processText = async () => {\n  if (!inputText.value) return\n\n  try {\n    isProcessing.value = true\n\n    // 模拟处理延迟\n    await new Promise(resolve => setTimeout(resolve, 500))\n\n    let result = ''\n\n    switch (selectedTool.value) {\n      case 'format':\n        result = formatText(inputText.value)\n        break\n      case 'convert':\n        result = convertText(inputText.value)\n        break\n      case 'replace':\n        result = replaceText(inputText.value)\n        break\n      case 'encode':\n        result = encodeText(inputText.value)\n        break\n      default:\n        result = inputText.value\n    }\n\n    processedText.value = result\n\n    // 添加到历史记录\n    const historyItem = {\n      id: Date.now().toString(),\n      tool: selectedTool.value,\n      originalText: inputText.value,\n      processedText: result,\n      preview: inputText.value.substring(0, 50) + (inputText.value.length > 50 ? '...' : ''),\n      createdAt: new Date().toISOString()\n    }\n\n    textHistory.value.unshift(historyItem)\n    saveTextHistory()\n\n    uni.showToast({\n      title: '处理完成',\n      icon: 'success'\n    })\n\n  } catch (error) {\n    uni.__f__('error','at pages/utilities/text/processor/index.vue:534','处理失败:', error)\n    uni.showToast({\n      title: '处理失败',\n      icon: 'error'\n    })\n  } finally {\n    isProcessing.value = false\n  }\n}\n\n// 格式化文本\nconst formatText = (text: string) => {\n  let result = text\n\n  // 移除多余空格\n  if (formatOptions.value.removeExtraSpaces) {\n    result = result.replace(/\\s+/g, ' ')\n  }\n\n  // 移除空行\n  if (formatOptions.value.removeEmptyLines) {\n    result = result.replace(/\\n\\s*\\n/g, '\\n')\n  }\n\n  // 去除行首尾空格\n  if (formatOptions.value.trimLines) {\n    result = result.split('\\n').map(line => line.trim()).join('\\n')\n  }\n\n  // 应用格式化类型\n  switch (formatOptions.value.type) {\n    case 'uppercase':\n      result = result.toUpperCase()\n      break\n    case 'lowercase':\n      result = result.toLowerCase()\n      break\n    case 'capitalize':\n      result = result.charAt(0).toUpperCase() + result.slice(1).toLowerCase()\n      break\n    case 'title':\n      result = result.replace(/\\w\\S*/g, (txt) =>\n        txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()\n      )\n      break\n  }\n\n  return result\n}\n\n// 转换文本\nconst convertText = (text: string) => {\n  switch (convertOptions.value.type) {\n    case 'markdown':\n      // 简单的文本到Markdown转换\n      return text.split('\\n\\n').map(paragraph =>\n        paragraph.trim() ? `${paragraph}\\n` : ''\n      ).join('\\n')\n\n    case 'html':\n      // 文本到HTML转换\n      return `<html>\\n<body>\\n${text.split('\\n\\n').map(p =>\n        p.trim() ? `<p>${p.replace(/\\n/g, '<br>')}</p>` : ''\n      ).join('\\n')}\\n</body>\\n</html>`\n\n    case 'json':\n      // 文本到JSON转换\n      const lines = text.split('\\n').filter(line => line.trim())\n      return JSON.stringify({ lines }, null, 2)\n\n    case 'csv':\n      // 文本到CSV转换（简单实现）\n      return text.split('\\n').map(line => `\"${line.replace(/\"/g, '\"\"')}\"`).join('\\n')\n\n    default:\n      return text\n  }\n}\n\n// 替换文本\nconst replaceText = (text: string) => {\n  if (!replaceOptions.value.findText) return text\n\n  try {\n    let searchText = replaceOptions.value.findText\n    let flags = 'g'\n\n    if (!replaceOptions.value.caseSensitive) {\n      flags += 'i'\n    }\n\n    if (replaceOptions.value.useRegex) {\n      const regex = new RegExp(searchText, flags)\n      return text.replace(regex, replaceOptions.value.replaceText)\n    } else {\n      if (replaceOptions.value.wholeWord) {\n        searchText = `\\\\b${searchText}\\\\b`\n      }\n      const regex = new RegExp(searchText, flags)\n      return text.replace(regex, replaceOptions.value.replaceText)\n    }\n  } catch (error) {\n    uni.__f__('error','at pages/utilities/text/processor/index.vue:636','替换失败:', error)\n    return text\n  }\n}\n\n// 编码文本\nconst encodeText = (text: string) => {\n  try {\n    switch (encodeOptions.value.type) {\n      case 'base64':\n        if (encodeOptions.value.operation === 'encode') {\n\n\n\n\n          return uni.arrayBufferToBase64(new TextEncoder().encode(text))\n\n        } else {\n\n\n\n\n          return new TextDecoder().decode(uni.base64ToArrayBuffer(text))\n\n        }\n\n      case 'url':\n        return encodeOptions.value.operation === 'encode'\n          ? encodeURIComponent(text)\n          : decodeURIComponent(text)\n\n      case 'html':\n        if (encodeOptions.value.operation === 'encode') {\n          return text\n            .replace(/&/g, '&amp;')\n            .replace(/</g, '&lt;')\n            .replace(/>/g, '&gt;')\n            .replace(/\"/g, '&quot;')\n            .replace(/'/g, '&#39;')\n        } else {\n          return text\n            .replace(/&amp;/g, '&')\n            .replace(/&lt;/g, '<')\n            .replace(/&gt;/g, '>')\n            .replace(/&quot;/g, '\"')\n            .replace(/&#39;/g, \"'\")\n        }\n\n      case 'unicode':\n        if (encodeOptions.value.operation === 'encode') {\n          return text.split('').map(char =>\n            '\\\\u' + char.charCodeAt(0).toString(16).padStart(4, '0')\n          ).join('')\n        } else {\n          return text.replace(/\\\\u[\\dA-Fa-f]{4}/g, (match) =>\n            String.fromCharCode(parseInt(match.replace('\\\\u', ''), 16))\n          )\n        }\n\n      default:\n        return text\n    }\n  } catch (error) {\n    uni.__f__('error','at pages/utilities/text/processor/index.vue:699','编码失败:', error)\n    return text\n  }\n}\n\n// 复制结果\nconst copyResult = () => {\n  if (!processedText.value) return\n\n  uni.setClipboardData({\n    data: processedText.value,\n    success: () => {\n      uni.showToast({\n        title: '复制成功',\n        icon: 'success'\n      })\n    }\n  })\n}\n\n// 下载结果\nconst downloadResult = () => {\n  if (!processedText.value) return\n\n  uni.showToast({\n    title: '下载功能开发中',\n    icon: 'none'\n  })\n}\n\n// 分享结果\nconst shareResult = () => {\n  if (!processedText.value) return\n\n  uni.share({\n    provider: 'weixin',\n    scene: 'WXSceneSession',\n    type: 0,\n    summary: processedText.value.substring(0, 100),\n    success: () => {\n      uni.showToast({\n        title: '分享成功',\n        icon: 'success'\n      })\n    }\n  })\n}\n\n// 工具函数\nconst getToolName = (toolId: string) => {\n  const tool = textTools.value.find(t => t.id === toolId)\n  return tool?.name || '未知工具'\n}\n\nconst getToolIcon = (toolId: string) => {\n  const tool = textTools.value.find(t => t.id === toolId)\n  return tool?.icon || 'text'\n}\n\nconst getResultHeight = () => {\n  const lines = processedText.value.split('\\n').length\n  return Math.min(Math.max(lines * 40, 200), 600) + 'rpx'\n}\n\nconst getProcessedStats = () => {\n  if (!processedText.value) {\n    return { characters: 0, words: 0 }\n  }\n\n  const characters = processedText.value.length\n  const words = processedText.value.trim() ? processedText.value.trim().split(/\\s+/).length : 0\n\n  return { characters, words }\n}\n\nconst getChangePercent = () => {\n  const original = textStats.value.characters\n  const processed = getProcessedStats().characters\n\n  if (original === 0) return 0\n\n  return Math.round(((processed - original) / original) * 100)\n}\n\nconst formatTime = (time: string) => {\n  return dayjs(time).format('MM-DD HH:mm')\n}\n\n// 历史记录相关\nconst saveTextHistory = () => {\n  uni.setStorageSync('text_processing_history', textHistory.value)\n}\n\nconst loadTextHistory = () => {\n  const history = uni.getStorageSync('text_processing_history') || []\n  textHistory.value = history\n}\n\nconst clearHistory = () => {\n  uni.showModal({\n    title: '确认清空',\n    content: '确定要清空所有处理历史吗？',\n    success: (res) => {\n      if (res.confirm) {\n        textHistory.value = []\n        saveTextHistory()\n        uni.showToast({\n          title: '清空成功',\n          icon: 'success'\n        })\n      }\n    }\n  })\n}\n\nconst loadHistoryItem = (item: any) => {\n  inputText.value = item.originalText\n  processedText.value = item.processedText\n  selectedTool.value = item.tool\n}\n\n// 页面下拉刷新\nconst onPullDownRefresh = () => {\n  loadTextHistory()\n  uni.stopPullDownRefresh()\n}\n\n// 导出方法供页面使用\ndefineExpose({\n  onPullDownRefresh\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.text-processor-page {\n  min-height: 100vh;\n  background-color: $bg-secondary;\n}\n\n// 页面头部\n.page-header {\n  background-color: $bg-primary;\n  padding: $spacing-6 $spacing-4;\n  text-align: center;\n  border-bottom: 1px solid $border-primary;\n\n  .page-title {\n    display: block;\n    font-size: $font-size-2xl;\n    font-weight: $font-weight-bold;\n    color: $text-primary;\n    margin-bottom: $spacing-2;\n  }\n\n  .page-desc {\n    font-size: $font-size-base;\n    color: $text-secondary;\n  }\n}\n\n// 通用区块标题\n.section-title {\n  padding: $spacing-4 $spacing-4 $spacing-3;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  .title-text {\n    font-size: $font-size-lg;\n    font-weight: $font-weight-semibold;\n    color: $text-primary;\n  }\n\n  .input-actions,\n  .result-actions {\n    display: flex;\n    align-items: center;\n    gap: $spacing-2;\n  }\n}\n\n// 工具选择\n.tools-selector {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .tools-tabs {\n    display: flex;\n    padding: 0 $spacing-4 $spacing-4;\n    gap: $spacing-2;\n    overflow-x: auto;\n\n    .tab-item {\n      flex: 0 0 auto;\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      padding: $spacing-3;\n      background-color: $bg-tertiary;\n      border-radius: $radius-lg;\n      border: 2px solid transparent;\n      min-width: 120rpx;\n\n      &.active {\n        border-color: $primary;\n        background-color: $primary-50;\n      }\n\n      .tab-text {\n        font-size: $font-size-sm;\n        color: $text-primary;\n        margin-top: $spacing-1;\n      }\n    }\n  }\n}\n\n// 文本输入区域\n.text-input-section {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .input-area {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .text-input {\n      width: 100%;\n      min-height: 300rpx;\n      padding: $spacing-3;\n      border: 1px solid $border-primary;\n      border-radius: $radius-md;\n      font-size: $font-size-base;\n      color: $text-primary;\n      background-color: $bg-tertiary;\n      line-height: $line-height-relaxed;\n\n      &::placeholder {\n        color: $text-quaternary;\n      }\n    }\n\n    .input-stats {\n      display: flex;\n      justify-content: space-between;\n      margin-top: $spacing-2;\n      padding: $spacing-2 $spacing-3;\n      background-color: $bg-tertiary;\n      border-radius: $radius-md;\n\n      .stat-item {\n        font-size: $font-size-sm;\n        color: $text-secondary;\n      }\n    }\n  }\n}\n\n// 工具选项\n.tool-options {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .options-content {\n    padding: 0 $spacing-4;\n\n    .option-group {\n      .option-item {\n        margin-bottom: $spacing-4;\n\n        .option-label {\n          display: block;\n          font-size: $font-size-base;\n          font-weight: $font-weight-medium;\n          color: $text-primary;\n          margin-bottom: $spacing-3;\n        }\n\n        .option-input {\n          width: 100%;\n          padding: $spacing-2 $spacing-3;\n          border: 1px solid $border-primary;\n          border-radius: $radius-md;\n          font-size: $font-size-base;\n          color: $text-primary;\n          background-color: $bg-tertiary;\n\n          &::placeholder {\n            color: $text-quaternary;\n          }\n        }\n\n        .checkbox-group {\n          display: flex;\n          flex-direction: column;\n          gap: $spacing-2;\n        }\n      }\n\n      .stats-display {\n        display: grid;\n        grid-template-columns: repeat(2, 1fr);\n        gap: $spacing-3;\n\n        .stat-card {\n          background-color: $bg-tertiary;\n          border-radius: $radius-lg;\n          padding: $spacing-4;\n          text-align: center;\n\n          .stat-number {\n            display: block;\n            font-size: $font-size-xl;\n            font-weight: $font-weight-bold;\n            color: $primary;\n            margin-bottom: $spacing-1;\n          }\n\n          .stat-label {\n            font-size: $font-size-sm;\n            color: $text-secondary;\n          }\n        }\n      }\n\n      .replace-preview {\n        background-color: $bg-tertiary;\n        border-radius: $radius-md;\n        padding: $spacing-3;\n\n        .preview-label {\n          font-size: $font-size-sm;\n          color: $text-secondary;\n        }\n      }\n    }\n  }\n\n  .process-button {\n    padding: 0 $spacing-4 $spacing-4;\n  }\n}\n\n// 处理结果\n.processing-result {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .result-content {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .result-text {\n      width: 100%;\n      padding: $spacing-3;\n      border: 1px solid $border-primary;\n      border-radius: $radius-md;\n      font-size: $font-size-base;\n      color: $text-primary;\n      background-color: $bg-tertiary;\n      line-height: $line-height-relaxed;\n      resize: none;\n      margin-bottom: $spacing-3;\n    }\n\n    .result-stats {\n      display: flex;\n      justify-content: space-between;\n      padding: $spacing-2 $spacing-3;\n      background-color: $bg-tertiary;\n      border-radius: $radius-md;\n\n      .stat-item {\n        font-size: $font-size-sm;\n        color: $text-secondary;\n      }\n    }\n  }\n}\n\n// 处理历史\n.processing-history {\n  background-color: $bg-primary;\n\n  .history-list {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .history-item {\n      display: flex;\n      align-items: center;\n      gap: $spacing-3;\n      padding: $spacing-3;\n      background-color: $bg-tertiary;\n      border-radius: $radius-lg;\n      margin-bottom: $spacing-2;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      &:active {\n        background-color: $gray-200;\n      }\n\n      .history-icon {\n        width: 64rpx;\n        height: 64rpx;\n        background-color: $primary-50;\n        border-radius: $radius-lg;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n\n      .history-content {\n        flex: 1;\n\n        .history-title {\n          display: block;\n          font-size: $font-size-base;\n          font-weight: $font-weight-medium;\n          color: $text-primary;\n          margin-bottom: $spacing-1;\n        }\n\n        .history-desc {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          margin-bottom: $spacing-1;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n\n        .history-time {\n          font-size: $font-size-xs;\n          color: $text-tertiary;\n        }\n      }\n\n      .history-actions {\n        padding: $spacing-2;\n      }\n    }\n  }\n}\n\n// 响应式适配\n@media screen and (max-width: 480px) {\n  .tools-tabs {\n    .tab-item {\n      min-width: 100rpx;\n      padding: $spacing-2;\n    }\n  }\n\n  .stats-display {\n    grid-template-columns: 1fr;\n  }\n\n  .input-stats {\n    flex-direction: column;\n    gap: $spacing-1;\n\n    .stat-item {\n      text-align: center;\n    }\n  }\n\n  .result-stats {\n    flex-direction: column;\n    gap: $spacing-1;\n\n    .stat-item {\n      text-align: center;\n    }\n  }\n}\n\n// 暗色模式适配\n@media (prefers-color-scheme: dark) {\n  .text-processor-page {\n    background-color: #1a1a1a;\n  }\n\n  .page-header,\n  .tools-selector,\n  .text-input-section,\n  .tool-options,\n  .processing-result,\n  .processing-history {\n    background-color: #2d2d2d;\n    border-color: #404040;\n  }\n\n  .tab-item,\n  .text-input,\n  .option-input,\n  .stat-card,\n  .replace-preview,\n  .result-text,\n  .input-stats,\n  .result-stats,\n  .history-item {\n    background-color: #404040;\n    border-color: #555555;\n  }\n\n  .tab-item.active {\n    background-color: #1e3a8a;\n    border-color: #3b82f6;\n  }\n\n  .text-input,\n  .option-input,\n  .result-text {\n    color: #ffffff;\n\n    &::placeholder {\n      color: #888888;\n    }\n  }\n\n  .history-icon {\n    background-color: #1e3a8a;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/AI_system/mobile-uniapp/src/pages/utilities/text/processor/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "watch", "uni", "dayjs"], "mappings": ";;;;;;;;;;;;;AA2QM,UAAA,eAAeA,kBAAY,QAAQ;AACnC,UAAA,YAAYA,kBAAY,EAAE;AAC1B,UAAA,gBAAgBA,kBAAY,EAAE;AAC9B,UAAA,eAAeA,kBAAa,KAAK;AACjC,UAAA,cAAcA,kBAAW,CAAA,CAAE;AAGjC,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB,MAAM;AAAA,MACN,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,WAAW;AAAA,IAAA,CACZ;AAED,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB,MAAM;AAAA,IAAA,CACP;AAED,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB,UAAU;AAAA,MACV,aAAa;AAAA,MACb,eAAe;AAAA,MACf,WAAW;AAAA,MACX,UAAU;AAAA,IAAA,CACX;AAED,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB,MAAM;AAAA,MACN,WAAW;AAAA,IAAA,CACZ;AAGD,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IAAA,CACD;AAGK,UAAA,YAAYC,cAAAA,SAAS,MAAM;AAC3B,UAAA,CAAC,UAAU,OAAO;AACb,eAAA,EAAE,YAAY,GAAG,OAAO,GAAG,OAAO,GAAG,YAAY;MAC1D;AAEA,YAAM,OAAO,UAAU;AACvB,YAAM,aAAa,KAAK;AAClB,YAAA,QAAQ,KAAK,KAAA,IAAS,KAAK,KAAO,EAAA,MAAM,KAAK,EAAE,SAAS;AAC9D,YAAM,QAAQ,KAAK,MAAM,IAAI,EAAE;AACzB,YAAA,aAAa,KAAK,MAAM,SAAS,EAAE,OAAO,CAAK,MAAA,EAAE,KAAM,CAAA,EAAE;AAE/D,aAAO,EAAE,YAAY,OAAO,OAAO,WAAW;AAAA,IAAA,CAC/C;AAEK,UAAA,gBAAgBA,cAAAA,SAAS,MAAM;AAC/B,UAAA,CAAC,UAAU,OAAO;AACb,eAAA;AAAA,UACL,YAAY;AAAA,UACZ,oBAAoB;AAAA,UACpB,OAAO;AAAA,UACP,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,aAAa;AAAA,QAAA;AAAA,MAEjB;AAEA,YAAM,OAAO,UAAU;AACvB,YAAM,aAAa,KAAK;AACxB,YAAM,qBAAqB,KAAK,QAAQ,OAAO,EAAE,EAAE;AAC7C,YAAA,QAAQ,KAAK,KAAA,IAAS,KAAK,KAAO,EAAA,MAAM,KAAK,EAAE,SAAS;AACxD,YAAA,YAAY,KAAK,MAAM,QAAQ,EAAE,OAAO,CAAK,MAAA,EAAE,KAAM,CAAA,EAAE;AACvD,YAAA,aAAa,KAAK,MAAM,SAAS,EAAE,OAAO,CAAK,MAAA,EAAE,KAAM,CAAA,EAAE;AAC/D,YAAM,cAAc,KAAK,KAAK,QAAQ,GAAG;AAElC,aAAA;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACF,CACD;AAEK,UAAA,cAAcA,cAAAA,SAAS,MAAM;AACjC,UAAI,CAAC,UAAU,SAAS,CAAC,eAAe,MAAM;AAAiB,eAAA;AAE3D,UAAA;AACE,YAAA,aAAa,eAAe,MAAM;AACtC,YAAI,QAAQ;AAER,YAAA,CAAC,eAAe,MAAM,eAAe;AAC9B,mBAAA;AAAA,QACX;AAEI,YAAA,eAAe,MAAM,UAAU;AACjC,gBAAM,QAAQ,IAAI,OAAO,YAAY,KAAK;AAC1C,gBAAM,UAAU,UAAU,MAAM,MAAM,KAAK;AACpC,iBAAA,UAAU,QAAQ,SAAS;AAAA,QAAA,OAC7B;AACD,cAAA,eAAe,MAAM,WAAW;AAClC,yBAAa,MAAM,UAAU;AAAA,UAC/B;AACA,gBAAM,QAAQ,IAAI,OAAO,YAAY,KAAK;AAC1C,gBAAM,UAAU,UAAU,MAAM,MAAM,KAAK;AACpC,iBAAA,UAAU,QAAQ,SAAS;AAAA,QACpC;AAAA,eACO,OAAO;AACP,eAAA;AAAA,MACT;AAAA,IAAA,CACD;AAGDC,kBAAAA,UAAU,MAAM;AACE;IAAA,CACjB;AAGDC,kBAAA,MAAM,WAAW,MAAM;AACjB,UAAA,aAAa,UAAU;AAAS;AAAA,IAEpC,CACD;AAGK,UAAA,aAAa,CAAC,WAAmB;AACrC,mBAAa,QAAQ;AACrB,oBAAc,QAAQ;AAAA,IAAA;AAIxB,UAAM,cAAc,MAAM;AACxB,oBAAc,QAAQ;AAAA,IAAA;AAIxB,UAAM,iBAAiB,MAAM;AAmB3BC,oBAAAA,MAAI,WAAW;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW,CAAC,QAAQ,OAAO,SAAS,MAAM;AAAA,QAC1C,SAAS,CAAC,QAAQ;AAEhBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;AAKH,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,SAAS,CAAC,QAAQ;AAChB,oBAAU,QAAQ,IAAI;AACtBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,QACA,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,aAAa,MAAM;AACvB,gBAAU,QAAQ;AAClB,oBAAc,QAAQ;AAAA,IAAA;AAIxB,UAAM,cAAc,YAAY;AAC9B,UAAI,CAAC,UAAU;AAAO;AAElB,UAAA;AACF,qBAAa,QAAQ;AAGrB,cAAM,IAAI,QAAQ,CAAA,YAAW,WAAW,SAAS,GAAG,CAAC;AAErD,YAAI,SAAS;AAEb,gBAAQ,aAAa,OAAO;AAAA,UAC1B,KAAK;AACM,qBAAA,WAAW,UAAU,KAAK;AACnC;AAAA,UACF,KAAK;AACM,qBAAA,YAAY,UAAU,KAAK;AACpC;AAAA,UACF,KAAK;AACM,qBAAA,YAAY,UAAU,KAAK;AACpC;AAAA,UACF,KAAK;AACM,qBAAA,WAAW,UAAU,KAAK;AACnC;AAAA,UACF;AACE,qBAAS,UAAU;AAAA,QACvB;AAEA,sBAAc,QAAQ;AAGtB,cAAM,cAAc;AAAA,UAClB,IAAI,KAAK,IAAI,EAAE,SAAS;AAAA,UACxB,MAAM,aAAa;AAAA,UACnB,cAAc,UAAU;AAAA,UACxB,eAAe;AAAA,UACf,SAAS,UAAU,MAAM,UAAU,GAAG,EAAE,KAAK,UAAU,MAAM,SAAS,KAAK,QAAQ;AAAA,UACnF,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAAA;AAGxB,oBAAA,MAAM,QAAQ,WAAW;AACrB;AAEhBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,eAEM,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,mDAAkD,SAAS,KAAK;AAClFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,qBAAa,QAAQ;AAAA,MACvB;AAAA,IAAA;AAII,UAAA,aAAa,CAAC,SAAiB;AACnC,UAAI,SAAS;AAGT,UAAA,cAAc,MAAM,mBAAmB;AAChC,iBAAA,OAAO,QAAQ,QAAQ,GAAG;AAAA,MACrC;AAGI,UAAA,cAAc,MAAM,kBAAkB;AAC/B,iBAAA,OAAO,QAAQ,YAAY,IAAI;AAAA,MAC1C;AAGI,UAAA,cAAc,MAAM,WAAW;AACxB,iBAAA,OAAO,MAAM,IAAI,EAAE,IAAI,CAAQ,SAAA,KAAK,KAAM,CAAA,EAAE,KAAK,IAAI;AAAA,MAChE;AAGQ,cAAA,cAAc,MAAM,MAAM;AAAA,QAChC,KAAK;AACH,mBAAS,OAAO;AAChB;AAAA,QACF,KAAK;AACH,mBAAS,OAAO;AAChB;AAAA,QACF,KAAK;AACM,mBAAA,OAAO,OAAO,CAAC,EAAE,gBAAgB,OAAO,MAAM,CAAC,EAAE,YAAY;AACtE;AAAA,QACF,KAAK;AACH,mBAAS,OAAO;AAAA,YAAQ;AAAA,YAAU,CAAC,QACjC,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,OAAO,CAAC,EAAE,YAAY;AAAA,UAAA;AAE1D;AAAA,MACJ;AAEO,aAAA;AAAA,IAAA;AAIH,UAAA,cAAc,CAAC,SAAiB;AAC5B,cAAA,eAAe,MAAM,MAAM;AAAA,QACjC,KAAK;AAEI,iBAAA,KAAK,MAAM,MAAM,EAAE;AAAA,YAAI,CAC5B,cAAA,UAAU,KAAK,IAAI,GAAG,SAAS;AAAA,IAAO;AAAA,UAAA,EACtC,KAAK,IAAI;AAAA,QAEb,KAAK;AAEI,iBAAA;AAAA;AAAA,EAAmB,KAAK,MAAM,MAAM,EAAE;AAAA,YAAI,CAAA,MAC/C,EAAE,KAAA,IAAS,MAAM,EAAE,QAAQ,OAAO,MAAM,CAAC,SAAS;AAAA,UAAA,EAClD,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA,QAEd,KAAK;AAEG,gBAAA,QAAQ,KAAK,MAAM,IAAI,EAAE,OAAO,CAAA,SAAQ,KAAK,KAAA,CAAM;AACzD,iBAAO,KAAK,UAAU,EAAE,MAAM,GAAG,MAAM,CAAC;AAAA,QAE1C,KAAK;AAEH,iBAAO,KAAK,MAAM,IAAI,EAAE,IAAI,CAAQ,SAAA,IAAI,KAAK,QAAQ,MAAM,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI;AAAA,QAEhF;AACS,iBAAA;AAAA,MACX;AAAA,IAAA;AAII,UAAA,cAAc,CAAC,SAAiB;AAChC,UAAA,CAAC,eAAe,MAAM;AAAiB,eAAA;AAEvC,UAAA;AACE,YAAA,aAAa,eAAe,MAAM;AACtC,YAAI,QAAQ;AAER,YAAA,CAAC,eAAe,MAAM,eAAe;AAC9B,mBAAA;AAAA,QACX;AAEI,YAAA,eAAe,MAAM,UAAU;AACjC,gBAAM,QAAQ,IAAI,OAAO,YAAY,KAAK;AAC1C,iBAAO,KAAK,QAAQ,OAAO,eAAe,MAAM,WAAW;AAAA,QAAA,OACtD;AACD,cAAA,eAAe,MAAM,WAAW;AAClC,yBAAa,MAAM,UAAU;AAAA,UAC/B;AACA,gBAAM,QAAQ,IAAI,OAAO,YAAY,KAAK;AAC1C,iBAAO,KAAK,QAAQ,OAAO,eAAe,MAAM,WAAW;AAAA,QAC7D;AAAA,eACO,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,mDAAkD,SAAS,KAAK;AAC3E,eAAA;AAAA,MACT;AAAA,IAAA;AAII,UAAA,aAAa,CAAC,SAAiB;AAC/B,UAAA;AACM,gBAAA,cAAc,MAAM,MAAM;AAAA,UAChC,KAAK;AACC,gBAAA,cAAc,MAAM,cAAc,UAAU;AAK9C,qBAAOA,cAAAA,MAAI,oBAAoB,IAAI,YAAc,EAAA,OAAO,IAAI,CAAC;AAAA,YAAA,OAExD;AAKL,qBAAO,IAAI,YAAY,EAAE,OAAOA,cAAI,MAAA,oBAAoB,IAAI,CAAC;AAAA,YAE/D;AAAA,UAEF,KAAK;AACI,mBAAA,cAAc,MAAM,cAAc,WACrC,mBAAmB,IAAI,IACvB,mBAAmB,IAAI;AAAA,UAE7B,KAAK;AACC,gBAAA,cAAc,MAAM,cAAc,UAAU;AAC9C,qBAAO,KACJ,QAAQ,MAAM,OAAO,EACrB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,OAAO;AAAA,YAAA,OACnB;AACL,qBAAO,KACJ,QAAQ,UAAU,GAAG,EACrB,QAAQ,SAAS,GAAG,EACpB,QAAQ,SAAS,GAAG,EACpB,QAAQ,WAAW,GAAG,EACtB,QAAQ,UAAU,GAAG;AAAA,YAC1B;AAAA,UAEF,KAAK;AACC,gBAAA,cAAc,MAAM,cAAc,UAAU;AACvC,qBAAA,KAAK,MAAM,EAAE,EAAE;AAAA,gBAAI,CAAA,SACxB,QAAQ,KAAK,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AAAA,cAAA,EACvD,KAAK,EAAE;AAAA,YAAA,OACJ;AACL,qBAAO,KAAK;AAAA,gBAAQ;AAAA,gBAAqB,CAAC,UACxC,OAAO,aAAa,SAAS,MAAM,QAAQ,OAAO,EAAE,GAAG,EAAE,CAAC;AAAA,cAAA;AAAA,YAE9D;AAAA,UAEF;AACS,mBAAA;AAAA,QACX;AAAA,eACO,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,mDAAkD,SAAS,KAAK;AAC3E,eAAA;AAAA,MACT;AAAA,IAAA;AAIF,UAAM,aAAa,MAAM;AACvB,UAAI,CAAC,cAAc;AAAO;AAE1BA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM,cAAc;AAAA,QACpB,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,cAAc;AAAO;AAE1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIH,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,cAAc;AAAO;AAE1BA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,SAAS,cAAc,MAAM,UAAU,GAAG,GAAG;AAAA,QAC7C,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;AAIG,UAAA,cAAc,CAAC,WAAmB;AACtC,YAAM,OAAO,UAAU,MAAM,KAAK,CAAK,MAAA,EAAE,OAAO,MAAM;AACtD,cAAO,6BAAM,SAAQ;AAAA,IAAA;AAGjB,UAAA,cAAc,CAAC,WAAmB;AACtC,YAAM,OAAO,UAAU,MAAM,KAAK,CAAK,MAAA,EAAE,OAAO,MAAM;AACtD,cAAO,6BAAM,SAAQ;AAAA,IAAA;AAGvB,UAAM,kBAAkB,MAAM;AAC5B,YAAM,QAAQ,cAAc,MAAM,MAAM,IAAI,EAAE;AACvC,aAAA,KAAK,IAAI,KAAK,IAAI,QAAQ,IAAI,GAAG,GAAG,GAAG,IAAI;AAAA,IAAA;AAGpD,UAAM,oBAAoB,MAAM;AAC1B,UAAA,CAAC,cAAc,OAAO;AACxB,eAAO,EAAE,YAAY,GAAG,OAAO,EAAE;AAAA,MACnC;AAEM,YAAA,aAAa,cAAc,MAAM;AACvC,YAAM,QAAQ,cAAc,MAAM,KAAS,IAAA,cAAc,MAAM,KAAO,EAAA,MAAM,KAAK,EAAE,SAAS;AAErF,aAAA,EAAE,YAAY;IAAM;AAG7B,UAAM,mBAAmB,MAAM;AACvB,YAAA,WAAW,UAAU,MAAM;AAC3B,YAAA,YAAY,kBAAoB,EAAA;AAEtC,UAAI,aAAa;AAAU,eAAA;AAE3B,aAAO,KAAK,OAAQ,YAAY,YAAY,WAAY,GAAG;AAAA,IAAA;AAGvD,UAAA,aAAa,CAAC,SAAiB;AACnC,aAAOC,cAAM,MAAA,IAAI,EAAE,OAAO,aAAa;AAAA,IAAA;AAIzC,UAAM,kBAAkB,MAAM;AACxBD,oBAAAA,MAAA,eAAe,2BAA2B,YAAY,KAAK;AAAA,IAAA;AAGjE,UAAM,kBAAkB,MAAM;AAC5B,YAAM,UAAUA,cAAA,MAAI,eAAe,yBAAyB,KAAK,CAAA;AACjE,kBAAY,QAAQ;AAAA,IAAA;AAGtB,UAAM,eAAe,MAAM;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,wBAAY,QAAQ;AACJ;AAChBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAAA,CACP;AAAA,UACH;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IAAA;AAGG,UAAA,kBAAkB,CAAC,SAAc;AACrC,gBAAU,QAAQ,KAAK;AACvB,oBAAc,QAAQ,KAAK;AAC3B,mBAAa,QAAQ,KAAK;AAAA,IAAA;AAI5B,UAAM,oBAAoB,MAAM;AACd;AAChBA,oBAAA,MAAI,oBAAoB;AAAA,IAAA;AAIb,aAAA;AAAA,MACX;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3zBD,GAAG,WAAW,eAAe;"}