/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 导入自定义样式变量 */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
.document-translation-page.data-v-19b7412d {
  min-height: 100vh;
  background-color: #F9FAFB;
}
.page-header.data-v-19b7412d {
  background-color: #FFFFFF;
  padding: 48rpx 32rpx;
  text-align: center;
  border-bottom: 1px solid #E5E7EB;
}
.page-header .page-title.data-v-19b7412d {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #111827;
  margin-bottom: 16rpx;
}
.page-header .page-desc.data-v-19b7412d {
  font-size: 28rpx;
  color: #374151;
}
.language-selector.data-v-19b7412d {
  background-color: #FFFFFF;
  padding: 32rpx;
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.language-selector .language-item.data-v-19b7412d {
  flex: 1;
  text-align: center;
}
.language-selector .language-item .language-label.data-v-19b7412d {
  display: block;
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 16rpx;
}
.language-selector .swap-button.data-v-19b7412d {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F3F4F6;
  border-radius: 9999rpx;
  margin: 0 32rpx;
}
.language-selector .swap-button.data-v-19b7412d:active {
  background-color: #E5E7EB;
}
.section-title.data-v-19b7412d {
  padding: 32rpx 32rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.section-title .title-text.data-v-19b7412d {
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
}
.section-title .title-desc.data-v-19b7412d {
  font-size: 24rpx;
  color: #6B7280;
}
.section-title .result-actions.data-v-19b7412d {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.upload-section.data-v-19b7412d {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.upload-section .upload-area.data-v-19b7412d {
  margin: 0 32rpx 32rpx;
  border: 2px dashed #E5E7EB;
  border-radius: 16rpx;
  padding: 48rpx;
}
.upload-section .upload-area .upload-content.data-v-19b7412d {
  text-align: center;
}
.upload-section .upload-area .upload-content .upload-text.data-v-19b7412d {
  display: block;
  font-size: 36rpx;
  font-weight: 500;
  color: #111827;
  margin: 32rpx 0 16rpx;
}
.upload-section .upload-area .upload-content .upload-desc.data-v-19b7412d {
  display: block;
  font-size: 28rpx;
  color: #374151;
  margin-bottom: 16rpx;
}
.upload-section .upload-area .upload-content .upload-limit.data-v-19b7412d {
  font-size: 24rpx;
  color: #6B7280;
}
.upload-section .upload-area .file-info.data-v-19b7412d {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.upload-section .upload-area .file-info .file-icon.data-v-19b7412d {
  width: 80rpx;
  height: 80rpx;
  background-color: #F3F4F6;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-section .upload-area .file-info .file-details.data-v-19b7412d {
  flex: 1;
}
.upload-section .upload-area .file-info .file-details .file-name.data-v-19b7412d {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.upload-section .upload-area .file-info .file-details .file-size.data-v-19b7412d,
.upload-section .upload-area .file-info .file-details .file-type.data-v-19b7412d,
.upload-section .upload-area .file-info .file-details .file-pages.data-v-19b7412d {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 4rpx;
}
.upload-section .upload-area .file-info .file-actions.data-v-19b7412d {
  display: flex;
  gap: 16rpx;
}
.translation-options.data-v-19b7412d {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.translation-options .options-list.data-v-19b7412d {
  padding: 0 32rpx;
}
.translation-options .options-list .option-item.data-v-19b7412d {
  padding: 32rpx 0;
  border-bottom: 1px solid #E5E7EB;
}
.translation-options .options-list .option-item.data-v-19b7412d:last-child {
  border-bottom: none;
}
.translation-options .options-list .option-item .option-label.data-v-19b7412d {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 24rpx;
}
.translation-options .options-list .option-item .option-desc.data-v-19b7412d {
  font-size: 24rpx;
  color: #6B7280;
  margin-left: 16rpx;
}
.translation-options .options-list .option-item .page-range-input.data-v-19b7412d {
  width: 100%;
  padding: 16rpx 24rpx;
  border: 1px solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #111827;
  background-color: #F3F4F6;
}
.translation-options .options-list .option-item .page-range-input.data-v-19b7412d::-webkit-input-placeholder {
  color: #9CA3AF;
}
.translation-options .options-list .option-item .page-range-input.data-v-19b7412d::placeholder {
  color: #9CA3AF;
}
.translation-options .translate-button.data-v-19b7412d {
  padding: 0 32rpx 32rpx;
}
.translation-progress.data-v-19b7412d {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
  padding: 32rpx;
}
.translation-progress .progress-header.data-v-19b7412d {
  text-align: center;
  margin-bottom: 32rpx;
}
.translation-progress .progress-header .progress-title.data-v-19b7412d {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8rpx;
}
.translation-progress .progress-header .progress-desc.data-v-19b7412d {
  font-size: 24rpx;
  color: #374151;
}
.translation-progress .progress-bar.data-v-19b7412d {
  margin-bottom: 32rpx;
}
.translation-progress .progress-details.data-v-19b7412d {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.translation-progress .progress-details .progress-text.data-v-19b7412d,
.translation-progress .progress-details .progress-time.data-v-19b7412d {
  font-size: 24rpx;
  color: #6B7280;
}
.translation-result.data-v-19b7412d {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.translation-result .result-content.data-v-19b7412d {
  padding: 0 32rpx 32rpx;
}
.translation-result .result-content .result-summary.data-v-19b7412d {
  background-color: #ECFDF5;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}
.translation-result .result-content .result-summary .summary-item.data-v-19b7412d {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}
.translation-result .result-content .result-summary .summary-item .summary-text.data-v-19b7412d {
  font-size: 28rpx;
  font-weight: 500;
  color: #047857;
}
.translation-result .result-content .result-summary .summary-stats.data-v-19b7412d {
  display: flex;
  flex-wrap: wrap;
  gap: 32rpx;
}
.translation-result .result-content .result-summary .summary-stats .stat-item.data-v-19b7412d {
  font-size: 24rpx;
  color: #059669;
}
.translation-result .result-content .result-file.data-v-19b7412d {
  background-color: #F3F4F6;
  border-radius: 16rpx;
  padding: 32rpx;
}
.translation-result .result-content .result-file .file-preview.data-v-19b7412d {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 32rpx;
}
.translation-result .result-content .result-file .file-preview .preview-info.data-v-19b7412d {
  flex: 1;
}
.translation-result .result-content .result-file .file-preview .preview-info .preview-name.data-v-19b7412d {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.translation-result .result-content .result-file .file-preview .preview-info .preview-size.data-v-19b7412d {
  font-size: 24rpx;
  color: #374151;
}
.translation-result .result-content .result-file .file-actions.data-v-19b7412d {
  display: flex;
  gap: 16rpx;
}
.translation-history.data-v-19b7412d {
  background-color: #FFFFFF;
}
.translation-history .history-list.data-v-19b7412d {
  padding: 0 32rpx 32rpx;
}
.translation-history .history-list .history-item.data-v-19b7412d {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  background-color: #F3F4F6;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}
.translation-history .history-list .history-item.data-v-19b7412d:last-child {
  margin-bottom: 0;
}
.translation-history .history-list .history-item.data-v-19b7412d:active {
  background-color: #E5E7EB;
}
.translation-history .history-list .history-item .history-icon.data-v-19b7412d {
  width: 64rpx;
  height: 64rpx;
  background-color: #E5E7EB;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.translation-history .history-list .history-item .history-content.data-v-19b7412d {
  flex: 1;
}
.translation-history .history-list .history-item .history-content .history-title.data-v-19b7412d {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.translation-history .history-list .history-item .history-content .history-desc.data-v-19b7412d {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 8rpx;
}
.translation-history .history-list .history-item .history-content .history-time.data-v-19b7412d {
  font-size: 20rpx;
  color: #6B7280;
}
.translation-history .history-list .history-item .history-status.data-v-19b7412d {
  padding: 16rpx;
}
@media screen and (max-width: 480px) {
.language-selector.data-v-19b7412d {
    flex-direction: column;
    gap: 24rpx;
}
.language-selector .swap-button.data-v-19b7412d {
    transform: rotate(90deg);
}
.summary-stats.data-v-19b7412d {
    flex-direction: column;
    gap: 16rpx;
}
.file-actions.data-v-19b7412d {
    flex-direction: column;
}
}
@media (prefers-color-scheme: dark) {
.document-translation-page.data-v-19b7412d {
    background-color: #1a1a1a;
}
.page-header.data-v-19b7412d,
.language-selector.data-v-19b7412d,
.upload-section.data-v-19b7412d,
.translation-options.data-v-19b7412d,
.translation-progress.data-v-19b7412d,
.translation-result.data-v-19b7412d,
.translation-history.data-v-19b7412d {
    background-color: #2d2d2d;
    border-color: #404040;
}
.upload-area.data-v-19b7412d,
.result-file.data-v-19b7412d,
.history-item.data-v-19b7412d {
    background-color: #404040;
    border-color: #555555;
}
.swap-button.data-v-19b7412d,
.file-icon.data-v-19b7412d,
.history-icon.data-v-19b7412d {
    background-color: #404040;
}
.result-summary.data-v-19b7412d {
    background-color: #1e3a2e;
}
.page-range-input.data-v-19b7412d {
    background-color: #555555;
    border-color: #666666;
    color: #ffffff;
}
.page-range-input.data-v-19b7412d::-webkit-input-placeholder {
    color: #888888;
}
.page-range-input.data-v-19b7412d::placeholder {
    color: #888888;
}
}