/* 导入图标字体 */
@import './iconfont.scss';

/* 全局样式重置 */
page, view, text, image, button, input, textarea, scroll-view {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

page {
  background-color: $bg-secondary;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  line-height: 1.6;
  color: $text-primary;
  font-size: 28rpx;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 现代化按钮样式 */
.modern-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  position: relative;
  overflow: hidden;
  
  &:active {
    transform: scale(0.98);
  }
  
  &.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #FFFFFF;
    box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
  }
  
  &.secondary {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    border: 1rpx solid rgba(102, 126, 234, 0.2);
  }
  
  &.ghost {
    background: transparent;
    color: #667eea;
    border: 1rpx solid rgba(102, 126, 234, 0.3);
  }
  
  &.danger {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    color: #FFFFFF;
    box-shadow: 0 4rpx 20rpx rgba(239, 68, 68, 0.3);
  }
  
  &.success {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
    color: #FFFFFF;
    box-shadow: 0 4rpx 20rpx rgba(16, 185, 129, 0.3);
  }
  
  &.disabled {
    opacity: 0.5;
    transform: none !important;
  }
  
  &.loading {
    opacity: 0.7;
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 32rpx;
      height: 32rpx;
      margin: -16rpx 0 0 -16rpx;
      border: 2rpx solid transparent;
      border-top-color: currentColor;
      border-radius: 50%;
      animation: button-loading 1s linear infinite;
    }
  }
}

@keyframes button-loading {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 现代化卡片样式 */
.modern-card {
  background: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.elevated {
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  }
  
  &.bordered {
    border: 2rpx solid rgba(102, 126, 234, 0.1);
  }
  
  &.glass {
    background: rgba(255, 255, 255, 0.8);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
  }
}

/* 现代化输入框样式 */
.modern-input {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
  font-size: 28rpx;
  color: $text-primary;
  background: #FFFFFF;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
  }
  
  &::placeholder {
    color: $text-quaternary;
  }
  
  &.error {
    border-color: #EF4444;
    
    &:focus {
      box-shadow: 0 0 0 6rpx rgba(239, 68, 68, 0.1);
    }
  }
  
  &.success {
    border-color: #10B981;
    
    &:focus {
      box-shadow: 0 0 0 6rpx rgba(16, 185, 129, 0.1);
    }
  }
}

/* 现代化标签样式 */
.modern-tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  
  &.primary {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
  }
  
  &.success {
    background: rgba(16, 185, 129, 0.1);
    color: #10B981;
  }
  
  &.warning {
    background: rgba(245, 158, 11, 0.1);
    color: #F59E0B;
  }
  
  &.danger {
    background: rgba(239, 68, 68, 0.1);
    color: #EF4444;
  }
  
  &.info {
    background: rgba(59, 130, 246, 0.1);
    color: #3B82F6;
  }
  
  &.gray {
    background: rgba(107, 114, 128, 0.1);
    color: #6B7280;
  }
}

/* 现代化徽章样式 */
.modern-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  background: linear-gradient(45deg, #EF4444, #DC2626);
  color: #FFFFFF;
  font-size: 20rpx;
  font-weight: 600;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.4);
  animation: badge-pulse 2s infinite;
}

@keyframes badge-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 现代化分割线 */
.modern-divider {
  height: 1rpx;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  margin: 32rpx 0;
  
  &.vertical {
    width: 1rpx;
    height: auto;
    background: linear-gradient(180deg, transparent, rgba(0, 0, 0, 0.1), transparent);
    margin: 0 32rpx;
  }
}

/* 现代化加载动画 */
.modern-loading {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(102, 126, 234, 0.2);
  border-top-color: #667eea;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 现代化骨架屏 */
.modern-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8rpx;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* 现代化渐变背景 */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

.gradient-warning {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
}

.gradient-danger {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
}

.gradient-info {
  background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
}

.gradient-purple {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
}

.gradient-pink {
  background: linear-gradient(135deg, #EC4899 0%, #DB2777 100%);
}

.gradient-cyan {
  background: linear-gradient(135deg, #06B6D4 0%, #0891B2 100%);
}

/* 现代化阴影 */
.shadow-modern {
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.shadow-modern-lg {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

.shadow-modern-xl {
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.16);
}

.shadow-colored {
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

/* 现代化毛玻璃效果 (小程序兼容版本) */
.glass-effect {
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.5);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
}
