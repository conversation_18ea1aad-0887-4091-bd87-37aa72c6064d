"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  _component_u_icon();
}
if (!Math) {
  TabBar();
}
const TabBar = () => "../../components/TabBar/index.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "modern",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(0);
    const mainContentHeight = common_vendor.ref(0);
    const hasNotifications = common_vendor.ref(true);
    const userInfo = common_vendor.ref({
      nickname: "<PERSON>",
      avatar: "/static/images/avatar-default.png"
    });
    const aiCapabilities = common_vendor.ref([
      {
        title: "智能翻译",
        description: "支持100+语言实时翻译",
        icon: "icon-translate",
        gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        usage: "1.2万",
        rating: "4.9",
        isNew: false,
        path: "/pages/translation/text/index"
      },
      {
        title: "AI对话",
        description: "智能助手24小时在线",
        icon: "icon-chat",
        gradient: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
        usage: "8.5千",
        rating: "4.8",
        isNew: true,
        path: "/pages/digital-human/chat/index"
      },
      {
        title: "文档处理",
        description: "智能文档分析与转换",
        icon: "icon-document",
        gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
        usage: "5.3千",
        rating: "4.7",
        isNew: false,
        path: "/pages/utilities/document/converter/index"
      },
      {
        title: "图像识别",
        description: "强大的视觉AI能力",
        icon: "icon-image",
        gradient: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
        usage: "3.1千",
        rating: "4.6",
        isNew: true,
        path: "/pages/utilities/image/processor/index"
      }
    ]);
    const recommendations = common_vendor.ref([
      {
        title: "语音转文字",
        description: "高精度语音识别",
        image: "/static/images/rec-voice.jpg",
        category: "语音处理",
        duration: "2分钟",
        path: "/pages/translation/audio/index"
      },
      {
        title: "智能摘要",
        description: "长文档快速摘要",
        image: "/static/images/rec-summary.jpg",
        category: "文本处理",
        duration: "30秒",
        path: "/pages/utilities/text/processor/index"
      }
    ]);
    const recentItems = common_vendor.ref([
      {
        title: "英文翻译",
        description: "商务邮件翻译",
        icon: "icon-translate",
        lastUsed: new Date(Date.now() - 2 * 60 * 60 * 1e3),
        path: "/pages/translation/text/index"
      },
      {
        title: "PDF转换",
        description: "合同文档转换",
        icon: "icon-pdf",
        lastUsed: new Date(Date.now() - 5 * 60 * 60 * 1e3),
        path: "/pages/utilities/document/converter/index"
      }
    ]);
    const getGreeting = () => {
      const hour = (/* @__PURE__ */ new Date()).getHours();
      if (hour < 12)
        return "早上好";
      if (hour < 18)
        return "下午好";
      return "晚上好";
    };
    common_vendor.onMounted(() => {
      common_vendor.index.getSystemInfo({
        success: (res) => {
          statusBarHeight.value = res.statusBarHeight || 0;
          mainContentHeight.value = res.windowHeight - 120 - statusBarHeight.value;
        }
      });
    });
    const goToProfile = () => {
      common_vendor.index.navigateTo({ url: "/pages/user/profile/index" });
    };
    const showNotifications = () => {
      common_vendor.index.showToast({ title: "通知中心", icon: "none" });
    };
    const showQRCode = () => {
      common_vendor.index.showToast({ title: "扫一扫", icon: "none" });
    };
    const goToSearch = () => {
      common_vendor.index.navigateTo({ url: "/pages/search/index" });
    };
    const startVoiceSearch = () => {
      common_vendor.index.showToast({ title: "语音搜索", icon: "none" });
    };
    const navigateToFeature = (item) => {
      common_vendor.index.navigateTo({ url: item.path });
    };
    const useRecommendation = (item) => {
      common_vendor.index.navigateTo({ url: item.path });
    };
    const viewAllRecent = () => {
      common_vendor.index.navigateTo({ url: "/pages/recent/index" });
    };
    const continueTask = (item) => {
      common_vendor.index.navigateTo({ url: item.path });
    };
    const showQuickActions = () => {
      common_vendor.index.showActionSheet({
        itemList: ["新建翻译", "语音输入", "扫描文档", "智能对话"],
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/index/modern.vue:327", "选择了第" + (res.tapIndex + 1) + "个按钮");
        }
      });
    };
    const formatTime = (time) => {
      const now = /* @__PURE__ */ new Date();
      const diff = now.getTime() - time.getTime();
      const hours = Math.floor(diff / (1e3 * 60 * 60));
      if (hours < 1)
        return "刚刚";
      if (hours < 24)
        return `${hours}小时前`;
      return `${Math.floor(hours / 24)}天前`;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: statusBarHeight.value + "px",
        b: userInfo.value.avatar,
        c: common_vendor.o(goToProfile),
        d: common_vendor.t(getGreeting()),
        e: common_vendor.t(userInfo.value.nickname || "AI Explorer"),
        f: common_vendor.p({
          name: "star-fill",
          size: "10",
          color: "#FFD700"
        }),
        g: common_vendor.p({
          name: "bell",
          size: "20",
          color: "#FFFFFF"
        }),
        h: hasNotifications.value
      }, hasNotifications.value ? {} : {}, {
        i: common_vendor.o(showNotifications),
        j: common_vendor.p({
          name: "qrcode-scan",
          size: "20",
          color: "#FFFFFF"
        }),
        k: common_vendor.o(showQRCode),
        l: common_vendor.p({
          name: "search",
          size: "16",
          color: "#8E8E93"
        }),
        m: common_vendor.p({
          name: "mic",
          size: "14",
          color: "#667eea"
        }),
        n: common_vendor.o(startVoiceSearch),
        o: common_vendor.o(goToSearch),
        p: common_vendor.f(aiCapabilities.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.n(item.icon),
            b: item.gradient,
            c: item.isNew
          }, item.isNew ? {} : {}, {
            d: common_vendor.t(item.title),
            e: common_vendor.t(item.description),
            f: common_vendor.t(item.usage),
            g: common_vendor.t(item.rating),
            h: "86525b0d-5-" + i0,
            i: index,
            j: common_vendor.o(($event) => navigateToFeature(item), index)
          });
        }),
        q: common_vendor.p({
          name: "arrow-right",
          size: "12",
          color: "#667eea"
        }),
        r: common_vendor.f(recommendations.value, (item, index, i0) => {
          return {
            a: item.image,
            b: "86525b0d-6-" + i0,
            c: common_vendor.t(item.title),
            d: common_vendor.t(item.description),
            e: common_vendor.t(item.category),
            f: common_vendor.t(item.duration),
            g: index,
            h: common_vendor.o(($event) => useRecommendation(item), index)
          };
        }),
        s: common_vendor.p({
          name: "play-circle",
          size: "24",
          color: "#FFFFFF"
        }),
        t: recentItems.value.length > 0
      }, recentItems.value.length > 0 ? {
        v: common_vendor.o(viewAllRecent),
        w: common_vendor.f(recentItems.value, (item, index, i0) => {
          return {
            a: common_vendor.n(item.icon),
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.description),
            d: common_vendor.t(formatTime(item.lastUsed)),
            e: "86525b0d-7-" + i0,
            f: index,
            g: common_vendor.o(($event) => continueTask(item), index)
          };
        }),
        x: common_vendor.p({
          name: "more-dot-fill",
          size: "16",
          color: "#C7C7CC"
        })
      } : {}, {
        y: mainContentHeight.value + "px",
        z: common_vendor.p({
          name: "plus",
          size: "24",
          color: "#FFFFFF"
        }),
        A: common_vendor.o(showQuickActions)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-86525b0d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/modern.js.map
