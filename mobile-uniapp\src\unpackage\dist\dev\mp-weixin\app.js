"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/index/modern.js";
  "./pages/translation/text/index.js";
  "./pages/translation/audio/index.js";
  "./pages/translation/video/index.js";
  "./pages/translation/document/index.js";
  "./pages/digital-human/chat/index.js";
  "./pages/ai-agents/market/index.js";
  "./pages/utilities/document/converter/index.js";
  "./pages/utilities/image/processor/index.js";
  "./pages/utilities/text/processor/index.js";
  "./pages/utilities/qrcode/generator/index.js";
  "./pages/user/profile/index.js";
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "App",
  setup(__props) {
    common_vendor.onLaunch(() => {
      common_vendor.index.__f__("log", "at App.vue:5", "App Launch");
    });
    common_vendor.onShow(() => {
      common_vendor.index.__f__("log", "at App.vue:9", "App Show");
    });
    common_vendor.onHide(() => {
      common_vendor.index.__f__("log", "at App.vue:13", "App Hide");
    });
    return () => {
    };
  }
});
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  const pinia = common_vendor.createPinia();
  app.use(pinia);
  return {
    app,
    pinia
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
