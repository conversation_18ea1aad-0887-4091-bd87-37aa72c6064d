<view class="custom-tabbar data-v-5b533c2d"><view wx:for="{{a}}" wx:for-item="item" wx:key="h" class="{{['tab-item', 'data-v-5b533c2d', item.i && 'active']}}" bindtap="{{item.j}}"><view class="tab-icon data-v-5b533c2d"><view class="{{['icon-wrapper', 'data-v-5b533c2d', item.d && 'active']}}"><text class="{{['iconfont', 'data-v-5b533c2d', item.a]}}"></text><view wx:if="{{item.b}}" class="icon-badge data-v-5b533c2d">{{item.c}}</view></view></view><text class="{{['tab-text', 'data-v-5b533c2d', item.f && 'active']}}">{{item.e}}</text><view wx:if="{{item.g}}" class="tab-indicator data-v-5b533c2d"></view></view></view>