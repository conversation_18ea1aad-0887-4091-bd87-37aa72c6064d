<template>
  <div class="digital-human-edit">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="back-button" @click="goBack">
        <left-outlined /> 返回
      </div>
      <h1 class="page-title">编辑数字人</h1>
    </div>

    <!-- 加载状态显示 -->
    <div v-if="loading" class="loading-container">
      <a-spin tip="正在加载数字人信息..." size="large" />
    </div>

    <!-- 内容区域（仅在加载完成后显示） -->
    <div v-else class="immersive-creation-container">
      <!-- 左侧步骤导航 -->
      <div class="steps-navigation">
        <div class="steps-nav-inner">
          <div 
            v-for="(step, index) in steps" 
            :key="index"
            class="step-nav-item"
            :class="{ 'active': currentStep === index, 'completed': currentStep > index }"
            @click="jumpToStep(index)"
          >
            <div class="step-icon">
              <component :is="step.icon" v-if="currentStep !== index || currentStep > index" />
              <span v-else>{{ index + 1 }}</span>
            </div>
            <div class="step-info">
              <div class="step-title">{{ step.title }}</div>
              <div class="step-desc">{{ step.description }}</div>
            </div>
          </div>
        </div>
        
        <!-- 步骤操作按钮 -->
        <div class="steps-action">
          <a-button 
            v-if="currentStep > 0" 
            class="prev-btn"
            @click="prevStep"
          >
            <template #icon><left-outlined /></template>
            上一步
          </a-button>
          <a-button 
            v-if="currentStep < steps.length - 1" 
            type="primary" 
            class="next-btn"
            @click="nextStep"
          >
            下一步
            <template #icon><right-outlined /></template>
          </a-button>
        </div>
      </div>
      
      <!-- 主内容区域 -->
      <div class="main-content-area">
        <!-- 内容编辑区 -->
        <div class="content-edit-area">
          <div class="edit-container">
            <!-- 步骤1: 基本信息 -->
            <div v-if="currentStep === 0" class="step-container">
              <h2 class="step-title">基本信息</h2>
              <p class="step-description">调整数字人的身份和用途</p>
              
              <a-form :model="formData" layout="vertical">
                <a-form-item label="数字人名称" required>
                  <a-input v-model:value="formData.name" placeholder="请输入数字人名称" :maxLength="30" />
                </a-form-item>
                
                <a-form-item label="数字人类型" required>
                  <a-radio-group v-model:value="formData.type" class="custom-radio-group">
                    <a-radio value="assistant">智能助手</a-radio>
                    <a-radio value="customer_service">客服专员</a-radio>
                    <a-radio value="broadcaster">主播讲解员</a-radio>
                    <a-radio value="teacher">教育培训</a-radio>
                  </a-radio-group>
                </a-form-item>
                
                <a-form-item label="性别" required>
                  <a-radio-group v-model:value="formData.gender" class="custom-radio-group">
                    <a-radio value="male">男性</a-radio>
                    <a-radio value="female">女性</a-radio>
                    <a-radio value="other">其他</a-radio>
                  </a-radio-group>
                </a-form-item>
                
                <a-form-item label="应用场景">
                  <a-select v-model:value="formData.scenario" mode="multiple" placeholder="请选择应用场景（可多选）">
                    <a-select-option value="website">网站客服</a-select-option>
                    <a-select-option value="ecommerce">电商直播</a-select-option>
                    <a-select-option value="education">教育培训</a-select-option>
                    <a-select-option value="healthcare">医疗健康</a-select-option>
                    <a-select-option value="finance">金融服务</a-select-option>
                    <a-select-option value="entertainment">娱乐演示</a-select-option>
                  </a-select>
                </a-form-item>
                
                <a-form-item label="数字人描述">
                  <a-textarea
                    v-model:value="formData.description"
                    placeholder="请简要描述数字人的功能定位和特点"
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    :maxLength="200"
                    show-count
                  />
                  <div class="ai-assist-button">
                    <a-button type="link" size="small" @click="generateDescription" :loading="isGeneratingDesc">
                      <robot-outlined /> AI生成描述
                    </a-button>
                    <span class="field-tip">基于已选择的类型和场景自动生成描述</span>
                  </div>
                </a-form-item>
              </a-form>
            </div>
            
            <!-- 步骤2: 外观设置 -->
            <div v-if="currentStep === 1" class="step-container">
              <h2 class="step-title">外观设置</h2>
              <p class="step-description">调整数字人的外观</p>
              
              <div class="appearance-options">
                <a-radio-group 
                  v-model:value="formData.appearanceType" 
                  class="custom-segment custom-radio-buttons"
                  button-style="solid"
                >
                  <a-radio-button value="template">选择模板</a-radio-button>
                  <a-radio-button value="upload">上传图像</a-radio-button>
                </a-radio-group>
              </div>
              
              <!-- 预设模板选择 -->
              <div v-if="formData.appearanceType === 'template'" class="template-selection">
                <a-row :gutter="[16, 16]">
                  <a-col :xs="24" :sm="12" v-for="template in templates" :key="template.id">
                    <div 
                      class="template-card" 
                      :class="{'selected': formData.selectedTemplate === template.id}"
                      @click="selectTemplate(template.id)"
                    >
                      <div class="template-image">
                        <img :src="template.imageUrl" :alt="template.name" />
                        <div class="template-selected-mark" v-if="formData.selectedTemplate === template.id">
                          <check-outlined />
                        </div>
                      </div>
                      <div class="template-info">
                        <h4>{{ template.name }}</h4>
                        <p>{{ template.description }}</p>
                        <a-tag color="blue">{{ getTypeText(template.type) }}</a-tag>
                      </div>
                    </div>
                  </a-col>
                </a-row>
              </div>
              
              <!-- 上传图像 -->
              <div v-else class="upload-section">
                <div class="upload-main">
                  <a-upload
                    list-type="picture-card"
                    :file-list="fileList"
                    :before-upload="beforeUpload"
                    :customRequest="handleUpload"
                    :show-upload-list="true"
                    @preview="handlePreview"
                    @remove="handleRemove"
                    accept="image/jpeg,image/png,image/webp"
                  >
                    <div v-if="fileList.length < 1">
                      <plus-outlined />
                      <div style="margin-top: 8px">上传图像</div>
                    </div>
                  </a-upload>
                  
                  <a-modal
                    :visible="previewVisible"
                    :title="previewTitle"
                    :footer="null"
                    @cancel="handleCancelPreview"
                  >
                    <img alt="预览图" style="width: 100%" :src="previewImage" />
                  </a-modal>
                </div>
                
                <div class="upload-tips">
                  <h4>上传要求</h4>
                  <ul>
                    <li>图像分辨率建议1080x1080 以上</li>
                    <li>支持 JPG, PNG, WebP 格式</li>
                    <li>人物应当面向正前方，五官清晰可见</li>
                    <li>建议纯色背景，避免复杂场景</li>
                  </ul>
                </div>
              </div>
            </div>
            
            <!-- 步骤3: 声音设置 -->
            <div v-if="currentStep === 2" class="step-container">
              <h2 class="step-title">声音设置</h2>
              <p class="step-description">调整数字人的语音特色</p>
              
              <a-form :model="formData" layout="vertical">
                <!-- 声音来源选择 -->
                <a-form-item label="声音来源">
                  <a-radio-group 
                    v-model:value="formData.voiceSource" 
                    class="custom-segment voice-source-selector custom-radio-buttons"
                    button-style="solid"
                  >
                    <a-radio-button value="preset">预设声音</a-radio-button>
                    <a-radio-button value="custom">上传声音</a-radio-button>
                  </a-radio-group>
                </a-form-item>
                
                <!-- 预设声音选择部分 -->
                <div v-if="formData.voiceSource === 'preset'">
                  <a-form-item label="声音类型" required>
                    <a-radio-group v-model:value="formData.voiceType" class="voice-radio-group">
                      <div class="voice-option-grid">
                        <a-radio value="standard" class="voice-option">
                          <div class="voice-option-content">
                            <sound-outlined class="voice-icon" />
                            <div class="voice-info">
                              <div class="voice-name">标准音色</div>
                              <div class="voice-desc">清晰自然的中性声音</div>
                            </div>
                            <a-button size="small" shape="circle" @click.stop="previewVoice('standard')">
                              <play-circle-outlined />
                            </a-button>
                          </div>
                        </a-radio>
                        <a-radio value="professional" class="voice-option">
                          <div class="voice-option-content">
                            <sound-outlined class="voice-icon" />
                            <div class="voice-info">
                              <div class="voice-name">专业播音</div>
                              <div class="voice-desc">富有磁性的专业播音风格</div>
                            </div>
                            <a-button size="small" shape="circle" @click.stop="previewVoice('professional')">
                              <play-circle-outlined />
                            </a-button>
                          </div>
                        </a-radio>
                        <a-radio value="warm" class="voice-option">
                          <div class="voice-option-content">
                            <sound-outlined class="voice-icon" />
                            <div class="voice-info">
                              <div class="voice-name">温暖亲切</div>
                              <div class="voice-desc">亲切柔和的交流语音</div>
                            </div>
                            <a-button size="small" shape="circle" @click.stop="previewVoice('warm')">
                              <play-circle-outlined />
                            </a-button>
                          </div>
                        </a-radio>
                        <a-radio value="clear" class="voice-option">
                          <div class="voice-option-content">
                            <sound-outlined class="voice-icon" />
                            <div class="voice-info">
                              <div class="voice-name">清晰明快</div>
                              <div class="voice-desc">高能量明快的语音风格</div>
                            </div>
                            <a-button size="small" shape="circle" @click.stop="previewVoice('clear')">
                              <play-circle-outlined />
                            </a-button>
                          </div>
                        </a-radio>
                      </div>
                    </a-radio-group>
                  </a-form-item>
                </div>
                
                <!-- 自定义声音上传部分 - 简化版 -->
                <div v-else class="voice-upload-section">
                  <div class="voice-upload-methods">
                    <a-upload
                      :file-list="audioFileList"
                      :before-upload="beforeAudioUpload"
                      :customRequest="handleAudioUpload"
                      :show-upload-list="true"
                      @remove="handleAudioRemove"
                      accept="audio/mp3,audio/wav,audio/ogg,audio/mpeg"
                    >
                      <a-button type="primary">
                        <upload-outlined /> 选择音频文件
                      </a-button>
                      <div class="upload-hint">支持 MP3, WAV, OGG 格式，大小不超过10MB</div>
                    </a-upload>
                    
                    <div class="audio-preview" v-if="formData.customVoiceUrl">
                      <h4 class="preview-title">音频预览</h4>
                      <audio 
                        ref="audioPlayer" 
                        controls 
                        class="audio-player"
                        :src="formData.customVoiceUrl"
                      ></audio>
                    </div>
                  </div>
                  
                  <div class="upload-tips">
                    <h4>声音要求</h4>
                    <ul>
                      <li>请上传清晰的人声音频</li>
                      <li>避免使用有背景音乐或噪音的音频</li>
                      <li>推荐使用5-10秒的样本，内容简短清晰</li>
                      <li>上传的声音将用于训练AI声音模型</li>
                    </ul>
                  </div>
                </div>
                
                <!-- 公共语音参数设置 -->
                <a-form-item label="语速">
                  <a-slider 
                    v-model:value="formData.voiceSpeed" 
                    :min="0.5" 
                    :max="2" 
                    :step="0.1" 
                    :marks="{
                      0.5: '较慢',
                      1: '正常',
                      1.5: '较快',
                      2: '快速'
                    }"
                  />
                </a-form-item>
                
                <a-form-item label="音调">
                  <a-slider 
                    v-model:value="formData.voicePitch" 
                    :min="0.5" 
                    :max="1.5" 
                    :step="0.1" 
                    :marks="{
                      0.5: '低沉',
                      1: '正常',
                      1.5: '高亢'
                    }"
                  />
                </a-form-item>
                
                <a-form-item label="欢迎语">
                  <a-textarea
                    v-model:value="formData.welcomeText"
                    placeholder="请输入数字人的欢迎语（用户首次访问时播放）"
                    :auto-size="{ minRows: 2, maxRows: 4 }"
                    :maxLength="100"
                    show-count
                  />
                  <div class="ai-assist-button">
                    <a-button type="link" size="small" @click="generateWelcomeText" :loading="isGeneratingWelcome">
                      <robot-outlined /> AI生成欢迎语
                    </a-button>
                    <span class="field-tip">基于数字人类型和描述自动生成欢迎语</span>
                  </div>
                </a-form-item>
                
                <a-form-item>
                  <a-button type="primary" @click="testVoice" :loading="isTestingVoice">
                    <sound-outlined /> 试听完整欢迎语
                  </a-button>
                </a-form-item>
              </a-form>
            </div>
            
            <!-- 步骤4: 保存更新 -->
            <div v-if="currentStep === 3" class="step-container">
              <h2 class="step-title">保存更新</h2>
              <p class="step-description">确认信息并保存修改</p>
              
              <div class="summary-container">
                <!-- 信息摘要展示，与创建页面类似 -->
                <div class="info-summary">
                  <h3 class="summary-section-title">更新信息确认</h3>
                  
                  <!-- 其余确认信息内容将在下一步完成 -->
                  
                  <!-- 显示变更摘要 -->
                  <a-card class="summary-card changes-summary">
                    <template #title>
                      <div class="summary-header">
                        <span>已更改内容</span>
                      </div>
                    </template>
                    <div class="summary-content">
                      <div v-if="hasChanges" class="changes-list">
                        <div v-for="(change, field) in changesDetail" :key="field" class="change-item">
                          <div class="field-name">{{ getFieldLabel(field) }}</div>
                          <div class="change-detail">
                            <div class="old-value">
                              <span class="label">原值:</span> 
                              <span class="value">{{ formatFieldValue(field, change.oldValue) }}</span>
                            </div>
                            <div class="new-value">
                              <span class="label">新值:</span> 
                              <span class="value">{{ formatFieldValue(field, change.newValue) }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div v-else class="no-changes">
                        未检测到任何更改
                      </div>
                    </div>
                  </a-card>
                </div>
                
                <a-divider />
                
                <!-- 保存操作 -->
                <div class="update-actions">
                  <a-button type="primary" @click="saveChanges" :loading="isSaving" size="large">
                    保存更新
                  </a-button>
                  <a-button @click="resetChanges" :disabled="isSaving || !hasChanges" size="large">
                    重置更改
                  </a-button>
                  <a-button danger @click="confirmCancel" :disabled="isSaving" size="large">
                    取消编辑
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 预览区域 -->
        <div class="preview-area">
          <div class="preview-container">
            <h3 class="preview-title">实时预览</h3>
            <div class="preview-content">
              <div class="preview-avatar-container">
                <img 
                  :src="getPreviewImageUrl()" 
                  alt="数字人预览图" 
                  class="preview-avatar"
                />
                <div class="preview-speaking-indicator" v-if="isSpeakingPreview">
                  <span></span><span></span><span></span>
                </div>
              </div>
              
              <div class="preview-details">
                <h3 class="preview-name">{{ formData.name || '未命名数字人' }}</h3>
                <div class="preview-type-tag">{{ getTypeText(formData.type) }}</div>
                <p class="preview-description">{{ formData.description || '暂无描述' }}</p>
                
                <div class="preview-attrs">
                  <div class="attr-item">
                    <user-outlined class="attr-icon" />
                    <span class="attr-value">{{ getGenderText(formData.gender) }}</span>
                  </div>
                  <div class="attr-item">
                    <sound-outlined class="attr-icon" />
                    <span class="attr-value">{{ getVoiceTypeText(formData.voiceType) }}</span>
                  </div>
                  <div class="attr-item">
                    <dashboard-outlined class="attr-icon" />
                    <span class="attr-value">{{ getSpeedText(formData.voiceSpeed) }}</span>
                  </div>
                </div>
                
                <div class="preview-welcome-text">
                  <p>"{{ formData.welcomeText || '您好，我是数字人助手' }}"</p>
                </div>
                
                <div class="preview-actions">
                  <a-button 
                    type="primary" 
                    shape="round"
                    @click="playWelcomePreview"
                    :disabled="isPlayingWelcome"
                  >
                    <sound-outlined /> 试听欢迎语
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, watch, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  LeftOutlined, 
  RightOutlined,
  PlusOutlined, 
  CheckOutlined,
  SoundOutlined,
  UserOutlined,
  FormOutlined,
  SkinOutlined, 
  PlayCircleOutlined,
  DashboardOutlined,
  UploadOutlined,
  EditOutlined,
  RobotOutlined
} from '@ant-design/icons-vue';
import { getStaticResourceUrl } from '../../../utils/asset-path';
// 导入数字人API
import { getDigitalHumanDetail, updateDigitalHuman } from '../../../api/digital-human';
import { useDigitalHumanStore } from '../../../stores/digitalHuman';

export default defineComponent({
  name: 'DigitalHumanEdit',
  
  components: {
    LeftOutlined,
    RightOutlined,
    PlusOutlined,
    CheckOutlined,
    SoundOutlined,
    UserOutlined,
    FormOutlined,
    SkinOutlined,
    PlayCircleOutlined,
    DashboardOutlined,
    UploadOutlined,
    EditOutlined,
    RobotOutlined
  },
  
  setup() {
    const router = useRouter();
    const route = useRoute();
    const digitalHumanId = computed(() => route.params.id);
    const digitalHumanStore = useDigitalHumanStore();
    
    // 步骤和加载状态
    const currentStep = ref(0);
    const loading = ref(true);
    const isSaving = ref(false);
    const isSpeakingPreview = ref(false);
    const isPlayingWelcome = ref(false);
    const isTestingVoice = ref(false);
    const isGeneratingDesc = ref(false);
    const isGeneratingWelcome = ref(false);
    
    // 步骤定义
    const steps = [
      {
        title: '基本信息',
        description: '调整身份与用途',
        icon: 'FormOutlined'
      },
      {
        title: '外观设置',
        description: '调整数字人形象',
        icon: 'SkinOutlined'
      },
      {
        title: '声音设置',
        description: '调整语音特色',
        icon: 'SoundOutlined'
      },
      {
        title: '保存更新',
        description: '确认并保存修改',
        icon: 'EditOutlined'
      }
    ];
    
    // 表单数据
    const formData = reactive({
      name: '',
      type: 'assistant',
      gender: 'female',
      scenario: [],
      description: '',
      appearanceType: 'template',
      selectedTemplate: '',
      uploadedImageUrl: '',
      voiceType: 'standard',
      voiceSpeed: 1,
      voicePitch: 1,
      welcomeText: '',
      voiceSource: 'preset', 
      customVoiceUrl: '',
      customVoiceFile: null
    });
    
    // 原始数据，用于比较变更
    const originalData = reactive({});
    
    // 上传相关
    const fileList = ref([]);
    const previewVisible = ref(false);
    const previewImage = ref('');
    const previewTitle = ref('');
    
    // 声音上传相关
    const audioFileList = ref([]);
    const audioPlayer = ref(null);
    
    // 预设模板
    const templates = [
      {
        id: 't001',
        name: '商务女性',
        description: '专业商务风格，适合客服、咨询场景',
        imageUrl: getStaticResourceUrl('/images/digital-human/avatar1.png'),
        type: 'customer_service'
      },
      {
        id: 't002',
        name: '专业男性',
        description: '正式专业形象，适合金融、教育领域',
        imageUrl: getStaticResourceUrl('/images/digital-human/avatar2.png'),
        type: 'teacher'
      },
      {
        id: 't003',
        name: '活力少女',
        description: '青春活力形象，适合娱乐、时尚领域',
        imageUrl: getStaticResourceUrl('/images/digital-human/avatar3.png'),
        type: 'broadcaster'
      },
      {
        id: 't004',
        name: '科技男性',
        description: '科技感形象，适合技术讲解、产品演示',
        imageUrl: getStaticResourceUrl('/images/digital-human/avatar2.png'), 
        type: 'assistant'
      }
    ];
    
    // 加载数字人数据
    const loadDigitalHumanData = async () => {
      if (!digitalHumanId.value) {
        ElMessage.error('未找到数字人ID，无法加载数据');
        router.replace('/digital-human');
        return;
      }
      
      loading.value = true;
      try {
        const response = await getDigitalHumanDetail(digitalHumanId.value);
        const dhData = response.data || response;
        
        // 填充表单数据
        formData.name = dhData.name || '';
        formData.type = dhData.type || 'assistant';
        formData.gender = dhData.gender || 'female';
        formData.description = dhData.description || '';
        formData.scenario = Array.isArray(dhData.scenario) ? dhData.scenario : 
                          dhData.scenario ? [dhData.scenario] : [];
        formData.voiceType = dhData.voiceType || 'standard';
        formData.voiceSpeed = dhData.voiceSpeed || 1;
        formData.voicePitch = dhData.voicePitch || 1;
        formData.welcomeText = dhData.welcomeText || '';
        
        // 处理外观设置
        if (dhData.appearanceType === 'upload' && dhData.avatar_url) {
          formData.appearanceType = 'upload';
          formData.uploadedImageUrl = dhData.avatar_url;
          fileList.value = [{
            uid: '-1',
            name: 'avatar.png',
            status: 'done',
            url: dhData.avatar_url
          }];
        } else {
          formData.appearanceType = 'template';
          formData.selectedTemplate = dhData.templateId || 't001';
        }
        
        // 处理声音设置
        if (dhData.voiceSource === 'custom' && dhData.custom_voice_url) {
          formData.voiceSource = 'custom';
          formData.customVoiceUrl = dhData.custom_voice_url;
          audioFileList.value = [{
            uid: '-1',
            name: 'voice.mp3',
            status: 'done',
            url: dhData.custom_voice_url
          }];
        } else {
          formData.voiceSource = 'preset';
        }
        
        // 保存原始数据副本用于比较
        Object.assign(originalData, JSON.parse(JSON.stringify(formData)));
        
        loading.value = false;
      } catch (error) {
        console.error('加载数字人详情失败:', error);
        ElMessage.error('加载数字人详情失败，请刷新重试');
        loading.value = false;
      }
    };
    
    // 在组件挂载时加载数据
    onMounted(() => {
      loadDigitalHumanData();
    });
    
    // 检测变更
    const changesDetail = computed(() => {
      const changes = {};
      
      // 遍历所有字段进行比较
      Object.keys(formData).forEach(field => {
        const originalValue = originalData[field];
        const currentValue = formData[field];
        
        // 特殊处理数组类型
        if (Array.isArray(originalValue) && Array.isArray(currentValue)) {
          // 数组长度或内容不同
          if (originalValue.length !== currentValue.length || 
              JSON.stringify(originalValue) !== JSON.stringify(currentValue)) {
            changes[field] = {
              oldValue: originalValue,
              newValue: currentValue
            };
          }
        } 
        // 处理其他类型
        else if (originalValue !== currentValue) {
          changes[field] = {
            oldValue: originalValue,
            newValue: currentValue
          };
        }
      });
      
      return changes;
    });
    
    // 是否有变更
    const hasChanges = computed(() => {
      return Object.keys(changesDetail.value).length > 0;
    });
    
    // 下一步
    const nextStep = () => {
      if (validateCurrentStep()) {
        currentStep.value += 1;
      }
    };
    
    // 上一步
    const prevStep = () => {
      currentStep.value -= 1;
    };
    
    // 跳转到特定步骤
    const jumpToStep = (step) => {
      // 只允许跳转到已完成的步骤或下一个待完成步骤
      if (step <= currentStep.value || step === currentStep.value + 1) {
        if (step < currentStep.value || validateCurrentStep()) {
          currentStep.value = step;
        }
      }
    };
    
    // 验证当前步骤
    const validateCurrentStep = () => {
      if (currentStep.value === 0) {
        if (!formData.name.trim()) {
          ElMessage.error('请输入数字人名称');
          return false;
        }
      } else if (currentStep.value === 1) {
        if (formData.appearanceType === 'template' && !formData.selectedTemplate) {
          ElMessage.error('请选择数字人模板');
          return false;
        } else if (formData.appearanceType === 'upload') {
          if (fileList.value.length === 0 && !formData.uploadedImageUrl) {
            ElMessage.error('请上传数字人图像');
            return false;
          }
        }
      } else if (currentStep.value === 2) {
        if (formData.voiceSource === 'preset') {
          if (!formData.voiceType) {
            ElMessage.error('请选择声音类型');
            return false;
          }
        } else if (formData.voiceSource === 'custom') {
          if (!formData.customVoiceUrl && audioFileList.value.length === 0) {
            ElMessage.error('请上传声音');
            return false;
          }
        }
        if (!formData.welcomeText.trim()) {
          ElMessage.error('请填写欢迎语');
          return false;
        }
      }
      
      return true;
    };
    
    // 选择模板
    const selectTemplate = (templateId) => {
      formData.selectedTemplate = templateId;
    };
    
    // 上传相关函数
    // 上传前检查
    const beforeUpload = (file) => {
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        ElMessage.error('您只能上传图片文件');
        return false;
      }

      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        ElMessage.error('图片大小不能超过5MB!');
        return false;
      }
      
      return true;
    };
    
    // 处理上传
    const handleUpload = ({ file, onSuccess, onError }) => {
      // 模拟上传过程
      setTimeout(() => {
        try {
          // 使用FileReader读取图片为base64
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = () => {
            fileList.value = [
              {
                uid: '-1',
                name: file.name,
                status: 'done',
                url: reader.result,
                originFileObj: file // 保存原始文件对象以便后续使用
              }
            ];
            formData.uploadedImageUrl = reader.result;
            onSuccess();
          };
        } catch (err) {
          console.error('上传失败:', err);
          onError();
          ElMessage.error('上传图片失败，请重试');
        }
      }, 1000);
    };
    
    // 音频上传前检查
    const beforeAudioUpload = (file) => {
      const isAudio = /audio\/(mp3|mpeg|wav|ogg)/.test(file.type);
      if (!isAudio) {
        ElMessage.error('您只能上传MP3、WAV或OGG格式的音频文件');
        return false;
      }

      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        ElMessage.error('音频大小不能超过10MB!');
        return false;
      }
      
      return true;
    };
    
    // 处理音频上传
    const handleAudioUpload = ({ file, onSuccess, onError }) => {
      // 模拟上传过程
      setTimeout(() => {
        try {
          // 使用FileReader读取音频为Data URL
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = () => {
            audioFileList.value = [
              {
                uid: '-1',
                name: file.name,
                status: 'done',
                url: reader.result
              }
            ];
            formData.customVoiceUrl = reader.result;
            formData.customVoiceFile = file;
            onSuccess();
          };
        } catch (err) {
          console.error('音频上传失败:', err);
          onError();
          ElMessage.error('上传音频失败，请重试');
        }
      }, 1000);
    };
    
    // 移除音频
    const handleAudioRemove = () => {
      audioFileList.value = [];
      formData.customVoiceUrl = '';
      formData.customVoiceFile = null;
    };
    
    // 处理预览
    const handlePreview = (file) => {
      previewImage.value = file.url || file.thumbUrl;
      previewVisible.value = true;
      previewTitle.value = file.name || '数字人图像';
    };
    
    // 取消预览
    const handleCancelPreview = () => {
      previewVisible.value = false;
    };
    
    // 移除图片
    const handleRemove = () => {
      fileList.value = [];
      formData.uploadedImageUrl = '';
      return true;
    };
    
    // 预览特定声音类型
    const previewVoice = (voiceType) => {
      ElMessage.info(`正在播放${getVoiceTypeText(voiceType)}示例...`);
      
      // 将当前选择更新到表单
      formData.voiceType = voiceType;
      
      // 模拟播放声音
      setTimeout(() => {
        isSpeakingPreview.value = true;
        
        // 2秒后结束播放
        setTimeout(() => {
          isSpeakingPreview.value = false;
        }, 2000);
      }, 500);
    };
    
    // 测试声音
    const testVoice = () => {
      isTestingVoice.value = true;
      message.loading('正在合成语音...', 1);
      
      // 模拟语音合成过程
      setTimeout(() => {
        playWelcomePreview();
        isTestingVoice.value = false;
      }, 1000);
    };
    
    // 播放欢迎语预览
    const playWelcomePreview = () => {
      if (isPlayingWelcome.value) return;
      
      isPlayingWelcome.value = true;
      isSpeakingPreview.value = true;
      
      // 使用自定义声音
      if (formData.voiceSource === 'custom' && formData.customVoiceUrl) {
        if (audioPlayer.value) {
          audioPlayer.value.play();
        }
        
        // 模拟语音播放3秒后结束
        setTimeout(() => {
          isPlayingWelcome.value = false;
          isSpeakingPreview.value = false;
        }, 3000);
      } else {
        // 使用预设声音
        // 模拟语音播放3秒后结束
        setTimeout(() => {
          isPlayingWelcome.value = false;
          isSpeakingPreview.value = false;
        }, 3000);
      }
    };
    
    // 保存更改
    const saveChanges = async () => {
      if (!validateCurrentStep()) return;
      
      // 验证是否有更改
      if (!hasChanges.value) {
        message.info('未检测到任何更改，无需保存');
        return;
      }
      
      isSaving.value = true;
      message.loading({ content: '正在保存更改，请稍候...', key: 'saving', duration: 0 });
      
      try {
        // 准备更新数据
        const updateData = {
          id: digitalHumanId.value,
          name: formData.name,
          type: formData.type,
          gender: formData.gender,
          scenario: formData.scenario,
          description: formData.description,
          appearanceType: formData.appearanceType,
          templateId: formData.appearanceType === 'template' ? formData.selectedTemplate : undefined,
          voiceType: formData.voiceType,
          voiceSpeed: formData.voiceSpeed,
          voicePitch: formData.voicePitch,
          welcomeText: formData.welcomeText,
          voiceSource: formData.voiceSource
        };
        
        // 如果是上传图像模式，需要处理图像文件
        if (formData.appearanceType === 'upload' && fileList.value.length > 0) {
          if (fileList.value[0].originFileObj) {
            updateData.avatar = fileList.value[0].originFileObj;
          } else if (formData.uploadedImageUrl) {
            updateData.avatar_url = formData.uploadedImageUrl;
          }
        }
        
        // 如果是自定义声音，需要处理声音文件
        if (formData.voiceSource === 'custom' && formData.customVoiceFile) {
          updateData.customVoice = formData.customVoiceFile;
        } else if (formData.voiceSource === 'custom' && formData.customVoiceUrl) {
          updateData.custom_voice_url = formData.customVoiceUrl;
        }
        
        // 调用API保存更改
        const result = await updateDigitalHuman(digitalHumanId.value, updateData);
        
        // 更新成功
        message.success({ content: '数字人更新成功', key: 'saving' });
        
        // 保存更新后的原始数据，以便重新比较
        Object.assign(originalData, JSON.parse(JSON.stringify(formData)));
        
        // 更新数字人列表（如果在store中）
        digitalHumanStore.fetchDigitalHumans();
        
        isSaving.value = false;
        
        // 询问是否返回列表
        Modal.confirm({
          title: '更新成功',
          content: '数字人已成功更新，是否返回数字人列表？',
          okText: '返回列表',
          cancelText: '继续编辑',
          onOk: () => {
            router.push('/digital-human/app');
          }
        });
      } catch (error) {
        console.error('保存数字人更改失败:', error);
        message.error({ content: `更新失败: ${error.message || '未知错误'}`, key: 'saving' });
        isSaving.value = false;
      }
    };
    
    // 重置更改
    const resetChanges = () => {
      Modal.confirm({
        title: '确认重置',
        content: '确定要放弃所有更改，恢复到初始状态吗？',
        okText: '确认重置',
        cancelText: '取消',
        onOk: () => {
          // 重置表单数据为原始数据
          Object.keys(originalData).forEach(key => {
            // 如果是数组，需要深拷贝
            if (Array.isArray(originalData[key])) {
              formData[key] = [...originalData[key]];
            } else {
              formData[key] = originalData[key];
            }
          });
          
          // 重置文件列表
          if (originalData.appearanceType === 'upload' && originalData.uploadedImageUrl) {
            fileList.value = [{
              uid: '-1',
              name: 'avatar.png',
              status: 'done',
              url: originalData.uploadedImageUrl
            }];
          } else {
            fileList.value = [];
          }
          
          // 重置音频文件列表
          if (originalData.voiceSource === 'custom' && originalData.customVoiceUrl) {
            audioFileList.value = [{
              uid: '-1',
              name: 'voice.mp3',
              status: 'done',
              url: originalData.customVoiceUrl
            }];
          } else {
            audioFileList.value = [];
          }
          
          message.success('已重置所有更改');
        }
      });
    };
    
    // 取消编辑
    const confirmCancel = () => {
      if (hasChanges.value) {
        Modal.confirm({
          title: '确认取消',
          content: '您有未保存的更改，确定要离开吗？',
          okText: '确认离开',
          cancelText: '继续编辑',
          onOk: () => {
            router.push('/digital-human/app');
          }
        });
      } else {
        router.push('/digital-human/app');
      }
    };
    
    // 获取预览图像URL
    const getPreviewImageUrl = () => {
      if (formData.appearanceType === 'upload' && formData.uploadedImageUrl) {
        return formData.uploadedImageUrl;
      } else if (formData.appearanceType === 'template' && formData.selectedTemplate) {
        const selectedTemplate = templates.find(t => t.id === formData.selectedTemplate);
        return selectedTemplate ? selectedTemplate.imageUrl : '';
      }
      return getStaticResourceUrl('/images/digital-human/avatar-placeholder.png');
    };
    
    // 辅助函数 - 获取类型文本
    const getTypeText = (type) => {
      const typeMap = {
        assistant: '智能助手',
        customer_service: '客服专员',
        broadcaster: '主播讲解员',
        teacher: '教育培训'
      };
      return typeMap[type] || '未知类型';
    };
    
    // 辅助函数 - 获取性别文本
    const getGenderText = (gender) => {
      const genderMap = {
        male: '男性',
        female: '女性',
        other: '其他'
      };
      return genderMap[gender] || '未知';
    };
    
    // 辅助函数 - 获取声音类型文本
    const getVoiceTypeText = (voiceType) => {
      const voiceTypeMap = {
        standard: '标准音色',
        professional: '专业播音',
        warm: '温暖亲切',
        clear: '清晰明快'
      };
      return voiceTypeMap[voiceType] || '标准';
    };
    
    // 辅助函数 - 获取语速文本
    const getSpeedText = (speed) => {
      if (speed <= 0.7) return '较慢';
      if (speed <= 0.9) return '正常偏慢';
      if (speed <= 1.1) return '正常';
      if (speed <= 1.5) return '较快';
      return '快速';
    };
    
    // 辅助函数 - 获取字段标签
    const getFieldLabel = (field) => {
      const labelMap = {
        name: '名称',
        type: '类型',
        gender: '性别',
        scenario: '应用场景',
        description: '描述',
        appearanceType: '外观类型',
        selectedTemplate: '模板',
        voiceType: '声音类型',
        voiceSpeed: '语速',
        voicePitch: '音调',
        welcomeText: '欢迎语',
        voiceSource: '声音来源'
      };
      return labelMap[field] || field;
    };
    
    // 辅助函数 - 格式化字段值
    const formatFieldValue = (field, value) => {
      if (field === 'scenario' && Array.isArray(value)) {
        return value.length === 0 ? '无' : value.join(', ');
      }
      
      if (field === 'type') {
        return getTypeText(value);
      }
      
      if (field === 'gender') {
        return getGenderText(value);
      }
      
      if (field === 'voiceType') {
        return getVoiceTypeText(value);
      }
      
      if (field === 'appearanceType') {
        return value === 'template' ? '预设模板' : '上传图像';
      }
      
      if (field === 'voiceSource') {
        return value === 'preset' ? '预设声音' : '自定义声音';
      }
      
      if (value === undefined || value === null) {
        return '无';
      }
      
      return value.toString();
    };
    
    // 生成描述
    const generateDescription = () => {
      // 简单版，实际实现可能需要调用API
      isGeneratingDesc.value = true;
      
      setTimeout(() => {
        const typeText = getTypeText(formData.type);
        const scenarios = formData.scenario.map(s => s).join('、');
        const genderText = getGenderText(formData.gender);
        
        // 根据已有信息生成描述
        let description = '';
        
        if (formData.type === 'assistant') {
          description = `这是一位专业的${genderText}${typeText}，`;
          description += scenarios ? `主要服务于${scenarios}等领域，` : '可适应多种应用场景，';
          description += '能够提供智能对话、信息查询和任务协助等功能，具有亲和力和专业素养。';
        } else if (formData.type === 'customer_service') {
          description = `这是一位专业的${genderText}${typeText}，`;
          description += scenarios ? `专注于${scenarios}等场景的客户服务，` : '致力于提供优质的客户服务体验，';
          description += '能够高效处理用户咨询、投诉和建议，服务态度热情友好，专业知识丰富。';
        } else if (formData.type === 'broadcaster') {
          description = `这是一位富有活力的${genderText}${typeText}，`;
          description += scenarios ? `主要在${scenarios}等场合进行内容呈现，` : '能够生动呈现各类内容，';
          description += '表达流畅自然，形象亲切，擅长吸引观众注意力并传递关键信息。';
        } else if (formData.type === 'teacher') {
          description = `这是一位专业的${genderText}${typeText}，`;
          description += scenarios ? `主要在${scenarios}等领域提供教育培训服务，` : '能够提供多领域的教育培训服务，';
          description += '讲解清晰明了，善于引导学习，具备丰富的专业知识和教学经验。';
        }
        
        formData.description = description;
        isGeneratingDesc.value = false;
        message.success('描述生成成功');
      }, 1500);
    };
    
    // 生成欢迎语
    const generateWelcomeText = () => {
      if (!formData.name) {
        message.warning('请先填写数字人名称，以便生成更个性化的欢迎语');
        return;
      }
      
      isGeneratingWelcome.value = true;
      
      // 模拟AI生成过程
      setTimeout(() => {
        const typeText = getTypeText(formData.type);
        const name = formData.name;
        
        // 根据数字人类型生成欢迎语
        let welcomeText = '';
        
        if (formData.type === 'assistant') {
          welcomeText = `您好，我是${name}，您的智能助手。有任何问题或需求，随时告诉我，我将竭诚为您服务。`;
        } else if (formData.type === 'customer_service') {
          welcomeText = `您好，我是${name}，很高兴为您提供服务。请问有什么可以帮助您的吗？`;
        } else if (formData.type === 'broadcaster') {
          welcomeText = `大家好！我是${name}，欢迎来到我们的频道。今天我将为大家带来精彩的内容，让我们一起开始吧！`;
        } else if (formData.type === 'teacher') {
          welcomeText = `各位同学好，我是${name}，欢迎参加今天的课程。希望在接下来的学习中，我们能共同进步。`;
        } else {
          welcomeText = `您好，我是${name}。很高兴认识您，有什么我可以帮助的吗？`;
        }
        
        formData.welcomeText = welcomeText;
        isGeneratingWelcome.value = false;
        message.success('欢迎语生成成功');
        
        // 自动播放欢迎语预览
        setTimeout(() => {
          playWelcomePreview();
        }, 500);
      }, 1500);
    };
    
    // 返回上一页
    const goBack = () => {
      confirmCancel();
    };
    
    return {
      // 状态
      currentStep,
      steps,
      formData,
      loading,
      isSaving,
      isSpeakingPreview,
      isPlayingWelcome,
      isTestingVoice,
      isGeneratingDesc,
      isGeneratingWelcome,
      fileList,
      audioFileList,
      audioPlayer,
      previewVisible,
      previewImage,
      previewTitle,
      templates,
      changesDetail,
      hasChanges,
      
      // 方法
      nextStep,
      prevStep,
      jumpToStep,
      selectTemplate,
      beforeUpload,
      handleUpload,
      handlePreview,
      handleCancelPreview,
      handleRemove,
      beforeAudioUpload,
      handleAudioUpload,
      handleAudioRemove,
      previewVoice,
      testVoice,
      playWelcomePreview,
      saveChanges,
      resetChanges,
      confirmCancel,
      getPreviewImageUrl,
      getTypeText,
      getGenderText,
      getVoiceTypeText,
      getSpeedText,
      getFieldLabel,
      formatFieldValue,
      generateDescription,
      generateWelcomeText,
      goBack
    };
  }
});
</script>

<style scoped>
/* 基础样式 */
.digital-human-edit {
  padding: 24px;
  min-height: calc(100vh - 120px);
  background: linear-gradient(135deg, #111827, #1f2937);
  color: #f9fafb;
}

/* 加载容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  width: 100%;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.back-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #e5e7eb;
  margin-right: 16px;
  transition: color 0.3s;
}

.back-button:hover {
  color: #60a5fa;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  color: #f9fafb;
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 沉浸式布局容器 */
.immersive-creation-container {
  display: flex;
  background: rgba(31, 41, 55, 0.7);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: calc(100vh - 180px);
}

/* 左侧步骤导航 */
.steps-navigation {
  width: 280px;
  background: rgba(17, 24, 39, 0.7);
  padding: 24px 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.steps-nav-inner {
  flex: 1;
}

.step-nav-item {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  margin-bottom: 4px;
}

.step-nav-item:hover {
  background: rgba(31, 41, 55, 0.5);
}

.step-nav-item.active {
  background: rgba(59, 130, 246, 0.15);
  border-left-color: #3b82f6;
}

.step-nav-item.completed {
  border-left-color: #10b981;
}

.step-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 16px;
  color: #60a5fa;
}

.step-nav-item.active .step-icon {
  background: #3b82f6;
  color: white;
}

.step-nav-item.completed .step-icon {
  background: #10b981;
  color: white;
}

.step-info {
  flex: 1;
}

.step-title {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
  color: #f9fafb;
}

.step-desc {
  font-size: 12px;
  color: #9ca3af;
}

.steps-action {
  padding: 16px 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
}

.prev-btn,
.next-btn {
  min-width: 90px;
}

/* 主内容区域 */
.main-content-area {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.content-edit-area {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
}

.edit-container {
  max-width: 600px;
  margin: 0 auto;
}

.step-container {
  animation: fadeIn 0.5s ease;
}

.step-title {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #f9fafb;
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.step-description {
  color: #9ca3af;
  margin-bottom: 32px;
  font-size: 15px;
}

/* 预览区域 */
.preview-area {
  width: 320px;
  padding: 32px 0;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(17, 24, 39, 0.5);
  overflow-y: auto;
}

.preview-container {
  padding: 0 24px;
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #f9fafb;
  text-align: center;
}

.preview-content {
  background: rgba(31, 41, 55, 0.7);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-avatar-container {
  position: relative;
  height: 280px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to bottom, rgba(17, 24, 39, 0.8), rgba(31, 41, 55, 0.8));
}

.preview-avatar {
  max-width: 100%;
  max-height: 280px;
  object-fit: contain;
}

.preview-speaking-indicator {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: flex-end;
  height: 20px;
  gap: 4px;
  padding: 4px 12px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
}

.preview-speaking-indicator span {
  display: inline-block;
  width: 4px;
  height: 10px;
  background: #3b82f6;
  border-radius: 2px;
  animation: soundWave 1s infinite;
}

.preview-speaking-indicator span:nth-child(2) {
  animation-delay: 0.3s;
}

.preview-speaking-indicator span:nth-child(3) {
  animation-delay: 0.6s;
}

.preview-details {
  padding: 20px;
}

.preview-name {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #f9fafb;
}

.preview-type-tag {
  display: inline-block;
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  padding: 2px 10px;
  border-radius: 12px;
  font-size: 12px;
  margin-bottom: 12px;
}

.preview-description {
  color: #9ca3af;
  font-size: 14px;
  margin-bottom: 16px;
  line-height: 1.5;
}

.preview-attrs {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
}

.attr-item {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(31, 41, 55, 0.5);
  padding: 4px 10px;
  border-radius: 6px;
}

.attr-icon {
  font-size: 14px;
  color: #60a5fa;
}

.attr-value {
  font-size: 14px;
  color: #d1d5db;
}

.preview-welcome-text {
  margin-top: 16px;
  padding: 12px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  border-left: 3px solid #3b82f6;
}

.preview-welcome-text p {
  margin: 0;
  color: #d1d5db;
  font-style: italic;
  font-size: 14px;
  line-height: 1.5;
}

.preview-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 表单自定义样式 */
.custom-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.custom-segment {
  margin-bottom: 24px;
}

.voice-radio-group {
  width: 100%;
}

.voice-option-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  width: 100%;
}

.voice-option {
  width: 100%;
  height: 100%;
  margin-right: 0;
}

.voice-option :deep(.ant-radio) {
  top: 14px;
  left: 14px;
}

.voice-option-content {
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 16px;
  margin-left: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid transparent;
  transition: all 0.3s ease;
}

.voice-option :deep(.ant-radio-checked) + .voice-option-content {
  background: rgba(59, 130, 246, 0.15);
  border-color: #3b82f6;
}

.voice-icon {
  font-size: 20px;
  color: #60a5fa;
}

.voice-info {
  flex: 1;
}

.voice-name {
  font-weight: 500;
  margin-bottom: 4px;
  color: #f9fafb;
}

.voice-desc {
  font-size: 12px;
  color: #9ca3af;
}

/* 表单元素样式覆盖 */
:deep(.ant-form-item-label > label) {
  color: #d1d5db;
}

:deep(.ant-input), 
:deep(.ant-input-affix-wrapper),
:deep(.ant-select-selector) {
  background: rgba(31, 41, 55, 0.5) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
  color: #f9fafb !important;
}

:deep(.ant-input::placeholder) {
  color: #6b7280;
}

:deep(.ant-select-selection-placeholder) {
  color: #6b7280 !important;
}

:deep(.ant-select-arrow) {
  color: #9ca3af;
}

:deep(.ant-radio-wrapper) {
  color: #d1d5db;
}

:deep(.ant-slider-rail) {
  background-color: rgba(31, 41, 55, 0.8);
}

:deep(.ant-slider-track) {
  background-color: #3b82f6;
}

:deep(.ant-slider-handle) {
  border-color: #3b82f6;
}

:deep(.ant-slider-mark-text) {
  color: #9ca3af;
}

/* 模板卡片 */
.appearance-options {
  text-align: center;
  margin-bottom: 24px;
}

.template-selection {
  margin-bottom: 24px;
}

.template-card {
  background: rgba(31, 41, 55, 0.5);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  height: 100%;
  border: 2px solid transparent;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.template-card.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.template-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.template-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s ease;
}

.template-selected-mark {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 28px;
  height: 28px;
  background-color: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

.template-info {
  padding: 16px;
}

.template-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #f9fafb;
}

.template-info p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #9ca3af;
  line-height: 1.5;
}

/* 上传区域 */
.upload-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.upload-main {
  text-align: center;
}

:deep(.ant-upload.ant-upload-select-picture-card) {
  background: rgba(31, 41, 55, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
  color: #d1d5db;
  width: 150px;
  height: 150px;
}

:deep(.ant-upload-list-picture-card-container) {
  width: 150px;
  height: 150px;
}

.upload-tips {
  background: rgba(31, 41, 55, 0.5);
  padding: 16px;
  border-radius: 8px;
  width: 100%;
  max-width: 500px;
  border-left: 3px solid #3b82f6;
}

.upload-tips h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #f9fafb;
}

.upload-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #d1d5db;
}

.upload-tips li {
  margin-bottom: 8px;
}

.summary-container {
  margin-bottom: 32px;
}

/* 更新和变更 */
.update-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
}

.changes-summary {
  margin-bottom: 24px;
}

.changes-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.change-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 12px;
}

.field-name {
  font-weight: 600;
  color: #f9fafb;
  margin-bottom: 8px;
}

.change-detail {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.old-value, .new-value {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.old-value .label {
  color: #9ca3af;
  min-width: 40px;
}

.new-value .label {
  color: #60a5fa;
  min-width: 40px;
}

.old-value .value {
  color: #9ca3af;
  flex: 1;
}

.new-value .value {
  color: #f9fafb;
  flex: 1;
}

.no-changes {
  text-align: center;
  padding: 24px;
  color: #9ca3af;
}

/* 其他元素样式 */
.ai-assist-button {
  margin-top: 8px;
  display: flex;
  align-items: center;
}

.field-tip {
  margin-left: 8px;
  color: #9ca3af;
  font-size: 12px;
}

.upload-hint {
  margin-top: 8px;
  color: #9ca3af;
  font-size: 12px;
}

.audio-player {
  width: 100%;
  margin-top: 16px;
}

.audio-preview {
  margin-top: 24px;
}

.preview-title {
  color: #f9fafb;
  font-size: 16px;
  margin-bottom: 8px;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes soundWave {
  0%, 100% {
    height: 6px;
  }
  50% {
    height: 14px;
  }
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .preview-area {
    width: 280px;
  }
}

@media (max-width: 992px) {
  .immersive-creation-container {
    flex-direction: column;
  }
  
  .steps-navigation {
    width: 100%;
    padding: 16px;
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .step-nav-item {
    display: inline-flex;
    width: auto;
    border-left: none;
    border-bottom: 3px solid transparent;
    padding: 12px;
  }
  
  .step-nav-item.active {
    border-bottom-color: #3b82f6;
    border-left-color: transparent;
  }
  
  .step-nav-item.completed {
    border-bottom-color: #10b981;
    border-left-color: transparent;
  }
  
  .steps-nav-inner {
    display: flex;
    justify-content: space-between;
  }
  
  .step-icon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }
  
  .step-desc {
    display: none;
  }
  
  .main-content-area {
    flex-direction: column;
  }
  
  .preview-area {
    width: 100%;
    border-left: none;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 24px;
  }
  
  .preview-container {
    max-width: 600px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .digital-human-edit {
    padding: 16px;
  }
  
  .content-edit-area {
    padding: 24px 16px;
  }
  
  .steps-nav-inner {
    flex-wrap: wrap;
  }
  
  .step-nav-item {
    flex: 1;
    min-width: 80px;
  }
  
  .step-info {
    display: none;
  }
  
  .step-icon {
    margin-right: 0;
  }
  
  .voice-option-grid {
    grid-template-columns: 1fr;
  }
  
  .old-value, .new-value {
    flex-direction: column;
  }
}
</style> 