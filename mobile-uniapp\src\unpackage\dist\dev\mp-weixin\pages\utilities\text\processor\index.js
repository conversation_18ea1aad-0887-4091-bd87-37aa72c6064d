"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  const _component_u_radio = common_vendor.resolveComponent("u-radio");
  const _component_u_radio_group = common_vendor.resolveComponent("u-radio-group");
  const _component_u_checkbox = common_vendor.resolveComponent("u-checkbox");
  const _component_u_button = common_vendor.resolveComponent("u-button");
  (_component_u_icon + _component_u_radio + _component_u_radio_group + _component_u_checkbox + _component_u_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props, { expose: __expose }) {
    const selectedTool = common_vendor.ref("format");
    const inputText = common_vendor.ref("");
    const processedText = common_vendor.ref("");
    const isProcessing = common_vendor.ref(false);
    const textHistory = common_vendor.ref([]);
    const formatOptions = common_vendor.ref({
      type: "capitalize",
      removeExtraSpaces: true,
      removeEmptyLines: false,
      trimLines: true
    });
    const convertOptions = common_vendor.ref({
      type: "markdown"
    });
    const replaceOptions = common_vendor.ref({
      findText: "",
      replaceText: "",
      caseSensitive: false,
      wholeWord: false,
      useRegex: false
    });
    const encodeOptions = common_vendor.ref({
      type: "base64",
      operation: "encode"
    });
    const textTools = common_vendor.ref([
      {
        id: "format",
        name: "格式化",
        icon: "text-format"
      },
      {
        id: "stats",
        name: "统计分析",
        icon: "chart-bar"
      },
      {
        id: "convert",
        name: "格式转换",
        icon: "swap"
      },
      {
        id: "replace",
        name: "查找替换",
        icon: "find-replace"
      },
      {
        id: "encode",
        name: "编码转换",
        icon: "code"
      }
    ]);
    const textStats = common_vendor.computed(() => {
      if (!inputText.value) {
        return { characters: 0, words: 0, lines: 0, paragraphs: 0 };
      }
      const text = inputText.value;
      const characters = text.length;
      const words = text.trim() ? text.trim().split(/\s+/).length : 0;
      const lines = text.split("\n").length;
      const paragraphs = text.split(/\n\s*\n/).filter((p) => p.trim()).length;
      return { characters, words, lines, paragraphs };
    });
    const detailedStats = common_vendor.computed(() => {
      if (!inputText.value) {
        return {
          characters: 0,
          charactersNoSpaces: 0,
          words: 0,
          sentences: 0,
          paragraphs: 0,
          readingTime: 0
        };
      }
      const text = inputText.value;
      const characters = text.length;
      const charactersNoSpaces = text.replace(/\s/g, "").length;
      const words = text.trim() ? text.trim().split(/\s+/).length : 0;
      const sentences = text.split(/[.!?]+/).filter((s) => s.trim()).length;
      const paragraphs = text.split(/\n\s*\n/).filter((p) => p.trim()).length;
      const readingTime = Math.ceil(words / 200);
      return {
        characters,
        charactersNoSpaces,
        words,
        sentences,
        paragraphs,
        readingTime
      };
    });
    const findMatches = common_vendor.computed(() => {
      if (!inputText.value || !replaceOptions.value.findText)
        return 0;
      try {
        let searchText = replaceOptions.value.findText;
        let flags = "g";
        if (!replaceOptions.value.caseSensitive) {
          flags += "i";
        }
        if (replaceOptions.value.useRegex) {
          const regex = new RegExp(searchText, flags);
          const matches = inputText.value.match(regex);
          return matches ? matches.length : 0;
        } else {
          if (replaceOptions.value.wholeWord) {
            searchText = `\\b${searchText}\\b`;
          }
          const regex = new RegExp(searchText, flags);
          const matches = inputText.value.match(regex);
          return matches ? matches.length : 0;
        }
      } catch (error) {
        return 0;
      }
    });
    common_vendor.onMounted(() => {
      loadTextHistory();
    });
    common_vendor.watch(inputText, () => {
      if (selectedTool.value === "stats")
        ;
    });
    const selectTool = (toolId) => {
      selectedTool.value = toolId;
      processedText.value = "";
    };
    const onTextInput = () => {
      processedText.value = "";
    };
    const importFromFile = () => {
      common_vendor.index.chooseFile({
        count: 1,
        type: "file",
        extension: [".txt", ".md", ".json", ".csv"],
        success: (res) => {
          common_vendor.index.showToast({
            title: "文件导入功能开发中",
            icon: "none"
          });
        }
      });
    };
    const pasteFromClipboard = () => {
      common_vendor.index.getClipboardData({
        success: (res) => {
          inputText.value = res.data;
          common_vendor.index.showToast({
            title: "粘贴成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "粘贴失败",
            icon: "error"
          });
        }
      });
    };
    const clearInput = () => {
      inputText.value = "";
      processedText.value = "";
    };
    const processText = async () => {
      if (!inputText.value)
        return;
      try {
        isProcessing.value = true;
        await new Promise((resolve) => setTimeout(resolve, 500));
        let result = "";
        switch (selectedTool.value) {
          case "format":
            result = formatText(inputText.value);
            break;
          case "convert":
            result = convertText(inputText.value);
            break;
          case "replace":
            result = replaceText(inputText.value);
            break;
          case "encode":
            result = encodeText(inputText.value);
            break;
          default:
            result = inputText.value;
        }
        processedText.value = result;
        const historyItem = {
          id: Date.now().toString(),
          tool: selectedTool.value,
          originalText: inputText.value,
          processedText: result,
          preview: inputText.value.substring(0, 50) + (inputText.value.length > 50 ? "..." : ""),
          createdAt: (/* @__PURE__ */ new Date()).toISOString()
        };
        textHistory.value.unshift(historyItem);
        saveTextHistory();
        common_vendor.index.showToast({
          title: "处理完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/utilities/text/processor/index.vue:534", "处理失败:", error);
        common_vendor.index.showToast({
          title: "处理失败",
          icon: "error"
        });
      } finally {
        isProcessing.value = false;
      }
    };
    const formatText = (text) => {
      let result = text;
      if (formatOptions.value.removeExtraSpaces) {
        result = result.replace(/\s+/g, " ");
      }
      if (formatOptions.value.removeEmptyLines) {
        result = result.replace(/\n\s*\n/g, "\n");
      }
      if (formatOptions.value.trimLines) {
        result = result.split("\n").map((line) => line.trim()).join("\n");
      }
      switch (formatOptions.value.type) {
        case "uppercase":
          result = result.toUpperCase();
          break;
        case "lowercase":
          result = result.toLowerCase();
          break;
        case "capitalize":
          result = result.charAt(0).toUpperCase() + result.slice(1).toLowerCase();
          break;
        case "title":
          result = result.replace(
            /\w\S*/g,
            (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
          );
          break;
      }
      return result;
    };
    const convertText = (text) => {
      switch (convertOptions.value.type) {
        case "markdown":
          return text.split("\n\n").map(
            (paragraph) => paragraph.trim() ? `${paragraph}
` : ""
          ).join("\n");
        case "html":
          return `<html>
<body>
${text.split("\n\n").map(
            (p) => p.trim() ? `<p>${p.replace(/\n/g, "<br>")}</p>` : ""
          ).join("\n")}
</body>
</html>`;
        case "json":
          const lines = text.split("\n").filter((line) => line.trim());
          return JSON.stringify({ lines }, null, 2);
        case "csv":
          return text.split("\n").map((line) => `"${line.replace(/"/g, '""')}"`).join("\n");
        default:
          return text;
      }
    };
    const replaceText = (text) => {
      if (!replaceOptions.value.findText)
        return text;
      try {
        let searchText = replaceOptions.value.findText;
        let flags = "g";
        if (!replaceOptions.value.caseSensitive) {
          flags += "i";
        }
        if (replaceOptions.value.useRegex) {
          const regex = new RegExp(searchText, flags);
          return text.replace(regex, replaceOptions.value.replaceText);
        } else {
          if (replaceOptions.value.wholeWord) {
            searchText = `\\b${searchText}\\b`;
          }
          const regex = new RegExp(searchText, flags);
          return text.replace(regex, replaceOptions.value.replaceText);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/utilities/text/processor/index.vue:636", "替换失败:", error);
        return text;
      }
    };
    const encodeText = (text) => {
      try {
        switch (encodeOptions.value.type) {
          case "base64":
            if (encodeOptions.value.operation === "encode") {
              return common_vendor.index.arrayBufferToBase64(new TextEncoder().encode(text));
            } else {
              return new TextDecoder().decode(common_vendor.index.base64ToArrayBuffer(text));
            }
          case "url":
            return encodeOptions.value.operation === "encode" ? encodeURIComponent(text) : decodeURIComponent(text);
          case "html":
            if (encodeOptions.value.operation === "encode") {
              return text.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#39;");
            } else {
              return text.replace(/&amp;/g, "&").replace(/&lt;/g, "<").replace(/&gt;/g, ">").replace(/&quot;/g, '"').replace(/&#39;/g, "'");
            }
          case "unicode":
            if (encodeOptions.value.operation === "encode") {
              return text.split("").map(
                (char) => "\\u" + char.charCodeAt(0).toString(16).padStart(4, "0")
              ).join("");
            } else {
              return text.replace(
                /\\u[\dA-Fa-f]{4}/g,
                (match) => String.fromCharCode(parseInt(match.replace("\\u", ""), 16))
              );
            }
          default:
            return text;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/utilities/text/processor/index.vue:699", "编码失败:", error);
        return text;
      }
    };
    const copyResult = () => {
      if (!processedText.value)
        return;
      common_vendor.index.setClipboardData({
        data: processedText.value,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        }
      });
    };
    const downloadResult = () => {
      if (!processedText.value)
        return;
      common_vendor.index.showToast({
        title: "下载功能开发中",
        icon: "none"
      });
    };
    const shareResult = () => {
      if (!processedText.value)
        return;
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 0,
        summary: processedText.value.substring(0, 100),
        success: () => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        }
      });
    };
    const getToolName = (toolId) => {
      const tool = textTools.value.find((t) => t.id === toolId);
      return (tool == null ? void 0 : tool.name) || "未知工具";
    };
    const getToolIcon = (toolId) => {
      const tool = textTools.value.find((t) => t.id === toolId);
      return (tool == null ? void 0 : tool.icon) || "text";
    };
    const getResultHeight = () => {
      const lines = processedText.value.split("\n").length;
      return Math.min(Math.max(lines * 40, 200), 600) + "rpx";
    };
    const getProcessedStats = () => {
      if (!processedText.value) {
        return { characters: 0, words: 0 };
      }
      const characters = processedText.value.length;
      const words = processedText.value.trim() ? processedText.value.trim().split(/\s+/).length : 0;
      return { characters, words };
    };
    const getChangePercent = () => {
      const original = textStats.value.characters;
      const processed = getProcessedStats().characters;
      if (original === 0)
        return 0;
      return Math.round((processed - original) / original * 100);
    };
    const formatTime = (time) => {
      return common_vendor.dayjs(time).format("MM-DD HH:mm");
    };
    const saveTextHistory = () => {
      common_vendor.index.setStorageSync("text_processing_history", textHistory.value);
    };
    const loadTextHistory = () => {
      const history = common_vendor.index.getStorageSync("text_processing_history") || [];
      textHistory.value = history;
    };
    const clearHistory = () => {
      common_vendor.index.showModal({
        title: "确认清空",
        content: "确定要清空所有处理历史吗？",
        success: (res) => {
          if (res.confirm) {
            textHistory.value = [];
            saveTextHistory();
            common_vendor.index.showToast({
              title: "清空成功",
              icon: "success"
            });
          }
        }
      });
    };
    const loadHistoryItem = (item) => {
      inputText.value = item.originalText;
      processedText.value = item.processedText;
      selectedTool.value = item.tool;
    };
    const onPullDownRefresh = () => {
      loadTextHistory();
      common_vendor.index.stopPullDownRefresh();
    };
    __expose({
      onPullDownRefresh
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(textTools.value, (tool, k0, i0) => {
          return {
            a: "7e6b32d9-0-" + i0,
            b: common_vendor.p({
              name: tool.icon,
              size: "18",
              color: selectedTool.value === tool.id ? "#2563EB" : "#6B7280"
            }),
            c: common_vendor.t(tool.name),
            d: tool.id,
            e: selectedTool.value === tool.id ? 1 : "",
            f: common_vendor.o(($event) => selectTool(tool.id), tool.id)
          };
        }),
        b: common_vendor.o(importFromFile),
        c: common_vendor.p({
          name: "file-text",
          size: "16",
          color: "#2563EB"
        }),
        d: common_vendor.o(pasteFromClipboard),
        e: common_vendor.p({
          name: "clipboard",
          size: "16",
          color: "#2563EB",
          ["margin-left"]: "8"
        }),
        f: common_vendor.o(clearInput),
        g: common_vendor.p({
          name: "close-circle",
          size: "16",
          color: "#EF4444",
          ["margin-left"]: "8"
        }),
        h: common_vendor.o([($event) => inputText.value = $event.detail.value, onTextInput]),
        i: inputText.value,
        j: common_vendor.t(textStats.value.characters),
        k: common_vendor.t(textStats.value.words),
        l: common_vendor.t(textStats.value.lines),
        m: common_vendor.t(textStats.value.paragraphs),
        n: inputText.value && selectedTool.value
      }, inputText.value && selectedTool.value ? common_vendor.e({
        o: common_vendor.t(getToolName(selectedTool.value)),
        p: selectedTool.value === "format"
      }, selectedTool.value === "format" ? {
        q: common_vendor.p({
          name: "capitalize",
          label: "首字母大写"
        }),
        r: common_vendor.p({
          name: "uppercase",
          label: "全部大写"
        }),
        s: common_vendor.p({
          name: "lowercase",
          label: "全部小写"
        }),
        t: common_vendor.p({
          name: "title",
          label: "标题格式"
        }),
        v: common_vendor.o(($event) => formatOptions.value.type = $event),
        w: common_vendor.p({
          modelValue: formatOptions.value.type
        }),
        x: common_vendor.o(($event) => formatOptions.value.removeExtraSpaces = $event),
        y: common_vendor.p({
          label: "移除多余空格",
          modelValue: formatOptions.value.removeExtraSpaces
        }),
        z: common_vendor.o(($event) => formatOptions.value.removeEmptyLines = $event),
        A: common_vendor.p({
          label: "移除空行",
          modelValue: formatOptions.value.removeEmptyLines
        }),
        B: common_vendor.o(($event) => formatOptions.value.trimLines = $event),
        C: common_vendor.p({
          label: "去除行首尾空格",
          modelValue: formatOptions.value.trimLines
        })
      } : {}, {
        D: selectedTool.value === "stats"
      }, selectedTool.value === "stats" ? {
        E: common_vendor.t(detailedStats.value.characters),
        F: common_vendor.t(detailedStats.value.charactersNoSpaces),
        G: common_vendor.t(detailedStats.value.words),
        H: common_vendor.t(detailedStats.value.sentences),
        I: common_vendor.t(detailedStats.value.paragraphs),
        J: common_vendor.t(detailedStats.value.readingTime)
      } : {}, {
        K: selectedTool.value === "convert"
      }, selectedTool.value === "convert" ? {
        L: common_vendor.p({
          name: "markdown",
          label: "转换为Markdown"
        }),
        M: common_vendor.p({
          name: "html",
          label: "转换为HTML"
        }),
        N: common_vendor.p({
          name: "json",
          label: "转换为JSON"
        }),
        O: common_vendor.p({
          name: "csv",
          label: "转换为CSV"
        }),
        P: common_vendor.o(($event) => convertOptions.value.type = $event),
        Q: common_vendor.p({
          modelValue: convertOptions.value.type
        })
      } : {}, {
        R: selectedTool.value === "replace"
      }, selectedTool.value === "replace" ? common_vendor.e({
        S: replaceOptions.value.findText,
        T: common_vendor.o(($event) => replaceOptions.value.findText = $event.detail.value),
        U: replaceOptions.value.replaceText,
        V: common_vendor.o(($event) => replaceOptions.value.replaceText = $event.detail.value),
        W: common_vendor.o(($event) => replaceOptions.value.caseSensitive = $event),
        X: common_vendor.p({
          label: "区分大小写",
          modelValue: replaceOptions.value.caseSensitive
        }),
        Y: common_vendor.o(($event) => replaceOptions.value.wholeWord = $event),
        Z: common_vendor.p({
          label: "全词匹配",
          modelValue: replaceOptions.value.wholeWord
        }),
        aa: common_vendor.o(($event) => replaceOptions.value.useRegex = $event),
        ab: common_vendor.p({
          label: "使用正则表达式",
          modelValue: replaceOptions.value.useRegex
        }),
        ac: replaceOptions.value.findText
      }, replaceOptions.value.findText ? {
        ad: common_vendor.t(findMatches.value)
      } : {}) : {}, {
        ae: selectedTool.value === "encode"
      }, selectedTool.value === "encode" ? {
        af: common_vendor.p({
          name: "base64",
          label: "Base64编码"
        }),
        ag: common_vendor.p({
          name: "url",
          label: "URL编码"
        }),
        ah: common_vendor.p({
          name: "html",
          label: "HTML实体编码"
        }),
        ai: common_vendor.p({
          name: "unicode",
          label: "Unicode编码"
        }),
        aj: common_vendor.o(($event) => encodeOptions.value.type = $event),
        ak: common_vendor.p({
          modelValue: encodeOptions.value.type
        }),
        al: common_vendor.p({
          name: "encode",
          label: "编码"
        }),
        am: common_vendor.p({
          name: "decode",
          label: "解码"
        }),
        an: common_vendor.o(($event) => encodeOptions.value.operation = $event),
        ao: common_vendor.p({
          modelValue: encodeOptions.value.operation
        })
      } : {}, {
        ap: selectedTool.value !== "stats"
      }, selectedTool.value !== "stats" ? {
        aq: common_vendor.t(isProcessing.value ? "处理中..." : "开始处理"),
        ar: common_vendor.o(processText),
        as: common_vendor.p({
          type: "primary",
          size: "large",
          loading: isProcessing.value,
          ["custom-style"]: {
            width: "100%"
          }
        })
      } : {}) : {}, {
        at: processedText.value
      }, processedText.value ? {
        av: common_vendor.o(copyResult),
        aw: common_vendor.p({
          name: "copy",
          size: "18",
          color: "#2563EB"
        }),
        ax: common_vendor.o(downloadResult),
        ay: common_vendor.p({
          name: "download",
          size: "18",
          color: "#2563EB",
          ["margin-left"]: "12"
        }),
        az: common_vendor.o(shareResult),
        aA: common_vendor.p({
          name: "share",
          size: "18",
          color: "#2563EB",
          ["margin-left"]: "12"
        }),
        aB: processedText.value,
        aC: getResultHeight(),
        aD: common_vendor.t(textStats.value.characters),
        aE: common_vendor.t(getProcessedStats().characters),
        aF: common_vendor.t(getChangePercent())
      } : {}, {
        aG: textHistory.value.length > 0
      }, textHistory.value.length > 0 ? {
        aH: common_vendor.o(clearHistory),
        aI: common_vendor.p({
          type: "text",
          text: "清空",
          size: "small",
          ["custom-style"]: {
            color: "#EF4444",
            fontSize: "24rpx"
          }
        }),
        aJ: common_vendor.f(textHistory.value.slice(0, 5), (item, k0, i0) => {
          return {
            a: "7e6b32d9-33-" + i0,
            b: common_vendor.p({
              name: getToolIcon(item.tool),
              size: "20",
              color: "#2563EB"
            }),
            c: common_vendor.t(getToolName(item.tool)),
            d: common_vendor.t(item.preview),
            e: common_vendor.t(formatTime(item.createdAt)),
            f: "7e6b32d9-34-" + i0,
            g: item.id,
            h: common_vendor.o(($event) => loadHistoryItem(item), item.id)
          };
        }),
        aK: common_vendor.p({
          name: "refresh",
          size: "16",
          color: "#6B7280"
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7e6b32d9"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pages/utilities/text/processor/index.js.map
