import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { resolve } from 'path'
import { networkInterfaces } from 'os'
import { fileURLToPath, URL } from 'node:url'
import 'isomorphic-fetch'

// 获取本地 IP 地址的函数
function getLocalIP() {
  const nets = networkInterfaces();
  const results = {};

  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      // 跳过非 IPv4 和内部 IP
      if (net.family === 'IPv4' && !net.internal) {
        if (!results[name]) {
          results[name] = [];
        }
        results[name].push(net.address);
      }
    }
  }

  // 尝试找到第一个有效的 IP
  for (const name in results) {
    if (results[name].length > 0) {
      return results[name][0];
    }
  }

  // 如果找不到，使用 localhost
  return 'localhost';
}

// 获取本地 IP
const localIP = getLocalIP();
console.log(`检测到本地 IP: ${localIP}`);

// 定义可能的后端API服务器地址，按优先级排序
const possibleApiHosts = [
  // 本地回环地址（总是可用）
  'localhost',
  // 当前机器的实际IP地址（动态检测）
  localIP,
  // 固定的内网IP地址（如果有）
  '*************'
];

console.log(`配置的API服务器候选地址: ${possibleApiHosts.join(', ')}`);

// https://vitejs.dev/config/
export default defineConfig({
  base: '/',
  plugins: [
    vue(),
    vueJsx(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      'vue': 'vue/dist/vue.esm-bundler.js',
      // 添加组件别名，确保可以通过多种路径导入
      'PageHeader': fileURLToPath(new URL('./src/components/PageHeader.vue', import.meta.url)),
      '@components': fileURLToPath(new URL('./src/components', import.meta.url)),
      '@views': fileURLToPath(new URL('./src/views', import.meta.url))
    },
    // 添加.vue扩展名优先解析
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
  },
  optimizeDeps: {
    // 排除vue-router以避免可能的循环依赖
    exclude: ['vue-router'],
    // 包含需要预构建的依赖
    include: ['vue', 'element-plus', 'axios'],
    // 确保.vue文件也被正确预构建
    entries: [
      './src/main.js',
      './src/components/PageHeader.vue',
      './src/views/resume/ResumeGenerator.vue'
    ]
  },
  esbuild: {
    jsxFactory: 'h',
    jsxFragment: 'Fragment',
    jsxInject: `import { h } from 'vue'`,
    target: 'es2020',
    loader: {
      '.js': 'jsx',
      '.vue': 'jsx'
    }
  },
  define: {
    'process.env': {},
    'import.meta.env.VITE_APP_BASE_URL': JSON.stringify('/'),
    'import.meta.env.VITE_API_BASE_URL': JSON.stringify('/api'),
    // 添加可用API主机列表供前端代码使用
    'import.meta.env.VITE_POSSIBLE_API_HOSTS': JSON.stringify(possibleApiHosts)
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: true,
    historyApiFallback: true,
    // 添加MIME类型修复中间件
    configureServer: (server) => {
      server.middlewares.use((req, res, next) => {
        // 检查是否是.vue文件请求
        if (req.url && req.url.endsWith('.vue')) {
          console.log(`[vite:middleware] 处理.vue文件请求: ${req.url}`);
          // 设置正确的MIME类型
          res.setHeader('Content-Type', 'application/javascript');
        }
        
        // 处理常见的MIME类型问题文件
        if (req.url && (
            req.url.includes('formatter') ||
            req.url.includes('auth.js') ||
            req.url.includes('format-utils')
        )) {
          console.log(`[vite:middleware] 处理MIME类型问题文件: ${req.url}`);
          res.setHeader('Content-Type', 'application/javascript');
        }
        
        next();
      });
    },
    proxy: {
      // 统一处理所有API请求
      '/api': {
        // 使用代理目标解析器，尝试连接不同的后端地址
        // 不写死target，而是由代理动态处理
        target: `http://localhost:8000`,
        changeOrigin: true,
        secure: false,
        // 禁用重定向跟随，确保使用透明代理
        followRedirects: false,
        // 允许WebSocket代理
        ws: true,
        // 添加代理请求处理器，可以动态修改请求目标
        // 禁用307临时重定向，使用透明代理直接转发请求
        configure: (proxy, options) => {
          console.log(`[vite:proxy] 初始化代理配置，默认目标: http://${localIP}:8000`);
          
          // 记录当前工作的API主机
          let workingApiHost = null;
          
          // 添加请求错误处理器
          proxy.on('error', (err, req, res) => {
            const currentTarget = req.originalUrl || req.url;
            console.error(`[vite:proxy] 请求错误: ${err.message}, 目标: ${currentTarget}`);
            
            // 如果是连接失败，尝试下一个可能的主机
            if (err.code === 'ECONNREFUSED' && !req._retried) {
              req._retried = true;
              
              // 确定当前尝试的主机
              const currentHostIndex = possibleApiHosts.findIndex(host => 
                options.target.includes(host));
              
              // 尝试下一个主机
              if (currentHostIndex >= 0 && currentHostIndex < possibleApiHosts.length - 1) {
                const nextHost = possibleApiHosts[currentHostIndex + 1];
                const newTarget = `http://${nextHost}:8000`;
                
                console.log(`[vite:proxy] 尝试下一个API主机: ${nextHost}`);
                
                // 创建新的代理请求到下一个主机
                const newOptions = { ...options, target: newTarget };
                proxy.web(req, res, newOptions);
                return;
              }
            }
            
            // 如果所有主机都失败，返回错误响应
            if (!res.headersSent) {
              res.writeHead(500, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ 
                error: 'API服务器连接失败',
                message: '所有可能的API服务器地址均无法连接，请检查后端服务是否正在运行。',
                hosts_tried: possibleApiHosts 
              }));
            }
          });
          
          // 添加代理请求完成处理器
          proxy.on('proxyRes', (proxyRes, req, res) => {
            // 记录成功的主机
            if (!workingApiHost && req.originalHost) {
              workingApiHost = req.originalHost;
              console.log(`[vite:proxy] 找到可用的API主机: ${workingApiHost}`);
            }
          });
          
          // 修改代理请求
          proxy.on('proxyReq', (proxyReq, req, res, options) => {
            // 在请求中记录原始主机
            req.originalHost = new URL(options.target).hostname;
            
            console.log(`[vite:proxy] 代理请求: ${req.url} -> ${options.target}${req.url}`);
            
            // 添加自定义请求头，帮助后端识别请求来源
            proxyReq.setHeader('X-Forwarded-Host', req.headers.host || 'unknown');
            proxyReq.setHeader('X-Client-IP', req.socket.remoteAddress || 'unknown');
          });
          
          // 监听WebSocket升级事件
          proxy.on('upgrade', (req, socket, head) => {
            console.log('[vite:proxy] 处理WebSocket升级请求');
            
            // 确保请求携带认证信息
            if (req.headers.authorization) {
              console.log('[vite:proxy] WebSocket请求包含授权头');
            } else {
              // 尝试从URL参数中提取token
              try {
                const url = req.url;
                if (url.includes('token=')) {
                  const tokenMatch = url.match(/[?&]token=([^&]+)/);
                  if (tokenMatch && tokenMatch[1]) {
                    req.headers.authorization = decodeURIComponent(tokenMatch[1]);
                    console.log('[vite:proxy] 从URL参数提取token作为授权头');
                  }
                }
              } catch (e) {
                console.warn(`[vite:proxy] 处理WebSocket授权失败: ${e.message}`);
              }
            }
          });
        },
        // 确保认证头正确传递
        headers: {
          Connection: 'keep-alive'
        },
        // 禁用重定向跟随，确保使用透明代理
        followRedirects: false,
        // 允许WebSocket代理
        ws: true,
        // 禁用307临时重定向，使用透明代理直接转发请求
        bypass: (req, res, options) => {
          console.log(`[vite:proxy] 请求: ${req.url}`);
          // 不执行重定向，返回null使请求继续由代理处理
          return null;
        },
        // 自定义body解析器，以便捕获并保存原始请求体
        onReq: (req, options) => {
          // 记录WebSocket请求
          if (req.headers && req.headers.upgrade && req.headers.upgrade.toLowerCase() === 'websocket') {
            console.log(`[vite:proxy] 检测到WebSocket连接请求: ${req.url}`);
            
            // 如果WebSocket URL中包含token参数，提取并作为授权头添加
            if (req.url && req.url.includes('token=')) {
              try {
                const urlObj = new URL(req.url, `http://${req.headers.host || 'localhost'}`);
                const token = urlObj.searchParams.get('token');
                if (token) {
                  // 设置授权头
                  req.headers.authorization = token;
                  console.log('[vite:proxy] 从WebSocket URL参数提取token到授权头');
                }
              } catch (e) {
                console.warn(`[vite:proxy] 解析WebSocket URL参数失败: ${e.message}`);
              }
            }
          }
          
          // 特殊处理术语API请求
          if (req.url && req.url.includes('/terminology')) {
            console.log(`[vite:proxy] 检测到术语API请求: ${req.url}`);
            
            // 确保请求包含认证头
            if (req.headers && req.headers.authorization) {
              console.log('[vite:proxy] 术语API请求包含认证头');
              
              // 检查URL中是否包含auth_token参数
              if (!req.url.includes('auth_token=')) {
                // 从认证头中提取token
                const authHeader = req.headers.authorization;
                const token = authHeader.replace(/^bearer\s+/i, '');
                
                // 添加auth_token参数到URL
                const separator = req.url.includes('?') ? '&' : '?';
                req.url = `${req.url}${separator}auth_token=${token}`;
                console.log(`[vite:proxy] 添加auth_token参数到术语API请求URL: ${req.url.replace(/auth_token=[^&]+/, 'auth_token=***')}`);
              }
            } else {
              console.warn('[vite:proxy] 术语API请求没有认证头，可能会被拒绝');
            }
          }
          
          if (req.method !== 'GET' && req.method !== 'HEAD') {
            let rawBody = [];
            req.on('data', (chunk) => {
              rawBody.push(chunk);
            });
            req.on('end', () => {
              rawBody = Buffer.concat(rawBody);
              req.rawBody = rawBody;
              console.log(`[vite:proxy] 捕获到原始请求体，长度: ${rawBody.length}`);
            });
          }
          
          // 检查URL参数中是否有auth_token，如果没有且有Authorization头，则添加
          if (req.url && req.headers.authorization) {
            const authHeader = req.headers.authorization;
            // 提取Bearer令牌
            const token = authHeader.replace('Bearer ', '');
            
            // 如果URL中不包含auth_token参数，则添加
            if (!req.url.includes('auth_token=')) {
              const separator = req.url.includes('?') ? '&' : '?';
              req.url = `${req.url}${separator}auth_token=${token}`;
              console.log(`[vite:proxy] 添加auth_token到URL: ${req.url}`);
            }
          }
        },
        configure: (proxy, _options) => {
          // 监听WebSocket升级事件
          proxy.on('upgrade', (req, socket, head) => {
            console.log('[vite:proxy] 处理WebSocket升级请求');
            
            // 确保请求携带认证信息
            if (req.headers.authorization) {
              console.log('[vite:proxy] WebSocket请求包含授权头');
            } else {
              // 尝试从URL参数中提取token
              try {
                const url = req.url;
                if (url.includes('token=')) {
                  const tokenMatch = url.match(/[?&]token=([^&]+)/);
                  if (tokenMatch && tokenMatch[1]) {
                    req.headers.authorization = decodeURIComponent(tokenMatch[1]);
                    console.log('[vite:proxy] 从URL参数提取token作为授权头');
                  }
                }
              } catch (e) {
                console.warn(`[vite:proxy] 处理WebSocket授权失败: ${e.message}`);
              }
            }
          });
          
          // 特殊处理术语API请求
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // 检查是否是术语API请求
            if (req.url && req.url.includes('/terminology')) {
              console.log(`[vite:proxy] 处理术语API代理请求: ${req.url}`);
              
              // 确保请求包含认证头
              if (req.headers && req.headers.authorization) {
                console.log('[vite:proxy] 确保术语API请求包含认证头');
                proxyReq.setHeader('Authorization', req.headers.authorization);
                
                // 从localStorage中获取token
                const getTokenFromStorage = () => {
                  try {
                    // 这段代码只在浏览器环境中执行
                    if (typeof localStorage !== 'undefined') {
                      return localStorage.getItem('auth_token') || localStorage.getItem('token');
                    }
                    return null;
                  } catch (e) {
                    console.warn('[vite:proxy] 无法从localStorage获取token:', e);
                    return null;
                  }
                };
                
                // 尝试获取token
                const token = getTokenFromStorage();
                if (token) {
                  console.log('[vite:proxy] 从localStorage获取到token');
                  proxyReq.setHeader('Authorization', `Bearer ${token}`);
                }
              }
            }
            
            // 特殊处理OPTIONS预检请求
            if (req.method === 'OPTIONS') {
              console.log('[vite:proxy] 处理OPTIONS预检请求:', req.url);
              
              // 确保保留认证头，用于后续的实际请求
              if (req.headers.authorization) {
                console.log('[vite:proxy] 预检请求包含授权头，确保转发');
                proxyReq.setHeader('Authorization', req.headers.authorization);
              }
              
              // 记录预检请求携带的授权请求头
              if (req.headers['access-control-request-headers']) {
                console.log('[vite:proxy] 预检请求头:', req.headers['access-control-request-headers']);
                
                // 确保Authorization头被允许
                if (!req.headers['access-control-request-headers'].toLowerCase().includes('authorization')) {
                  const originalHeaders = req.headers['access-control-request-headers'];
                  const enhancedHeaders = originalHeaders + ', authorization';
                  proxyReq.setHeader('access-control-request-headers', enhancedHeaders);
                  console.log('[vite:proxy] 添加authorization到预检请求头:', enhancedHeaders);
                }
              }
            }
            
            // 处理所有请求，确保授权头被正确传递
            if (req.headers.authorization) {
              proxyReq.setHeader('Authorization', req.headers.authorization);
              console.log('[vite:proxy] 转发授权头:', req.headers.authorization.substring(0, 15) + '...');
            }
            
            // 捕获请求体以便在重定向中重用
            if (req.method !== 'GET' && req.method !== 'HEAD') {
              let rawBody = [];
              req.on('data', (chunk) => {
                rawBody.push(chunk);
              });
              req.on('end', () => {
                rawBody = Buffer.concat(rawBody);
                req.rawBody = rawBody;
                console.log(`[vite:proxy] 捕获到请求体，长度: ${rawBody.length}`);
              });
            }
          });
          
          proxy.on('proxyRes', (proxyRes, req, res) => {
            const statusCode = proxyRes.statusCode;
            
            // 标记请求是否已被处理或正在处理
            if (req._isHandled || req._processing || req._responseEnded) {
              console.log('[vite:proxy] 请求已被处理或正在处理，跳过');
              return;
            }
            
            // 处理重定向响应
            if ([301, 302, 307, 308].includes(statusCode)) {
              const location = proxyRes.headers.location;
              console.log(`[vite:proxy] 拦截到重定向到: ${location}, 状态码: ${statusCode}`);
              
              // 强制拦截所有重定向，不允许浏览器自动处理，因为浏览器会丢失Authorization头
              console.log('[vite:proxy] 强制拦截重定向，由代理服务器处理');
              
              // 优化：从多个可能的来源获取授权头
              let authHeader = null;
              
              // 尝试从请求头中获取授权头（支持多种格式和大小写）
              if (req.headers.authorization) {
                authHeader = req.headers.authorization;
                console.log('[vite:proxy] 从请求头获取到授权:', authHeader.substring(0, 15) + '...');
              } else if (req.headers.Authorization) {
                authHeader = req.headers.Authorization;
                console.log('[vite:proxy] 从请求头(大写)获取到授权:', authHeader.substring(0, 15) + '...');
              } else if (req._authHeader) {
                authHeader = req._authHeader;
                console.log('[vite:proxy] 从缓存获取到授权:', authHeader.substring(0, 15) + '...');
              }
              
              // 如果仍然没有找到授权头，尝试从请求的其他部分获取
              if (!authHeader) {
                console.warn('[vite:proxy] 在请求头中未找到授权信息，尝试其他来源');
                
                // 尝试从URL查询参数中获取
                try {
                  // 原始请求URL
                  const originalUrl = req.url;
                  if (originalUrl) {
                    const urlObj = new URL(originalUrl, `http://${req.headers.host || 'localhost'}`);
                    // 检查多种可能的授权参数名
                    const possibleParams = ['auth_token', 'token', 'access_token', 'jwt'];
                    
                    for (const param of possibleParams) {
                      if (urlObj.searchParams.has(param)) {
                        const tokenFromUrl = urlObj.searchParams.get(param);
                        if (tokenFromUrl) {
                          authHeader = `Bearer ${tokenFromUrl}`;
                          console.log(`[vite:proxy] 从URL参数'${param}'中提取到token`);
                          break;
                        }
                      }
                    }
                  }
                } catch (e) {
                  console.warn(`[vite:proxy] 解析URL获取token失败: ${e.message}`);
                }
              }
              
              // 取消当前响应
              proxyRes.destroy();
              
              // 标记请求已被处理，防止重复处理
              req._isHandled = true;
              
              // 确保不管有没有找到授权头，都由代理处理重定向
              if (authHeader) {
                req._authHeader = authHeader; // 保存授权头，以便在后续处理中使用
                console.log(`[vite:proxy] 使用授权头处理重定向: ${authHeader.substring(0, 15)}...`);
              } else {
                console.warn('[vite:proxy] 无法找到授权头，将尝试无授权重定向');
              }
              
              // 递归跟随重定向，最多3次
              followRedirectWithAuth(location, req, res, authHeader || '', 0);
              
              return;
            } else {
              // 标记请求已被处理，防止重复处理
              req._isHandled = true;
              
              // 非重定向响应的处理
              console.log(`[vite:proxy] 处理普通响应，状态码: ${statusCode}`);
              
              // 添加必要的CORS头
              proxyRes.headers['Access-Control-Allow-Origin'] = req.headers.origin || 'http://localhost:3001';
              proxyRes.headers['Access-Control-Allow-Credentials'] = 'true';
              proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
              proxyRes.headers['Access-Control-Allow-Headers'] = 'Origin, Content-Type, Accept, Authorization, X-Requested-With';
              
              // 记录响应头，帮助调试
              console.log('[vite:proxy] 响应状态:', proxyRes.statusCode);
              console.log('[vite:proxy] 响应头部:', JSON.stringify(proxyRes.headers));
              
              // 如果是401错误，记录更多详细信息
              if (proxyRes.statusCode === 401) {
                console.log('[PROXY] 收到401响应，查看请求和响应细节');
                try {
                  // 打印请求信息但避免使用未定义的proxyReq
                  console.log(`[PROXY] 请求URL: ${req.url}`);
                  console.log(`[PROXY] 请求方法: ${req.method}`);
                  console.log(`[PROXY] 请求头: ${JSON.stringify(req.headers)}`);
                  
                  // 记录授权头状态
                  if (req.headers.authorization) {
                    console.log(`[PROXY] 请求包含授权头: ${req.headers.authorization.substring(0, 15)}...`);
                  } else {
                    console.log(`[PROXY] 请求不包含授权头!`);
                  }
                  
                  // 记录响应信息
                  console.log(`[PROXY] 响应状态码: ${proxyRes.statusCode}`);
                  console.log(`[PROXY] 响应头: ${JSON.stringify(proxyRes.headers)}`);
                } catch (error) {
                  console.error('[PROXY] 记录代理请求/响应信息时出错:', error);
                }
              }
            }
          });
          
          // 添加错误处理
          proxy.on('error', (err, req, res) => {
            console.error(`[vite:proxy] 代理错误: ${err.message}`);
            console.error(`[vite:proxy] 出错的请求路径: ${req.url}`);
            
            // 如果响应已经结束，跳过处理
            if (req._responseEnded) {
              console.log('[vite:proxy] 响应已结束，跳过错误处理');
              return;
            }
            
            // 检查是否是WebSocket请求
            const isWebSocket = req.headers && 
                               req.headers.upgrade && 
                               req.headers.upgrade.toLowerCase() === 'websocket';
                               
            // WebSocket错误处理与普通HTTP请求不同
            if (isWebSocket) {
              console.log('[vite:proxy] WebSocket连接错误，无法处理响应');
              // WebSocket连接错误无法发送HTTP响应，只记录日志
              return;
            }
            
            // 尝试处理HTTP请求错误，通知前端
            try {
              if (res && typeof res.writeHead === 'function' && !res.headersSent && !req._responseEnded) {
                res.writeHead(500, {
                  'Content-Type': 'application/json',
                  'Access-Control-Allow-Origin': req.headers.origin || 'http://localhost:3001',
                  'Access-Control-Allow-Credentials': 'true'
                });
                
                res.end(JSON.stringify({
                  code: 'PROXY_ERROR',
                  message: '代理服务器错误，无法连接后端API',
                  originalError: err.message,
                  url: req.url,
                  method: req.method
                }));
                
                // 标记响应已结束
                req._responseEnded = true;
              } else if (res && typeof res.end === 'function' && !req._responseEnded) {
                // 如果无法使用writeHead，尝试直接结束响应
                console.log('[vite:proxy] 使用备用方法结束响应');
                res.statusCode = 500;
                res.end(JSON.stringify({
                  code: 'PROXY_ERROR',
                  message: '代理服务器错误，无法连接后端API'
                }));
                req._responseEnded = true;
              } else {
                console.log('[vite:proxy] 无法发送错误响应，res对象不可用或已结束');
              }
            } catch (responseError) {
              console.error('[vite:proxy] 发送错误响应时遇到错误:', responseError);
            }
            
            console.error('[vite:proxy] 请求方法:', req.method);
            console.error('[vite:proxy] 请求路径:', req.url);
            console.error('[vite:proxy] 代理目标:', _options.target);
            console.error('[vite:proxy] 错误代码:', err.code);
            
            // 检查后端可用性
            const http = require('http');
            const pingReq = http.request({
              hostname: 'localhost',
              port: 8000,
              path: '/health',
              method: 'GET',
              timeout: 3000
            }, (pingRes) => {
              console.log(`[vite:proxy] 后端健康检查响应: ${pingRes.statusCode}`);
              pingRes.on('data', () => {});
              pingRes.on('end', () => {});
            });
            
            pingReq.on('error', (e) => {
              console.error(`[vite:proxy] 后端服务器不可达: ${e.message}`);
            });
            
            pingReq.on('timeout', () => {
              console.error('[vite:proxy] 后端健康检查超时');
              pingReq.destroy();
            });
            
            pingReq.end();
          });
          
          // 递归处理重定向，保留授权头
          function followRedirectWithAuth(location, req, res, authHeader, redirectCount) {
            const MAX_REDIRECTS = 3;
            
            // 如果请求已经被其他地方处理，直接返回
            if (req._responseEnded) {
              console.log('[vite:proxy] 响应已结束，终止重定向处理');
              return;
            }
            
            if (redirectCount >= MAX_REDIRECTS) {
              console.error(`[vite:proxy] 已达到最大重定向次数(${MAX_REDIRECTS})，放弃处理`);
              if (!res.headersSent && !req._responseEnded) {
                res.statusCode = 500;
                res.end(JSON.stringify({ 
                  error: '重定向次数过多', 
                  message: `已达到最大${MAX_REDIRECTS}次重定向限制` 
                }));
                req._responseEnded = true;
              }
              return;
            }
            
            // 检查URL中是否存在多个auth_token参数，这是一个常见问题来源
            if (location.includes('auth_token=')) {
              // 尝试通过正则表达式检测重复的auth_token参数
              const authTokenMatches = location.match(/auth_token=[^&]*/g);
              if (authTokenMatches && authTokenMatches.length > 1) {
                console.warn(`[vite:proxy] 检测到URL中存在${authTokenMatches.length}个auth_token参数，这可能导致认证问题`);
                
                // 尝试修复URL - 使用第一个auth_token
                try {
                  const urlObj = new URL(location);
                  // 获取所有auth_token值
                  const tokens = urlObj.searchParams.getAll('auth_token');
                  // 移除所有auth_token
                  urlObj.searchParams.delete('auth_token');
                  // 只添加第一个有效的token
                  if (tokens.length > 0) {
                    const firstValidToken = tokens.find(t => t && t.length > 10) || tokens[0];
                    urlObj.searchParams.set('auth_token', firstValidToken);
                    console.log('[vite:proxy] 已修复URL，只保留一个auth_token参数');
                    location = urlObj.toString();
                  }
                } catch (e) {
                  console.error(`[vite:proxy] 修复重复auth_token参数失败: ${e.message}`);
                }
              }
            }
            
            // 构建完整URL（如果location是相对路径）
            let redirectUrl = location;
            if (location.startsWith('/')) {
              // 确保重定向URL指向后端服务器
              redirectUrl = `http://localhost:8000${location}`;
            }
            
            // 添加请求日志以便调试
            const safeUrl = redirectUrl.replace(/auth_token=[^&]+/g, 'auth_token=***');
            console.log(`[vite:proxy] 重定向${redirectCount+1}: ${safeUrl}`);
            
            // 改进：更可靠地从授权头中提取token
            let token = '';
            if (authHeader) {
              // 提取token，支持多种格式
              if (authHeader.toLowerCase().startsWith('bearer ')) {
                token = authHeader.substring(7);
              } else if (authHeader.toLowerCase().startsWith('token ')) {
                token = authHeader.substring(6);
              } else if (authHeader.toLowerCase().startsWith('jwt ')) {
                token = authHeader.substring(4);
              } else {
                // 可能是直接的token
                token = authHeader;
              }
              
              if (token) {
                console.log(`[vite:proxy] 提取到认证令牌: ${token.substring(0, 10)}...`);
              } else {
                console.warn('[vite:proxy] 授权头存在但无法提取有效token');
              }
            }
            
            // 确保token非空
            if (!token) {
              console.warn('[vite:proxy] 无法从授权头中提取有效token，尝试其他来源');
              
              // 尝试从URL中提取token
              try {
                const urlObj = new URL(redirectUrl);
                
                // 尝试多种可能的参数名
                const tokenParams = ['auth_token', 'token', 'access_token', 'jwt'];
                for (const param of tokenParams) {
                  if (urlObj.searchParams.has(param)) {
                    token = urlObj.searchParams.get(param);
                    if (token) {
                      console.log(`[vite:proxy] 从重定向URL的'${param}'参数中提取到token`);
                      break;
                    }
                  }
                }
              } catch (e) {
                console.warn(`[vite:proxy] 从重定向URL提取token失败: ${e.message}`);
              }
              
              // 如果仍然没有token，尝试备选方案
              if (!token && req.headers && req.headers.cookie) {
                console.log('[vite:proxy] 尝试从cookie中查找token信息');
                // 这里可以添加从cookie中提取token的逻辑
              }
            }
            
            // 在URL中添加auth_token参数（如果尚未包含）
            const hasToken = token && token.length > 0;
            try {
              // 解析URL以处理查询参数
              const urlObj = new URL(redirectUrl);
              
              // 首先移除URL中所有现有的auth_token参数，避免重复
              let hasExistingToken = false;
              const tokenParams = ['auth_token', 'token', 'access_token', 'jwt'];
              
              // 检查并记录是否已有token参数
              for (const param of tokenParams) {
                if (urlObj.searchParams.has(param)) {
                  hasExistingToken = true;
                  console.log(`[vite:proxy] URL中已存在${param}参数，将移除以避免重复`);
                  urlObj.searchParams.delete(param);
                }
              }
              
              // 首先检查URL中是否已经包含auth_token
              if (hasToken) {
                urlObj.searchParams.set('auth_token', token);
                console.log('[vite:proxy] 添加/更新auth_token参数到URL');
                
                // 记录更新前和更新后的URL，隐藏token信息
                const beforeUrl = redirectUrl.replace(/auth_token=[^&]+/g, 'auth_token=***');
                // 使用格式化后的URL
                redirectUrl = urlObj.toString();
                const afterUrl = redirectUrl.replace(/auth_token=[^&]+/g, 'auth_token=***');
                
                console.log(`[vite:proxy] URL更新: 
                  之前: ${beforeUrl}
                  之后: ${afterUrl}`);
              }
            } catch (e) {
              console.error(`[vite:proxy] 解析重定向URL出错: ${e.message}, URL: ${redirectUrl}`);
              // 使用更简单的方法添加token，但先移除已有的token
              if (hasToken) {
                // 先移除可能存在的auth_token参数
                redirectUrl = redirectUrl.replace(/([?&])auth_token=[^&]*(&|$)/g, function(match, p1, p2) {
                  // 如果p2是&，保留它；如果是结尾，替换为空
                  return p2 === '&' ? p1 + p2 : p1;
                });
                
                // 同样处理其他可能的token参数
                const otherTokenParams = ['token', 'access_token', 'jwt'];
                for (const param of otherTokenParams) {
                  redirectUrl = redirectUrl.replace(new RegExp(`([?&])${param}=[^&]*(&|$)`, 'g'), function(match, p1, p2) {
                    return p2 === '&' ? p1 + p2 : p1;
                  });
                }
                
                // 确保URL格式正确，处理连续的&或者以?或&结尾的情况
                redirectUrl = redirectUrl.replace(/[?&]+$/, '');  // 移除末尾的?或&
                redirectUrl = redirectUrl.replace(/&&+/g, '&');   // 将多个连续的&替换为单个&
                
                // 如果没有参数但有?，移除?
                if (redirectUrl.endsWith('?')) {
                  redirectUrl = redirectUrl.slice(0, -1);
                }
                
                // 添加新的auth_token参数
                const separator = redirectUrl.includes('?') ? '&' : '?';
                redirectUrl = `${redirectUrl}${separator}auth_token=${encodeURIComponent(token)}`;
                console.log(`[vite:proxy] 使用简单方式更新token到URL: ${redirectUrl.replace(/auth_token=[^&]+/g, 'auth_token=***')}`);
              }
            }
            
            console.log(`[vite:proxy] 最终重定向URL: ${redirectUrl.replace(/auth_token=[^&]+/, 'auth_token=***')}`);
            
            // 准备请求头，确保包含授权头和其它必要头部
            const headers = { ...req.headers };
            
            // 处理授权策略 - 优先使用请求头授权，配合URL参数授权作为后备
            if (hasToken) {
              const normalizedAuthHeader = `Bearer ${token}`;
              
              // 设置使用哪种授权方式的策略
              const authStrategy = 'headers-only'; // 可选值: 'headers-only', 'dual', 'url-only'
              
              // 根据策略决定如何设置授权信息
              if (authStrategy === 'headers-only' || authStrategy === 'dual') {
                // 使用请求头方式
                headers['Authorization'] = normalizedAuthHeader;
                headers['authorization'] = normalizedAuthHeader;
                
                // 添加特殊头部以增加兼容性
                headers['X-Auth-Token'] = token;
                headers['x-auth-token'] = token;
                
                // 如果只使用请求头，则从URL中移除token
                if (authStrategy === 'headers-only' && redirectUrl.includes('auth_token=')) {
                  console.log('[vite:proxy] 只使用请求头授权，从URL中移除token参数');
                  try {
                    const urlWithoutToken = new URL(redirectUrl);
                    urlWithoutToken.searchParams.delete('auth_token');
                    redirectUrl = urlWithoutToken.toString();
                  } catch (e) {
                    // 如果URL解析失败，使用正则表达式
                    redirectUrl = redirectUrl.replace(/([?&])auth_token=[^&]*(&|$)/g, function(match, p1, p2) {
                      return p2 === '&' ? p1 + p2 : p1;
                    });
                    // 清理URL
                    redirectUrl = redirectUrl.replace(/[?&]+$/, '');
                  }
                }
              } else if (authStrategy === 'url-only') {
                // 只使用URL参数，移除请求头中的授权信息
                delete headers['Authorization'];
                delete headers['authorization'];
                console.log('[vite:proxy] 只使用URL参数授权，移除请求头授权');
              }
              
              // 输出日志，保护token隐私
              const authValue = token.length > 20 ? 
                            token.substring(0, 10) + '...' + token.substring(token.length - 5) : 
                            token;
              console.log(`[vite:proxy] 重定向请求授权策略: ${authStrategy}`);
              if (headers.Authorization) {
                console.log(`[vite:proxy] 使用请求头授权: Bearer ${authValue}`);
              }
              if (redirectUrl.includes('auth_token=')) {
                console.log(`[vite:proxy] 使用URL参数授权: auth_token=***`);
              }
            }
            
            // 删除可能导致问题的头部
            delete headers['host'];
            delete headers['content-length'];
            
            // 显式保留CORS相关头部
            headers['origin'] = req.headers.origin || 'http://localhost:3000';
            headers['referer'] = req.headers.referer || 'http://localhost:3000';
            headers['x-requested-with'] = 'XMLHttpRequest';
            
            // 使用更标准的http/https模块手动处理重定向
            try {
              // 创建请求配置
              const parsedUrl = new URL(redirectUrl);
              const options = {
                hostname: parsedUrl.hostname,
                port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
                path: parsedUrl.pathname + parsedUrl.search,
                method: req.method,
                headers: headers
              };
              
              // 根据协议选择http或https模块
              const httpModule = parsedUrl.protocol === 'https:' ? require('https') : require('http');
              
              // 详细记录请求信息，方便调试
              console.log(`[vite:proxy] 发送重定向请求:`, {
                url: redirectUrl.replace(/auth_token=[^&]+/, 'auth_token=***'),
                method: options.method,
                hostname: options.hostname,
                port: options.port,
                path: options.path.replace(/auth_token=[^&]+/, 'auth_token=***'),
                headers: {
                  ...options.headers,
                  Authorization: options.headers.Authorization ? 
                    options.headers.Authorization.substring(0, 15) + '...' : 
                    undefined
                }
              });
              
              // 创建请求
              const redirectReq = httpModule.request(options, (redirectRes) => {
                console.log(`[vite:proxy] 重定向响应状态码: ${redirectRes.statusCode}`);
                
                // 记录详细的响应头信息
                const importantHeaders = ['content-type', 'content-length', 'www-authenticate', 'location'];
                const headerInfo = {};
                importantHeaders.forEach(h => {
                  if (redirectRes.headers[h]) {
                    headerInfo[h] = redirectRes.headers[h];
                  }
                });
                console.log(`[vite:proxy] 重要响应头: ${JSON.stringify(headerInfo)}`);
                
                // 检查是否有进一步的重定向
                if ([301, 302, 307, 308].includes(redirectRes.statusCode)) {
                  const nextLocation = redirectRes.headers.location;
                  console.log(`[vite:proxy] 发现进一步重定向到: ${nextLocation}`);
                  
                  // 终止当前请求
                  redirectReq.abort();
                  
                  // 递归处理下一级重定向
                  followRedirectWithAuth(nextLocation, req, res, hasToken ? `Bearer ${token}` : '', redirectCount + 1);
                  return;
                }
                
                // 标记请求正在处理中，防止其他处理器干扰
                req._processing = true;
                
                // 特别处理401错误 - 说明认证失败
                if (redirectRes.statusCode === 401) {
                  console.warn('[vite:proxy] 重定向请求返回401 Unauthorized，认证失败');
                  console.warn('[vite:proxy] 认证头: ', hasToken ? 'Bearer ' + token.substring(0, 10) + '...' : '无');
                  console.warn('[vite:proxy] 尝试URL参数: auth_token=' + (hasToken ? token.substring(0, 10) + '...' : '无'));
                  
                  // 如果有WWW-Authenticate头，记录它
                  if (redirectRes.headers['www-authenticate']) {
                    console.warn('[vite:proxy] WWW-Authenticate头:', redirectRes.headers['www-authenticate']);
                  }
                }
                
                // 收集响应数据
                const chunks = [];
                redirectRes.on('data', (chunk) => {
                  chunks.push(chunk);
                });
                
                // 响应完成时发送数据
                redirectRes.on('end', () => {
                  // 确保响应未结束
                  if (req._responseEnded) {
                    console.log('[vite:proxy] 响应已经结束，跳过处理');
                    return;
                  }
                  
                  const responseBody = Buffer.concat(chunks);
                  console.log(`[vite:proxy] 收到重定向响应体，长度: ${responseBody.length}`);
                  
                  // 记录响应体内容用于调试（仅限小型响应）
                  if (responseBody.length <= 1024 && responseBody.length > 0) {
                    try {
                      const bodyStr = responseBody.toString('utf-8');
                      console.log(`[vite:proxy] 响应体内容: ${bodyStr}`);
                      
                      // 响应体内容分析
                      if (bodyStr === '[]' || bodyStr === '{}') {
                        console.warn('[vite:proxy] 接收到空数据响应，可能是后端API未正确处理认证');
                      } else if (bodyStr.includes('Not authenticated') || bodyStr.includes('token')) {
                        console.warn('[vite:proxy] 响应体中包含认证相关信息，可能存在认证问题');
                        
                        // 尝试解析JSON响应
                        try {
                          const jsonResponse = JSON.parse(bodyStr);
                          console.warn('[vite:proxy] 解析到的错误信息:', jsonResponse);
                        } catch (e) {
                          // 不是JSON格式
                        }
                      }
                    } catch (e) {
                      console.error(`[vite:proxy] 解析响应体时出错: ${e.message}`);
                    }
                  } else if (responseBody.length === 0) {
                    console.warn('[vite:proxy] 警告：收到空响应体');
                  }
                  
                  // 发送响应体，优化的处理流程
                  try {
                    if (res.finished) {
                      console.log('[vite:proxy] 响应已完成，无法发送响应体');
                      return;
                    }
                    
                    // 设置响应头（如果尚未设置）
                    if (!res.headersSent) {
                      // 确保包含所有需要的头部
                      const responseHeaders = {
                        ...redirectRes.headers,
                        'Content-Type': redirectRes.headers['content-type'] || 'application/json',
                        'Access-Control-Allow-Origin': req.headers.origin || '*',
                        'Access-Control-Allow-Credentials': 'true',
                        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                        'Access-Control-Allow-Headers': 'Origin, Content-Type, Accept, Authorization, X-Requested-With, X-Auth-Token'
                      };
                      
                      // 对特定错误进行处理
                      let finalStatusCode = redirectRes.statusCode;
                      
                      // 特殊处理401错误
                      if (redirectRes.statusCode === 401) {
                        console.warn('[vite:proxy] 重定向请求返回401 Unauthorized');
                        console.log('[vite:proxy] 调试信息:');
                        console.log('  - 请求URL:', redirectUrl.replace(/auth_token=[^&]+/g, 'auth_token=***'));
                        console.log('  - 授权头: ', hasToken ? '存在' : '不存在');
                        console.log('  - URL包含auth_token: ', redirectUrl.includes('auth_token='))
                        
                        // 如果是401错误但URL中包含了token，可能是后端不支持URL token
                        if (hasToken && redirectUrl.includes('auth_token=')) {
                          console.log('[vite:proxy] URL中有token但仍返回401，可能后端不支持URL参数认证');
                          
                          // 修改响应体，添加自定义信息
                          try {
                            let originalBody = {};
                            try {
                              if (responseBody.length > 0) {
                                originalBody = JSON.parse(responseBody.toString('utf-8'));
                              }
                            } catch (e) {
                              console.log('[vite:proxy] 无法解析401响应体为JSON');
                            }
                            
                            // 增加额外信息
                            const enhancedBody = {
                              ...originalBody,
                              _proxyInfo: {
                                originalStatus: 401,
                                tokenProvided: true,
                                urlToken: redirectUrl.includes('auth_token='),
                                headerToken: hasToken,
                                message: 'Token在URL中提供但后端返回401，前端应重试使用请求头认证'
                              }
                            };
                            
                            // 发送自定义响应，维持401状态码
                            const enhancedBodyBuffer = Buffer.from(JSON.stringify(enhancedBody));
                            res.writeHead(401, responseHeaders);
                            res.end(enhancedBodyBuffer);
                            req._responseEnded = true;
                            console.log('[vite:proxy] 发送增强的401响应');
                            return;
                          } catch (e) {
                            console.error('[vite:proxy] 创建增强错误响应失败:', e.message);
                          }
                        }
                      }
                      
                      // 发送原始响应
                      res.writeHead(finalStatusCode, responseHeaders);
                      console.log(`[vite:proxy] 响应头已设置，状态码: ${finalStatusCode}`);
                    }
                    
                    // 发送响应体并结束响应
                    res.end(responseBody);
                    req._responseEnded = true;
                    console.log(`[vite:proxy] 成功处理重定向请求并发送响应，长度: ${responseBody.length}`);
                  } catch (error) {
                    console.error(`[vite:proxy] 发送响应体时出错: ${error.message}`);
                    
                    // 紧急错误恢复 - 尝试发送错误响应
                    if (!req._responseEnded && !res.headersSent && !res.finished) {
                      try {
                        res.writeHead(500, {'Content-Type': 'application/json'});
                        res.end(JSON.stringify({
                          error: '重定向处理错误',
                          message: error.message
                        }));
                        req._responseEnded = true;
                      } catch (finalError) {
                        console.error(`[vite:proxy] 严重错误: ${finalError.message}`);
                      }
                    }
                  }
                });
              });
              
              // 错误处理
              redirectReq.on('error', (error) => {
                console.error(`[vite:proxy] 重定向请求错误: ${error.message}`);
                
                // 确保响应头还未发送且响应未结束
                if (!res.headersSent && !req._responseEnded) {
                  res.statusCode = 500;
                  res.end(JSON.stringify({ 
                    error: '处理重定向时出错',
                    message: error.message,
                    url: redirectUrl.replace(/auth_token=[^&]+/, 'auth_token=***')
                  }));
                  // 标记响应已结束
                  req._responseEnded = true;
                }
              });
              
              // 设置请求超时
              redirectReq.setTimeout(10000, () => {
                redirectReq.abort();
                console.error('[vite:proxy] 重定向请求超时');
                
                if (!res.headersSent && !req._responseEnded) {
                  res.statusCode = 504;
                  res.end(JSON.stringify({ error: '重定向请求超时' }));
                  req._responseEnded = true;
                }
              });
              
              // 如果有请求体数据，发送它
              if (req.method !== 'GET' && req.method !== 'HEAD' && req.rawBody) {
                const bodyBuffer = Buffer.isBuffer(req.rawBody) ? req.rawBody : Buffer.from(req.rawBody);
                console.log(`[vite:proxy] 发送请求体，大小: ${bodyBuffer.length} 字节`);
                redirectReq.setHeader('Content-Length', bodyBuffer.length);
                redirectReq.write(bodyBuffer);
              }
              
              // 结束请求
              redirectReq.end();
              
            } catch (error) {
              console.error(`[vite:proxy] 创建重定向请求时出错: ${error.message}`);
              
              // 确保响应头还未发送且响应未结束
              if (!res.headersSent && !req._responseEnded) {
                res.statusCode = 500;
                res.end(JSON.stringify({ 
                  error: '处理重定向时出错',
                  message: error.message,
                  url: redirectUrl.replace(/auth_token=[^&]+/, 'auth_token=***')
                }));
                // 标记响应已结束
                req._responseEnded = true;
              }
            }
          }
        }
      },
      // 添加静态资源代理规则
      '/static': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        // 增加此选项，防止代理自动跟随重定向
        followRedirects: false,
      },
      // 直接转发用户请求路径
      '/user': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        // 不需要重写路径，直接转发
        configure: (proxy, _options) => {
          console.log('[vite:proxy] 配置/user路径代理');
          
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // 调试日志
            console.log(`[vite:proxy] 处理/user请求: ${req.url}`);
            
            // 特别处理认证头
            if (req.headers.authorization) {
              // 确保authorization头格式正确（Bearer + token）
              let authHeader = req.headers.authorization;
              if (!authHeader.toLowerCase().startsWith('bearer ') && !authHeader.includes('emergency_test_token')) {
                authHeader = `Bearer ${authHeader}`;
              }
              
              // 设置认证头
              proxyReq.setHeader('Authorization', authHeader);
              
              console.log('[vite:proxy] 转发用户请求认证头:', authHeader.substring(0, 20) + '...');
            }
          });
        }
      },
      // 代理静态文件存储路径
      '/storage': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          console.log('[vite:proxy] 配置/storage路径代理');

          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log(`[vite:proxy] 处理/storage请求: ${req.url}`);
          });
        }
      },
      // 直接转发健康检查请求到根路径
      '/health': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      // 处理API代理请求
      '/api-proxy': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => {
          console.log(`[vite:proxy] 处理API代理请求: ${path}`);
          // 正确处理路径，去除api-proxy前缀，保留其他部分
          return path.replace(/^\/api-proxy\/?/, '/');
        }
      },
      // 处理直接的用户路径 /users/me 请求
      '/users': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      // 添加对TTS路径的直接代理
      '/tts': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          console.log('[vite:proxy] 配置/tts路径代理');
          
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // 调试日志
            console.log(`[vite:proxy] 处理/tts请求: ${req.url}`);
            
            // 特别处理认证头
            if (req.headers.authorization) {
              // 确保authorization头格式正确（Bearer + token）
              let authHeader = req.headers.authorization;
              if (!authHeader.toLowerCase().startsWith('bearer ') && !authHeader.includes('emergency_test_token')) {
                authHeader = `Bearer ${authHeader}`;
              }
              
              // 设置认证头
              proxyReq.setHeader('Authorization', authHeader);
              
              console.log('[vite:proxy] 转发TTS请求认证头:', authHeader.substring(0, 20) + '...');
            }
          });
        }
      },
      // 添加专门用于处理重定向的路径
      '/_redirect': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('[vite:proxy] 处理重定向请求:', req.url);
            
            // 从查询参数中获取目标URL和授权头
            const url = new URL(req.url, 'http://localhost:3000');
            const target = url.searchParams.get('target');
            const auth = url.searchParams.get('auth');
            
            if (!target) {
              res.statusCode = 400;
              res.end(JSON.stringify({ error: '缺少target参数' }));
              return;
            }
            
            // 设置授权头
            if (auth) {
              proxyReq.setHeader('Authorization', auth);
            }
            
            // 修改请求URL为目标URL
            const targetUrl = new URL(target);
            req.url = targetUrl.pathname + targetUrl.search;
            
            console.log(`[vite:proxy] 重定向请求URL修改为: ${req.url}`);
          });
        }
      }
    },
    watch: {
      usePolling: true
    },
    // 添加自定义中间件处理错误
    middlewares: [
      (req, res, next) => {
        // 检查是否是对Vue文件的直接请求
        const isVueFileRequest = req.url && (
          req.url.endsWith('.vue') || 
          req.url.includes('.vue?') || 
          req.url.includes('/src/views/')
        );
        
        if (isVueFileRequest) {
          console.warn(`[vite:server] 检测到对Vue源文件的直接请求: ${req.url}`);
          
          // 返回友好的错误响应，而不是500错误
          res.writeHead(400, {
            'Content-Type': 'application/json',
          });
          
          res.end(JSON.stringify({
            code: 'DIRECT_VUE_FILE_ACCESS',
            message: '无法直接访问Vue源文件，请通过正确的路由访问编译后的资源',
            url: req.url,
            solution: '请检查您的路由配置和组件导入路径',
            timestamp: new Date().toISOString()
          }));
          
          return; // 不继续处理请求
        }
        
        // 为所有响应添加错误处理逻辑
        const originalEnd = res.end;
        res.end = function() {
          // 检查是否是5xx错误响应
          if (res.statusCode >= 500) {
            console.error(`[vite:server] 服务器错误 ${res.statusCode}: ${req.url}`);
            
            // 记录错误详情以便调试
            console.error(`[vite:server] 请求方法: ${req.method}`);
            console.error(`[vite:server] 请求头: ${JSON.stringify(req.headers)}`);
            
            // 尝试打印请求体
            if (req.rawBody) {
              try {
                const bodyStr = req.rawBody.toString('utf8');
                console.error(`[vite:server] 请求体: ${bodyStr.substring(0, 200)}${bodyStr.length > 200 ? '...' : ''}`);
              } catch (e) {
                console.error(`[vite:server] 无法读取请求体: ${e.message}`);
              }
            }
          }
          
          // 调用原始的end方法
          return originalEnd.apply(this, arguments);
        };
        
        next();
      }
    ]
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    minify: 'esbuild',
    terserOptions: {
      compress: {
        drop_console: false,
        drop_debugger: false
      },
      keep_classnames: true,
      keep_fnames: true
    },
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      },
      output: {
        manualChunks(id) {
          if (id.includes('node_modules')) {
            return 'vendor';
          }
          if (id.includes('/src/components/')) {
            return 'components';
          }
          if (id.includes('/src/views/')) {
            return 'views';
          }
        }
      }
    },
    target: 'es2015',
    commonjsOptions: {
      transformMixedEsModules: true
    },
    assetsInlineLimit: 4096,
    cssCodeSplit: true,
    sourcemap: true
  }
}) 