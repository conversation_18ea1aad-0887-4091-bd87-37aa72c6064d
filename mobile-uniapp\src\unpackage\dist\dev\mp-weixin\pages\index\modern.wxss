/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 导入自定义样式变量 */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
.modern-home.data-v-86525b0d {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
  position: relative;
}
.status-bar.data-v-86525b0d {
  background: transparent;
}
.header-section.data-v-86525b0d {
  position: relative;
  padding-bottom: 40rpx;
}
.header-section .header-background.data-v-86525b0d {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  overflow: hidden;
}
.header-section .header-background .gradient-bg.data-v-86525b0d {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.header-section .header-background .pattern-overlay.data-v-86525b0d {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}
.header-section .header-content.data-v-86525b0d {
  position: relative;
  z-index: 2;
  padding: 40rpx 32rpx 0;
}
.header-section .header-content .user-area.data-v-86525b0d {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40rpx;
}
.header-section .header-content .user-area .user-info.data-v-86525b0d {
  display: flex;
  align-items: center;
}
.header-section .header-content .user-area .user-info .avatar-container.data-v-86525b0d {
  position: relative;
  margin-right: 24rpx;
}
.header-section .header-content .user-area .user-info .avatar-container .user-avatar.data-v-86525b0d {
  width: 96rpx;
  height: 96rpx;
  border-radius: 48rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}
.header-section .header-content .user-area .user-info .avatar-container .avatar-ring.data-v-86525b0d {
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 52rpx;
  animation: pulse-86525b0d 2s infinite;
}
.header-section .header-content .user-area .user-info .avatar-container .online-indicator.data-v-86525b0d {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 20rpx;
  height: 20rpx;
  background: #00D4AA;
  border: 3rpx solid #FFFFFF;
  border-radius: 50%;
}
.header-section .header-content .user-area .user-info .user-details .greeting.data-v-86525b0d {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}
.header-section .header-content .user-area .user-info .user-details .username.data-v-86525b0d {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: 12rpx;
}
.header-section .header-content .user-area .user-info .user-details .user-badge.data-v-86525b0d {
  display: flex;
  align-items: center;
  background: rgba(255, 215, 0, 0.2);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 215, 0, 0.3);
}
.header-section .header-content .user-area .user-info .user-details .user-badge .badge-text.data-v-86525b0d {
  font-size: 20rpx;
  color: #FFD700;
  margin-right: 6rpx;
  font-weight: 600;
}
.header-section .header-content .user-area .header-actions.data-v-86525b0d {
  display: flex;
  gap: 20rpx;
}
.header-section .header-content .user-area .header-actions .action-item.data-v-86525b0d {
  position: relative;
  width: 72rpx;
  height: 72rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.header-section .header-content .user-area .header-actions .action-item.data-v-86525b0d:active {
  transform: scale(0.95);
}
.header-section .header-content .user-area .header-actions .action-item .notification-dot.data-v-86525b0d {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 16rpx;
  height: 16rpx;
  background: #FF3B30;
  border-radius: 50%;
  border: 2rpx solid #FFFFFF;
}
.header-section .header-content .search-container .search-box.data-v-86525b0d {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 28rpx;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.header-section .header-content .search-container .search-box .search-text.data-v-86525b0d {
  flex: 1;
  font-size: 28rpx;
  color: #8E8E93;
  margin-left: 16rpx;
}
.header-section .header-content .search-container .search-box .voice-search.data-v-86525b0d {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header-section .header-content .search-container .search-box .voice-search.data-v-86525b0d:active {
  transform: scale(0.95);
}
.main-content.data-v-86525b0d {
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 0 0;
  margin-top: -32rpx;
  position: relative;
  z-index: 3;
}
.main-content .section-header.data-v-86525b0d {
  padding: 48rpx 32rpx 32rpx;
}
.main-content .section-header .section-title.data-v-86525b0d {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #1D1D1F;
  margin-bottom: 8rpx;
}
.main-content .section-header .section-subtitle.data-v-86525b0d {
  font-size: 28rpx;
  color: #8E8E93;
}
.main-content .section-header .section-link.data-v-86525b0d {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 500;
}
.ai-capabilities .capabilities-grid.data-v-86525b0d {
  padding: 0 32rpx;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}
.ai-capabilities .capabilities-grid .capability-card.data-v-86525b0d {
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.ai-capabilities .capabilities-grid .capability-card.data-v-86525b0d:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.12);
}
.ai-capabilities .capabilities-grid .capability-card .card-header.data-v-86525b0d {
  position: relative;
  margin-bottom: 24rpx;
}
.ai-capabilities .capabilities-grid .capability-card .card-header .card-icon.data-v-86525b0d {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ai-capabilities .capabilities-grid .capability-card .card-header .card-icon .iconfont.data-v-86525b0d {
  font-size: 32rpx;
  color: #FFFFFF;
}
.ai-capabilities .capabilities-grid .capability-card .card-header .card-badge.data-v-86525b0d {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  color: #FFFFFF;
  font-size: 18rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);
}
.ai-capabilities .capabilities-grid .capability-card .card-content.data-v-86525b0d {
  margin-bottom: 24rpx;
}
.ai-capabilities .capabilities-grid .capability-card .card-content .card-title.data-v-86525b0d {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 8rpx;
}
.ai-capabilities .capabilities-grid .capability-card .card-content .card-desc.data-v-86525b0d {
  display: block;
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
  margin-bottom: 16rpx;
}
.ai-capabilities .capabilities-grid .capability-card .card-content .card-stats.data-v-86525b0d {
  display: flex;
  justify-content: space-between;
}
.ai-capabilities .capabilities-grid .capability-card .card-content .card-stats .stat-item.data-v-86525b0d,
.ai-capabilities .capabilities-grid .capability-card .card-content .card-stats .stat-rating.data-v-86525b0d {
  font-size: 22rpx;
  color: #C7C7CC;
}
.ai-capabilities .capabilities-grid .capability-card .card-action.data-v-86525b0d {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.ai-capabilities .capabilities-grid .capability-card .card-action .action-text.data-v-86525b0d {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 500;
}
.smart-recommendations.data-v-86525b0d {
  margin-top: 48rpx;
}
.smart-recommendations .recommendations-scroll.data-v-86525b0d {
  white-space: nowrap;
}
.smart-recommendations .recommendations-scroll .recommendations-list.data-v-86525b0d {
  display: flex;
  padding: 0 32rpx;
  gap: 24rpx;
}
.smart-recommendations .recommendations-scroll .recommendations-list .recommendation-card.data-v-86525b0d {
  flex-shrink: 0;
  width: 320rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.smart-recommendations .recommendations-scroll .recommendations-list .recommendation-card .rec-image.data-v-86525b0d {
  position: relative;
  height: 180rpx;
}
.smart-recommendations .recommendations-scroll .recommendations-list .recommendation-card .rec-image image.data-v-86525b0d {
  width: 100%;
  height: 100%;
}
.smart-recommendations .recommendations-scroll .recommendations-list .recommendation-card .rec-image .rec-overlay.data-v-86525b0d {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.smart-recommendations .recommendations-scroll .recommendations-list .recommendation-card .rec-info.data-v-86525b0d {
  padding: 24rpx;
}
.smart-recommendations .recommendations-scroll .recommendations-list .recommendation-card .rec-info .rec-title.data-v-86525b0d {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 8rpx;
}
.smart-recommendations .recommendations-scroll .recommendations-list .recommendation-card .rec-info .rec-desc.data-v-86525b0d {
  display: block;
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 16rpx;
  line-height: 1.4;
}
.smart-recommendations .recommendations-scroll .recommendations-list .recommendation-card .rec-info .rec-meta.data-v-86525b0d {
  display: flex;
  justify-content: space-between;
}
.smart-recommendations .recommendations-scroll .recommendations-list .recommendation-card .rec-info .rec-meta .rec-category.data-v-86525b0d,
.smart-recommendations .recommendations-scroll .recommendations-list .recommendation-card .rec-info .rec-meta .rec-time.data-v-86525b0d {
  font-size: 22rpx;
  color: #C7C7CC;
}
.recent-usage.data-v-86525b0d {
  margin-top: 48rpx;
}
.recent-usage .section-header.data-v-86525b0d {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.recent-usage .recent-list.data-v-86525b0d {
  padding: 0 32rpx;
}
.recent-usage .recent-list .recent-item.data-v-86525b0d {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F2F2F7;
}
.recent-usage .recent-list .recent-item.data-v-86525b0d:last-child {
  border-bottom: none;
}
.recent-usage .recent-list .recent-item.data-v-86525b0d:active {
  background: #F2F2F7;
  margin: 0 -32rpx;
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
}
.recent-usage .recent-list .recent-item .recent-icon.data-v-86525b0d {
  width: 80rpx;
  height: 80rpx;
  background: #F2F2F7;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.recent-usage .recent-list .recent-item .recent-icon .iconfont.data-v-86525b0d {
  font-size: 28rpx;
  color: #8E8E93;
}
.recent-usage .recent-list .recent-item .recent-content.data-v-86525b0d {
  flex: 1;
}
.recent-usage .recent-list .recent-item .recent-content .recent-title.data-v-86525b0d {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #1D1D1F;
  margin-bottom: 6rpx;
}
.recent-usage .recent-list .recent-item .recent-content .recent-desc.data-v-86525b0d {
  display: block;
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}
.recent-usage .recent-list .recent-item .recent-content .recent-time.data-v-86525b0d {
  font-size: 22rpx;
  color: #C7C7CC;
}
.recent-usage .recent-list .recent-item .recent-action.data-v-86525b0d {
  padding: 16rpx;
}
.floating-action.data-v-86525b0d {
  position: fixed;
  bottom: 200rpx;
  right: 32rpx;
  z-index: 999;
}
.floating-action .fab-button.data-v-86525b0d {
  width: 112rpx;
  height: 112rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.floating-action .fab-button.data-v-86525b0d:active {
  transform: scale(0.95);
}
.floating-action .fab-ripple.data-v-86525b0d {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 112rpx;
  height: 112rpx;
  border: 2rpx solid rgba(102, 126, 234, 0.3);
  border-radius: 56rpx;
  animation: ripple-86525b0d 2s infinite;
}
.bottom-safe-area.data-v-86525b0d {
  height: calc(120rpx + env(safe-area-inset-bottom));
}
@keyframes pulse-86525b0d {
0%, 100% {
    opacity: 1;
    transform: scale(1);
}
50% {
    opacity: 0.8;
    transform: scale(1.05);
}
}
@keyframes ripple-86525b0d {
0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}
100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.5);
}
}
@media (prefers-color-scheme: dark) {
.modern-home.data-v-86525b0d {
    background: linear-gradient(180deg, #1c1c1e 0%, #000000 100%);
}
.main-content.data-v-86525b0d {
    background: #1c1c1e;
}
.capability-card.data-v-86525b0d,
.recommendation-card.data-v-86525b0d {
    background: #2c2c2e;
    border-color: #38383a;
}
.section-title.data-v-86525b0d {
    color: #ffffff !important;
}
.card-title.data-v-86525b0d,
.recent-title.data-v-86525b0d {
    color: #ffffff !important;
}
.recent-item.data-v-86525b0d {
    border-bottom-color: #38383a;
}
.recent-item.data-v-86525b0d:active {
    background: #38383a;
}
.recent-icon.data-v-86525b0d {
    background: #38383a;
}
}
@media screen and (max-width: 480px) {
.capabilities-grid.data-v-86525b0d {
    grid-template-columns: 1fr;
}
.user-area.data-v-86525b0d {
    flex-direction: column;
    align-items: flex-start;
    gap: 24rpx;
}
.header-actions.data-v-86525b0d {
    align-self: flex-end;
}
}