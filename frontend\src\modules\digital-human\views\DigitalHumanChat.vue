<template>
  <div class="digital-human-chat" :class="{'light-theme': settings?.theme === 'light'}">
    <!-- 顶部导航 -->
    <div class="chat-header">
      <div class="header-left">
        <div class="back-button" @click="goBack">
          <left-outlined /> <span class="back-text">返回</span>
        </div>
      </div>
      
      <div class="digital-human-info">
        <span class="info-name">{{ digitalHuman?.name || '数字人聊天' }}</span>
        <el-tag class="info-tag" :class="'tag-type-' + (digitalHuman?.type || '')" v-if="digitalHuman?.type">
          {{ getTypeText(digitalHuman?.type) }}
        </el-tag>
      </div>
      
      <div class="chat-actions">
        <el-tooltip content="实时视频对话">
          <el-button class="action-button realtime-chat" text circle @click="startRealtimeChat">
            <el-icon>
              <VideoCamera />
            </el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="主题切换">
          <el-button class="action-button theme-toggle" text circle @click="toggleTheme">
            <el-icon>
              <Sunny v-if="settings?.theme === 'light'" />
              <Moon v-else />
            </el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="查看详情">
          <el-button class="action-button" text circle @click="viewDigitalHumanDetails">
            <el-icon><InfoFilled /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="设置">
          <el-button class="action-button" text circle @click="toggleSettings">
            <el-icon><Setting /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>
    
    <!-- 调试信息已移除 -->

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载数字人...">
      <div style="height: 200px;"></div>
    </div>

    <!-- 主聊天区域 -->
    <div v-else class="chat-container" :class="{'compact-layout': isCompactLayout}">
      <!-- 左侧数字人区域 -->
      <div class="digital-human-container" :class="[{ 'mini-mode': miniMode }, 'dh-type-' + digitalHuman?.type]">
        <div class="resize-handle" @mousedown="startResize" v-if="!isCompactLayout"></div>
        <div class="dh-video-container">
          <!-- 数字人视频或图像 -->
          <template v-if="hasVideo">
            <video 
              ref="videoPlayer" 
              class="dh-video" 
              :src="digitalHuman.video_url" 
              autoplay 
              loop
              @canplay="onVideoReady"
              @error="onVideoError"
            ></video>
          </template>
          <template v-else>
            <div class="dh-avatar-container" :class="{'is-speaking': isSpeaking}">
              <img :src="digitalHuman?.avatar_url ?? defaultAvatar" class="dh-avatar" />
              <!-- 口型动画Canvas -->
              <canvas v-if="settings?.enableLipSync" ref="lipSyncCanvas" class="lip-sync-canvas" width="128" height="128"></canvas>
              <!-- 简单口型动画备用 -->
              <div v-else-if="isSpeaking" class="mouth-animation"></div>
              <div class="dh-speaking-indicator" v-if="isSpeaking">
                <span></span><span></span><span></span>
              </div>
            </div>
          </template>
          
          <!-- 错误状态 -->
          <div v-if="videoError" class="video-error">
            <el-icon class="error-icon"><Warning /></el-icon>
            <p class="error-message">视频加载失败</p>
            <el-button size="small" @click="retryVideo">重试</el-button>
          </div>
          
          <!-- 音频播放器 -->
          <audio ref="audioPlayer" class="dh-audio" :src="currentAudioUrl" @ended="onAudioEnded"></audio>
          
          <!-- 控制按钮 -->
          <div class="dh-controls">
            <el-button
              class="control-button"
              :type="settings?.theme === 'light' ? 'default' : 'primary'"
              circle
              size="large"
              @click="toggleMute"
            >
              <el-icon>
                <Mute v-if="isMuted" />
                <VideoPlay v-else />
              </el-icon>
            </el-button>
            <el-button
              class="control-button"
              :type="settings?.theme === 'light' ? 'default' : 'primary'"
              circle
              size="large"
              @click="toggleMiniMode"
            >
              <el-icon>
                <FullScreen v-if="miniMode" />
                <FullScreen v-else />
              </el-icon>
            </el-button>
            <el-button
              class="control-button layout-toggle"
              :type="settings?.theme === 'light' ? 'default' : 'primary'"
              circle
              size="large"
              @click="toggleLayout"
              v-if="isSmallScreen"
            >
              <el-icon>
                <Grid />
              </el-icon>
            </el-button>
          </div>
          
          <!-- 切换布局提示 -->
          <div class="layout-tip" v-if="isSmallScreen && isCompactLayout && !showChatArea">
            <p>点击 <column-width-outlined /> 按钮切换到聊天区域</p>
          </div>
        </div>
        
        <!-- 数字人详情 -->
        <div class="dh-details" v-if="!miniMode">
          <h3 class="dh-name">{{ digitalHuman?.name }}</h3>
          <p class="dh-description">{{ digitalHuman?.description }}</p>
          
          <!-- 分类特定功能区 -->
          <div class="type-specific-features" v-if="digitalHuman?.type">
            <component 
              :is="getTypeComponent(digitalHuman?.type)" 
              :digital-human="digitalHuman"
              @send-message="handleFeatureAction"
              v-if="digitalHuman?.type && getTypeComponent(digitalHuman?.type)"
            ></component>
          </div>
        </div>
      </div>
      
      <!-- 右侧聊天区域 -->
      <div class="chat-message-area always-show-on-desktop"
        :class="{'hidden-on-mobile': isSmallScreen && isCompactLayout && !showChatArea}"
      >
        <!-- 聊天消息列表 -->
        <div class="message-list" ref="messageList">
          <!-- 欢迎消息 -->
          <div class="welcome-message" v-if="messages?.length === 0">
            <div class="welcome-avatar">
              <img :src="digitalHuman?.avatar_url ?? defaultAvatar" alt="Avatar" />
            </div>
            <div class="welcome-content">
              <h3>欢迎与{{ digitalHuman?.name ?? '数字人' }}对话</h3>
              <p>{{ getWelcomeMessage() }}</p>
            </div>
          </div>
          
          <!-- 聊天消息 -->
          <div 
            v-for="(message, index) in messages || []" 
            :key="index"
            class="message-item"
            :class="{ 
              'user-message': message.role === 'user', 
              'ai-message': message.role === 'assistant',
              'error-message': message.isError
            }"
          >
            <div class="message-avatar">
              <img 
                :src="message.role === 'user' ? userAvatar : (digitalHuman?.avatar_url ?? defaultAvatar)" 
                alt="Avatar" 
              />
            </div>
            <div class="message-bubble">
              <div class="message-content" v-html="formatMessage(message.content)"></div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
              <!-- 添加调试信息 -->
              <div v-if="settings.debugMode && message.role === 'assistant'" class="message-debug-info">
                <div v-if="message.audio_url">🔊 {{ message.audio_url }}</div>
                <div v-if="message.lip_sync_data && message.lip_sync_data.frames">
                  🎭 {{ message.lip_sync_data.frames.length || 0 }} 帧
                </div>
              </div>
            </div>
            <div class="message-actions">
              <el-button text circle size="small" v-if="message.role === 'assistant'" @click="copyMessage(message.content)">
                <el-icon><CopyDocument /></el-icon>
              </el-button>
            </div>
          </div>
          
          <!-- 类型特定消息组件 -->
          <div v-if="typeSpecificMessage && typeSpecificMessage.show" 
               class="type-specific-message"
               :class="'type-message-' + digitalHuman?.type">
            <div class="type-message-content">
              <div class="type-message-header">
                <span>{{ typeSpecificMessage.title }}</span>
                <el-icon class="close-btn" @click="closeTypeSpecificMessage"><Close /></el-icon>
              </div>
              <div class="type-message-body" v-html="typeSpecificMessage.content"></div>
              <div class="type-message-actions" v-if="typeSpecificMessage.actions && typeSpecificMessage.actions.length">
                <el-button
                  v-for="(action, idx) in typeSpecificMessage.actions"
                  :key="idx"
                  :type="action.primary ? 'primary' : 'default'"
                  size="small"
                  @click="handleTypeMessageAction(action)"
                >
                  {{ action.text }}
                </el-button>
              </div>
            </div>
          </div>
          
          <!-- 输入中状态 -->
          <div class="typing-indicator" v-if="isTyping">
            <div class="message-avatar">
              <img :src="digitalHuman?.avatar_url ?? defaultAvatar" alt="Avatar" />
            </div>
            <div class="typing-bubble">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
        
        <!-- 消息输入区域 -->
        <div class="message-input-container">
          <div class="custom-input-container">
            <el-button
              class="voice-button"
              :type="isRecording ? 'primary' : (settings?.theme === 'light' ? 'default' : 'primary')"
              @mousedown="startRecording"
              @mouseup="stopRecording"
              @mouseleave="stopRecording"
              :loading="isProcessingSpeech"
              size="large"
            >
              <el-icon>
                <Microphone v-if="!isRecording" />
                <Loading v-else-if="isProcessingSpeech" />
                <VideoPause v-else />
              </el-icon>
            </el-button>
            
            <input
              class="message-input"
              v-model="inputMessage"
              @keydown.enter.prevent="onEnterPress"
              placeholder="输入消息..."
            />
            
            <el-button
              class="send-button"
              type="primary"
              :disabled="!inputMessage?.trim() && !isProcessingSpeech"
              @click="sendMessage"
              size="large"
            >
              <el-icon><Send /></el-icon>
            </el-button>
          </div>
          
          <!-- 语音识别反馈 -->
          <div class="speech-feedback" v-if="isRecording && !isProcessingSpeech">
            <sound-outlined class="sound-icon" />
            <span class="recording-text">正在录音... 松开发送</span>
          </div>
          
          <div class="speech-feedback" v-if="isProcessingSpeech">
            <loading-outlined class="sound-icon" />
            <span class="recording-text">正在处理语音...</span>
          </div>
          
          <!-- 快捷回复 -->
          <div class="quick-replies" v-if="quickReplies.length > 0">
            <el-button
              v-for="(reply, index) in quickReplies"
              :key="index"
              class="quick-reply"
              :type="settings?.theme === 'light' ? 'default' : 'primary'"
              size="default"
              @click="useQuickReply(reply)"
            >
              {{ reply }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 设置面板 -->
    <el-drawer
      v-model="settingsVisible"
      title="聊天设置"
      direction="rtl"
      size="320px"
    >
      <div class="settings-container">
        <!-- 语音设置 -->
        <div class="settings-section">
          <h3>语音设置</h3>
          <div class="settings-item">
            <el-switch v-model="settings.enableVoice" />
            <span class="settings-label">启用语音</span>
          </div>
          <div class="settings-item" v-if="settings.enableVoice">
            <span class="settings-label">语音音量</span>
            <el-slider
              v-model="settings.voiceVolume"
              :min="0"
              :max="1"
              :step="0.1"
              :disabled="!settings.enableVoice"
            />
          </div>
          <div class="settings-item" v-if="settings.enableVoice">
            <span class="settings-label">语音速度</span>
            <el-slider
              v-model="settings.voiceSpeed"
              :min="0.5"
              :max="2"
              :step="0.1"
              :disabled="!settings.enableVoice"
            />
          </div>
        </div>
        
        <!-- 口型动画设置 -->
        <div class="settings-section">
          <h3>口型动画设置</h3>
          <div class="settings-item">
            <span>启用口型同步</span>
            <el-switch v-model="settings.enableLipSync" @change="saveLipSyncSettings" />
          </div>

          <div class="settings-item" v-if="settings.enableLipSync">
            <span>口型质量</span>
            <el-select
              v-model="settings.lipSyncQuality"
              style="width: 120px"
              @change="saveLipSyncSettings"
            >
              <el-option value="low" label="低" />
              <el-option value="medium" label="中" />
              <el-option value="high" label="高" />
            </el-select>
          </div>

          <div class="settings-item" v-if="settings.enableLipSync">
            <el-button-group>
              <el-button type="default" @click="testLipSync">本地测试</el-button>
              <el-button type="primary" @click="testLipSyncWithAPI">API测试</el-button>
            </el-button-group>
          </div>
        </div>
        
        <!-- 本地模型设置 -->
        <div class="settings-section">
          <h3>本地模型设置</h3>
          <el-alert
            v-if="!ollamaAvailable"
            title="Ollama服务不可用"
            description="请确保Ollama服务已启动并运行在http://localhost:5003"
            type="warning"
            show-icon
            class="settings-alert"
          />
          <div class="settings-item">
            <el-switch v-model="ollamaSettings.enabled" :disabled="!ollamaAvailable" @change="saveOllamaSettings" />
            <span class="settings-label">使用本地Ollama模型</span>
          </div>
          <div class="settings-item" v-if="ollamaSettings.enabled">
            <span class="settings-label">选择模型</span>
            <el-select
              v-model="ollamaSettings.model"
              style="width: 100%"
              :disabled="!ollamaSettings.enabled || !ollamaAvailable"
              @change="saveOllamaSettings"
            >
              <el-option v-for="model in ollamaModels" :key="model.name" :value="model.name" :label="model.name" />
            </el-select>
          </div>
          <div class="settings-item" v-if="ollamaSettings.enabled">
            <el-button
              type="primary"
              @click="refreshOllamaModels"
              :loading="loadingOllamaModels"
              :disabled="!ollamaAvailable"
            >
              刷新模型列表
            </el-button>
          </div>
        </div>
        
        <!-- 其他设置 -->
        <div class="settings-section">
          <h3>其他设置</h3>
          <div class="settings-item">
            <el-switch v-model="settings.debugMode" @change="saveSettings('debug_mode', settings.debugMode)" />
            <span class="settings-label">调试模式</span>
          </div>
          <div class="settings-item">
            <el-button type="danger" @click="confirmClearChat">清除聊天记录</el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { defineComponent, ref, computed, onMounted, nextTick, watch, reactive, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useDigitalHumanStore } from '../../../stores/digitalHuman';
import { getStaticResourceUrl } from '../../../utils/resourceUtils';
import {
  ArrowLeft,
  InfoFilled,
  Setting,
  Warning,
  Mute,
  VideoPlay,
  Microphone,
  Loading,
  VideoPause,
  CopyDocument,
  Send,
  FullScreen,
  Sunny,
  Moon,
  Grid,
  Close
} from '@element-plus/icons-vue';
import { getDigitalHumanDetail } from '../../../api/digital-human';
import { chatWithDigitalHuman, getLipSyncData } from '../../../api/digital-human';
import { post } from '../../../api'; // Import the authenticated post function
import { format, formatDistance } from 'date-fns';
import zhCN from 'date-fns/locale/zh-CN';
import cloneDeep from 'lodash/cloneDeep';
import { getDigitalHumanApiId, getDigitalHumanResourceId, normalizeDigitalHumanId, ensureStringId } from '../../../utils/digital-human-helper';
import { checkOllamaProxyAvailable, getOllamaModels, getOllamaSettings, saveOllamaSettings as saveOllamaSettingsApi } from '../../../api/digital-human';
import axios from 'axios';
import { useWindowSize } from '@vueuse/core';
import { createLipSyncRenderer, createLipSyncFrames } from '../../../utils/lipSyncUtils';
import { generateUUID } from '../../../utils/uuid';  // 导入UUID生成工具

// 导入功能组件
import AssistantFeatures from '../components/features/AssistantFeatures.vue';
import CustomerServiceFeatures from '../components/features/CustomerServiceFeatures.vue';
import BroadcasterFeatures from '../components/features/BroadcasterFeatures.vue';
import TeacherFeatures from '../components/features/TeacherFeatures.vue';

// 导入口型同步渲染器（添加别名避免冲突）
import { LipSyncRenderer, createLipSyncFrames as createLipSyncFramesAlt, createLipSyncRenderer as createLipSyncRendererAlt } from '../../../utils/lip-sync-renderer';
import { ApiDebugger } from '../../../utils/api-debug';
import { formatDate } from '../../../utils/date';

export default defineComponent({
  name: 'DigitalHumanChat',
  
  components: {
    LeftOutlined,
    InfoCircleOutlined,
    SettingOutlined,
    WarningOutlined,
    SoundOutlined,
    SoundFilled,
    AudioOutlined,
    LoadingOutlined,
    PauseOutlined,
    CopyOutlined,
    SendOutlined,
    FullscreenOutlined,
    FullscreenExitOutlined,
    BulbOutlined,
    BulbFilled,
    ColumnWidthOutlined,
    CloseOutlined,
    AssistantFeatures,
    CustomerServiceFeatures,
    BroadcasterFeatures,
    TeacherFeatures
  },
  
  setup() {
    const router = useRouter();
    const route = useRoute();
    const digitalHumanStore = useDigitalHumanStore();
    
    // 获取数字人ID
    const digitalHumanId = computed(() => route.params.id);
    
    // 默认设置
    const defaultSettings = {
      enableVoice: true,
      voiceSpeed: 1,
      voiceVolume: 0.8,
      displayMode: 'standard',
      theme: localStorage.getItem('chat_theme') || 'dark',
      enableLipSync: localStorage.getItem('use_lip_sync') === 'true' || false,
      lipSyncQuality: localStorage.getItem('lip_sync_quality') || 'medium',
      debugMode: localStorage.getItem('debug_mode') === 'true' || false
    };
    
    // 使用默认设置初始化reactive对象
    const settings = reactive({...defaultSettings});
    
    // 状态变量
    const loading = ref(true);
    const digitalHuman = ref({
      name: '',
      avatar_url: '',
      video_url: '',
      description: '',
      type: '',
      apiId: '',
      digital_human_id: '',
      voice_id: '',
      welcomeText: '',
      ...defaultSettings
    });
    const messages = ref([]);
    const messageList = ref(null);
    const videoPlayer = ref(null);
    const audioPlayer = ref(null);
    const inputMessage = ref('');  // 确保初始化为空字符串
    const isTyping = ref(false);
    const settingsVisible = ref(false);
    const miniMode = ref(false);
    const isMuted = ref(false);
    const hasVideo = ref(false);
    const videoError = ref(false);
    const isSpeaking = ref(false);
    
    // 语音录制相关状态
    const isRecording = ref(false);
    const isProcessingSpeech = ref(false);
    const mediaRecorder = ref(null);
    const audioChunks = ref([]);
    
    const userAvatar = ref('/assets/avatars/default.svg');
    const quickReplies = ref([]);
    const typeSpecificMessage = ref(null);
    const containerWidth = ref(400); // 默认宽度
    const startX = ref(0);
    const isResizing = ref(false);
    const isSmallScreen = ref(window.innerWidth < 768);
    const isCompactLayout = ref(false);
    const showChatArea = ref(true);
    const lipSyncCanvas = ref(null);
    const isSending = ref(false); // 添加发送状态变量
    const currentAudioUrl = ref(''); // 添加音频URL响应式变量
    
    // 口型同步渲染器
    let lipSyncRenderer = null;
    
    // 本地模型设置
    const ollamaSettings = ref(getOllamaSettings());
    const ollamaModels = ref([]);
    const ollamaAvailable = ref(false);
    const loadingOllamaModels = ref(false);
    
    // 初始化Ollama设置
    const initOllamaSettings = async () => {
      try {
        // 检查Ollama服务是否可用
        ollamaAvailable.value = await checkOllamaProxyAvailable();
        
        if (ollamaAvailable.value) {
          console.log('Ollama服务可用，正在获取模型列表');
          await refreshOllamaModels();
        } else {
          console.log('Ollama服务不可用');
        }
      } catch (error) {
        console.error('初始化Ollama设置失败:', error);
        ollamaAvailable.value = false;
      }
    };
    
    // 刷新Ollama模型列表
    const refreshOllamaModels = async () => {
      loadingOllamaModels.value = true;
      try {
        const models = await getOllamaModels();
        ollamaModels.value = models;
        console.log('获取到Ollama模型列表:', models);
        
        // 如果当前选择的模型不在列表中，选择第一个可用模型
        if (models.length > 0 && !models.some(m => m.name === ollamaSettings.value.model)) {
          ollamaSettings.value.model = models[0].name;
          saveOllamaSettings();
        }
      } catch (error) {
        console.error('获取Ollama模型列表失败:', error);
        ElMessage.error('获取模型列表失败');
      } finally {
        loadingOllamaModels.value = false;
      }
    };
    
    // 保存Ollama设置
    const saveOllamaSettings = () => {
      saveOllamaSettingsApi(ollamaSettings.value);
      ElMessage.success('设置已保存');
    };
    
    // 加载数字人数据
    const loadDigitalHumanData = async () => {
      if (!digitalHumanId.value) {
        ElMessage.error('未找到数字人ID，无法加载数据');
        router.replace('/digital-human/app');
        return;
      }
      
      loading.value = true;
      try {
        console.log(`正在加载数字人详情，ID: ${digitalHumanId.value}`);
        
        // 处理ID可能的前缀格式，保证API调用兼容性
        let apiCallId = digitalHumanId.value;
        // 如果ID是以dh_或dh-开头，尝试使用原始ID和标准化ID两种方式获取
        const normalizedId = normalizeDigitalHumanId(digitalHumanId.value);
        
        try {
          const response = await getDigitalHumanDetail(apiCallId);
          const dhData = response.data || response;
          
          // 填充数字人数据
          digitalHuman.value = dhData;
          
          // 使用工具函数获取API ID
          const apiId = getDigitalHumanApiId(dhData);
          if (apiId) {
            digitalHuman.value.apiId = apiId;
            console.log(`成功获取数字人API ID: ${apiId}`);
          } else if (dhData.id) {
            // 如果没有能获取到有效的API ID，使用id作为fallback
            digitalHuman.value.apiId = ensureStringId(dhData.id);
            console.log(`未找到digital_human_id，使用id作为API ID: ${dhData.id}`);
          } else {
            // 最后的fallback使用路由参数
            digitalHuman.value.apiId = digitalHumanId.value;
            console.log(`未找到任何ID，使用路由参数作为API ID: ${digitalHumanId.value}`);
          }
          
          console.log(`数字人ID信息: 路由ID=${digitalHumanId.value}, API ID=${digitalHuman.value.apiId}, 实际ID=${dhData.digital_human_id || dhData.id}`);
          
          // 处理音频URL（如果有）
          if (dhData.audio_url) {
            if (!dhData.audio_url.startsWith('http') && !dhData.audio_url.startsWith('/')) {
              dhData.audio_url = `/${dhData.audio_url}`;
            }
            console.log(`数字人音频URL: ${dhData.audio_url}`);
          }
          
          // 检查是否有视频
          hasVideo.value = !!dhData.video_url;
          
          // 根据数字人类型生成快捷回复
          generateQuickReplies(dhData);
          
          // 加载可能的历史消息
          loadChatHistory(digitalHuman.value.apiId);
          
        } catch (error) {
          console.error(`使用ID ${apiCallId} 获取数字人详情失败, 尝试使用标准化ID:`, error);
          
          // 如果原始ID失败，并且存在前缀，尝试使用标准化ID (没有前缀的ID)
          if (normalizedId !== digitalHumanId.value) {
            try {
              console.log(`尝试使用标准化ID获取数字人详情: ${normalizedId}`);
              const response = await getDigitalHumanDetail(normalizedId);
              const dhData = response.data || response;
              
              // 填充数字人数据
              digitalHuman.value = dhData;
              
              // 使用工具函数获取API ID
              const apiId = getDigitalHumanApiId(dhData);
              if (apiId) {
                digitalHuman.value.apiId = apiId;
                console.log(`成功获取数字人API ID: ${apiId}`);
              } else {
                digitalHuman.value.apiId = normalizedId;
              }
              
              console.log(`使用标准化ID成功获取数字人详情: ${normalizedId}`);
              
              // 处理音频URL（如果有）
              if (dhData.audio_url) {
                if (!dhData.audio_url.startsWith('http') && !dhData.audio_url.startsWith('/')) {
                  dhData.audio_url = `/${dhData.audio_url}`;
                }
                console.log(`数字人音频URL: ${dhData.audio_url}`);
              }
              
              // 检查是否有视频
              hasVideo.value = !!dhData.video_url;
              
              // 根据数字人类型生成快捷回复
              generateQuickReplies(dhData);
              
              // 加载可能的历史消息
              loadChatHistory(digitalHuman.value.apiId);
              
            } catch (secondError) {
              // 两种方式都失败，抛出错误
              console.error(`使用标准化ID ${normalizedId} 获取数字人详情也失败:`, secondError);
              throw error; // 抛出原始错误
            }
          } else {
            // 没有前缀或其他方式可尝试，直接抛出原始错误
            throw error;
          }
        }
        
        loading.value = false;
      } catch (error) {
        console.error('加载数字人详情失败:', error);
        let errorMsg = '加载数字人详情失败';
        
        // 尝试从错误对象中提取详细错误信息
        if (error.response && error.response.data) {
          const errorData = error.response.data;
          if (errorData.detail) {
            errorMsg += `: ${errorData.detail}`;
          }
        }
        
        ElMessage.error(errorMsg);
        loading.value = false;
        
        // 如果是404错误，返回应用列表页
        if (error.response && error.response.status === 404) {
          setTimeout(() => {
            router.replace('/digital-human/app');
          }, 2000);
        }
      }
    };
    
    // 生成快捷回复
    const generateQuickReplies = (dhData) => {
      // 根据数字人类型生成一些常见问题作为快捷回复
      const typeQuickReplies = {
        assistant: ['你能帮我做什么?', '你有哪些功能?', '你是如何工作的?', '查询天气信息', '帮我计算一下'],
        customer_service: ['产品有哪些功能?', '如何联系客服?', '退款流程是怎样的?', '查询我的订单', '产品使用指南'],
        broadcaster: ['最新产品介绍', '有什么促销活动?', '如何购买产品?', '热门产品推荐', '查看直播计划'],
        teacher: ['这门课程有哪些内容?', '如何提高学习效率?', '有没有学习资料?', '课程大纲', '知识点总结']
      };
      
      // 设置快捷回复
      if (dhData.type && typeQuickReplies[dhData.type]) {
        quickReplies.value = typeQuickReplies[dhData.type];
      } else {
        quickReplies.value = ['你好', '你是谁?', '你能做什么?'];
      }
    };
    
    // 响应式布局处理
    const checkScreenSize = () => {
      const windowWidth = window.innerWidth;
      console.log(`检测屏幕宽度: ${windowWidth}px`);
      
      // 判断是否为小屏幕设备
      isSmallScreen.value = windowWidth < 768;
      
      if (isSmallScreen.value) {
        // 小屏幕设备设置为紧凑布局，隐藏聊天区域
        console.log('小屏幕设备，使用紧凑布局');
        isCompactLayout.value = true;
        showChatArea.value = false;
      } else {
        // 大屏幕设备设置为左右布局，显示聊天区域
        console.log('大屏幕设备，使用左右布局');
        isCompactLayout.value = false;
        showChatArea.value = true;
      }
      
      // 使用nextTick确保DOM更新
      nextTick(() => {
        console.log('布局更新后状态:', {
          isSmallScreen: isSmallScreen.value,
          isCompactLayout: isCompactLayout.value,
          showChatArea: showChatArea.value,
          windowWidth
        });
        
        // 确保大屏幕设备显示聊天区域
        if (windowWidth >= 768) {
          const chatArea = document.querySelector('.chat-message-area');
          if (chatArea) {
            console.log('确保大屏幕设备显示聊天区域');
            chatArea.style.display = 'flex';
          }
        }
      });
    };
    
    // 切换布局
    const toggleLayout = () => {
      console.log('切换布局前:', { isCompactLayout: isCompactLayout.value, showChatArea: showChatArea.value });
      
      // 反转布局状态
      isCompactLayout.value = !isCompactLayout.value;
      showChatArea.value = !isCompactLayout.value;
      
      console.log('切换布局后:', { isCompactLayout: isCompactLayout.value, showChatArea: showChatArea.value });
      
      // 提示用户
      if (!isCompactLayout.value) {
        ElMessage.success('已切换到左右布局');
      } else {
        ElMessage.success('已切换到上下布局');
      }
      
      // 确保DOM更新 - 使用多层保障确保布局正确
      nextTick(() => {
        // 获取DOM元素
        const chatArea = document.querySelector('.chat-message-area');
        const dhContainer = document.querySelector('.digital-human-container');
        const container = document.querySelector('.chat-container');
        
        if (isCompactLayout.value) {
          // 紧凑布局（上下结构）
          if (container) {
            container.style.display = 'flex';
            container.style.flexDirection = 'column';
          }
          
          if (dhContainer) {
            dhContainer.style.width = '100%';
            dhContainer.style.maxWidth = '100%';
          }
          
          if (chatArea) {
            chatArea.style.display = showChatArea.value ? 'flex' : 'none';
          }
        } else {
          // 常规布局（左右结构）
          if (container) {
            container.style.display = 'flex';
            container.style.flexDirection = 'row';
          }
          
          if (dhContainer) {
            dhContainer.style.width = '400px';
            dhContainer.style.maxWidth = '40%';
            dhContainer.style.minWidth = '300px';
            dhContainer.style.flexShrink = '0';
          }
          
          if (chatArea) {
            chatArea.style.display = 'flex';
            chatArea.style.flexDirection = 'column';
            chatArea.style.flex = '1';
            chatArea.style.minWidth = '300px';
            chatArea.style.width = '60%';
          }
        }
        
        console.log('布局切换完成:', isCompactLayout.value ? '紧凑布局' : '常规布局');
      });
    };
    
    // 调整大小相关
    const startResize = (e) => {
      isResizing.value = true;
      startX.value = e.clientX;
      
      // 添加鼠标移动和松开事件
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    };
    
    const handleMouseMove = (e) => {
      if (!isResizing.value) return;
      
      const dx = e.clientX - startX.value;
      containerWidth.value = Math.max(280, Math.min(600, containerWidth.value + dx));
      startX.value = e.clientX;
      
      // 应用宽度
      const container = document.querySelector('.digital-human-container');
      if (container) {
        container.style.width = `${containerWidth.value}px`;
      }
    };
    
    const handleMouseUp = () => {
      isResizing.value = false;
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    // 主题切换
    const toggleTheme = () => {
      settings.theme = settings?.theme === 'light' ? 'dark' : 'light';
      localStorage.setItem('chat_theme', settings.theme);
    };
    
    // 获取类型特定组件
    const getTypeComponent = (type) => {
      const typeComponents = {
        assistant: 'AssistantFeatures',
        customer_service: 'CustomerServiceFeatures',
        broadcaster: 'BroadcasterFeatures',
        teacher: 'TeacherFeatures'
      };
      
      return typeComponents[type] || null;
    };
    
    // 处理特定功能动作
    const handleFeatureAction = (action) => {
      if (action.type === 'sendMessage') {
        inputMessage.value = action.content;
        sendMessage();
      } else if (action.type === 'showCard') {
        typeSpecificMessage.value = {
          show: true,
          title: action.title || '信息',
          content: action.content,
          actions: action.actions || []
        };
        
        // 滚动到底部
        nextTick(() => {
          scrollToBottom();
        });
      }
    };
    
    // 加载聊天历史
    const loadChatHistory = (id) => {
      // 使用正确的ID加载历史记录
      const historyId = id || digitalHuman.value?.apiId || digitalHumanId.value;
      
      // 尝试从本地存储加载历史消息
      const historyKey = `chat_history_${historyId}`;
      const savedHistory = localStorage.getItem(historyKey);
      
      if (savedHistory) {
        try {
          const parsedHistory = JSON.parse(savedHistory);
          if (Array.isArray(parsedHistory) && parsedHistory.length > 0) {
            messages.value = parsedHistory;
            
            // 确保所有消息都有时间戳
            messages.value.forEach(msg => {
              if (!msg.timestamp) {
                msg.timestamp = new Date();
              } else if (typeof msg.timestamp === 'string') {
                msg.timestamp = new Date(msg.timestamp);
              }
            });
            
            // 滚动到底部
            nextTick(() => {
              scrollToBottom();
            });
            
            return;
          }
        } catch (e) {
          console.error('解析聊天历史失败:', e);
        }
      }
      
      // 如果没有有效历史或解析失败，保持空数组
      messages.value = [];
    };
    
    // 保存聊天历史
    const saveChatHistory = () => {
      const historyId = digitalHuman.value?.apiId || digitalHumanId.value;
      if (!historyId) return;
      
      const historyKey = `chat_history_${historyId}`;
      
      // 深拷贝消息以避免循环引用
      const historyToSave = cloneDeep(messages.value);
      
      try {
        localStorage.setItem(historyKey, JSON.stringify(historyToSave));
      } catch (e) {
        console.error('保存聊天历史失败:', e);
      }
    };
    
    // 发送消息给数字人并接收回复
    const sendMessage = async (text) => {
      if (isSending.value) return;
      
      // 检查text参数类型，确保是字符串
      if (typeof text !== 'string') {
        text = inputMessage.value.trim();
      } else {
        text = text.trim();
      }
      
      // 检查输入是否为空
      if (!text) return;
      
      // 清空输入框
      inputMessage.value = '';
      
      // 获取数字人ID
      const dhId = digitalHuman.value?.apiId || digitalHuman.value?.digital_human_id;
      if (!dhId) {
        console.error('数字人ID未定义，无法发送消息');
        return;
      }
      
      // 设置发送状态
      isSending.value = true;
      
      try {
        // 添加用户消息
        const userMessage = {
          id: Date.now().toString(),
          type: 'user',
          content: text,
          timestamp: new Date().toISOString(),
          role: 'user'
        };
        messages.value.push(userMessage);
        
        // 准备请求数据 - 确保正确格式
        const requestData = {
          message: text,
          voice_id: digitalHuman.value?.voice_id || null,
          language: 'zh-CN'
        };
        
        console.log(`[API] POST /api/lip-sync/digital-human/${dhId}/message`, requestData);
        
        // 使用API调试工具记录请求
        const requestRecord = ApiDebugger.logRequest(
          `/api/lip-sync/digital-human/${dhId}/message`,
          'POST',
          requestData,
          {}
        );
        
        try {
          // 发送请求到口型同步API，使用已导入的post函数
          const response = await post(`/api/lip-sync/digital-human/${dhId}/message`, requestData);
          
          // 更新API调试记录
          ApiDebugger.updateRequest(requestRecord, response, null);
          
          // 获取响应数据，post函数可能已经处理了JSON解析
          const data = response.data || response;
          console.log(`[API] Response:`, data);
          
          // 检查响应结果是否成功
          if (!data.success) {
            throw new Error(`API错误: ${data.error || '未知错误'}`);
          }
          
          // 处理数字人回复
          const replyContent = data.message;
          let audioUrl = data.audio_url;
          const lipSyncData = data.lip_sync;
          
          console.log(`处理AI回复:`, {
            message: replyContent,
            audio_url: audioUrl,
            lip_sync_data: lipSyncData
          });
          
          // 添加数字人回复消息
          const aiMessage = {
            id: Date.now().toString(),
            type: 'ai',
            content: replyContent,
            timestamp: new Date().toISOString(),
            audio_url: audioUrl,
            lip_sync_data: lipSyncData,
            role: 'assistant'
          };
          messages.value.push(aiMessage);
          
          // 处理音频URL
          if (audioUrl) {
            console.log(`收到音频URL: ${audioUrl}`);
            
            // 检查是否为有效的音频URL
            let audioToPlay = audioUrl;
            
            // 检测是否是静态文件路径，可能需要特殊处理
            const isStaticUrl = audioUrl.startsWith('/static/audio/');
            const isWindowsPath = /[A-Z]:\\\\|\\\\\\\\/.test(audioUrl);
            
            if (isStaticUrl) {
              console.log('检测到静态音频文件路径，直接使用API路径');
              
              // 直接使用API路径，避免尝试访问不存在的静态资源
              audioToPlay = `/api/digital-human/media/audio/${dhId}?t=${Date.now()}`;
              console.log(`替换为API URL: ${audioToPlay}`);
            } else if (isWindowsPath) {
              console.warn('检测到Windows路径格式，这在Web环境中无法访问');
              audioToPlay = `/api/digital-human/media/audio/${dhId}?t=${Date.now()}`;
              console.log(`替换为API URL: ${audioToPlay}`);
            }
            
            // 保存处理后的URL
            audioUrl = audioToPlay;
          }
          
          // 延迟500ms，以便UI更新和滚动到新消息
          setTimeout(() => {
            // 控制口型同步动画和音频播放
            if (lipSyncData && settings.enableLipSync) {
              console.log("使用口型同步数据播放音频和动画");
              speakMessage(replyContent, audioUrl, lipSyncData);
            } else {
              console.log("仅播放音频，无口型同步");
              playAudioWithFallback(audioUrl);
              simulateSpeaking(replyContent, 2500);
            }
          }, 500);
        } catch (error) {
          console.error(`发送消息失败:`, error);
          
          // 更新API调试记录
          ApiDebugger.updateRequest(requestRecord, null, error);
          
          // 提取更有用的错误信息
          let errorMessage = '发送消息失败';
          
          if (error && error.response) {
            // 从响应中提取错误信息
            const responseData = error.response.data;
            if (responseData) {
              if (responseData.detail) {
                errorMessage = `发送消息失败: ${responseData.detail}`;
              } else if (responseData.message) {
                errorMessage = `发送消息失败: ${responseData.message}`;
              } else if (responseData.error) {
                errorMessage = `发送消息失败: ${responseData.error}`;
              }
            }
            
            // 如果有特定状态码，添加更具体的信息
            if (error.response.status === 422) {
              errorMessage = `请求参数错误 (422): 请检查消息格式`;
              
              // 输出请求数据以帮助调试
              console.warn(`[API DEBUG] 原始请求数据:`, requestData);
              
              // 尝试检查常见问题
              if (typeof requestData.message !== 'string') {
                console.warn(`[API DEBUG] 消息类型错误: ${typeof requestData.message}`);
                errorMessage += ` - 消息类型错误`;
              }
            }
          } else if (error && error.message) {
            // 直接使用错误消息
            errorMessage = `发送消息失败: ${error.message}`;
          }
          
          // 检查是否需要诊断信息
          if (process.env.NODE_ENV === 'development') {
            console.log(`[API DEBUG] 请求失败详情: `, {
              url: `/api/lip-sync/digital-human/${dhId}/message`,
              data: requestData,
              error: {
                message: error.message,
                status: error.response?.status,
                data: error.response?.data
              }
            });
          }
          
          // 添加错误消息
          messages.value.push({
            id: Date.now().toString(),
            type: 'system',
            content: errorMessage,
            timestamp: new Date().toISOString(),
            isError: true,
            role: 'system'
          });
          
          // 重置状态
          isSending.value = false;
          
          // 滚动到底部
          scrollToBottom();
        }
      } catch (error) {
        console.error(`发送消息失败:`, error);
        isSending.value = false;
      }
    };
    
    // 处理特定类型数字人的功能
    const processMessageForTypeSpecificFeatures = (userMessage, aiMessage) => {
      if (!digitalHuman.value || !digitalHuman.value.type) return;
      
      const type = digitalHuman.value.type;
      
      // 示例功能处理逻辑
      if (type === 'assistant') {
        // 检测是否是天气查询
        if (userMessage.includes('天气') || userMessage.includes('气温')) {
          typeSpecificMessage.value = {
            show: true,
            title: '天气查询',
            content: '北京市今日天气：晴，15°C-25°C，空气质量良好，紫外线中等',
            actions: [
              { text: '查看详情', primary: true },
              { text: '未来一周', primary: false }
            ]
          };
        }
        // 检测是否是计算请求
        else if (userMessage.includes('计算')) {
          // 这里可以添加计算器功能
        }
      } 
      else if (type === 'customer_service') {
        // 检测是否是订单查询
        if (userMessage.includes('订单') || userMessage.includes('查询')) {
          typeSpecificMessage.value = {
            show: true,
            title: '订单查询',
            content: '您有3个未完成订单：<br/>1. 订单号: 2023112001 - 待发货<br/>2. 订单号: 2023111502 - 已发货<br/>3. 订单号: 2023110305 - 运输中',
            actions: [
              { text: '查看详情', primary: true },
              { text: '联系客服', primary: false }
            ]
          };
        }
      }
      else if (type === 'broadcaster') {
        // 检测是否是产品查询
        if (userMessage.includes('产品') || userMessage.includes('介绍')) {
          typeSpecificMessage.value = {            show: true,            title: '产品介绍',            content: '新款智能手表 XW-200<br/><img src="/images/digital-human/avatar2.png" style="width:100%;margin:10px 0"><br/>售价: ¥1299.00<br/>特点: 心率监测、GPS定位、续航7天',            actions: [              { text: '立即购买', primary: true },              { text: '查看详情', primary: false }            ]          };
        }
      }
      else if (type === 'teacher') {
        // 检测是否是课程查询
        if (userMessage.includes('课程') || userMessage.includes('内容')) {
          typeSpecificMessage.value = {
            show: true,
            title: '课程大纲',
            content: '<b>人工智能基础课程</b><br/><ol><li>人工智能概述</li><li>机器学习基础</li><li>神经网络入门</li><li>深度学习应用</li><li>强化学习理论</li></ol>',
            actions: [
              { text: '查看详情', primary: true },
              { text: '下载资料', primary: false }
            ]
          };
        }
      }
    };
    
    // 使用快捷回复
    const useQuickReply = (reply) => {
      inputMessage.value = reply;
      sendMessage();
    };
    
    // 滚动到底部
    const scrollToBottom = () => {
      if (messageList.value) {
        // 使用更可靠的方式滚动到底部
        const scrollHeight = messageList.value.scrollHeight;
        const height = messageList.value.clientHeight;
        const maxScrollTop = scrollHeight - height;
        messageList.value.scrollTop = maxScrollTop > 0 ? maxScrollTop : 0;
        
        // 如果浏览器支持平滑滚动，使用平滑滚动效果
        try {
          messageList.value.scrollTo({
            top: messageList.value.scrollHeight,
            behavior: 'smooth'
          });
        } catch (error) {
          // 回退到常规滚动
          messageList.value.scrollTop = messageList.value.scrollHeight;
        }
      }
    };
    
    // 回车发送消息
    const onEnterPress = (e) => {
      if (!e.shiftKey) {
        sendMessage();
      }
    };
    
    // 格式化消息，处理链接和换行
    const formatMessage = (content) => {
      if (content === null || content === undefined) return '';
      try {
        // 处理不同类型的内容
        if (typeof content === 'string') {
          return content.trim();
        } else if (typeof content === 'object') {
          // 尝试将对象转换为JSON字符串
          return JSON.stringify(content).trim();
        } else {
          // 其他类型（数字、布尔值等）转换为字符串
          return String(content).trim();
        }
      } catch (e) {
        console.error('消息格式化错误:', e, content);
        return String(content || '');
      }
    };
    
    // 格式化时间
    const formatTime = (timestamp) => {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleTimeString();
    };
    
    // 复制消息内容
    const copyMessage = (content) => {
      if (!content) return;
      
      try {
        // 创建临时元素复制文本
        const tempInput = document.createElement('textarea');
        tempInput.value = content;
        document.body.appendChild(tempInput);
        tempInput.select();
        document.execCommand('copy');
        document.body.removeChild(tempInput);
        
        ElMessage.success('消息已复制到剪贴板');
      } catch (e) {
        console.error('复制失败:', e);
        ElMessage.error('复制失败，请手动选择并复制');
      }
    };
    
    // 检查音频URL可访问性
    const checkAudioUrlAccessibility = async (url) => {
      try {
        // 静态音频路径不可访问，直接返回false
        if (url.startsWith('/static/audio/')) {
          console.log(`静态音频路径 ${url} 不可直接访问，需要使用API路径`);
          return false;
        }
        
        // 对于静态文件URL，使用HEAD请求
        if (url.startsWith('/assets/')) {
          const response = await fetch(url, { method: 'HEAD' });
          return response.ok;
        }
        
        // 对于API URL，使用GET请求
        const response = await fetch(url, { method: 'GET' });
        return response.ok;
      } catch (error) {
        console.warn(`音频URL不可访问: ${url}`, error);
        return false;
      }
    };
    
    // 带有多重回退和错误处理的音频播放函数
    const playAudioWithFallback = (url, maxRetries = 2) => {
      let retryCount = 0;
      
      // 获取数字人ID
      const dhId = digitalHuman.value?.apiId || digitalHuman.value?.digital_human_id;
      
      // 如果URL为空，使用静音动画
      if (!url) {
        console.error('音频URL为空，使用静音动画');
        simulateSpeaking('', 2500);
        return;
      }
      
      // 直接检查是否是静态路径，如果是，立即转换为API路径
      if (url.startsWith('/static/audio/')) {
        if (dhId) {
          url = `/api/digital-human/media/audio/${dhId}?t=${Date.now()}`;
          console.log(`预处理：将静态音频路径直接转换为API路径: ${url}`);
        } else {
          console.error('无法转换静态音频路径：缺少数字人ID');
          simulateSpeaking('', 2500);
          return;
        }
      }
      
      // 确保音频播放器已初始化
      if (!audioPlayer.value) {
        audioPlayer.value = new Audio();
      }
      
      const attemptPlay = async (currentUrl) => {
        console.log(`尝试播放音频 (${retryCount}/${maxRetries}): ${currentUrl}`);
        
        try {
          // 验证URL可访问性
          const isAccessible = await checkAudioUrlAccessibility(currentUrl);
          if (!isAccessible) {
            // 根据URL类型选择回退策略
            if (currentUrl.startsWith('/assets/audio/')) {
              // 如果是资源路径，尝试使用API路径
              if (dhId) {
                retryCount++;
                const apiUrl = `/api/digital-human/media/audio/${dhId}?t=${Date.now()}`;
                console.log(`资源路径不可访问，尝试API路径: ${apiUrl}`);
                return attemptPlay(apiUrl);
              }
            } else if (!currentUrl.includes('/api/digital-human/media/audio/')) {
              // 如果不是API路径，尝试使用内置欢迎音频
              retryCount++;
              const defaultAudio = '/assets/audio/greeting_default.mp3';
              console.log(`尝试使用默认欢迎音频: ${defaultAudio}`);
              return attemptPlay(defaultAudio);
            }
            
            // 其他情况，使用静音动画
            throw new Error('音频URL不可访问，无有效回退路径');
          }
          
          // 设置音频源
          audioPlayer.value.src = currentUrl;
          
          // 添加一次性错误事件监听器
          const handleError = (error) => {
            console.error(`音频播放失败 (${retryCount}/${maxRetries}): ${currentUrl}`, error);
            
            // 增加重试计数
            retryCount++;
            
            if (retryCount <= maxRetries) {
              // 根据当前URL类型选择回退策略
              if (dhId && !currentUrl.includes(`/api/digital-human/media/audio/${dhId}`)) {
                // 尝试使用API路径回退
                const apiUrl = `/api/digital-human/media/audio/${dhId}?t=${Date.now()}`;
                console.log(`播放失败，尝试API路径: ${apiUrl}`);
                attemptPlay(apiUrl);
              } else if (dhId) {
                // 已经是API路径，再次尝试（添加时间戳）
                const refreshedUrl = `/api/digital-human/media/audio/${dhId}?t=${Date.now()}`;
                console.log(`尝试刷新API路径: ${refreshedUrl}`);
                attemptPlay(refreshedUrl);
              } else {
                // 尝试内置欢迎音频
                const defaultAudio = '/assets/audio/greeting_default.mp3';
                console.log(`尝试使用默认欢迎音频: ${defaultAudio}`);
                attemptPlay(defaultAudio);
              }
            } else {
              // 重试次数用尽，使用静音动画
              console.warn(`音频播放失败次数过多，使用静音动画`);
              simulateSpeaking('', 2500);
            }
          };
          
          // 监听错误事件
          audioPlayer.value.addEventListener('error', handleError, { once: true });
          
          // 尝试播放
          await audioPlayer.value.play();
        } catch (error) {
          console.error(`播放调用失败: ${currentUrl}`, error);
          // 使用静音动画作为最终回退
          simulateSpeaking('', 2500);
        }
      };
      
      // 开始尝试播放
      attemptPlay(url);
    };
    
    // 使用lip sync数据说话，附带音频
    const speakMessage = async (text, audioUrl, lipSyncData) => {
      try {
        if (audioUrl) {
          console.log(`准备播放音频: ${audioUrl}`);
          
          // 检测是否启用了口型同步
          if (lipSyncData && Object.keys(lipSyncData).length > 0) {
            // 准备口型同步数据
            const frames = lipSyncData.frames || [];
            if (frames.length > 0) {
              console.log(`使用口型同步数据，${frames.length}帧`);
              
              // 初始化渲染器
              if (!lipSyncRenderer) {
                console.log("口型渲染器未初始化，尝试初始化...");
                const success = initLipSyncRenderer();
                if (!success) {
                  console.warn("口型渲染器不可用，使用备用动画");
                  simulateSpeaking(text, 2000);
                  
                  // 仍然播放音频
                  playAudioWithFallback(audioUrl);
                  return;
                }
              }
              
              // 设置音频
              if (!audioPlayer.value) {
                console.log("创建新的音频播放器");
                audioPlayer.value = new Audio();
              }
              
              // 清除旧的事件监听器
              const player = audioPlayer.value;
              player.oncanplay = null;
              player.onended = null;
              player.onerror = null;
              
              // 设置新的音频URL
              player.src = audioUrl;
              
              // 音频准备就绪时开始播放
              player.oncanplay = function canPlayHandler() {
                console.log(`音频已准备好播放: ${audioUrl}`);
                
                // 播放音频
                player.play().catch(error => {
                  console.error("播放音频失败:", error);
                  // 尝试备用播放方法
                  playAudioWithFallback(audioUrl);
                });
                
                // 开始口型同步动画
                startSpeaking(text, frames);
              };
              
              // 错误处理
              player.onerror = function errorHandler(error) {
                console.error("音频加载失败:", error);
                // 使用备用动画
                simulateSpeaking(text, 2000);
                // 尝试备用播放方法
                playAudioWithFallback(audioUrl);
              };
              
              // 加载音频
              player.load();
              return;
            }
          }
          
          // 如果没有口型同步数据或处理失败，直接播放音频
          console.log("没有口型同步数据，仅播放音频");
          playAudioWithFallback(audioUrl);
          simulateSpeaking(text, 2000);
        } else {
          // 没有音频URL，使用静音动画
          console.log("没有音频URL，使用静音动画");
          simulateSpeaking(text, 2000);
        }
      } catch (error) {
        console.error("播放音频和口型同步时出错:", error);
        // 使用备用动画
        simulateSpeaking(text, 2000);
      }
    };
    
    // 开始播放语音和口型动画
    const startSpeaking = (text, lipSyncData) => {
      // 只有在真正需要播放时才设置说话状态
      if (!text) return;
      
      // 开始说话状态
      isSpeaking.value = true;
      
      // 确认有效的口型同步数据
      const hasValidLipSync = lipSyncData && 
                              lipSyncData.frames && 
                              Array.isArray(lipSyncData.frames) && 
                              lipSyncData.frames.length > 0;
      
      if (settings.enableLipSync && hasValidLipSync) {
        console.log(`使用口型同步数据，${lipSyncData.frames.length}帧`);
        
        // 确保口型渲染器已初始化
        let rendererReady = false;
        
        // 如果渲染器未初始化或不可用，尝试初始化
        if (!lipSyncRenderer) {
          console.log('口型渲染器未初始化，尝试初始化...');
          rendererReady = initLipSyncRenderer();
        } else {
          // 验证渲染器可用
          rendererReady = true;
        }
        
        if (rendererReady && lipSyncRenderer) {
          try {
            // 重置渲染器状态
            lipSyncRenderer.reset();
            
            // 设置口型帧数据
            lipSyncRenderer.setFrames(lipSyncData.frames);
            
            // 启动口型动画
            lipSyncRenderer.play();
            
            console.log('口型动画已启动');
          } catch (lipSyncError) {
            console.error('启动口型动画失败:', lipSyncError);
            // 如果口型动画启动失败，使用备用动画
            simulateSpeaking(text, estimateDuration(text));
          }
        } else {
          console.warn('口型渲染器不可用，使用备用动画');
          simulateSpeaking(text, estimateDuration(text));
        }
      } else {
        // 如果没有口型数据或禁用了口型同步，使用备用动画
        console.log('没有有效的口型数据或口型同步已禁用，使用备用动画');
        simulateSpeaking(text, estimateDuration(text));
      }
      
      // 滚动到底部确保消息可见
      scrollToBottom();
    };
    
    // 根据文本长度估计持续时间（毫秒）
    const estimateDuration = (text) => {
      if (!text) return 2000;
      // 假设每个字符约150毫秒，最短2秒，最长10秒
      const duration = Math.max(2000, Math.min(10000, text.length * 150));
      return duration;
    };
    
    // 模拟说话动画（备用方案）
    const simulateSpeaking = (text, duration) => {
      if (!text) return;
      
      console.log(`模拟说话动画，持续时间: ${duration}ms`);
      
      // 设置说话状态
      isSpeaking.value = true;
      
      // 简单的说话动画定时器
      const animateInterval = setInterval(() => {
        // 模拟口型随机变化
        const avatarElement = document.querySelector('.digital-human-avatar');
        if (avatarElement) {
          // 随机微小抖动效果
          const shakeAmount = 2;
          const randomX = (Math.random() - 0.5) * shakeAmount;
          const randomY = (Math.random() - 0.5) * shakeAmount;
          avatarElement.style.transform = `translate(${randomX}px, ${randomY}px)`;
        }
      }, 100);
      
      // 在指定时间后停止动画
      setTimeout(() => {
        clearInterval(animateInterval);
        isSpeaking.value = false;
        
        // 重置任何样式变化
        const avatarElement = document.querySelector('.digital-human-avatar');
        if (avatarElement) {
          avatarElement.style.transform = '';
        }
        
        console.log('模拟说话动画已结束');
      }, duration);
    };
    
    // 音频相关处理
    const onAudioEnded = () => {
      console.log('音频播放结束');
      isSpeaking.value = false;
      currentAudioUrl.value = '';
      
      // 触发音频播放结束事件，可能需要更新UI状态
      if (audioPlayer.value) {
        // 重置音频元素
        audioPlayer.value.currentTime = 0;
      }
    };
    
    // 为音频播放器添加错误处理
    onMounted(() => {
      // 在挂载后添加错误事件监听
      if (audioPlayer.value) {
        audioPlayer.value.addEventListener('error', (e) => {
          const errorTypes = [
            '未知错误',
            '用户中断',
            '网络错误',
            '解码错误',
            '不支持的格式'
          ];
          const errorCode = e.target.error ? e.target.error.code : 0;
          const errorMessage = errorTypes[errorCode] || errorTypes[0];
          
          console.error(`音频播放错误(${errorCode}): ${errorMessage}`, e);
          console.error(`音频URL: ${currentAudioUrl.value}`);
          
          // 回退到模拟说话
          if (isSpeaking.value) {
            simulateSpeaking(messages.value[messages.value.length - 1]?.fullContent || '');
          }
        });
      }
    });
    
    // 获取欢迎消息
    const getWelcomeMessage = () => {
      if (!digitalHuman.value?.welcome_message) {
        return '很高兴见到你！我是你的AI助手，有什么我可以帮你的吗？';
      }
      return digitalHuman.value.welcome_message.trim();
    };
    
    // 处理类型特定消息
    const handleTypeMessageAction = (action) => {
      // 实现类型特定消息的处理逻辑
      ElMessage.info('执行操作: ' + action.text);
      
      // 关闭消息
      setTimeout(() => {
        closeTypeSpecificMessage();
      }, 500);
    };
    
    // 关闭类型特定消息
    const closeTypeSpecificMessage = () => {
      typeSpecificMessage.value = null;
    };
    
    // 强制大屏幕布局
    const forceDesktopLayout = () => {
      const windowWidth = window.innerWidth;
      console.log('强制布局检查，窗口宽度:', windowWidth);
      
      if (windowWidth >= 768) {
        // 强制设置状态变量
        isCompactLayout.value = false;
        showChatArea.value = true;
        
        // 强制设置DOM
        nextTick(() => {
          // 确保聊天区域显示
          const chatArea = document.querySelector('.chat-message-area');
          if (chatArea) {
            chatArea.style.display = 'flex';
            chatArea.style.flexDirection = 'column';
            chatArea.style.flex = '1';
            chatArea.style.minWidth = '300px';
            chatArea.style.width = '60%';
            
            console.log('已强制设置大屏幕布局 - 聊天区域');
          }
          
          // 确保数字人容器使用固定宽度
          const dhContainer = document.querySelector('.digital-human-container');
          if (dhContainer) {
            dhContainer.style.width = '400px';
            dhContainer.style.maxWidth = '40%';
            dhContainer.style.minWidth = '300px';
            dhContainer.style.flexShrink = '0';
            
            console.log('已强制设置数字人容器宽度');
          }
          
          // 确保容器使用正确的布局方向
          const container = document.querySelector('.chat-container');
          if (container) {
            container.style.display = 'flex';
            container.style.flexDirection = 'row';
            
            console.log('已强制设置容器为左右布局');
          }
        });
      }
    };
    
    // 组件挂载时加载数据
    onMounted(() => {
      loadDigitalHumanData();
      
      // 加载设置
      const savedSettings = localStorage.getItem('chat_settings');
      if (savedSettings) {
        try {
          const parsedSettings = JSON.parse(savedSettings);
          Object.assign(settings, parsedSettings);
        } catch (e) {
          console.error('加载设置失败:', e);
        }
      }
      
      // 初始化Ollama设置
      initOllamaSettings();
      
      // 检查屏幕大小，设置初始布局
      checkScreenSize();
      
      // 初始化音频错误处理
      if (audioPlayer.value) {
        audioPlayer.value.onerror = (e) => {
          const errorTypes = [
            '未知错误',
            '用户中断',
            '网络错误',
            '解码错误',
            '不支持的格式'
          ];
          const errorCode = e.target.error ? e.target.error.code : 0;
          const errorMessage = errorTypes[errorCode] || errorTypes[0];
          
          console.error(`全局音频错误处理 - 错误(${errorCode}): ${errorMessage}`, e);
          console.error(`当前音频URL: ${currentAudioUrl.value || '无'}`);
          
          // 如果当前正在说话，尝试使用模拟动画
          if (isSpeaking.value) {
            const lastMessage = messages.value.length > 0 ? 
              messages.value[messages.value.length - 1] : null;
            
            if (lastMessage && lastMessage.role === 'assistant') {
              simulateSpeaking(lastMessage.fullContent || lastMessage.content);
            } else {
              // 如果找不到最后的消息内容，则使用默认持续时间的模拟动画
              isSpeaking.value = false;
              setTimeout(() => {
                isSpeaking.value = false;
              }, 2000);
            }
          }
        };
      }
      
      // 延迟二次检查，确保布局正确
      setTimeout(() => {
        console.log('执行延迟布局校正...');
        const windowWidth = window.innerWidth;
        // 确保大屏幕设备时聊天区域显示
        if (windowWidth >= 768) {
          isCompactLayout.value = false;
          showChatArea.value = true;
          
          // 强制更新DOM
          nextTick(() => {
            const chatArea = document.querySelector('.chat-message-area');
            if (chatArea) {
              chatArea.style.display = 'flex';
              console.log('已修复大屏幕设备布局');
            }
          });
        }
      }, 500);
      
      // 进一步延迟执行强制布局
      setTimeout(forceDesktopLayout, 1000);
      
      // 添加MutationObserver监视DOM变化
      const observer = new MutationObserver(() => {
        // 当DOM发生变化时再次确保布局正确
        forceDesktopLayout();
      });
      
      // 延迟启动观察器
      setTimeout(() => {
        const container = document.querySelector('.chat-container');
        if (container) {
          // 开始观察
          observer.observe(container, { 
            childList: true, 
            subtree: true 
          });
          console.log('已启动DOM变化观察器');
        }
      }, 1500);
      
      // 存储observer以便卸载时清理
      window.layoutObserver = observer;
      
      // 监听窗口大小变化
      const handleResize = () => {
        // 添加节流，避免频繁调用
        if (window.resizeTimer) {
          clearTimeout(window.resizeTimer);
        }
        window.resizeTimer = setTimeout(() => {
          checkScreenSize();
          // 大屏幕设备时进行额外布局强制
          if (window.innerWidth >= 768) {
            forceDesktopLayout();
          }
        }, 200);
      };
      
      window.addEventListener('resize', handleResize);
      
      // 将事件处理函数保存在组件实例上，以便在卸载时移除
      window.handleResizeEvent = handleResize;
      
      // 从localStorage加载设置并更新settings对象
      Object.assign(settings, {
        theme: localStorage.getItem('chat_theme') || defaultSettings.theme,
        enableLipSync: localStorage.getItem('use_lip_sync') === 'true' || defaultSettings.enableLipSync,
        lipSyncQuality: localStorage.getItem('lip_sync_quality') || defaultSettings.lipSyncQuality,
        debugMode: localStorage.getItem('debug_mode') === 'true' || defaultSettings.debugMode
      });
    });
    
    // 组件卸载时清理
    onUnmounted(() => {
      // 移除resize事件监听
      if (window.handleResizeEvent) {
        window.removeEventListener('resize', window.handleResizeEvent);
        delete window.handleResizeEvent;
      }
      
      // 清理resize节流定时器
      if (window.resizeTimer) {
        clearTimeout(window.resizeTimer);
        delete window.resizeTimer;
      }
      
      // 清理MutationObserver
      if (window.layoutObserver) {
        window.layoutObserver.disconnect();
        delete window.layoutObserver;
      }
      
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      
      // 清理可能存在的计时器
      if (window.speakingTimer) {
        clearTimeout(window.speakingTimer);
      }
      
      // 停止任何正在播放的音频
      stopCurrentAudio();
    });
    
    // 监听设置变化并保存
    watch(settings, (newSettings) => {
      try {
        localStorage.setItem('chat_settings', JSON.stringify(newSettings));
      } catch (e) {
        console.error('保存设置失败:', e);
      }
    }, { deep: true });
    
    // 视频相关处理
    const onVideoReady = () => {
      videoError.value = false;
      console.log('视频准备就绪');
    };
    
    const onVideoError = (error) => {
      console.error('视频加载错误:', error);
      videoError.value = true;
      hasVideo.value = false;
    };
    
    const retryVideo = () => {
      videoError.value = false;
      if (videoPlayer.value) {
        videoPlayer.value.load();
      }
    };
    
    // 控制按钮相关
    const toggleMute = () => {
      isMuted.value = !isMuted.value;
      if (audioPlayer.value) {
        audioPlayer.value.muted = isMuted.value;
      }
    };
    
    const toggleMiniMode = () => {
      miniMode.value = !miniMode.value;
    };
    
    // 查看数字人详情
    const viewDigitalHumanDetails = () => {
      if (digitalHumanId.value) {
        // 使用路由参数ID跳转，保持ID类型一致性
        router.push(`/digital-human/detail/${digitalHumanId.value}`);
      }
    };
    
    // 清除聊天记录
    const confirmClearChat = () => {
      ElMessageBox.confirm(
        '清除后不可恢复，确认继续吗？',
        '确认清除聊天记录',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        // 清除聊天记录
        messages.value = [];
        // 清除本地存储
        if (digitalHumanId.value) {
          localStorage.removeItem(`chat_history_${digitalHumanId.value}`);
        }
        ElMessage.success('聊天记录已清除');
      }).catch(() => {
        // 用户取消，不做任何操作
      });
    };
    
    // 获取默认头像
    const defaultAvatar = computed(() => {
      // 根据数字人类型返回不同的默认头像
      if (digitalHuman.value && digitalHuman.value.type) {
        const type = digitalHuman.value.type;
        const typeAvatars = {
          assistant: getStaticResourceUrl('/images/avatar/assistant-default.svg'),
          customer_service: getStaticResourceUrl('/images/avatar/customer-service-default.svg'),
          broadcaster: getStaticResourceUrl('/images/avatar/broadcaster-default.svg'),
          teacher: getStaticResourceUrl('/images/avatar/teacher-default.svg')
        };
        
        return typeAvatars[type] || getStaticResourceUrl('/images/avatar/digital-human-default.svg');
      }
      
      return getStaticResourceUrl('/images/avatar/digital-human-default.svg');
    });
    
    // 获取类型文本
    const getTypeText = (type) => {
      const typeTexts = {
        assistant: '智能助手',
        customer_service: '客服专员',
        broadcaster: '主播讲解员',
        teacher: '教育培训'
      };
      
      return typeTexts[type] || '通用数字人';
    };
    
    // 返回上一页
    const goBack = () => {
      router.back();
    };
    
    // 语音输入相关
    const startRecording = async (event) => {
      try {
        // 验证事件
        if (!event || !event.isTrusted) {
          console.warn('收到不可信的事件');
          return;
        }

        // 检查是否已经在录音
        if (isRecording.value) {
          console.log('已经在录音中');
          return;
        }

        // 检查是否正在处理语音
        if (isProcessingSpeech.value) {
          console.log('正在处理语音中');
          return;
        }

        // 获取麦克风权限
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        
        // 创建 MediaRecorder
        mediaRecorder.value = new MediaRecorder(stream);
        audioChunks.value = [];
        
        // 设置数据可用事件处理
        mediaRecorder.value.ondataavailable = (event) => {
          if (event.data.size > 0) {
            audioChunks.value.push(event.data);
          }
        };
        
        // 设置停止事件处理
        mediaRecorder.value.onstop = () => {
          const audioBlob = new Blob(audioChunks.value, { type: 'audio/wav' });
          processAudioData(audioBlob);
          
          // 释放麦克风
          stream.getTracks().forEach(track => track.stop());
        };
        
        // 开始录音
        mediaRecorder.value.start();
        isRecording.value = true;
        
        // 显示录音状态
        ElMessage.success('开始录音，请说话...');
      } catch (error) {
        console.error('开始录音失败:', error);
        ElMessage.error('无法访问麦克风，请检查权限设置');
        isRecording.value = false;
      }
    };

    const stopRecording = async (event) => {
      try {
        // 验证事件
        if (!event || !event.isTrusted) {
          console.warn('收到不可信的事件');
          return;
        }

        // 检查是否在录音中
        if (!isRecording.value) {
          return;
        }

        // 停止录音
        if (mediaRecorder.value && mediaRecorder.value.state === 'recording') {
          mediaRecorder.value.stop();
          isRecording.value = false;
          message.info('录音已停止');
        }
      } catch (error) {
        console.error('停止录音失败:', error);
        message.error('停止录音失败');
        isRecording.value = false;
      }
    };

    // 处理音频数据
    const processAudioData = async (audioBlob) => {
      try {
        isProcessingSpeech.value = true;
        
        // 创建 FormData
        const formData = new FormData();
        formData.append('audio', audioBlob, 'recording.wav');
        
        // 发送到服务器进行语音识别
        const response = await fetch('/api/speech-to-text', {
          method: 'POST',
          body: formData
        });
        
        if (!response.ok) {
          throw new Error('语音识别请求失败');
        }
        
        const result = await response.json();
        
        if (result.text) {
          // 将识别的文本添加到输入框
          inputMessage.value = result.text;
          // 自动发送消息
          await sendMessage(result.text);
        }
      } catch (error) {
        console.error('处理音频数据失败:', error);
        message.error('语音识别失败，请重试');
      } finally {
        isProcessingSpeech.value = false;
      }
    };
    
    // 切换设置抽屉
    const toggleSettings = () => {
      settingsVisible.value = !settingsVisible.value;
    };
    
    // 初始化口型同步渲染器
    const initLipSyncRenderer = () => {
      try {
        // 检查DOM是否准备好
        const canvas = document.getElementById('lip-sync-canvas');
        if (!canvas) {
          console.warn('口型同步画布元素不存在，等待DOM元素挂载');
          
          // 先检查数字人容器是否存在
          const container = document.querySelector('.digital-human-container');
          if (!container) {
            console.error('数字人容器不存在，无法创建口型同步渲染器');
            return false;
          }
          
          // 创建新的canvas元素
          console.log('创建新的口型同步画布元素');
          const newCanvas = document.createElement('canvas');
          newCanvas.id = 'lip-sync-canvas';
          newCanvas.classList.add('lip-sync-canvas');
          newCanvas.width = 300;
          newCanvas.height = 100;
          newCanvas.style.position = 'absolute';
          newCanvas.style.bottom = '0';
          newCanvas.style.left = '0';
          newCanvas.style.zIndex = '10';
          newCanvas.style.pointerEvents = 'none'; // 确保不阻止点击
          
          // 添加到数字人容器
          container.appendChild(newCanvas);
          
          // 等待DOM更新后再尝试初始化
          setTimeout(() => {
            const retryCanvas = document.getElementById('lip-sync-canvas');
            if (retryCanvas) {
              console.log('已创建口型同步画布元素，尝试初始化渲染器');
              // 递归调用初始化
              initLipSyncRenderer();
            } else {
              console.error('口型同步画布元素依然不可用，放弃口型同步渲染器初始化');
            }
          }, 100);
          
          return false;
        }
        
        // 确保有适当的尺寸
        if (!canvas.width || !canvas.height) {
          canvas.width = 300;
          canvas.height = 100;
        }
        
        // 创建渲染器
        lipSyncRenderer = {
          canvas: canvas,
          ctx: canvas.getContext('2d'),
          frames: [],
          currentFrame: 0,
          isPlaying: false,
          animationId: null,
          
          setFrames(frames) {
            this.frames = frames || [];
            this.currentFrame = 0;
          },
          
          play() {
            if (!this.frames || this.frames.length === 0) {
              console.warn('没有口型帧数据，无法播放');
              return;
            }
            
            this.isPlaying = true;
            this.renderLoop();
          },
          
          stop() {
            this.isPlaying = false;
            if (this.animationId) {
              cancelAnimationFrame(this.animationId);
              this.animationId = null;
            }
          },
          
          reset() {
            this.stop();
            this.currentFrame = 0;
            this.frames = [];
            this.clear();
          },
          
          clear() {
            if (this.ctx && this.canvas) {
              this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            }
          },
          
          renderLoop() {
            if (!this.isPlaying) return;
            
            // 清除上一帧
            this.clear();
            
            // 渲染当前帧
            if (this.frames.length > 0) {
              const frameData = this.frames[this.currentFrame];
              this.renderFrame(frameData);
              
              // 增加帧索引
              this.currentFrame = (this.currentFrame + 1) % this.frames.length;
            }
            
            // 请求下一帧
            this.animationId = requestAnimationFrame(() => this.renderLoop());
          },
          
          renderFrame(frameData) {
            // 简单实现：绘制矩形表示口型
            if (!this.ctx || !frameData) return;
            
            const { value } = frameData;
            
            // 根据口型值调整口型显示
            const mouthOpenness = value ? Math.min(value / 100, 1) : 0.1;
            
            // 绘制嘴巴形状
            const centerX = this.canvas.width / 2;
            const centerY = this.canvas.height / 2;
            const width = 60;
            const height = 30 * mouthOpenness;
            
            this.ctx.fillStyle = 'rgba(255, 100, 100, 0.6)';
            this.ctx.beginPath();
            this.ctx.ellipse(centerX, centerY, width / 2, height / 2, 0, 0, Math.PI * 2);
            this.ctx.fill();
          }
        };
        
        console.log('口型同步渲染器初始化成功');
        return true;
      } catch (error) {
        console.error('初始化口型同步渲染器出错:', error);
        return false;
      }
    };

    // 保存口型同步设置
    const saveLipSyncSettings = () => {
      localStorage.setItem('use_lip_sync', settings.enableLipSync);
      localStorage.setItem('lip_sync_quality', settings.lipSyncQuality);
    };
    
    // 保存通用设置
    const saveSettings = (key, value) => {
      localStorage.setItem(key, value);
      message.success(`设置已保存`);
    };
    
    // 测试口型同步功能
    const testLipSync = () => {
      if (!lipSyncRenderer) {
        initLipSyncRenderer();
        if (!lipSyncRenderer) {
          message.error('初始化口型同步渲染器失败');
          return;
        }
      }
      
      // 测试用的音素序列
      const testPhonemes = [
        { phoneme: 'sil', time: 0.0, duration: 0.2 },
        { phoneme: 'a', time: 0.2, duration: 0.3 },
        { phoneme: 'o', time: 0.5, duration: 0.3 },
        { phoneme: 'e', time: 0.8, duration: 0.3 },
        { phoneme: 'i', time: 1.1, duration: 0.3 },
        { phoneme: 'u', time: 1.4, duration: 0.3 },
        { phoneme: 'm', time: 1.7, duration: 0.2 },
        { phoneme: 'b', time: 1.9, duration: 0.2 },
        { phoneme: 'f', time: 2.1, duration: 0.2 },
        { phoneme: 's', time: 2.3, duration: 0.2 },
        { phoneme: 'sil', time: 2.5, duration: 0.5 }
      ];
      
      // 创建测试帧
      const testFrames = createLipSyncFramesAlt(testPhonemes, 3.0);
      
      // 播放测试动画
      lipSyncRenderer.setFrames(testFrames, 3.0).reset().play();
      
      message.success('正在播放测试口型动画');
    };
    
    // 使用API测试口型同步功能
    const testLipSyncWithAPI = async () => {
      try {
        message.loading('正在获取口型同步数据...');
        
        // 测试文本
        const testText = '你好，我是数字人，我可以进行自然的口型动画。';
        
        // 使用新的分离流程
        // 1. 获取口型同步数据
        const lipSyncResponse = await post(`/api/lip-sync/text`, {
          text: testText,
          duration: testText.length * 0.1  // 估算时长：每个字0.1秒
        });
        
        if (lipSyncResponse && lipSyncResponse.success) {
          // 显示结果
          message.success('获取口型同步数据成功');
          console.log('口型同步数据:', lipSyncResponse);
          
          // 播放口型动画
          if (!lipSyncRenderer) {
            initLipSyncRenderer();
          }
          
          if (lipSyncRenderer) {
            lipSyncRenderer.setFrames(lipSyncResponse.frames, lipSyncResponse.duration).reset().play();
            
            // 如果有音频URL，播放音频
            if (lipSyncResponse.audio_url) {
              currentAudioUrl.value = lipSyncResponse.audio_url;
              if (audioPlayer.value) {
                // 添加错误处理
                const errorHandler = (e) => {
                  console.error(`测试音频加载失败:`, e);
                  message.error('音频加载失败，仅显示口型动画');
                  // 移除错误处理器
                  audioPlayer.value.removeEventListener('error', errorHandler);
                };
                
                // 添加一次性错误处理器
                audioPlayer.value.addEventListener('error', errorHandler, { once: true });
                
                audioPlayer.value.load();
                audioPlayer.value.play()
                  .then(() => {
                    console.log('测试音频开始播放');
                  })
                  .catch(err => {
                    console.error('播放测试音频失败:', err);
                  });
              }
            }
          }
        } else {
          message.error('获取口型同步数据失败: ' + (lipSyncResponse.data?.error || '未知错误'));
        }
      } catch (error) {
        message.error('测试口型同步失败: ' + error.message);
        console.error('测试口型同步失败:', error);
      }
    };
    
    // 获取口型同步数据
    const getLipSyncData = async (text, options = {}) => {
      try {
        const response = await post('/api/lip-sync/text', {
          text,
          language: options.language || 'zh-CN',
          duration: options.duration || null,
          format: options.format || 'json',
          voice_id: options.voice_id || null
        });
        
        if (response && response.success) {
          return {
            success: true,
            frames: response.frames,
            duration: response.duration,
            audio_url: response.audio_url
          };
        } else {
          return {
            success: false,
            error: response?.error || '获取口型同步数据失败'
          };
        }
      } catch (error) {
        console.error('获取口型同步数据错误:', error);
        return {
          success: false,
          error: error.message || '请求口型同步数据时发生错误'
        };
      }
    };
    
    // 获取数字人消息的口型同步数据
    const getDigitalHumanMessageLipSync = async (digitalHumanId, message, voiceId = null) => {
      if (!digitalHumanId) return null;
      
      try {
        console.log(`请求口型同步API: /api/lip-sync/digital-human/${digitalHumanId}/message，消息:`, message);
        
        const response = await post(`/api/lip-sync/digital-human/${digitalHumanId}/message`, {
          message,
          voice_id: voiceId,
          language: 'zh-CN'
        });
        
        if (response && response.success) {
          console.log(`口型同步API返回成功:`, response);
          
          // 注意：不再使用API返回的消息作为AI回复
          // 这个函数现在仅用于获取口型同步数据
          
          // 构造标准化的响应数据结构
          const result = {
            success: true,
            lip_sync_data: response.lip_sync,
            audio_url: response.audio_url
          };
          
          // 检查音频URL路径并处理可能的模拟URL
          if (result.audio_url) {
            // 确保音频URL使用一致的格式
            if (!result.audio_url.startsWith('http') && !result.audio_url.startsWith('/')) {
              result.audio_url = `/${result.audio_url}`;
            }
            
            // 检查是否是模拟URL (匹配随机字符串格式)
            const isMockUrl = result.audio_url.match(/\/api\/audio\/digital-human\/.*\/[a-f0-9]+\.mp3$/i);
            if (isMockUrl) {
              console.warn(`检测到模拟音频URL: ${result.audio_url}，尝试替换为有效URL`);
              
              // 尝试使用已知的有效音频URL格式
              if (digitalHumanId) {
                const alternativeUrl = `/api/digital-human/media/audio/${digitalHumanId}`;
                console.log(`替换为可能有效的API URL: ${alternativeUrl}`);
                result.audio_url = alternativeUrl;
              }
            }
            
            console.log(`处理后的音频URL:`, result.audio_url);
          }
          
          return result;
        } else {
          console.error('获取口型同步数据失败:', response.data?.error || '未知错误');
          return null;
        }
      } catch (error) {
        console.error('获取口型同步数据错误:', error);
        return null;
      }
    };
    
    // 获取数字人备用音频URL
    const getBackupAudioUrl = () => {
      if (!digitalHuman.value || !digitalHuman.value.apiId) return null;
      return `/api/digital-human/media/audio/${digitalHuman.value.apiId}?t=${Date.now()}`;
    };
    
    // 创建AudioContext并设置虚拟音频源（当音频无法播放时使用）
    const createVirtualAudio = (duration = 3000) => {
      try {
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        if (!AudioContext) return null;
        
        const audioCtx = new AudioContext();
        const oscillator = audioCtx.createOscillator();
        const gainNode = audioCtx.createGain();
        
        // 设置为静音
        gainNode.gain.value = 0;
        
        // 连接节点
        oscillator.connect(gainNode);
        gainNode.connect(audioCtx.destination);
        
        // 创建模拟的audio元素API
        const virtualAudio = {
          play: () => {
            oscillator.start();
            setTimeout(() => {
              oscillator.stop();
              if (virtualAudio.onended) virtualAudio.onended();
            }, duration);
            return Promise.resolve();
          },
          pause: () => oscillator.stop(),
          onended: null,
          onerror: null
        };
        
        return virtualAudio;
      } catch (e) {
        console.error("创建虚拟音频失败:", e);
        return null;
      }
    };
    
    // 添加调试信息，显示音频URL信息
    const showDebugInfo = computed(() => settings.debugMode);
    
    // 输出调试信息
    const logDebugInfo = (message, data) => {
      if (settings.debugMode) {
        console.log(`[DEBUG] ${message}`, data);
      }
    };
    
    // 获取音频URL的简洁表示
    const formatAudioUrl = (url) => {
      if (!url) return '无';
      if (url.length > 50) {
        return `${url.substring(0, 25)}...${url.substring(url.length - 25)}`;
      }
      return url;
    };
    
    // 显示音频URL调试信息
    const getMessageDebugInfo = (message) => {
      if (!message || !showDebugInfo.value) return '';
      
      let info = '';
      if (message.audio_url) {
        info += `音频: ${formatAudioUrl(message.audio_url)}`;
      }
      
      if (message.lip_sync_data) {
        const frames = message.lip_sync_data.frames || [];
        info += ` | 口型同步: ${frames.length}帧`;
      }
      
      return info;
    };
    
    // 开始实时聊天
    const startRealtimeChat = () => {
      if (!digitalHuman.value) {
        message.error('无法启动实时聊天，数字人信息无效');
        return;
      }

      try {
        // 跳转到实时聊天页面，并传递数字人ID
        const digitalHumanId = digitalHuman.value.digital_human_id || digitalHuman.value.id;
        if (!digitalHumanId) {
          message.error('数字人ID无效，无法启动实时聊天');
          return;
        }

        // 跳转到实时聊天页面
        router.push({
          name: 'RealtimeDigitalHumanChat',
          query: {
            digitalHumanId: digitalHumanId,
            name: digitalHuman.value.name || '数字人'
          }
        });

        message.success(`正在启动与 ${digitalHuman.value.name || '数字人'} 的实时视频对话...`);

      } catch (error) {
        console.error('启动实时聊天失败:', error);
        message.error('启动实时聊天失败，请重试');
      }
    };

    // 初始化函数
    const init = async () => {
      try {
        await initOllamaSettings();
        await loadDigitalHumanData();
      } catch (error) {
        console.error('初始化失败:', error);
        message.error('初始化失败');
      }
    };
    
    // 监听路由参数变化
    watch(() => route.params.id, (newId) => {
      if (newId) {
        init();
      }
    }, { immediate: true });
    
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      isSmallScreen.value = window.innerWidth < 768;
    });
    
    // 组件挂载时初始化
    onMounted(() => {
      init();
    });
    
    // 返回所有需要在模板中使用的响应式数据和方法
    const exposed = {
      settings,
      digitalHuman,
      loading,
      hasVideo,
      videoError,
      isSpeaking,
      isMuted,
      miniMode,
      showChatArea,
      isCompactLayout,
      isSmallScreen,
      messages,
      currentAudioUrl,
      userAvatar,
      quickReplies,
      settings,
      typeSpecificMessage,
      isSmallScreen,
      isCompactLayout,
      showChatArea,
      isSending,
      defaultAvatar,
      isRecording, // 添加此属性
      isProcessingSpeech, // 添加此属性
      settingsVisible, // 添加此属性
      inputMessage, // 添加缺失的属性
      isTyping, // 添加缺失的属性
      
      // 方法
      sendMessage,
      useQuickReply,
      onEnterPress,
      formatMessage,
      formatTime,
      copyMessage,
      speakMessage,
      startRecording,
      stopRecording,
      onVideoReady,
      onVideoError,
      retryVideo,
      onAudioEnded,
      toggleMute,
      toggleMiniMode,
      toggleSettings,
      viewDigitalHumanDetails,
      confirmClearChat,
      getTypeText,
      goBack,
      getWelcomeMessage,
      handleTypeMessageAction,
      closeTypeSpecificMessage,
      startRealtimeChat,
      startResize,
      toggleTheme,
      toggleLayout,
      getTypeComponent,
      handleFeatureAction,
      ollamaSettings,
      ollamaModels,
      ollamaAvailable,
      loadingOllamaModels,
      refreshOllamaModels,
      saveOllamaSettings,
      testLipSyncWithAPI,
      saveLipSyncSettings,
      testLipSync,
      lipSyncCanvas,
      saveSettings,
    };

    return exposed;
  }
});
</script>

<style scoped>
/* 基础样式 */
.digital-human-chat {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #111827, #1f2937);
  color: #f9fafb;
  transition: background 0.3s ease, color 0.3s ease;
}

/* 浅色主题 */
.digital-human-chat.light-theme {
  background: linear-gradient(135deg, #f5f7fa, #e4e7eb);
  color: #1f2937;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  height: 100%;
}

/* 头部导航 */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background-color: rgba(31, 41, 55, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 10;
  height: 70px;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.light-theme .chat-header {
  background-color: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.back-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #e5e7eb;
  transition: color 0.3s;
  font-size: 16px;
  font-weight: 500;
}

.light-theme .back-button {
  color: #374151;
}

.back-text {
  margin-left: 8px;
}

.back-button:hover {
  color: #ffffff;
}

.light-theme .back-button:hover {
  color: #000000;
}

.digital-human-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.info-name {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
}

.light-theme .info-name {
  color: #1f2937;
}

.info-tag {
  font-size: 12px;
  background: rgba(255, 255, 255, 0.15);
  border: none;
  color: #d1d5db;
  transition: background 0.3s ease, color 0.3s ease;
}

.light-theme .info-tag {
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
}

.tag-type-assistant {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
}

.tag-type-customer_service {
  background: rgba(16, 185, 129, 0.2);
  color: #34d399;
}

.tag-type-broadcaster {
  background: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

.tag-type-teacher {
  background: rgba(245, 158, 11, 0.2);
  color: #fbbf24;
}

.light-theme .tag-type-assistant {
  background: rgba(59, 130, 246, 0.1);
  color: #1d4ed8;
}

.light-theme .tag-type-customer_service {
  background: rgba(16, 185, 129, 0.1);
  color: #047857;
}

.light-theme .tag-type-broadcaster {
  background: rgba(239, 68, 68, 0.1);
  color: #b91c1c;
}

.light-theme .tag-type-teacher {
  background: rgba(245, 158, 11, 0.1);
  color: #b45309;
}

.chat-actions {
  display: flex;
  gap: 12px;
}

.action-button {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  transition: all 0.3s;
  font-size: 18px;
}

.light-theme .action-button {
  background: rgba(0, 0, 0, 0.05);
  color: #1f2937;
}

.action-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.light-theme .action-button:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #000000;
}

.action-button.realtime-chat {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

.action-button.realtime-chat:hover {
  background: rgba(82, 196, 26, 0.2);
  color: #52c41a;
  transform: scale(1.1);
}

.light-theme .action-button.realtime-chat {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

.light-theme .action-button.realtime-chat:hover {
  background: rgba(82, 196, 26, 0.2);
  color: #52c41a;
}

.theme-toggle {
  color: #facc15;
}

.light-theme .theme-toggle {
  color: #9333ea;
}

/* 主聊天区域 */
.chat-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* 紧凑布局（上下排列）*/
.chat-container.compact-layout {
  flex-direction: column;
}

/* 正常布局（左右排列）*/
.chat-container:not(.compact-layout) {
  display: flex !important; /* 使用flex替代grid */
  flex-direction: row !important;
}

/* 左侧数字人区域 */
.digital-human-container {
  width: 400px !important; /* 强制使用固定宽度 */
  max-width: 40% !important; /* 确保不超过容器宽度的40% */
  min-width: 300px !important; /* 确保最小宽度 */
  display: flex;
  flex-direction: column;
  background-color: rgba(17, 24, 39, 0.8);
  transition: width 0.3s ease, background-color 0.3s ease;
  position: relative;
  flex-shrink: 0;
}

.light-theme .digital-human-container {
  background-color: rgba(255, 255, 255, 0.9);
}

.resize-handle {
  position: absolute;
  right: -6px;
  top: 0;
  width: 12px;
  height: 100%;
  cursor: ew-resize;
  z-index: 5;
}

.resize-handle:hover::after {
  content: '';
  position: absolute;
  top: 0;
  right: 4px;
  width: 3px;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.light-theme .resize-handle:hover::after {
  background-color: rgba(0, 0, 0, 0.2);
}

.compact-layout .digital-human-container {
  width: 100%;
  max-height: 350px;
}

.digital-human-container.mini-mode {
  width: 280px;
}

.compact-layout .digital-human-container.mini-mode {
  width: 100%;
  max-height: 250px;
}

/* 类型特定背景 */
.digital-human-container.dh-type-assistant {
  background-color: rgba(37, 99, 235, 0.05);
}

.digital-human-container.dh-type-customer_service {
  background-color: rgba(5, 150, 105, 0.05);
}

.digital-human-container.dh-type-broadcaster {
  background-color: rgba(220, 38, 38, 0.05);
}

.digital-human-container.dh-type-teacher {
  background-color: rgba(217, 119, 6, 0.05);
}

.light-theme .digital-human-container.dh-type-assistant {
  background-color: rgba(37, 99, 235, 0.03);
}

.light-theme .digital-human-container.dh-type-customer_service {
  background-color: rgba(5, 150, 105, 0.03);
}

.light-theme .digital-human-container.dh-type-broadcaster {
  background-color: rgba(220, 38, 38, 0.03);
}

.light-theme .digital-human-container.dh-type-teacher {
  background-color: rgba(217, 119, 6, 0.03);
}

.dh-video-container {
  position: relative;
  height: 350px;
  overflow: hidden;
  background-color: #0f172a;
  transition: background-color 0.3s ease;
}

.light-theme .dh-video-container {
  background-color: #f8fafc;
}

.mini-mode .dh-video-container {
  height: 250px;
}

.dh-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.dh-avatar-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.dh-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dh-type-assistant .dh-avatar {
  border-color: rgba(59, 130, 246, 0.3);
}

.dh-type-customer_service .dh-avatar {
  border-color: rgba(16, 185, 129, 0.3);
}

.dh-type-broadcaster .dh-avatar {
  border-color: rgba(239, 68, 68, 0.3);
}

.dh-type-teacher .dh-avatar {
  border-color: rgba(245, 158, 11, 0.3);
}

/* 说话指示器样式增强 */
.dh-speaking-indicator {
  position: absolute;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: flex-end;
  gap: 4px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 2px 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
  z-index: 5;
}

.dh-speaking-indicator span {
  display: inline-block;
  width: 5px;
  height: 12px;
  background-color: #60a5fa;
  border-radius: 2px;
  animation: soundWave 1s infinite ease-in-out;
}

.dh-type-customer_service .dh-speaking-indicator span {
  background-color: #34d399;
}

.dh-type-broadcaster .dh-speaking-indicator span {
  background-color: #f87171;
}

.dh-type-teacher .dh-speaking-indicator span {
  background-color: #fbbf24;
}

.dh-speaking-indicator span:nth-child(2) {
  animation-delay: 0.3s;
}

.dh-speaking-indicator span:nth-child(3) {
  animation-delay: 0.6s;
}

/* 嘴部动画 - 添加到头像下方 */
.dh-avatar-container:after {
  content: '';
  position: absolute;
  bottom: 65px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 3px;
  opacity: 0;
  transition: all 0.2s ease;
}

.dh-avatar-container.is-speaking:after {
  opacity: 1;
  animation: mouthAnimation 0.5s infinite alternate;
}

@keyframes mouthAnimation {
  0% {
    width: 30px;
    height: 3px;
    border-radius: 3px;
  }
  100% {
    width: 40px;
    height: 12px;
    border-radius: 10px 10px 15px 15px;
  }
}

@keyframes soundWave {
  0%, 100% {
    height: 6px;
  }
  50% {
    height: 18px;
  }
}

.video-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(17, 24, 39, 0.9);
}

.light-theme .video-error {
  background-color: rgba(248, 250, 252, 0.9);
}

.error-icon {
  font-size: 36px;
  color: #ef4444;
  margin-bottom: 16px;
}

.error-message {
  color: #f9fafb;
  margin-bottom: 16px;
  font-size: 16px;
}

.light-theme .error-message {
  color: #1f2937;
}

.dh-audio {
  display: none;
}

.dh-controls {
  position: absolute;
  bottom: 16px;
  right: 16px;
  display: flex;
  gap: 12px;
}

.control-button {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  font-size: 20px;
}

.light-theme .control-button {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dh-details {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.dh-name {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #ffffff;
}

.light-theme .dh-name {
  color: #1f2937;
}

.dh-description {
  font-size: 14px;
  color: #d1d5db;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.light-theme .dh-description {
  color: #4b5563;
}

/* 分类特定功能区 */
.type-specific-features {
  margin-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
}

.light-theme .type-specific-features {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* 右侧聊天区域 */
.chat-message-area {
  flex: 1;
  display: flex !important;
  flex-direction: column !important;
  background-color: rgba(17, 24, 39, 0.6);
  transition: all 0.3s ease;
  min-width: 300px !important; /* 确保最小宽度 */
  width: 60% !important; /* 确保占据至少60%的宽度 */
}

.light-theme .chat-message-area {
  background-color: rgba(248, 250, 252, 0.8);
}

.hidden-on-mobile {
  display: flex; /* 默认显示 */
}

/* 在小屏幕设备上隐藏，并确保最高优先级 */
@media (max-width: 767px) {
  .hidden-on-mobile {
    display: none !important;
  }
  
  /* 小屏幕设备上强制使用column布局 */
  .chat-container {
    display: flex !important;
    flex-direction: column !important;
  }
  
  .digital-human-container {
    width: 100% !important;
    max-width: 100% !important;
  }
}

/* 添加明确的大屏幕显示规则，确保优先级 */
@media (min-width: 768px) {
  .chat-message-area, .always-show-on-desktop {
    display: flex !important; /* 强制大屏幕设备显示聊天区域 */
  }
  
  .chat-container {
    flex-direction: row !important;
  }
  
  .digital-human-container {
    width: 400px !important;
    max-width: 40% !important;
    min-width: 300px !important;
  }
}

/* 聊天消息列表 */
.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 欢迎消息样式 */
.welcome-message {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: border-color 0.3s ease;
}

.light-theme .welcome-avatar {
  border: 2px solid rgba(0, 0, 0, 0.1);
}

.welcome-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.welcome-content {
  background-color: rgba(59, 130, 246, 0.15);
  padding: 20px;
  border-radius: 16px;
  border-top-left-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.light-theme .welcome-content {
  background-color: rgba(59, 130, 246, 0.08);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.welcome-content h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #f9fafb;
}

.light-theme .welcome-content h3 {
  color: #1f2937;
}

.welcome-content p {
  font-size: 15px;
  color: #e5e7eb;
  margin: 0;
  line-height: 1.6;
}

.light-theme .welcome-content p {
  color: #4b5563;
}

/* 消息输入区域 */
.message-input-container {
  padding: 16px 24px 24px;
  background: rgba(17, 24, 39, 0.7);
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  z-index: 5;
}

.light-theme .message-input-container {
  background: rgba(255, 255, 255, 0.95);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

/* 新的自定义输入容器 */
.custom-input-container {
  display: flex;
  flex-direction: row;
  margin-bottom: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 24px;
  overflow: hidden;
  align-items: center;
  position: relative;
  width: 100%;
}

.light-theme .input-with-buttons {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.voice-button, .send-button {
  height: auto !important;
  min-height: 54px !important;
  width: 54px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none !important;
  font-size: 20px;
  flex-shrink: 0;
  transition: all 0.3s ease !important;
  z-index: 2;
}

.voice-button {
  border-top-left-radius: 24px !important;
  border-bottom-left-radius: 24px !important;
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  background-color: rgba(255, 255, 255, 0.15) !important;
  color: #ffffff !important;
}

.light-theme .voice-button {
  background-color: rgba(0, 0, 0, 0.05) !important;
  color: #1f2937 !important;
}

.voice-button:hover {
  background-color: rgba(255, 255, 255, 0.25) !important;
}

.light-theme .voice-button:hover {
  background-color: rgba(0, 0, 0, 0.08) !important;
}

.send-button {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-top-right-radius: 24px !important;
  border-bottom-right-radius: 24px !important;
  background-color: #1890ff !important;
}

.send-button:hover {
  background-color: #40a9ff !important;
}

:deep(.message-input) {
  flex: 1 !important;
  border: none !important;
  border-radius: 0 !important;
}

:deep(.ant-input) {
  min-height: 54px !important;
  background: rgba(255, 255, 255, 0.1);
  border: none !important;
  color: #f9fafb;
  font-size: 16px;
  padding: 14px 18px;
  border-radius: 0;
  transition: background-color 0.3s ease, color 0.3s ease;
  resize: none;
  line-height: 1.5;
  overflow-y: auto;
}

.light-theme :deep(.ant-input) {
  background: rgba(0, 0, 0, 0.03);
  color: #1f2937;
}

:deep(.ant-input):focus {
  outline: none;
  box-shadow: none;
  background: rgba(255, 255, 255, 0.15);
}

.light-theme :deep(.ant-input):focus {
  background: rgba(0, 0, 0, 0.05);
}

.message-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.light-theme .message-input::placeholder {
  color: rgba(0, 0, 0, 0.4);
}

.speech-feedback {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 12px;
  animation: fadeIn 0.3s ease-out;
}

.light-theme .speech-feedback {
  color: rgba(0, 0, 0, 0.6);
}

.sound-icon {
  color: #1890ff;
  font-size: 16px;
}

.recording-text {
  font-size: 14px;
}

/* 快捷回复区域 */
.quick-replies {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 6px;
}

.quick-reply {
  border-radius: 18px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.08);
  padding: 6px 14px;
  font-size: 14px;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.9);
  height: auto;
}

.light-theme .quick-reply {
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.03);
  color: rgba(0, 0, 0, 0.85);
}

.quick-reply:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
}

.light-theme .quick-reply:hover {
  background: rgba(0, 0, 0, 0.06);
  border-color: rgba(0, 0, 0, 0.15);
}

/* 设置抽屉样式 */
:deep(.chat-settings-drawer) {
  .ant-drawer-content {
    background: #1f2937;
  }

  .ant-drawer-header {
    background: #111827;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .ant-drawer-title {
    color: #f9fafb;
    font-weight: 600;
  }

  .ant-drawer-body {
    padding: 24px;
  }

  .ant-form-item-label label {
    color: #d1d5db;
  }

  .ant-radio-button-wrapper {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .ant-radio-button-wrapper-checked {
    background: #3b82f6;
    border-color: #2563eb;
    color: #ffffff;
  }

  .ant-slider-rail {
    background-color: #374151;
  }

  .ant-slider-track {
    background-color: #3b82f6;
  }

  .ant-slider-handle {
    border-color: #3b82f6;
  }

  .ant-divider {
    border-color: rgba(255, 255, 255, 0.1);
  }
}

:deep(.chat-settings-drawer.light-theme) {
  .ant-drawer-content {
    background: #ffffff;
  }

  .ant-drawer-header {
    background: #f9fafb;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  .ant-drawer-title {
    color: #1f2937;
  }

  .ant-form-item-label label {
    color: #4b5563;
  }

  .ant-radio-button-wrapper {
    background: #f9fafb;
    border-color: #d1d5db;
    color: #1f2937;
  }

  .ant-radio-button-wrapper-checked {
    background: #3b82f6;
    border-color: #2563eb;
    color: #ffffff;
  }

  .ant-slider-rail {
    background-color: #e5e7eb;
  }

  .ant-slider-track {
    background-color: #3b82f6;
  }

  .ant-slider-handle {
    border-color: #3b82f6;
  }

  .ant-divider {
    border-color: rgba(0, 0, 0, 0.1);
  }
}

/* 响应式调整 */
@media (max-width: 992px) {
  .digital-human-container {
    width: 340px;
  }

  .digital-human-container.mini-mode {
    width: 240px;
  }
  
  .dh-video-container {
    height: 340px;
  }
  
  .dh-avatar {
    width: 220px;
    height: 220px;
  }
}

@media (max-width: 768px) {
  .digital-human-container {
    width: 320px;
  }
  
  .chat-header {
    padding: 14px 16px;
    gap: 10px;
  }
  
  .dh-name {
    font-size: 16px;
  }
  
  .voice-button, .send-button {
    height: 48px !important;
    width: 48px !important;
    font-size: 18px;
  }
  
  .message-input {
    height: 48px !important;
    font-size: 15px;
    padding: 12px 14px;
  }
  
  .input-with-buttons {
    border-radius: 22px;
  }
  
  .voice-button {
    border-top-left-radius: 22px !important;
    border-bottom-left-radius: 22px !important;
  }
  
  .send-button {
    border-top-right-radius: 22px !important;
    border-bottom-right-radius: 22px !important;
  }
  
  .welcome-avatar {
    width: 50px;
    height: 50px;
  }
  
  .welcome-content h3 {
    font-size: 16px;
  }
  
  .welcome-content p {
    font-size: 14px;
  }
  
  .message-input-container {
    padding: 12px 20px 20px;
  }
}

@media (max-width: 576px) {
  .chat-actions {
    gap: 8px;
  }
  
  .action-button {
    width: 36px;
    height: 36px;
  }
  
  .control-button {
    width: 40px;
    height: 40px;
  }
  
  .message-avatar {
    width: 36px;
    height: 36px;
  }
  
  .message-bubble {
    padding: 12px 16px;
  }
  
  .quick-replies {
    gap: 8px;
  }
  
  .quick-reply {
    font-size: 13px;
    padding: 5px 12px;
  }
  
  .voice-button, .send-button {
    height: 44px !important;
    width: 44px !important;
    font-size: 16px;
  }
  
  .message-input {
    height: 44px !important;
    font-size: 14px;
    padding: 10px 12px;
  }
  
  .input-with-buttons {
    border-radius: 20px;
    margin-bottom: 10px;
  }
  
  .voice-button {
    border-top-left-radius: 20px !important;
    border-bottom-left-radius: 20px !important;
  }
  
  .send-button {
    border-top-right-radius: 20px !important;
    border-bottom-right-radius: 20px !important;
  }
  
  .message-input-container {
    padding: 10px 16px 16px;
  }
}

.message-item {
  display: flex;
  gap: 12px;
  max-width: 85%;
  animation: fadeIn 0.3s ease-out;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.ai-message {
  align-self: flex-start;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: border-color 0.3s ease;
}

.light-theme .message-avatar {
  border: 2px solid rgba(0, 0, 0, 0.1);
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-bubble {
  padding: 16px 20px;
  border-radius: 18px;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.user-message .message-bubble {
  background-color: #3b82f6;
  border-top-right-radius: 4px;
  color: #f9fafb;
}

.light-theme .user-message .message-bubble {
  background-color: #2563eb;
  color: #ffffff;
}

.ai-message .message-bubble {
  background-color: rgba(31, 41, 55, 0.6);
  border-top-left-radius: 4px;
  color: #f9fafb;
}

.light-theme .ai-message .message-bubble {
  background-color: rgba(255, 255, 255, 0.9);
  color: #1f2937;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.error-message .message-bubble {
  background-color: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.light-theme .error-message .message-bubble {
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: #b91c1c;
}

.message-content {
  font-size: 15px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

.message-content a {
  color: #93c5fd;
  text-decoration: underline;
}

.light-theme .message-content a {
  color: #2563eb;
}

.message-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 8px;
  text-align: right;
}

.light-theme .message-time {
  color: rgba(0, 0, 0, 0.5);
}

.message-actions {
  display: flex;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.message-item:hover .message-actions {
  opacity: 1;
}

/* 类型特定消息 */
.type-specific-message {
  align-self: center;
  width: 90%;
  max-width: 500px;
  margin: 12px 0;
  animation: fadeIn 0.3s ease-out;
}

.type-message-content {
  background-color: rgba(31, 41, 55, 0.8);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.light-theme .type-message-content {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.type-message-header {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(17, 24, 39, 0.5);
  color: #f9fafb;
  font-weight: 600;
  font-size: 15px;
}

.light-theme .type-message-header {
  background-color: rgba(243, 244, 246, 0.9);
  color: #1f2937;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.close-btn {
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.close-btn:hover {
  opacity: 1;
}

.type-message-body {
  padding: 16px;
  color: #f0f2f5;
  font-size: 14px;
  line-height: 1.6;
}

.light-theme .type-message-body {
  color: #333;
}

.type-message-actions {
  padding: 12px 16px;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.light-theme .type-message-actions {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* 类型特定颜色 */
.type-message-assistant .type-message-header {
  background-color: rgba(37, 99, 235, 0.2);
}

.type-message-customer_service .type-message-header {
  background-color: rgba(5, 150, 105, 0.2);
}

.type-message-broadcaster .type-message-header {
  background-color: rgba(220, 38, 38, 0.2);
}

.type-message-teacher .type-message-header {
  background-color: rgba(217, 119, 6, 0.2);
}

.light-theme .type-message-assistant .type-message-header {
  background-color: rgba(37, 99, 235, 0.1);
  color: #1d4ed8;
}

.light-theme .type-message-customer_service .type-message-header {
  background-color: rgba(5, 150, 105, 0.1);
  color: #047857;
}

.light-theme .type-message-broadcaster .type-message-header {
  background-color: rgba(220, 38, 38, 0.1);
  color: #b91c1c;
}

.light-theme .type-message-teacher .type-message-header {
  background-color: rgba(217, 119, 6, 0.1);
  color: #b45309;
}

/* 输入中状态 */
.typing-indicator {
  display: flex;
  gap: 12px;
  align-self: flex-start;
}

.typing-bubble {
  padding: 12px 16px;
  background-color: rgba(31, 41, 55, 0.6);
  border-radius: 18px;
  border-top-left-radius: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.light-theme .typing-bubble {
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.typing-bubble span {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #9ca3af;
  animation: typingBubble 1.4s infinite ease-in-out both;
}

.typing-bubble span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-bubble span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typingBubble {
  0%, 80%, 100% { 
    transform: scale(0.6);
    opacity: 0.6;
  }
  40% { 
    transform: scale(1);
    opacity: 1;
  }
}

/* 处理Ant Design组件的深层样式，确保在Vue 3中正确渲染 */
:deep(.ant-input) {
  background: transparent !important;
  color: inherit !important;
  border: none !important;
  box-shadow: none !important;
}

:deep(.ant-input-textarea) {
  height: 100% !important;
}

:deep(.ant-input-textarea-show-count::after) {
  display: none !important;
}

:deep(.ant-btn) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 媒体查询，确保在移动设备上正确显示 */
@media (max-width: 576px) {
  .custom-input-container {
    border-radius: 20px;
  }
  
  .voice-button, .send-button {
    height: 48px !important;
    width: 48px !important;
    font-size: 18px;
  }
  
  .voice-button {
    border-top-left-radius: 20px !important;
    border-bottom-left-radius: 20px !important;
  }
  
  .send-button {
    border-top-right-radius: 20px !important;
    border-bottom-right-radius: 20px !important;
  }
  
  .message-input {
    min-height: 48px !important;
    font-size: 15px;
    padding: 12px 14px !important;
  }
}

/* 口型动画样式 */
.mouth-animation {
  position: absolute;
  bottom: 30%;
  left: 50%;
  transform: translateX(-50%);
  width: 20%;
  height: 4%;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: none;
  animation: mouthAnimate 0.5s infinite alternate;
}

.is-speaking .mouth-animation {
  display: block;
}

@keyframes mouthAnimate {
  0% { 
    transform: translateX(-50%) scaleY(0.3); 
  }
  100% { 
    transform: translateX(-50%) scaleY(1.2); 
  }
}

.settings-container {
  padding: 16px;
}

.settings-section {
  margin-bottom: 24px;
}

.settings-section h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
}

.settings-item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.settings-label {
  margin-left: 10px;
  color: #333;
}

.settings-alert {
  margin-bottom: 16px;
}

.light-theme .settings-label {
  color: #555;
}

.digital-human-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.lip-sync-canvas {
  position: absolute;
  bottom: 32%; /* 根据数字人模型调整位置 */
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  pointer-events: none; /* 允许点击通过 */
  opacity: 0.9;
}

/* 深色模式下的口型调整 */
.dark-mode .lip-sync-canvas {
  filter: brightness(0.8);
}

/* 媒体查询，适应不同屏幕尺寸 */
@media (max-width: 768px) {
  .lip-sync-canvas {
    width: 80px;
    height: 80px;
    bottom: 28%;
  }
}

@media (min-width: 769px) and (max-width: 1200px) {
  .lip-sync-canvas {
    width: 100px;
    height: 100px;
    bottom: 30%;
  }
}

.layout-toggle {
  position: relative;
}

.layout-toggle:after {
  content: '';
  position: absolute;
  top: -5px;
  right: -5px;
  width: 10px;
  height: 10px;
  background-color: #1890ff;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

.layout-tip {
  position: absolute;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  z-index: 10;
  text-align: center;
  max-width: 80%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  animation: fadeInOut 5s infinite;
}

@keyframes fadeInOut {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.light-theme .layout-tip {
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.layout-tip p {
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 确保大屏幕设备显示聊天区域 */
.always-show-on-desktop {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

/* 调试信息样式 */
.message-debug-info {
  font-size: 10px;
  color: #888;
  margin-top: 4px;
  padding: 4px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  font-family: monospace;
  word-break: break-all;
}

.light-theme .message-debug-info {
  background-color: rgba(0, 0, 0, 0.05);
  color: #555;
}
</style> 