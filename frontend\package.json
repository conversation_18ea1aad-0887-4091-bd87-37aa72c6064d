{"name": "ai-professional-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "dev-working": "vite --config vite.working.config.js index_working.html", "dev-minimal": "vite index_minimal.html", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@antv/g6": "^5.0.49", "@antv/x6": "^2.18.1", "@element-plus/icons-vue": "^2.3.1", "animate.css": "^4.1.1", "axios": "^1.5.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dompurify": "^3.0.8", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.10.4", "file-saver": "^2.0.5", "highlight.js": "^11.9.0", "howler": "^2.2.4", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "leaflet": "^1.9.4", "lottie-web": "^5.13.0", "marked": "^11.1.1", "node-fetch": "^3.3.2", "particles.js": "^2.0.0", "pinia": "^2.1.6", "recordrtc": "^5.6.2", "socket.io-client": "^4.7.4", "store": "^2.0.12", "three": "^0.175.0", "uuid": "^11.1.0", "vis-network": "^10.0.1", "vue": "^3.3.4", "vue-i18n": "^9.2.2", "vue-router": "^4.2.4", "vue3-leaflet": "^1.0.50", "wavesurfer.js": "^7.9.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/plugin-transform-react-jsx": "^7.25.9", "@vitejs/plugin-vue": "^4.6.2", "@vitejs/plugin-vue-jsx": "^3.0.1", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "isomorphic-fetch": "^3.0.0", "sass": "^1.86.1", "vite": "^4.5.14"}}