{"pages": [{"path": "pages/index/index", "style": {"navigationBarTitleText": "AI系统", "enablePullDownRefresh": true}}, {"path": "pages/index/modern", "style": {"navigationBarTitleText": "AI系统", "navigationStyle": "custom"}}, {"path": "pages/translation/text/index", "style": {"navigationBarTitleText": "文本翻译", "enablePullDownRefresh": true}}, {"path": "pages/translation/audio/index", "style": {"navigationBarTitleText": "音频翻译", "enablePullDownRefresh": true}}, {"path": "pages/translation/video/index", "style": {"navigationBarTitleText": "视频翻译", "enablePullDownRefresh": true}}, {"path": "pages/translation/document/index", "style": {"navigationBarTitleText": "文档翻译", "enablePullDownRefresh": true}}, {"path": "pages/digital-human/chat/index", "style": {"navigationBarTitleText": "数字人对话", "enablePullDownRefresh": true}}, {"path": "pages/ai-agents/market/index", "style": {"navigationBarTitleText": "AI智能体市场", "enablePullDownRefresh": true}}, {"path": "pages/utilities/document/converter/index", "style": {"navigationBarTitleText": "文档转换", "enablePullDownRefresh": true}}, {"path": "pages/utilities/image/processor/index", "style": {"navigationBarTitleText": "图片处理", "enablePullDownRefresh": true}}, {"path": "pages/utilities/text/processor/index", "style": {"navigationBarTitleText": "文本处理", "enablePullDownRefresh": true}}, {"path": "pages/utilities/qrcode/generator/index", "style": {"navigationBarTitleText": "二维码生成", "enablePullDownRefresh": true}}, {"path": "pages/user/profile/index", "style": {"navigationBarTitleText": "个人中心", "enablePullDownRefresh": true}}], "globalStyle": {"navigationBarTextStyle": "black", "navigationBarTitleText": "AI系统", "navigationBarBackgroundColor": "#F8F8F8", "backgroundColor": "#F8F8F8", "app-plus": {"background": "#efeff4"}}, "tabBar": {"custom": true, "color": "#7A7E83", "selectedColor": "#667eea", "borderStyle": "black", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/index/index", "text": "首页"}, {"pagePath": "pages/translation/text/index", "text": "翻译"}, {"pagePath": "pages/digital-human/chat/index", "text": "对话"}, {"pagePath": "pages/ai-agents/market/index", "text": "智能体"}, {"pagePath": "pages/user/profile/index", "text": "我的"}]}, "uniIdRouter": {}}