import { http, uploadFile } from '@/utils/request'

// 数字人相关接口
export interface DigitalHuman {
  id: string
  name: string
  avatar: string
  description: string
  voice_id: string
  language: string
  personality: string
  created_at: string
  is_active: boolean
}

export interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: string
  message_type: 'text' | 'audio' | 'video'
  audio_url?: string
  video_url?: string
  duration?: number
}

export interface ChatSession {
  id: string
  digital_human_id: string
  digital_human_name: string
  title: string
  created_at: string
  updated_at: string
  message_count: number
  last_message?: string
}

export interface VoiceConfig {
  voice_id: string
  speed: number
  pitch: number
  volume: number
  emotion: string
}

export const digitalHumanApi = {
  // 获取数字人列表
  getDigitalHumans: (params?: { page?: number; limit?: number }) => {
    return http.get<{
      items: DigitalHuman[]
      total: number
      page: number
      limit: number
    }>('/digital-human/list', params)
  },

  // 获取数字人详情
  getDigitalHuman: (id: string) => {
    return http.get<DigitalHuman>(`/digital-human/${id}`)
  },

  // 创建数字人
  createDigitalHuman: (data: {
    name: string
    description: string
    voice_id: string
    language: string
    personality: string
    avatar?: string
  }) => {
    return http.post<DigitalHuman>('/digital-human/create', data)
  },

  // 更新数字人
  updateDigitalHuman: (id: string, data: Partial<DigitalHuman>) => {
    return http.put<DigitalHuman>(`/digital-human/${id}`, data)
  },

  // 删除数字人
  deleteDigitalHuman: (id: string) => {
    return http.delete(`/digital-human/${id}`)
  },

  // 上传数字人头像
  uploadAvatar: (filePath: string) => {
    return uploadFile({
      url: '/digital-human/upload-avatar',
      filePath,
      name: 'avatar'
    })
  },

  // 获取对话会话列表
  getChatSessions: (params?: { page?: number; limit?: number }) => {
    return http.get<{
      items: ChatSession[]
      total: number
      page: number
      limit: number
    }>('/digital-human/sessions', params)
  },

  // 创建对话会话
  createChatSession: (digitalHumanId: string, title?: string) => {
    return http.post<ChatSession>('/digital-human/sessions', {
      digital_human_id: digitalHumanId,
      title: title || '新对话'
    })
  },

  // 获取会话消息
  getChatMessages: (sessionId: string, params?: { page?: number; limit?: number }) => {
    return http.get<{
      items: ChatMessage[]
      total: number
      page: number
      limit: number
    }>(`/digital-human/sessions/${sessionId}/messages`, params)
  },

  // 发送文本消息
  sendTextMessage: (sessionId: string, content: string) => {
    return http.post<ChatMessage>(`/digital-human/sessions/${sessionId}/messages`, {
      content,
      message_type: 'text'
    })
  },

  // 发送语音消息
  sendAudioMessage: (sessionId: string, filePath: string) => {
    return uploadFile({
      url: `/digital-human/sessions/${sessionId}/audio`,
      filePath,
      name: 'audio',
      formData: {
        message_type: 'audio'
      }
    })
  },

  // 获取数字人回复
  getDigitalHumanReply: (sessionId: string, messageId: string, options?: {
    voice_config?: VoiceConfig
    response_type?: 'text' | 'audio' | 'video'
  }) => {
    return http.post<ChatMessage>(`/digital-human/sessions/${sessionId}/reply`, {
      message_id: messageId,
      ...options
    })
  },

  // 生成数字人视频回复
  generateVideoReply: (sessionId: string, messageId: string, voiceConfig?: VoiceConfig) => {
    return http.post<{
      task_id: string
      status: 'pending' | 'processing' | 'completed' | 'failed'
      video_url?: string
    }>(`/digital-human/sessions/${sessionId}/video`, {
      message_id: messageId,
      voice_config: voiceConfig
    })
  },

  // 获取视频生成任务状态
  getVideoTaskStatus: (taskId: string) => {
    return http.get<{
      task_id: string
      status: 'pending' | 'processing' | 'completed' | 'failed'
      progress: number
      video_url?: string
      error_message?: string
    }>(`/digital-human/video-task/${taskId}`)
  },

  // 删除对话会话
  deleteChatSession: (sessionId: string) => {
    return http.delete(`/digital-human/sessions/${sessionId}`)
  },

  // 清空会话消息
  clearChatMessages: (sessionId: string) => {
    return http.delete(`/digital-human/sessions/${sessionId}/messages`)
  },

  // 获取可用语音列表
  getAvailableVoices: (language?: string) => {
    return http.get<Array<{
      voice_id: string
      name: string
      language: string
      gender: 'male' | 'female'
      age: string
      style: string
      preview_url?: string
    }>>('/digital-human/voices', { language })
  },

  // 语音合成预览
  previewVoice: (text: string, voiceConfig: VoiceConfig) => {
    return http.post<{
      audio_url: string
      duration: number
    }>('/digital-human/voice-preview', {
      text,
      voice_config: voiceConfig
    })
  },

  // 获取数字人统计信息
  getDigitalHumanStats: (id: string) => {
    return http.get<{
      total_sessions: number
      total_messages: number
      total_duration: number
      last_active: string
    }>(`/digital-human/${id}/stats`)
  },

  // 导出对话记录
  exportChatHistory: (sessionId: string, format: 'json' | 'txt' | 'pdf') => {
    return http.get(`/digital-human/sessions/${sessionId}/export`, 
      { format }, 
      { responseType: 'blob' }
    )
  },

  // 搜索对话记录
  searchMessages: (query: string, params?: {
    session_id?: string
    digital_human_id?: string
    start_date?: string
    end_date?: string
    page?: number
    limit?: number
  }) => {
    return http.get<{
      items: ChatMessage[]
      total: number
      page: number
      limit: number
    }>('/digital-human/search', { query, ...params })
  }
}
