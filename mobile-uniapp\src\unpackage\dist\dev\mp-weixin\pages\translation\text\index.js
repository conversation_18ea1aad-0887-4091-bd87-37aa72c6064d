"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_modules_translation = require("../../../store/modules/translation.js");
require("../../../store/modules/user.js");
if (!Array) {
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  const _component_u_button = common_vendor.resolveComponent("u-button");
  const _component_u_picker = common_vendor.resolveComponent("u-picker");
  const _component_u_modal = common_vendor.resolveComponent("u-modal");
  (_component_u_icon + _component_u_button + _component_u_picker + _component_u_modal)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props, { expose: __expose }) {
    common_vendor.dayjs.extend(common_vendor.relativeTime);
    const translationStore = store_modules_translation.useTranslationStore();
    const sourceText = common_vendor.ref("");
    const translationResult = common_vendor.ref(null);
    const inputFocused = common_vendor.ref(false);
    const showSourceLanguagePicker = common_vendor.ref(false);
    const showTargetLanguagePicker = common_vendor.ref(false);
    const showHistoryModal = common_vendor.ref(false);
    const isResultFavorite = common_vendor.ref(false);
    const {
      languages,
      currentSourceLang,
      currentTargetLang,
      translationHistory,
      isTranslating,
      sourceLanguage,
      targetLanguage,
      recentLanguagePairs
    } = translationStore;
    const sourceLanguageColumns = common_vendor.computed(() => {
      const autoDetect = [{ text: "自动检测", value: "auto" }];
      const languageOptions = languages.map((lang) => ({
        text: lang.name,
        value: lang.code
      }));
      return [autoDetect.concat(languageOptions)];
    });
    const targetLanguageColumns = common_vendor.computed(() => {
      const languageOptions = languages.filter((lang) => lang.code !== "auto").map((lang) => ({
        text: lang.name,
        value: lang.code
      }));
      return [languageOptions];
    });
    common_vendor.onMounted(async () => {
      await translationStore.loadLanguages();
      await translationStore.loadHistory();
      if (translationStore.lastTranslation) {
        sourceText.value = translationStore.lastTranslation.source;
        translationResult.value = {
          translated_text: translationStore.lastTranslation.target,
          source_lang: translationStore.lastTranslation.sourceLang,
          target_lang: translationStore.lastTranslation.targetLang,
          confidence: 0.95,
          word_count: translationStore.lastTranslation.source.split(" ").length,
          char_count: translationStore.lastTranslation.source.length
        };
      }
    });
    let inputTimer = null;
    const handleInputChange = () => {
      if (inputTimer) {
        clearTimeout(inputTimer);
      }
      if (translationResult.value) {
        translationResult.value = null;
        isResultFavorite.value = false;
      }
      if (sourceText.value.trim().length > 0 && sourceText.value.trim().length < 500) {
        inputTimer = setTimeout(() => {
          handleTranslate();
        }, 1e3);
      }
    };
    const handleTranslate = async () => {
      if (!sourceText.value.trim()) {
        common_vendor.index.showToast({
          title: "请输入要翻译的文本",
          icon: "none"
        });
        return;
      }
      try {
        const result = await translationStore.translateText(sourceText.value.trim());
        translationResult.value = result;
        isResultFavorite.value = false;
        checkIfFavorite();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/translation/text/index.vue:338", "翻译失败:", error);
        common_vendor.index.showToast({
          title: error.message || "翻译失败",
          icon: "error"
        });
      }
    };
    const checkIfFavorite = () => {
      if (!translationResult.value)
        return;
      isResultFavorite.value = false;
    };
    const clearSourceText = () => {
      sourceText.value = "";
      translationResult.value = null;
      isResultFavorite.value = false;
    };
    const startVoiceInput = () => {
      common_vendor.index.showToast({
        title: "小程序暂不支持语音输入",
        icon: "none"
      });
    };
    const copyResult = () => {
      if (!translationResult.value)
        return;
      common_vendor.index.setClipboardData({
        data: translationResult.value.translated_text,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "error"
          });
        }
      });
    };
    const shareResult = () => {
      if (!translationResult.value)
        return;
      const shareContent = `原文: ${sourceText.value}
译文: ${translationResult.value.translated_text}`;
      common_vendor.index.setClipboardData({
        data: shareContent,
        success: () => {
          common_vendor.index.showToast({
            title: "内容已复制，可分享到其他应用",
            icon: "success"
          });
        }
      });
    };
    const toggleResultFavorite = async () => {
      if (!translationResult.value)
        return;
      isResultFavorite.value = !isResultFavorite.value;
      common_vendor.index.showToast({
        title: isResultFavorite.value ? "收藏成功" : "取消收藏",
        icon: "success"
      });
    };
    const handleSwapLanguages = () => {
      translationStore.swapLanguages();
      if (translationResult.value) {
        sourceText.value;
        sourceText.value = translationResult.value.translated_text;
        translationResult.value = null;
        isResultFavorite.value = false;
      }
    };
    const onSourceLanguageConfirm = (value) => {
      translationStore.setSourceLanguage(value[0].value);
      showSourceLanguagePicker.value = false;
      if (sourceText.value.trim()) {
        handleTranslate();
      }
    };
    const onTargetLanguageConfirm = (value) => {
      translationStore.setTargetLanguage(value[0].value);
      showTargetLanguagePicker.value = false;
      if (sourceText.value.trim()) {
        handleTranslate();
      }
    };
    const useLanguagePair = (sourceLang, targetLang) => {
      translationStore.useLanguagePair(sourceLang, targetLang);
      if (sourceText.value.trim()) {
        handleTranslate();
      }
    };
    const useHistoryItem = (item) => {
      sourceText.value = item.source_text;
      translationResult.value = {
        translated_text: item.translated_text,
        source_lang: item.source_lang,
        target_lang: item.target_lang,
        confidence: 0.95,
        word_count: item.word_count || item.source_text.split(" ").length,
        char_count: item.source_text.length
      };
      translationStore.setSourceLanguage(item.source_lang);
      translationStore.setTargetLanguage(item.target_lang);
      showHistoryModal.value = false;
      checkIfFavorite();
    };
    const deleteHistoryItem = async (id) => {
      common_vendor.index.showModal({
        title: "确认删除",
        content: "确定要删除这条翻译记录吗？",
        success: async (res) => {
          if (res.confirm) {
            await translationStore.deleteHistoryItem(id);
          }
        }
      });
    };
    const handleClearHistory = () => {
      common_vendor.index.showModal({
        title: "确认清空",
        content: "确定要清空所有翻译历史吗？此操作不可恢复。",
        success: async (res) => {
          if (res.confirm) {
            await translationStore.clearHistory();
            showHistoryModal.value = false;
          }
        }
      });
    };
    const getLanguageName = (langCode) => {
      if (langCode === "auto")
        return "自动检测";
      const lang = languages.find((l) => l.code === langCode);
      return (lang == null ? void 0 : lang.name) || langCode;
    };
    const formatTime = (time) => {
      return common_vendor.dayjs(time).fromNow();
    };
    const onPullDownRefresh = async () => {
      try {
        await translationStore.loadHistory();
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "刷新失败",
          icon: "error"
        });
      } finally {
        common_vendor.index.stopPullDownRefresh();
      }
    };
    __expose({
      onPullDownRefresh
    });
    return (_ctx, _cache) => {
      var _a, _b, _c, _d;
      return common_vendor.e({
        a: common_vendor.p({
          name: "arrow-down",
          size: "12",
          color: "#2563EB",
          ["margin-left"]: "4"
        }),
        b: common_vendor.o(($event) => showSourceLanguagePicker.value = true),
        c: common_vendor.p({
          type: "text",
          text: ((_a = common_vendor.unref(sourceLanguage)) == null ? void 0 : _a.name) || "自动检测",
          ["custom-style"]: {
            color: "#2563EB",
            fontSize: "28rpx"
          }
        }),
        d: common_vendor.p({
          name: "arrow-left-right",
          size: "20",
          color: "#6B7280"
        }),
        e: common_vendor.o(handleSwapLanguages),
        f: common_vendor.p({
          name: "arrow-down",
          size: "12",
          color: "#2563EB",
          ["margin-left"]: "4"
        }),
        g: common_vendor.o(($event) => showTargetLanguagePicker.value = true),
        h: common_vendor.p({
          type: "text",
          text: ((_b = common_vendor.unref(targetLanguage)) == null ? void 0 : _b.name) || "中文",
          ["custom-style"]: {
            color: "#2563EB",
            fontSize: "28rpx"
          }
        }),
        i: common_vendor.unref(recentLanguagePairs).length > 0
      }, common_vendor.unref(recentLanguagePairs).length > 0 ? {
        j: common_vendor.f(common_vendor.unref(recentLanguagePairs), (pair, index, i0) => {
          return {
            a: common_vendor.t(getLanguageName(pair.source)),
            b: common_vendor.t(getLanguageName(pair.target)),
            c: index,
            d: common_vendor.o(($event) => useLanguagePair(pair.source, pair.target), index)
          };
        })
      } : {}, {
        k: common_vendor.t(((_c = common_vendor.unref(sourceLanguage)) == null ? void 0 : _c.name) || "自动检测"),
        l: sourceText.value
      }, sourceText.value ? {
        m: common_vendor.o(clearSourceText),
        n: common_vendor.p({
          name: "close-circle-fill",
          size: "18",
          color: "#9CA3AF"
        })
      } : {}, {
        o: common_vendor.o(startVoiceInput),
        p: common_vendor.p({
          name: "mic",
          size: "18",
          color: "#2563EB",
          ["margin-left"]: "12"
        }),
        q: common_vendor.o([($event) => sourceText.value = $event.detail.value, handleInputChange]),
        r: common_vendor.o(($event) => inputFocused.value = true),
        s: common_vendor.o(($event) => inputFocused.value = false),
        t: sourceText.value,
        v: common_vendor.t(sourceText.value.length),
        w: common_vendor.t(common_vendor.unref(isTranslating) ? "翻译中..." : "翻译"),
        x: common_vendor.o(handleTranslate),
        y: common_vendor.p({
          type: "primary",
          size: "small",
          loading: common_vendor.unref(isTranslating),
          disabled: !sourceText.value.trim()
        }),
        z: translationResult.value
      }, translationResult.value ? {
        A: common_vendor.t(((_d = common_vendor.unref(targetLanguage)) == null ? void 0 : _d.name) || "中文"),
        B: common_vendor.o(copyResult),
        C: common_vendor.p({
          name: "copy",
          size: "18",
          color: "#2563EB"
        }),
        D: common_vendor.o(shareResult),
        E: common_vendor.p({
          name: "share",
          size: "18",
          color: "#2563EB",
          ["margin-left"]: "12"
        }),
        F: common_vendor.o(toggleResultFavorite),
        G: common_vendor.p({
          name: isResultFavorite.value ? "heart-fill" : "heart",
          size: "18",
          color: isResultFavorite.value ? "#EF4444" : "#2563EB",
          ["margin-left"]: "12"
        }),
        H: common_vendor.t(translationResult.value.translated_text),
        I: common_vendor.t(Math.round(translationResult.value.confidence * 100)),
        J: common_vendor.t(translationResult.value.word_count)
      } : {}, {
        K: common_vendor.unref(translationHistory).length > 0
      }, common_vendor.unref(translationHistory).length > 0 ? {
        L: common_vendor.o(($event) => showHistoryModal.value = true),
        M: common_vendor.p({
          type: "text",
          text: "查看全部",
          size: "small",
          ["custom-style"]: {
            color: "#2563EB",
            fontSize: "24rpx"
          }
        }),
        N: common_vendor.f(common_vendor.unref(translationHistory).slice(0, 3), (item, index, i0) => {
          return {
            a: common_vendor.t(item.source_text),
            b: common_vendor.t(item.translated_text),
            c: common_vendor.t(getLanguageName(item.source_lang)),
            d: common_vendor.t(getLanguageName(item.target_lang)),
            e: common_vendor.t(formatTime(item.created_at)),
            f: item.id,
            g: common_vendor.o(($event) => useHistoryItem(item), item.id)
          };
        })
      } : {}, {
        O: common_vendor.o(onSourceLanguageConfirm),
        P: common_vendor.o(($event) => showSourceLanguagePicker.value = false),
        Q: common_vendor.o(($event) => showSourceLanguagePicker.value = $event),
        R: common_vendor.p({
          columns: sourceLanguageColumns.value,
          modelValue: showSourceLanguagePicker.value
        }),
        S: common_vendor.o(onTargetLanguageConfirm),
        T: common_vendor.o(($event) => showTargetLanguagePicker.value = false),
        U: common_vendor.o(($event) => showTargetLanguagePicker.value = $event),
        V: common_vendor.p({
          columns: targetLanguageColumns.value,
          modelValue: showTargetLanguagePicker.value
        }),
        W: common_vendor.f(common_vendor.unref(translationHistory), (item, k0, i0) => {
          return {
            a: common_vendor.t(item.source_text),
            b: common_vendor.t(item.translated_text),
            c: common_vendor.t(getLanguageName(item.source_lang)),
            d: common_vendor.t(getLanguageName(item.target_lang)),
            e: common_vendor.t(formatTime(item.created_at)),
            f: common_vendor.o(($event) => deleteHistoryItem(item.id), item.id),
            g: "0df80e11-15-" + i0 + ",0df80e11-14",
            h: item.id,
            i: common_vendor.o(($event) => useHistoryItem(item), item.id)
          };
        }),
        X: common_vendor.p({
          name: "delete",
          size: "16",
          color: "#EF4444"
        }),
        Y: common_vendor.o(handleClearHistory),
        Z: common_vendor.o(($event) => showHistoryModal.value = false),
        aa: common_vendor.o(($event) => showHistoryModal.value = $event),
        ab: common_vendor.p({
          title: "翻译历史",
          ["show-cancel-button"]: true,
          ["cancel-text"]: "清空",
          ["confirm-text"]: "关闭",
          modelValue: showHistoryModal.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0df80e11"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/translation/text/index.js.map
