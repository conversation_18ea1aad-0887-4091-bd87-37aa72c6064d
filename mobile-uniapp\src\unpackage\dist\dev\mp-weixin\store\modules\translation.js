"use strict";
const common_vendor = require("../../common/vendor.js");
const api_translation = require("../../api/translation.js");
const useTranslationStore = common_vendor.defineStore("translation", () => {
  const languages = common_vendor.ref([]);
  const currentSourceLang = common_vendor.ref("auto");
  const currentTargetLang = common_vendor.ref("zh-CN");
  const translationHistory = common_vendor.ref([]);
  const favorites = common_vendor.ref([]);
  const isTranslating = common_vendor.ref(false);
  const lastTranslation = common_vendor.ref(null);
  const sourceLanguage = common_vendor.computed(() => {
    return languages.value.find((lang) => lang.code === currentSourceLang.value);
  });
  const targetLanguage = common_vendor.computed(() => {
    return languages.value.find((lang) => lang.code === currentTargetLang.value);
  });
  const recentLanguagePairs = common_vendor.computed(() => {
    const pairs = /* @__PURE__ */ new Set();
    const recentPairs = [];
    translationHistory.value.slice(0, 10).forEach((item) => {
      const pairKey = `${item.source_lang}-${item.target_lang}`;
      if (!pairs.has(pairKey)) {
        pairs.add(pairKey);
        recentPairs.push({
          source: item.source_lang,
          target: item.target_lang
        });
      }
    });
    return recentPairs.slice(0, 5);
  });
  const loadLanguages = async () => {
    try {
      const response = await api_translation.translationApi.getLanguages();
      if (response.success) {
        languages.value = response.data;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/translation.ts:55", "加载语言列表失败:", error);
    }
  };
  const translateText = async (text, options) => {
    if (!text.trim()) {
      throw new Error("请输入要翻译的文本");
    }
    try {
      isTranslating.value = true;
      const sourceLang = (options == null ? void 0 : options.sourceLang) || currentSourceLang.value;
      const targetLang = (options == null ? void 0 : options.targetLang) || currentTargetLang.value;
      const response = await api_translation.translationApi.translateText({
        text: text.trim(),
        source_lang: sourceLang,
        target_lang: targetLang,
        domain: options == null ? void 0 : options.domain,
        style: options == null ? void 0 : options.style
      });
      if (response.success) {
        lastTranslation.value = {
          source: text,
          target: response.data.translated_text,
          sourceLang: response.data.source_lang,
          targetLang: response.data.target_lang
        };
        if (sourceLang === "auto") {
          currentSourceLang.value = response.data.source_lang;
        }
        await loadHistory();
        return response.data;
      } else {
        throw new Error(response.message || "翻译失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/translation.ts:106", "翻译失败:", error);
      throw error;
    } finally {
      isTranslating.value = false;
    }
  };
  const detectLanguage = async (text) => {
    if (!text.trim())
      return null;
    try {
      const response = await api_translation.translationApi.detectLanguage(text.trim());
      if (response.success) {
        return response.data;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/translation.ts:123", "语言检测失败:", error);
    }
    return null;
  };
  const loadHistory = async (page = 1, limit = 20) => {
    try {
      const response = await api_translation.translationApi.getHistory({ page, limit });
      if (response.success) {
        if (page === 1) {
          translationHistory.value = response.data.items;
        } else {
          translationHistory.value.push(...response.data.items);
        }
        return response.data;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/translation.ts:141", "加载翻译历史失败:", error);
    }
    return null;
  };
  const deleteHistoryItem = async (id) => {
    try {
      const response = await api_translation.translationApi.deleteHistory(id);
      if (response.success) {
        translationHistory.value = translationHistory.value.filter((item) => item.id !== id);
        common_vendor.index.showToast({
          title: "删除成功",
          icon: "success"
        });
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/translation.ts:158", "删除历史记录失败:", error);
      common_vendor.index.showToast({
        title: "删除失败",
        icon: "error"
      });
    }
  };
  const clearHistory = async () => {
    try {
      const response = await api_translation.translationApi.clearHistory();
      if (response.success) {
        translationHistory.value = [];
        common_vendor.index.showToast({
          title: "清空成功",
          icon: "success"
        });
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/translation.ts:178", "清空历史记录失败:", error);
      common_vendor.index.showToast({
        title: "清空失败",
        icon: "error"
      });
    }
  };
  const toggleFavorite = async (id, isFavorite) => {
    try {
      const response = isFavorite ? await api_translation.translationApi.unfavoriteTranslation(id) : await api_translation.translationApi.favoriteTranslation(id);
      if (response.success) {
        const historyItem = translationHistory.value.find((item) => item.id === id);
        if (historyItem) {
          historyItem.is_favorite = !isFavorite;
        }
        if (isFavorite) {
          favorites.value = favorites.value.filter((item) => item.id !== id);
        } else {
          await loadFavorites();
        }
        common_vendor.index.showToast({
          title: isFavorite ? "取消收藏" : "收藏成功",
          icon: "success"
        });
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/translation.ts:213", "操作收藏失败:", error);
      common_vendor.index.showToast({
        title: "操作失败",
        icon: "error"
      });
    }
  };
  const loadFavorites = async (page = 1, limit = 20) => {
    try {
      const response = await api_translation.translationApi.getFavorites({ page, limit });
      if (response.success) {
        if (page === 1) {
          favorites.value = response.data.items;
        } else {
          favorites.value.push(...response.data.items);
        }
        return response.data;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/translation.ts:234", "加载收藏列表失败:", error);
    }
    return null;
  };
  const swapLanguages = () => {
    if (currentSourceLang.value === "auto") {
      common_vendor.index.showToast({
        title: "自动检测语言无法交换",
        icon: "none"
      });
      return;
    }
    const temp = currentSourceLang.value;
    currentSourceLang.value = currentTargetLang.value;
    currentTargetLang.value = temp;
  };
  const setSourceLanguage = (langCode) => {
    currentSourceLang.value = langCode;
  };
  const setTargetLanguage = (langCode) => {
    currentTargetLang.value = langCode;
  };
  const useLanguagePair = (sourceLang, targetLang) => {
    currentSourceLang.value = sourceLang;
    currentTargetLang.value = targetLang;
  };
  return {
    // 状态
    languages,
    currentSourceLang,
    currentTargetLang,
    translationHistory,
    favorites,
    isTranslating,
    lastTranslation,
    // 计算属性
    sourceLanguage,
    targetLanguage,
    recentLanguagePairs,
    // 方法
    loadLanguages,
    translateText,
    detectLanguage,
    loadHistory,
    deleteHistoryItem,
    clearHistory,
    toggleFavorite,
    loadFavorites,
    swapLanguages,
    setSourceLanguage,
    setTargetLanguage,
    useLanguagePair
  };
}, {
  persist: {
    key: "translation-store",
    paths: ["currentSourceLang", "currentTargetLang", "lastTranslation"]
  }
});
exports.useTranslationStore = useTranslationStore;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/store/modules/translation.js.map
