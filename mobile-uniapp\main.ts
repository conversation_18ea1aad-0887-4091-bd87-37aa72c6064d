import { createSSRApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

// 引入 uView UI
import uView from 'uview-ui'

// 引入全局样式
import '@/styles/index.scss'

// 引入 API 配置
import '@/utils/request'

export function createApp() {
  const app = createSSRApp(App)
  
  // 使用 Pinia
  const pinia = createPinia()
  app.use(pinia)
  
  // 使用 uView UI
  app.use(uView)
  
  // 全局属性
  app.config.globalProperties.$api = '/api'
  
  // 全局错误处理
  app.config.errorHandler = (err, vm, info) => {
    console.error('全局错误:', err, info)
    
    // 在生产环境中，可以将错误上报到服务器
    if (process.env.NODE_ENV === 'production') {
      // 错误上报逻辑
      reportError(err, info)
    }
  }
  
  return {
    app
  }
}

// 错误上报函数
function reportError(error: Error, info: string) {
  // 这里可以集成错误监控服务，如 Sentry
  console.log('上报错误:', error.message, info)
}
