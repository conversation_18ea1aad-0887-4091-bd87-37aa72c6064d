<template>
  <view class="ai-agents-market">
    <!-- 头部搜索区域 -->
    <view class="header-section">
      <view class="search-container">
        <view class="search-box">
          <u-icon name="search" size="18" color="#8E8E93"></u-icon>
          <input 
            class="search-input" 
            placeholder="搜索智能体..." 
            v-model="searchKeyword"
            @input="handleSearch"
          />
          <view class="filter-btn" @click="showFilter">
            <u-icon name="filter" size="16" color="#667eea"></u-icon>
          </view>
        </view>
      </view>
      
      <!-- 分类标签 -->
      <scroll-view class="category-scroll" scroll-x="true">
        <view class="category-list">
          <view 
            class="category-item"
            v-for="(category, index) in categories"
            :key="index"
            :class="{ active: currentCategory === index }"
            @click="switchCategory(index)"
          >
            <text class="category-text">{{ category.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 智能体列表 -->
    <scroll-view class="agents-content" scroll-y="true">
      <!-- 推荐智能体 -->
      <view class="section" v-if="currentCategory === 0">
        <view class="section-header">
          <text class="section-title">🔥 热门推荐</text>
          <text class="section-more" @click="viewMore('hot')">更多</text>
        </view>
        
        <view class="agents-grid">
          <view 
            class="agent-card featured"
            v-for="(agent, index) in featuredAgents"
            :key="index"
            @click="viewAgent(agent)"
          >
            <view class="card-header">
              <image class="agent-avatar" :src="agent.avatar" mode="aspectFill"></image>
              <view class="agent-badge" v-if="agent.isPro">PRO</view>
              <view class="agent-rating">
                <u-icon name="star-fill" size="12" color="#FFD700"></u-icon>
                <text class="rating-text">{{ agent.rating }}</text>
              </view>
            </view>
            
            <view class="card-content">
              <text class="agent-name">{{ agent.name }}</text>
              <text class="agent-desc">{{ agent.description }}</text>
              
              <view class="agent-tags">
                <text 
                  class="tag"
                  v-for="(tag, tagIndex) in agent.tags.slice(0, 2)"
                  :key="tagIndex"
                >
                  {{ tag }}
                </text>
              </view>
              
              <view class="agent-stats">
                <view class="stat-item">
                  <u-icon name="user" size="12" color="#8E8E93"></u-icon>
                  <text class="stat-text">{{ agent.users }}</text>
                </view>
                <view class="stat-item">
                  <u-icon name="heart" size="12" color="#8E8E93"></u-icon>
                  <text class="stat-text">{{ agent.likes }}</text>
                </view>
              </view>
            </view>
            
            <view class="card-action">
              <view class="price-info" v-if="agent.price > 0">
                <text class="price">¥{{ agent.price }}/月</text>
              </view>
              <view class="price-info" v-else>
                <text class="price free">免费</text>
              </view>
              <view class="action-btn" @click.stop="useAgent(agent)">
                <text class="btn-text">立即使用</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 普通智能体列表 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">{{ categories[currentCategory].name }}</text>
          <text class="section-count">{{ filteredAgents.length }}个智能体</text>
        </view>
        
        <view class="agents-list">
          <view 
            class="agent-item"
            v-for="(agent, index) in filteredAgents"
            :key="index"
            @click="viewAgent(agent)"
          >
            <image class="item-avatar" :src="agent.avatar" mode="aspectFill"></image>
            
            <view class="item-content">
              <view class="item-header">
                <text class="item-name">{{ agent.name }}</text>
                <view class="item-rating">
                  <u-icon name="star-fill" size="10" color="#FFD700"></u-icon>
                  <text class="rating-text">{{ agent.rating }}</text>
                </view>
              </view>
              
              <text class="item-desc">{{ agent.description }}</text>
              
              <view class="item-footer">
                <view class="item-tags">
                  <text 
                    class="mini-tag"
                    v-for="(tag, tagIndex) in agent.tags.slice(0, 3)"
                    :key="tagIndex"
                  >
                    {{ tag }}
                  </text>
                </view>
                
                <view class="item-price">
                  <text class="price-text" v-if="agent.price > 0">¥{{ agent.price }}/月</text>
                  <text class="price-text free" v-else>免费</text>
                </view>
              </view>
            </view>
            
            <view class="item-action">
              <view class="action-btn mini" @click.stop="useAgent(agent)">
                <u-icon name="plus" size="14" color="#667eea"></u-icon>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="bottom-safe-area"></view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const searchKeyword = ref<string>('')
const currentCategory = ref<number>(0)

const categories = ref([
  { name: '全部', icon: 'apps' },
  { name: '写作助手', icon: 'edit' },
  { name: '编程助手', icon: 'code' },
  { name: '翻译助手', icon: 'translate' },
  { name: '设计助手', icon: 'palette' },
  { name: '商务助手', icon: 'briefcase' },
  { name: '学习助手', icon: 'book' },
  { name: '生活助手', icon: 'home' }
])

const featuredAgents = ref([
  {
    id: 1,
    name: 'GPT-4 写作大师',
    description: '专业的写作助手，帮您创作高质量文章',
    avatar: '/static/images/agent-writer.png',
    rating: 4.9,
    users: '10万+',
    likes: '8.5万',
    tags: ['写作', '创意', '文案'],
    price: 29,
    isPro: true,
    category: 'writing'
  },
  {
    id: 2,
    name: '代码助手Pro',
    description: '智能编程助手，支持多种编程语言',
    avatar: '/static/images/agent-coder.png',
    rating: 4.8,
    users: '8万+',
    likes: '6.2万',
    tags: ['编程', 'AI', '调试'],
    price: 0,
    isPro: false,
    category: 'coding'
  },
  {
    id: 3,
    name: '多语言翻译专家',
    description: '支持100+语言的专业翻译助手',
    avatar: '/static/images/agent-translator.png',
    rating: 4.7,
    users: '15万+',
    likes: '12万',
    tags: ['翻译', '多语言', '商务'],
    price: 19,
    isPro: true,
    category: 'translation'
  },
  {
    id: 4,
    name: 'UI设计师',
    description: '专业的UI/UX设计建议和灵感助手',
    avatar: '/static/images/agent-designer.png',
    rating: 4.6,
    users: '5万+',
    likes: '4.1万',
    tags: ['设计', 'UI', 'UX'],
    price: 39,
    isPro: true,
    category: 'design'
  }
])

const allAgents = ref([
  ...featuredAgents.value,
  {
    id: 5,
    name: '商务邮件助手',
    description: '帮您撰写专业的商务邮件和文档',
    avatar: '/static/images/agent-business.png',
    rating: 4.5,
    users: '3万+',
    likes: '2.8万',
    tags: ['商务', '邮件', '文档'],
    price: 15,
    isPro: false,
    category: 'business'
  },
  {
    id: 6,
    name: '学习规划师',
    description: '个性化学习计划制定和进度跟踪',
    avatar: '/static/images/agent-study.png',
    rating: 4.4,
    users: '6万+',
    likes: '5.2万',
    tags: ['学习', '规划', '教育'],
    price: 0,
    isPro: false,
    category: 'study'
  }
])

// 计算属性
const filteredAgents = computed(() => {
  let agents = allAgents.value
  
  // 分类筛选
  if (currentCategory.value > 0) {
    const categoryName = categories.value[currentCategory.value].name
    agents = agents.filter(agent => {
      return agent.tags.some(tag => tag.includes(categoryName.replace('助手', '')))
    })
  }
  
  // 搜索筛选
  if (searchKeyword.value) {
    agents = agents.filter(agent => 
      agent.name.includes(searchKeyword.value) || 
      agent.description.includes(searchKeyword.value) ||
      agent.tags.some(tag => tag.includes(searchKeyword.value))
    )
  }
  
  return agents
})

// 方法
const handleSearch = () => {
  // 搜索逻辑
  console.log('搜索:', searchKeyword.value)
}

const showFilter = () => {
  uni.showActionSheet({
    itemList: ['按评分排序', '按使用量排序', '按价格排序', '只看免费'],
    success: (res) => {
      console.log('筛选选项:', res.tapIndex)
    }
  })
}

const switchCategory = (index: number) => {
  currentCategory.value = index
}

const viewAgent = (agent: any) => {
  uni.navigateTo({
    url: `/pages/ai-agents/detail/index?id=${agent.id}`
  })
}

const useAgent = (agent: any) => {
  if (agent.price > 0) {
    uni.showModal({
      title: '付费智能体',
      content: `${agent.name} 需要付费使用，价格：¥${agent.price}/月`,
      confirmText: '立即购买',
      success: (res) => {
        if (res.confirm) {
          // 跳转到支付页面
          console.log('购买智能体:', agent.name)
        }
      }
    })
  } else {
    // 免费智能体直接使用
    uni.navigateTo({
      url: `/pages/digital-human/chat/index?agentId=${agent.id}`
    })
  }
}

const viewMore = (type: string) => {
  console.log('查看更多:', type)
}

// 生命周期
onMounted(() => {
  console.log('AI智能体市场页面加载完成')
})
</script>

<style lang="scss" scoped>
.ai-agents-market {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
}

// 头部区域
.header-section {
  background: #FFFFFF;
  padding: 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
  
  .search-container {
    margin-bottom: 32rpx;
    
    .search-box {
      display: flex;
      align-items: center;
      background: #F2F2F7;
      border-radius: 20rpx;
      padding: 24rpx 32rpx;
      
      .search-input {
        flex: 1;
        font-size: 28rpx;
        color: #1D1D1F;
        margin-left: 16rpx;
        
        &::placeholder {
          color: #8E8E93;
        }
      }
      
      .filter-btn {
        width: 56rpx;
        height: 56rpx;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 28rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 16rpx;
      }
    }
  }
  
  .category-scroll {
    white-space: nowrap;
    
    .category-list {
      display: flex;
      gap: 16rpx;
      
      .category-item {
        flex-shrink: 0;
        padding: 16rpx 32rpx;
        background: #F2F2F7;
        border-radius: 40rpx;
        transition: all 0.3s ease;
        
        &.active {
          background: linear-gradient(135deg, #667eea, #764ba2);
          
          .category-text {
            color: #FFFFFF;
          }
        }
        
        .category-text {
          font-size: 26rpx;
          color: #8E8E93;
          font-weight: 500;
        }
      }
    }
  }
}

// 内容区域
.agents-content {
  flex: 1;
  padding: 32rpx;
}

.section {
  margin-bottom: 48rpx;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;
    
    .section-title {
      font-size: 36rpx;
      font-weight: 700;
      color: #1D1D1F;
    }
    
    .section-more,
    .section-count {
      font-size: 26rpx;
      color: #667eea;
    }
  }
}

// 推荐智能体网格
.agents-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  
  .agent-card {
    background: #FFFFFF;
    border-radius: 24rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    
    &:active {
      transform: scale(0.98);
    }
    
    &.featured {
      border: 2rpx solid rgba(102, 126, 234, 0.2);
    }
    
    .card-header {
      position: relative;
      margin-bottom: 24rpx;
      
      .agent-avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 20rpx;
      }
      
      .agent-badge {
        position: absolute;
        top: -8rpx;
        right: -8rpx;
        background: linear-gradient(45deg, #FFD700, #FFA500);
        color: #FFFFFF;
        font-size: 18rpx;
        font-weight: 600;
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
      }
      
      .agent-rating {
        position: absolute;
        bottom: -8rpx;
        right: 0;
        display: flex;
        align-items: center;
        background: rgba(255, 215, 0, 0.1);
        padding: 6rpx 12rpx;
        border-radius: 16rpx;
        
        .rating-text {
          font-size: 20rpx;
          color: #FFD700;
          margin-left: 4rpx;
          font-weight: 600;
        }
      }
    }
    
    .card-content {
      margin-bottom: 24rpx;
      
      .agent-name {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: #1D1D1F;
        margin-bottom: 8rpx;
      }
      
      .agent-desc {
        display: block;
        font-size: 24rpx;
        color: #8E8E93;
        line-height: 1.4;
        margin-bottom: 16rpx;
      }
      
      .agent-tags {
        display: flex;
        gap: 8rpx;
        margin-bottom: 16rpx;
        
        .tag {
          font-size: 20rpx;
          color: #667eea;
          background: rgba(102, 126, 234, 0.1);
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
        }
      }
      
      .agent-stats {
        display: flex;
        gap: 16rpx;
        
        .stat-item {
          display: flex;
          align-items: center;
          
          .stat-text {
            font-size: 20rpx;
            color: #8E8E93;
            margin-left: 4rpx;
          }
        }
      }
    }
    
    .card-action {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .price-info {
        .price {
          font-size: 24rpx;
          font-weight: 600;
          color: #1D1D1F;
          
          &.free {
            color: #10B981;
          }
        }
      }
      
      .action-btn {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: #FFFFFF;
        font-size: 22rpx;
        font-weight: 500;
        padding: 12rpx 24rpx;
        border-radius: 20rpx;
        
        .btn-text {
          color: #FFFFFF;
        }
      }
    }
  }
}

// 智能体列表
.agents-list {
  .agent-item {
    display: flex;
    align-items: center;
    background: #FFFFFF;
    border-radius: 20rpx;
    padding: 32rpx;
    margin-bottom: 16rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    
    &:active {
      transform: scale(0.98);
    }
    
    .item-avatar {
      width: 96rpx;
      height: 96rpx;
      border-radius: 24rpx;
      margin-right: 24rpx;
    }
    
    .item-content {
      flex: 1;
      
      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8rpx;
        
        .item-name {
          font-size: 28rpx;
          font-weight: 600;
          color: #1D1D1F;
        }
        
        .item-rating {
          display: flex;
          align-items: center;
          
          .rating-text {
            font-size: 20rpx;
            color: #FFD700;
            margin-left: 4rpx;
          }
        }
      }
      
      .item-desc {
        display: block;
        font-size: 24rpx;
        color: #8E8E93;
        line-height: 1.4;
        margin-bottom: 16rpx;
      }
      
      .item-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .item-tags {
          display: flex;
          gap: 8rpx;
          
          .mini-tag {
            font-size: 18rpx;
            color: #8E8E93;
            background: #F2F2F7;
            padding: 4rpx 8rpx;
            border-radius: 8rpx;
          }
        }
        
        .item-price {
          .price-text {
            font-size: 22rpx;
            font-weight: 600;
            color: #1D1D1F;
            
            &.free {
              color: #10B981;
            }
          }
        }
      }
    }
    
    .item-action {
      .action-btn {
        width: 64rpx;
        height: 64rpx;
        background: rgba(102, 126, 234, 0.1);
        border-radius: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &.mini {
          width: 56rpx;
          height: 56rpx;
          border-radius: 28rpx;
        }
      }
    }
  }
}

// 底部安全区域
.bottom-safe-area {
  height: calc(120rpx + env(safe-area-inset-bottom));
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .ai-agents-market {
    background: linear-gradient(180deg, #1c1c1e 0%, #000000 100%);
  }
  
  .header-section {
    background: #1c1c1e;
    border-bottom-color: #38383a;
  }
  
  .agent-card,
  .agent-item {
    background: #2c2c2e;
    border-color: #38383a;
  }
  
  .agent-name,
  .item-name {
    color: #ffffff !important;
  }
  
  .search-box {
    background: #38383a !important;
  }
  
  .category-item {
    background: #38383a;
    
    &:not(.active) .category-text {
      color: #8e8e93;
    }
  }
}
</style>
