"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  _component_u_icon();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const selectedFile = common_vendor.ref(null);
    const uploadSuccess = common_vendor.ref(false);
    const uploadError = common_vendor.ref(false);
    const isConverting = common_vendor.ref(false);
    const convertProgress = common_vendor.ref(0);
    const sourceFormat = common_vendor.ref({
      name: "PDF",
      icon: "icon-pdf",
      color: "#EF4444",
      type: "pdf"
    });
    const targetFormat = common_vendor.ref({
      name: "Word",
      icon: "icon-document",
      color: "#2563EB",
      type: "docx"
    });
    const options = common_vendor.ref({
      keepFormat: true,
      highQuality: false,
      batchMode: false
    });
    const supportedFormats = common_vendor.ref([
      { name: "PDF", icon: "icon-pdf", color: "#EF4444", type: "pdf" },
      { name: "Word", icon: "icon-document", color: "#2563EB", type: "docx" },
      { name: "Excel", icon: "icon-table", color: "#10B981", type: "xlsx" },
      { name: "PowerPoint", icon: "icon-presentation", color: "#F59E0B", type: "pptx" },
      { name: "Text", icon: "icon-text", color: "#8E8E93", type: "txt" },
      { name: "HTML", icon: "icon-code", color: "#EC4899", type: "html" }
    ]);
    const convertHistory = common_vendor.ref([
      {
        fileName: "商业计划书.docx",
        sourceType: "pdf",
        targetType: "docx",
        convertTime: new Date(Date.now() - 2 * 60 * 60 * 1e3),
        downloadUrl: "/downloads/business-plan.docx"
      },
      {
        fileName: "财务报表.xlsx",
        sourceType: "pdf",
        targetType: "xlsx",
        convertTime: new Date(Date.now() - 5 * 60 * 60 * 1e3),
        downloadUrl: "/downloads/financial-report.xlsx"
      }
    ]);
    const canConvert = common_vendor.computed(() => {
      return selectedFile.value && !isConverting.value && sourceFormat.value.type !== targetFormat.value.type;
    });
    const showSourcePicker = () => {
      const itemList = supportedFormats.value.map((format) => format.name);
      common_vendor.index.showActionSheet({
        itemList,
        success: (res) => {
          sourceFormat.value = supportedFormats.value[res.tapIndex];
        }
      });
    };
    const showTargetPicker = () => {
      const itemList = supportedFormats.value.map((format) => format.name);
      common_vendor.index.showActionSheet({
        itemList,
        success: (res) => {
          targetFormat.value = supportedFormats.value[res.tapIndex];
        }
      });
    };
    const swapFormats = () => {
      const temp = sourceFormat.value;
      sourceFormat.value = targetFormat.value;
      targetFormat.value = temp;
    };
    const chooseFile = () => {
      common_vendor.index.chooseFile({
        count: 1,
        type: "file",
        success: (res) => {
          const file = res.tempFiles[0];
          selectedFile.value = {
            name: file.name,
            size: file.size,
            type: getFileTypeFromName(file.name),
            path: file.path
          };
          uploadSuccess.value = true;
          uploadError.value = false;
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/utilities/document/converter/index.vue:287", "选择文件失败:", err);
          uploadError.value = true;
        }
      });
    };
    const removeFile = () => {
      selectedFile.value = null;
      uploadSuccess.value = false;
      uploadError.value = false;
    };
    const toggleOption = (key) => {
      options.value[key] = !options.value[key];
    };
    const startConvert = () => {
      if (!canConvert.value)
        return;
      isConverting.value = true;
      convertProgress.value = 0;
      const progressInterval = setInterval(() => {
        convertProgress.value += Math.random() * 15;
        if (convertProgress.value >= 100) {
          convertProgress.value = 100;
          clearInterval(progressInterval);
          setTimeout(() => {
            isConverting.value = false;
            convertProgress.value = 0;
            convertHistory.value.unshift({
              fileName: selectedFile.value.name.replace(/\.[^/.]+$/, "") + "." + targetFormat.value.type,
              sourceType: sourceFormat.value.type,
              targetType: targetFormat.value.type,
              convertTime: /* @__PURE__ */ new Date(),
              downloadUrl: "/downloads/converted-file." + targetFormat.value.type
            });
            common_vendor.index.showToast({
              title: "转换完成",
              icon: "success"
            });
            removeFile();
          }, 1e3);
        }
      }, 200);
    };
    const downloadFile = (item) => {
      common_vendor.index.showToast({
        title: "开始下载",
        icon: "success"
      });
      common_vendor.index.__f__("log", "at pages/utilities/document/converter/index.vue:346", "下载文件:", item.fileName);
    };
    const getFileTypeFromName = (fileName) => {
      var _a;
      const ext = (_a = fileName.split(".").pop()) == null ? void 0 : _a.toLowerCase();
      return ext || "unknown";
    };
    const getFileIcon = (type) => {
      const iconMap = {
        pdf: "icon-pdf",
        doc: "icon-document",
        docx: "icon-document",
        xls: "icon-table",
        xlsx: "icon-table",
        ppt: "icon-presentation",
        pptx: "icon-presentation",
        txt: "icon-text",
        html: "icon-code"
      };
      return iconMap[type] || "icon-file";
    };
    const getFileColor = (type) => {
      const colorMap = {
        pdf: "#EF4444",
        doc: "#2563EB",
        docx: "#2563EB",
        xls: "#10B981",
        xlsx: "#10B981",
        ppt: "#F59E0B",
        pptx: "#F59E0B",
        txt: "#8E8E93",
        html: "#EC4899"
      };
      return colorMap[type] || "#6B7280";
    };
    const formatFileSize = (bytes) => {
      if (bytes === 0)
        return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };
    const formatTime = (time) => {
      const now = /* @__PURE__ */ new Date();
      const diff = now.getTime() - time.getTime();
      const hours = Math.floor(diff / (1e3 * 60 * 60));
      if (hours < 1)
        return "刚刚";
      if (hours < 24)
        return `${hours}小时前`;
      return `${Math.floor(hours / 24)}天前`;
    };
    const getUploadStatus = () => {
      if (uploadError.value)
        return "上传失败";
      if (uploadSuccess.value)
        return "上传成功";
      return "准备上传";
    };
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at pages/utilities/document/converter/index.vue:410", "文档转换页面加载完成");
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.n(sourceFormat.value.icon),
        b: sourceFormat.value.color,
        c: common_vendor.t(sourceFormat.value.name),
        d: common_vendor.p({
          name: "arrow-down",
          size: "16",
          color: "#8E8E93"
        }),
        e: common_vendor.o(showSourcePicker),
        f: common_vendor.p({
          name: "arrow-right",
          size: "20",
          color: "#667eea"
        }),
        g: common_vendor.o(swapFormats),
        h: common_vendor.n(targetFormat.value.icon),
        i: targetFormat.value.color,
        j: common_vendor.t(targetFormat.value.name),
        k: common_vendor.p({
          name: "arrow-down",
          size: "16",
          color: "#8E8E93"
        }),
        l: common_vendor.o(showTargetPicker),
        m: !selectedFile.value
      }, !selectedFile.value ? {
        n: common_vendor.p({
          name: "cloud-upload",
          size: "48",
          color: "#667eea"
        })
      } : {
        o: common_vendor.n(getFileIcon(selectedFile.value.type)),
        p: getFileColor(selectedFile.value.type),
        q: common_vendor.t(selectedFile.value.name),
        r: common_vendor.t(formatFileSize(selectedFile.value.size)),
        s: common_vendor.t(getUploadStatus()),
        t: uploadSuccess.value ? 1 : "",
        v: uploadError.value ? 1 : "",
        w: common_vendor.p({
          name: "close",
          size: "16",
          color: "#EF4444"
        }),
        x: common_vendor.o(removeFile)
      }, {
        y: common_vendor.o(chooseFile),
        z: options.value.keepFormat,
        A: common_vendor.o(($event) => toggleOption("keepFormat")),
        B: options.value.highQuality,
        C: common_vendor.o(($event) => toggleOption("highQuality")),
        D: options.value.batchMode,
        E: common_vendor.o(($event) => toggleOption("batchMode")),
        F: !isConverting.value
      }, !isConverting.value ? {
        G: common_vendor.p({
          name: "refresh",
          size: "20",
          color: "#FFFFFF"
        })
      } : {
        H: common_vendor.p({
          name: "loading",
          size: "20",
          color: "#FFFFFF"
        }),
        I: common_vendor.t(convertProgress.value)
      }, {
        J: !canConvert.value ? 1 : "",
        K: isConverting.value ? 1 : "",
        L: common_vendor.o(startConvert),
        M: convertHistory.value.length > 0
      }, convertHistory.value.length > 0 ? {
        N: common_vendor.f(convertHistory.value, (item, index, i0) => {
          return {
            a: common_vendor.n(getFileIcon(item.targetType)),
            b: common_vendor.t(item.fileName),
            c: common_vendor.t(item.sourceType.toUpperCase()),
            d: common_vendor.t(item.targetType.toUpperCase()),
            e: common_vendor.t(formatTime(item.convertTime)),
            f: "7998750f-7-" + i0,
            g: index,
            h: common_vendor.o(($event) => downloadFile(item), index)
          };
        }),
        O: common_vendor.p({
          name: "download",
          size: "16",
          color: "#667eea"
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7998750f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pages/utilities/document/converter/index.js.map
