"use strict";
const common_vendor = require("../../common/vendor.js");
const api_digitalHuman = require("../../api/digital-human.js");
const useDigitalHumanStore = common_vendor.defineStore("digitalHuman", () => {
  const digitalHumans = common_vendor.ref([]);
  const currentDigitalHuman = common_vendor.ref(null);
  const chatSessions = common_vendor.ref([]);
  const currentSession = common_vendor.ref(null);
  const messages = common_vendor.ref([]);
  const isLoading = common_vendor.ref(false);
  const isSending = common_vendor.ref(false);
  const isGeneratingVideo = common_vendor.ref(false);
  const voiceConfig = common_vendor.ref({
    voice_id: "default",
    speed: 1,
    pitch: 1,
    volume: 1,
    emotion: "neutral"
  });
  const hasDigitalHumans = common_vendor.computed(() => digitalHumans.value.length > 0);
  const currentSessionMessages = common_vendor.computed(() => {
    if (!currentSession.value)
      return [];
    return messages.value.filter((msg) => msg.id.includes(currentSession.value.id));
  });
  const recentSessions = common_vendor.computed(() => {
    return chatSessions.value.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()).slice(0, 5);
  });
  const loadDigitalHumans = async (page = 1, limit = 20) => {
    try {
      isLoading.value = true;
      const response = await api_digitalHuman.digitalHumanApi.getDigitalHumans({ page, limit });
      if (response.success) {
        if (page === 1) {
          digitalHumans.value = response.data.items;
        } else {
          digitalHumans.value.push(...response.data.items);
        }
        return response.data;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/digitalHuman.ts:58", "加载数字人列表失败:", error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };
  const selectDigitalHuman = async (digitalHuman) => {
    currentDigitalHuman.value = digitalHuman;
    await loadChatSessions();
  };
  const loadChatSessions = async (page = 1, limit = 20) => {
    if (!currentDigitalHuman.value)
      return;
    try {
      const response = await api_digitalHuman.digitalHumanApi.getChatSessions({ page, limit });
      if (response.success) {
        if (page === 1) {
          chatSessions.value = response.data.items;
        } else {
          chatSessions.value.push(...response.data.items);
        }
        return response.data;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/digitalHuman.ts:89", "加载对话会话失败:", error);
      throw error;
    }
  };
  const createNewSession = async (title) => {
    if (!currentDigitalHuman.value) {
      throw new Error("请先选择数字人");
    }
    try {
      const response = await api_digitalHuman.digitalHumanApi.createChatSession(
        currentDigitalHuman.value.id,
        title
      );
      if (response.success) {
        const newSession = response.data;
        chatSessions.value.unshift(newSession);
        currentSession.value = newSession;
        messages.value = [];
        return newSession;
      } else {
        throw new Error(response.message || "创建会话失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/digitalHuman.ts:117", "创建会话失败:", error);
      throw error;
    }
  };
  const selectSession = async (session) => {
    currentSession.value = session;
    await loadMessages();
  };
  const loadMessages = async (page = 1, limit = 50) => {
    if (!currentSession.value)
      return;
    try {
      const response = await api_digitalHuman.digitalHumanApi.getChatMessages(
        currentSession.value.id,
        { page, limit }
      );
      if (response.success) {
        if (page === 1) {
          messages.value = response.data.items.reverse();
        } else {
          messages.value.unshift(...response.data.items.reverse());
        }
        return response.data;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/digitalHuman.ts:147", "加载消息失败:", error);
      throw error;
    }
  };
  const sendTextMessage = async (content) => {
    if (!currentSession.value || !content.trim()) {
      throw new Error("会话或消息内容不能为空");
    }
    try {
      isSending.value = true;
      const userMessage = {
        id: `temp_${Date.now()}`,
        role: "user",
        content: content.trim(),
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        message_type: "text"
      };
      messages.value.push(userMessage);
      const response = await api_digitalHuman.digitalHumanApi.sendTextMessage(
        currentSession.value.id,
        content.trim()
      );
      if (response.success) {
        const messageIndex = messages.value.findIndex((msg) => msg.id === userMessage.id);
        if (messageIndex !== -1) {
          messages.value[messageIndex] = response.data;
        }
        await getDigitalHumanReply(response.data.id);
        return response.data;
      } else {
        throw new Error(response.message || "发送消息失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/digitalHuman.ts:192", "发送消息失败:", error);
      messages.value = messages.value.filter((msg) => !msg.id.startsWith("temp_"));
      throw error;
    } finally {
      isSending.value = false;
    }
  };
  const sendAudioMessage = async (filePath) => {
    if (!currentSession.value) {
      throw new Error("请先选择会话");
    }
    try {
      isSending.value = true;
      const response = await api_digitalHuman.digitalHumanApi.sendAudioMessage(
        currentSession.value.id,
        filePath
      );
      if (response.success) {
        messages.value.push(response.data);
        await getDigitalHumanReply(response.data.id);
        return response.data;
      } else {
        throw new Error(response.message || "发送语音消息失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/digitalHuman.ts:228", "发送语音消息失败:", error);
      throw error;
    } finally {
      isSending.value = false;
    }
  };
  const getDigitalHumanReply = async (messageId, responseType = "text") => {
    if (!currentSession.value)
      return;
    try {
      const response = await api_digitalHuman.digitalHumanApi.getDigitalHumanReply(
        currentSession.value.id,
        messageId,
        {
          voice_config: voiceConfig.value,
          response_type: responseType
        }
      );
      if (response.success) {
        messages.value.push(response.data);
        if (currentSession.value) {
          currentSession.value.updated_at = (/* @__PURE__ */ new Date()).toISOString();
          currentSession.value.last_message = response.data.content;
        }
        return response.data;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/digitalHuman.ts:261", "获取数字人回复失败:", error);
      const errorMessage = {
        id: `error_${Date.now()}`,
        role: "assistant",
        content: "抱歉，我现在无法回复您的消息，请稍后再试。",
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        message_type: "text"
      };
      messages.value.push(errorMessage);
    }
  };
  const generateVideoReply = async (messageId) => {
    if (!currentSession.value)
      return;
    try {
      isGeneratingVideo.value = true;
      const response = await api_digitalHuman.digitalHumanApi.generateVideoReply(
        currentSession.value.id,
        messageId,
        voiceConfig.value
      );
      if (response.success) {
        const taskId = response.data.task_id;
        return await pollVideoTaskStatus(taskId);
      } else {
        throw new Error(response.message || "生成视频失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/digitalHuman.ts:297", "生成视频回复失败:", error);
      throw error;
    } finally {
      isGeneratingVideo.value = false;
    }
  };
  const pollVideoTaskStatus = async (taskId) => {
    const maxAttempts = 30;
    let attempts = 0;
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++;
          const response = await api_digitalHuman.digitalHumanApi.getVideoTaskStatus(taskId);
          if (response.success) {
            const { status, video_url, error_message } = response.data;
            if (status === "completed" && video_url) {
              resolve(video_url);
            } else if (status === "failed") {
              reject(new Error(error_message || "视频生成失败"));
            } else if (attempts >= maxAttempts) {
              reject(new Error("视频生成超时"));
            } else {
              setTimeout(poll, 2e3);
            }
          } else {
            reject(new Error("获取任务状态失败"));
          }
        } catch (error) {
          reject(error);
        }
      };
      poll();
    });
  };
  const deleteSession = async (sessionId) => {
    var _a;
    try {
      const response = await api_digitalHuman.digitalHumanApi.deleteChatSession(sessionId);
      if (response.success) {
        chatSessions.value = chatSessions.value.filter((session) => session.id !== sessionId);
        if (((_a = currentSession.value) == null ? void 0 : _a.id) === sessionId) {
          currentSession.value = null;
          messages.value = [];
        }
        common_vendor.index.showToast({
          title: "删除成功",
          icon: "success"
        });
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/digitalHuman.ts:360", "删除会话失败:", error);
      common_vendor.index.showToast({
        title: "删除失败",
        icon: "error"
      });
    }
  };
  const clearMessages = async () => {
    if (!currentSession.value)
      return;
    try {
      const response = await api_digitalHuman.digitalHumanApi.clearChatMessages(currentSession.value.id);
      if (response.success) {
        messages.value = [];
        common_vendor.index.showToast({
          title: "清空成功",
          icon: "success"
        });
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/digitalHuman.ts:383", "清空消息失败:", error);
      common_vendor.index.showToast({
        title: "清空失败",
        icon: "error"
      });
    }
  };
  const updateVoiceConfig = (config) => {
    voiceConfig.value = { ...voiceConfig.value, ...config };
  };
  return {
    // 状态
    digitalHumans,
    currentDigitalHuman,
    chatSessions,
    currentSession,
    messages,
    isLoading,
    isSending,
    isGeneratingVideo,
    voiceConfig,
    // 计算属性
    hasDigitalHumans,
    currentSessionMessages,
    recentSessions,
    // 方法
    loadDigitalHumans,
    selectDigitalHuman,
    loadChatSessions,
    createNewSession,
    selectSession,
    loadMessages,
    sendTextMessage,
    sendAudioMessage,
    getDigitalHumanReply,
    generateVideoReply,
    deleteSession,
    clearMessages,
    updateVoiceConfig
  };
}, {
  persist: {
    key: "digital-human-store",
    paths: ["currentDigitalHuman", "voiceConfig"]
  }
});
exports.useDigitalHumanStore = useDigitalHumanStore;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/store/modules/digitalHuman.js.map
