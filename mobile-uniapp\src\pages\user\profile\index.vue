<template>
  <view class="profile-page">
    <!-- 用户信息头部 -->
    <view class="profile-header">
      <view class="header-bg"></view>
      <view class="user-info">
        <view class="avatar-container" @click="changeAvatar">
          <image 
            class="user-avatar" 
            :src="userInfo?.avatar || '/static/images/default-avatar.png'"
            mode="aspectFill"
          ></image>
          <view class="avatar-edit">
            <u-icon name="camera" size="16" color="#fff"></u-icon>
          </view>
        </view>
        
        <view class="user-details">
          <text class="user-name">{{ userDisplayName }}</text>
          <text class="user-email" v-if="userInfo?.email">{{ userInfo.email }}</text>
          <view class="user-stats">
            <view class="stat-item">
              <text class="stat-number">{{ userStats?.total_translations || 0 }}</text>
              <text class="stat-label">翻译次数</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{ userStats?.total_conversations || 0 }}</text>
              <text class="stat-label">对话次数</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{ userStats?.total_agents || 0 }}</text>
              <text class="stat-label">智能体</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 快捷功能 -->
    <view class="quick-actions">
      <view 
        class="action-item"
        v-for="action in quickActions"
        :key="action.id"
        @click="handleQuickAction(action)"
      >
        <view class="action-icon" :style="{ backgroundColor: action.color }">
          <u-icon :name="action.icon" color="#fff" size="20"></u-icon>
        </view>
        <text class="action-title">{{ action.title }}</text>
        <view class="action-badge" v-if="action.badge">
          <text class="badge-text">{{ action.badge }}</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-sections">
      <!-- 我的内容 -->
      <view class="menu-section">
        <view class="section-title">
          <text class="title-text">我的内容</text>
        </view>
        <view class="menu-list">
          <view 
            class="menu-item"
            v-for="item in myContentMenus"
            :key="item.id"
            @click="navigateTo(item.path)"
          >
            <view class="menu-icon">
              <u-icon :name="item.icon" :color="item.color" size="20"></u-icon>
            </view>
            <text class="menu-title">{{ item.title }}</text>
            <view class="menu-extra" v-if="item.count !== undefined">
              <text class="extra-text">{{ item.count }}</text>
            </view>
            <u-icon name="arrow-right" color="#C4C4C4" size="16"></u-icon>
          </view>
        </view>
      </view>

      <!-- 应用设置 -->
      <view class="menu-section">
        <view class="section-title">
          <text class="title-text">应用设置</text>
        </view>
        <view class="menu-list">
          <view 
            class="menu-item"
            v-for="item in settingsMenus"
            :key="item.id"
            @click="handleSettingsMenu(item)"
          >
            <view class="menu-icon">
              <u-icon :name="item.icon" :color="item.color" size="20"></u-icon>
            </view>
            <text class="menu-title">{{ item.title }}</text>
            <view class="menu-extra" v-if="item.extra">
              <text class="extra-text">{{ item.extra }}</text>
            </view>
            <u-icon name="arrow-right" color="#C4C4C4" size="16"></u-icon>
          </view>
        </view>
      </view>

      <!-- 帮助与反馈 -->
      <view class="menu-section">
        <view class="section-title">
          <text class="title-text">帮助与反馈</text>
        </view>
        <view class="menu-list">
          <view 
            class="menu-item"
            v-for="item in helpMenus"
            :key="item.id"
            @click="handleHelpMenu(item)"
          >
            <view class="menu-icon">
              <u-icon :name="item.icon" :color="item.color" size="20"></u-icon>
            </view>
            <text class="menu-title">{{ item.title }}</text>
            <view class="menu-extra" v-if="item.badge">
              <view class="badge-dot"></view>
            </view>
            <u-icon name="arrow-right" color="#C4C4C4" size="16"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section">
      <u-button 
        type="error" 
        size="large"
        :custom-style="{ width: '100%', marginTop: '40rpx' }"
        @click="handleLogout"
      >
        退出登录
      </u-button>
    </view>

    <!-- 版本信息 -->
    <view class="version-info">
      <text class="version-text">版本 {{ appVersion }}</text>
    </view>

    <!-- 头像选择弹窗 -->
    <u-action-sheet 
      v-model="showAvatarSheet"
      :actions="avatarActions"
      @click="handleAvatarAction"
    ></u-action-sheet>

    <!-- 语言选择弹窗 -->
    <u-picker
      v-model="showLanguagePicker"
      :columns="languageColumns"
      @confirm="onLanguageConfirm"
      @cancel="showLanguagePicker = false"
    ></u-picker>

    <!-- 主题选择弹窗 -->
    <u-action-sheet 
      v-model="showThemeSheet"
      :actions="themeActions"
      @click="handleThemeAction"
    ></u-action-sheet>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store/modules/user'

// Store
const userStore = useUserStore()

// 响应式数据
const userStats = ref<any>(null)
const showAvatarSheet = ref<boolean>(false)
const showLanguagePicker = ref<boolean>(false)
const showThemeSheet = ref<boolean>(false)
const appVersion = ref<string>('1.0.0')

// 计算属性
const { userInfo, userDisplayName } = userStore

// 快捷功能
const quickActions = ref([
  {
    id: 'favorites',
    title: '我的收藏',
    icon: 'heart-fill',
    color: '#EF4444',
    badge: '12',
    path: '/pages/user/favorites/index'
  },
  {
    id: 'history',
    title: '使用历史',
    icon: 'clock-fill',
    color: '#2563EB',
    path: '/pages/user/history/index'
  },
  {
    id: 'downloads',
    title: '我的下载',
    icon: 'download',
    color: '#10B981',
    path: '/pages/user/downloads/index'
  },
  {
    id: 'wallet',
    title: '我的钱包',
    icon: 'wallet',
    color: '#F59E0B',
    path: '/pages/user/wallet/index'
  }
])

// 我的内容菜单
const myContentMenus = ref([
  {
    id: 'translations',
    title: '翻译记录',
    icon: 'globe',
    color: '#2563EB',
    count: 156,
    path: '/pages/user/translations/index'
  },
  {
    id: 'digital-humans',
    title: '数字人对话',
    icon: 'account-circle',
    color: '#10B981',
    count: 23,
    path: '/pages/user/digital-humans/index'
  },
  {
    id: 'ai-agents',
    title: 'AI智能体',
    icon: 'robot',
    color: '#F59E0B',
    count: 8,
    path: '/pages/user/ai-agents/index'
  },
  {
    id: 'documents',
    title: '文档转换',
    icon: 'file-document',
    color: '#8B5CF6',
    count: 45,
    path: '/pages/user/documents/index'
  }
])

// 设置菜单
const settingsMenus = ref([
  {
    id: 'account',
    title: '账号设置',
    icon: 'account-settings',
    color: '#6B7280',
    path: '/pages/user/account/index'
  },
  {
    id: 'privacy',
    title: '隐私设置',
    icon: 'shield-check',
    color: '#059669',
    path: '/pages/user/privacy/index'
  },
  {
    id: 'notifications',
    title: '消息通知',
    icon: 'bell',
    color: '#DC2626',
    extra: '开启'
  },
  {
    id: 'language',
    title: '语言设置',
    icon: 'translate',
    color: '#2563EB',
    extra: '中文'
  },
  {
    id: 'theme',
    title: '主题设置',
    icon: 'palette',
    color: '#7C3AED',
    extra: '跟随系统'
  },
  {
    id: 'cache',
    title: '清理缓存',
    icon: 'delete-sweep',
    color: '#EF4444',
    extra: '125MB'
  }
])

// 帮助菜单
const helpMenus = ref([
  {
    id: 'help',
    title: '使用帮助',
    icon: 'help-circle',
    color: '#2563EB',
    path: '/pages/help/index'
  },
  {
    id: 'feedback',
    title: '意见反馈',
    icon: 'message-text',
    color: '#10B981',
    path: '/pages/feedback/index'
  },
  {
    id: 'about',
    title: '关于我们',
    icon: 'information',
    color: '#6B7280',
    path: '/pages/about/index'
  },
  {
    id: 'update',
    title: '检查更新',
    icon: 'update',
    color: '#F59E0B',
    badge: true
  }
])

// 头像操作选项
const avatarActions = ref([
  { name: '拍照', value: 'camera' },
  { name: '从相册选择', value: 'album' },
  { name: '查看头像', value: 'view' }
])

// 语言选项
const languageColumns = ref([[
  { text: '中文', value: 'zh-CN' },
  { text: 'English', value: 'en-US' },
  { text: '日本語', value: 'ja-JP' },
  { text: '한국어', value: 'ko-KR' }
]])

// 主题选项
const themeActions = ref([
  { name: '跟随系统', value: 'auto' },
  { name: '浅色模式', value: 'light' },
  { name: '深色模式', value: 'dark' }
])

// 页面加载
onMounted(async () => {
  await loadUserStats()

  // 获取应用版本信息
  // #ifdef APP-PLUS
  const appInfo = plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
    appVersion.value = widgetInfo.version
  })
  // #endif
})

// 加载用户统计信息
const loadUserStats = async () => {
  try {
    const stats = await userStore.getUserStats()
    if (stats) {
      userStats.value = stats
    }
  } catch (error) {
    console.error('加载用户统计失败:', error)
  }
}

// 处理快捷功能
const handleQuickAction = (action: any) => {
  if (action.path) {
    navigateTo(action.path)
  }
}

// 处理设置菜单
const handleSettingsMenu = (item: any) => {
  switch (item.id) {
    case 'notifications':
      toggleNotifications()
      break
    case 'language':
      showLanguagePicker.value = true
      break
    case 'theme':
      showThemeSheet.value = true
      break
    case 'cache':
      clearCache()
      break
    default:
      if (item.path) {
        navigateTo(item.path)
      }
  }
}

// 处理帮助菜单
const handleHelpMenu = (item: any) => {
  switch (item.id) {
    case 'update':
      checkForUpdate()
      break
    default:
      if (item.path) {
        navigateTo(item.path)
      }
  }
}

// 更换头像
const changeAvatar = () => {
  showAvatarSheet.value = true
}

// 处理头像操作
const handleAvatarAction = (action: any) => {
  showAvatarSheet.value = false

  switch (action.value) {
    case 'camera':
      chooseAvatar('camera')
      break
    case 'album':
      chooseAvatar('album')
      break
    case 'view':
      previewAvatar()
      break
  }
}

// 选择头像
const chooseAvatar = (sourceType: 'camera' | 'album') => {
  uni.chooseImage({
    count: 1,
    sourceType: [sourceType],
    success: async (res) => {
      const filePath = res.tempFilePaths[0]

      try {
        uni.showLoading({ title: '上传中...' })

        const result = await userStore.uploadAvatar(filePath)

        uni.hideLoading()

        if (result.success) {
          uni.showToast({
            title: '头像更新成功',
            icon: 'success'
          })
        }
      } catch (error) {
        uni.hideLoading()
        console.error('上传头像失败:', error)
      }
    },
    fail: (error) => {
      console.error('选择图片失败:', error)
    }
  })
}

// 预览头像
const previewAvatar = () => {
  if (userInfo.value?.avatar) {
    uni.previewImage({
      urls: [userInfo.value.avatar],
      current: 0
    })
  }
}

// 切换通知设置
const toggleNotifications = () => {
  uni.showModal({
    title: '消息通知',
    content: '是否开启消息通知？',
    success: (res) => {
      if (res.confirm) {
        // 处理通知设置
        uni.showToast({
          title: '设置成功',
          icon: 'success'
        })
      }
    }
  })
}

// 语言选择确认
const onLanguageConfirm = (value: any) => {
  const language = value[0].value

  // 更新语言设置
  uni.setStorageSync('app_language', language)

  // 更新菜单显示
  const languageMenu = settingsMenus.value.find(m => m.id === 'language')
  if (languageMenu) {
    languageMenu.extra = value[0].text
  }

  showLanguagePicker.value = false

  uni.showToast({
    title: '语言设置成功',
    icon: 'success'
  })
}

// 处理主题选择
const handleThemeAction = (action: any) => {
  showThemeSheet.value = false

  // 更新主题设置
  uni.setStorageSync('app_theme', action.value)

  // 更新菜单显示
  const themeMenu = settingsMenus.value.find(m => m.id === 'theme')
  if (themeMenu) {
    themeMenu.extra = action.name
  }

  uni.showToast({
    title: '主题设置成功',
    icon: 'success'
  })
}

// 清理缓存
const clearCache = () => {
  uni.showModal({
    title: '清理缓存',
    content: '确定要清理应用缓存吗？这将删除临时文件和图片缓存。',
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({ title: '清理中...' })

        // 模拟清理过程
        setTimeout(() => {
          uni.hideLoading()

          // 更新缓存大小显示
          const cacheMenu = settingsMenus.value.find(m => m.id === 'cache')
          if (cacheMenu) {
            cacheMenu.extra = '0MB'
          }

          uni.showToast({
            title: '清理完成',
            icon: 'success'
          })
        }, 2000)
      }
    }
  })
}

// 检查更新
const checkForUpdate = () => {
  uni.showLoading({ title: '检查中...' })

  // 模拟检查更新
  setTimeout(() => {
    uni.hideLoading()

    uni.showModal({
      title: '检查更新',
      content: '当前已是最新版本',
      showCancel: false,
      confirmText: '确定'
    })
  }, 1500)
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '退出登录',
    content: '确定要退出当前账号吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await userStore.logout()
        } catch (error) {
          console.error('退出登录失败:', error)
        }
      }
    }
  })
}

// 页面导航
const navigateTo = (path: string) => {
  uni.navigateTo({
    url: path,
    fail: (err) => {
      console.error('页面跳转失败:', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'error'
      })
    }
  })
}

// 页面下拉刷新
const onPullDownRefresh = async () => {
  try {
    await loadUserStats()
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '刷新失败',
      icon: 'error'
    })
  } finally {
    uni.stopPullDownRefresh()
  }
}

// 导出方法供页面使用
defineExpose({
  onPullDownRefresh
})
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background-color: $bg-secondary;
}

// 用户信息头部
.profile-header {
  position: relative;
  background-color: $bg-primary;
  padding-bottom: $spacing-6;

  .header-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 300rpx;
    background: linear-gradient(135deg, #667eea, #764ba2);
  }

  .user-info {
    position: relative;
    z-index: 1;
    padding: $spacing-6 $spacing-4;
    display: flex;
    align-items: center;
    gap: $spacing-4;

    .avatar-container {
      position: relative;

      .user-avatar {
        width: 160rpx;
        height: 160rpx;
        border-radius: $radius-full;
        border: 4px solid rgba(255, 255, 255, 0.2);
      }

      .avatar-edit {
        position: absolute;
        bottom: 8rpx;
        right: 8rpx;
        width: 48rpx;
        height: 48rpx;
        background-color: #667eea;
        border-radius: $radius-full;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid white;
      }
    }

    .user-details {
      flex: 1;
      color: white;

      .user-name {
        display: block;
        font-size: $font-size-xl;
        font-weight: $font-weight-bold;
        margin-bottom: $spacing-1;
      }

      .user-email {
        display: block;
        font-size: $font-size-sm;
        opacity: 0.8;
        margin-bottom: $spacing-3;
      }

      .user-stats {
        display: flex;
        gap: $spacing-6;

        .stat-item {
          text-align: center;

          .stat-number {
            display: block;
            font-size: $font-size-lg;
            font-weight: $font-weight-bold;
            margin-bottom: 4rpx;
          }

          .stat-label {
            font-size: $font-size-xs;
            opacity: 0.8;
          }
        }
      }
    }
  }
}

// 快捷功能
.quick-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: $spacing-4;
  padding: $spacing-4;
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;

    .action-icon {
      width: 96rpx;
      height: 96rpx;
      border-radius: $radius-2xl;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: $spacing-2;
    }

    .action-title {
      font-size: $font-size-sm;
      color: $text-primary;
    }

    .action-badge {
      position: absolute;
      top: -8rpx;
      right: 16rpx;
      background-color: $error;
      border-radius: $radius-full;
      padding: 4rpx 12rpx;
      min-width: 32rpx;

      .badge-text {
        font-size: $font-size-xs;
        color: white;
        text-align: center;
      }
    }
  }
}

// 菜单区域
.menu-sections {
  .menu-section {
    background-color: $bg-primary;
    margin-bottom: $spacing-3;

    .section-title {
      padding: $spacing-4 $spacing-4 $spacing-2;

      .title-text {
        font-size: $font-size-base;
        font-weight: $font-weight-semibold;
        color: $text-secondary;
      }
    }

    .menu-list {
      .menu-item {
        display: flex;
        align-items: center;
        padding: $spacing-4;
        border-bottom: 1px solid $border-primary;

        &:last-child {
          border-bottom: none;
        }

        &:active {
          background-color: $bg-tertiary;
        }

        .menu-icon {
          width: 64rpx;
          height: 64rpx;
          background-color: $bg-tertiary;
          border-radius: $radius-lg;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: $spacing-3;
        }

        .menu-title {
          flex: 1;
          font-size: $font-size-base;
          color: $text-primary;
        }

        .menu-extra {
          margin-right: $spacing-2;

          .extra-text {
            font-size: $font-size-sm;
            color: $text-tertiary;
          }

          .badge-dot {
            width: 16rpx;
            height: 16rpx;
            background-color: $error;
            border-radius: $radius-full;
          }
        }
      }
    }
  }
}

// 退出登录区域
.logout-section {
  padding: $spacing-4;
}

// 版本信息
.version-info {
  text-align: center;
  padding: $spacing-4;

  .version-text {
    font-size: $font-size-sm;
    color: $text-quaternary;
  }
}

// 响应式适配
@media screen and (max-width: 480px) {
  .profile-header .user-info {
    flex-direction: column;
    text-align: center;

    .user-details .user-stats {
      justify-content: center;
    }
  }

  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-6;

    .action-item .action-icon {
      width: 120rpx;
      height: 120rpx;
    }
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .profile-page {
    background-color: #1a1a1a;
  }

  .profile-header {
    background-color: #2d2d2d;

    .header-bg {
      background: linear-gradient(135deg, #1e40af, #3b82f6);
    }
  }

  .quick-actions,
  .menu-section {
    background-color: #2d2d2d;
    border-color: #404040;
  }

  .menu-item {
    border-color: #404040;

    &:active {
      background-color: #404040;
    }
  }

  .menu-icon {
    background-color: #404040;
  }

  .user-details {
    color: #ffffff;
  }

  .menu-title {
    color: #ffffff;
  }

  .extra-text {
    color: #cccccc;
  }

  .version-text {
    color: #888888;
  }
}

// 安全区域适配
.profile-header {
  padding-top: calc($spacing-6 + env(safe-area-inset-top));
}

.logout-section {
  padding-bottom: calc($spacing-4 + env(safe-area-inset-bottom));
}
</style>
