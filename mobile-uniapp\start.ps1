Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   AI System Mobile Quick Start" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Starting project preview..." -ForegroundColor Green
Write-Host ""

Write-Host "Project includes complete features:" -ForegroundColor Yellow
Write-Host "   - Smart Translation (Text/Audio/Video/Document)" -ForegroundColor White
Write-Host "   - Digital Human Chat" -ForegroundColor White
Write-Host "   - AI Agents Market" -ForegroundColor White
Write-Host "   - Utility Tools" -ForegroundColor White
Write-Host "   - User Management System" -ForegroundColor White
Write-Host ""

Write-Host "Opening project preview page..." -ForegroundColor Green
Start-Process "index.html"

Write-Host ""
Write-Host "Tips:" -ForegroundColor Yellow
Write-Host "   - This is the project preview page" -ForegroundColor White
Write-Host "   - To run full Uni-app project, use HBuilderX" -ForegroundColor White
Write-Host "   - Or install dependencies and use npm run dev:h5" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
