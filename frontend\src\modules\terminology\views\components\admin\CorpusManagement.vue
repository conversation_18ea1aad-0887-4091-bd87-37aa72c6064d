<template>
  <div class="corpus-management-container">
    <div class="corpus-management-layout">
      <!-- 左侧语料库列表 -->
      <div class="corpus-list-area">
        <div class="panel">
          <div class="panel-header">
            <span class="panel-title">术语库列表</span>
            <div class="header-actions">
              <a-button type="primary" @click="showAddCorpusModal">
                <plus-outlined /> 新建术语库
              </a-button>
              <a-button @click="refreshCorpusList" :loading="loading">
                <reload-outlined /> 刷新数据
              </a-button>
            </div>
          </div>
          <div class="panel-content">
            <div class="filter-bar">
              <div class="filter-item">
                <a-select
                  v-model:value="languageFilter"
                  size="small"
                  class="language-select"
                  placeholder="选择语言"
                  style="width: 120px"
                  @change="handleLanguageFilterChange"
                >
                  <a-select-option value="all">全部语言</a-select-option>
                  <a-select-option v-for="option in languageOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </a-select-option>
                </a-select>
              </div>
              <div class="filter-item">
                <a-radio-group v-model:value="visibilityFilter" size="small" class="custom-radio-group">
                  <a-radio-button value="all">全部</a-radio-button>
                  <a-radio-button value="PRIVATE">仅自己可见</a-radio-button>
                  <a-radio-button value="TEAM">团队可见</a-radio-button>
                  <a-radio-button value="PUBLIC">公开可见</a-radio-button>
                </a-radio-group>
              </div>
              <div class="filter-item search-area">
                <div class="custom-search-container">
                  <div class="search-icon">
                    <search-outlined />
                  </div>
                  <input
                    v-model="searchText"
                    class="custom-search-input"
                    placeholder="搜索术语库"
                    @input="handleSearchInput"
                  />
                  <div v-if="searchText" class="clear-icon" @click="searchText = ''">
                    <close-outlined />
                  </div>
                </div>
              </div>
            </div>
            
            <a-table
              :dataSource="filteredCorpusData"
              :columns="corpusColumns"
              :pagination="{ pageSize: 10 }"
              :loading="loading"
              :rowClassName="getRowClassName"
              :bordered="true"
              :rowKey="record => record.key"
              @row-click="handleCorpusSelect"
            />
          </div>
        </div>
      </div>
      
      <!-- 右侧术语库详情 -->
      <div class="corpus-detail-area">
        <div class="panel" v-if="selectedCorpus">
          <div class="panel-header">
            <span class="panel-title">术语库详情</span>
            <div class="header-actions">
              <a-button type="primary" size="small" @click="showEditCorpusModal">
                <edit-outlined /> 编辑
              </a-button>
              <a-button danger size="small" @click="showDeleteConfirm">
                <delete-outlined /> 删除
              </a-button>
            </div>
          </div>
          <div class="panel-content">
            <div class="detail-item">
              <div class="detail-label">名称</div>
              <div class="detail-value">{{ selectedCorpus.name }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">语言</div>
              <div class="detail-value">{{ getLanguageLabel(selectedCorpus.language) }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">类型</div>
              <div class="detail-value">{{ selectedCorpus.isPrivate ? '团队术语库' : '公共术语库' }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">术语数量</div>
              <div class="detail-value">{{ selectedCorpus.termCount }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">符号数</div>
              <div class="detail-value">{{ selectedCorpus.tokenCount }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">创建时间</div>
              <div class="detail-value">{{ selectedCorpus.createTime }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">最后更新</div>
              <div class="detail-value">{{ selectedCorpus.updateTime }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">描述</div>
              <div class="detail-value description">{{ selectedCorpus.description || '无' }}</div>
            </div>
            
            <a-divider />
            
            <div class="action-buttons">
              <a-button type="primary" @click="showImportTermsModal">
                <upload-outlined /> 导入术语
              </a-button>
              <a-button @click="showExportTermsModal">
                <download-outlined /> 导出术语
              </a-button>
              <a-button @click="showMembersModal" v-if="selectedCorpus.isPrivate">
                <team-outlined /> 成员管理
              </a-button>
            </div>
          </div>
        </div>
        
        <div class="empty-placeholder" v-else>
          <a-empty description="请选择一个术语库" />
        </div>
      </div>
    </div>
    
    <!-- 新建术语库对话框 -->
    <a-modal
      v-model:visible="addCorpusModalVisible"
      title="新建术语库"
      @ok="handleAddCorpus"
      :confirmLoading="confirmLoading"
    >
      <a-form :model="corpusForm" layout="vertical">
        <a-form-item label="术语库名称" name="name" :rules="[{ required: true, message: '请输入术语库名称' }]">
          <a-input v-model:value="corpusForm.name" placeholder="请输入术语库名称" />
        </a-form-item>
        <a-form-item label="语言" name="language" :rules="[{ required: true, message: '请选择语言' }]">
          <a-select v-model:value="corpusForm.language" placeholder="请选择语言">
            <a-select-option v-for="option in languageOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="类型" name="isPrivate">
          <a-radio-group v-model:value="corpusForm.isPrivate">
            <a-radio :value="true">团队术语库</a-radio>
            <a-radio :value="false">公共术语库</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="可见性" name="visibility">
          <a-radio-group v-model:value="corpusForm.visibility">
            <a-radio value="PRIVATE">仅自己可见</a-radio>
            <a-radio value="TEAM">团队可见</a-radio>
            <a-radio value="PUBLIC">公开可见</a-radio>
          </a-radio-group>
          <div class="form-item-help">
            <div v-if="String(corpusForm.visibility || '').toLowerCase() === 'private'">
              <check-circle-outlined class="help-icon" /> 仅自己可见的语料库无需审核即可入库
            </div>
            <div v-else-if="String(corpusForm.visibility || '').toLowerCase() === 'team'">
              <info-circle-outlined class="help-icon" /> 团队可见的语料库需要管理员审核
            </div>
            <div v-else>
              <warning-outlined class="help-icon" /> 公开可见的语料库对所有人开放
            </div>
          </div>
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea v-model:value="corpusForm.description" placeholder="请输入术语库描述" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
    
    <!-- 编辑术语库对话框 -->
    <a-modal
      v-model:visible="editCorpusModalVisible"
      title="编辑术语库"
      @ok="handleEditCorpus"
      :confirmLoading="confirmLoading"
    >
      <a-form :model="corpusForm" layout="vertical">
        <a-form-item label="术语库名称" name="name" :rules="[{ required: true, message: '请输入术语库名称' }]">
          <a-input v-model:value="corpusForm.name" placeholder="请输入术语库名称" />
        </a-form-item>
        <a-form-item label="语言" name="language" :rules="[{ required: true, message: '请选择语言' }]">
          <a-select v-model:value="corpusForm.language" placeholder="请选择语言" disabled>
            <a-select-option v-for="option in languageOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="类型" name="isPrivate">
          <a-radio-group v-model:value="corpusForm.isPrivate">
            <a-radio :value="true">团队术语库</a-radio>
            <a-radio :value="false">公共术语库</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="可见性" name="visibility">
          <a-radio-group v-model:value="corpusForm.visibility">
            <a-radio value="PRIVATE">仅自己可见</a-radio>
            <a-radio value="TEAM">团队可见</a-radio>
            <a-radio value="PUBLIC">公开可见</a-radio>
          </a-radio-group>
          <div class="form-item-help">
            <div v-if="String(corpusForm.visibility || '').toLowerCase() === 'private'">
              <check-circle-outlined class="help-icon" /> 仅自己可见的语料库无需审核即可入库
            </div>
            <div v-else-if="String(corpusForm.visibility || '').toLowerCase() === 'team'">
              <info-circle-outlined class="help-icon" /> 团队可见的语料库需要管理员审核
            </div>
            <div v-else>
              <warning-outlined class="help-icon" /> 公开可见的语料库对所有人开放
            </div>
          </div>
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea v-model:value="corpusForm.description" placeholder="请输入术语库描述" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
    
    <!-- 导入文件对话框 -->
    <a-modal
      v-model:visible="importFileModalVisible"
      title="导入文件"
      width="700px"
      :footer="null"
    >
      <div class="import-file-container">
        <a-steps :current="importStep" size="small" class="import-steps">
          <a-step title="选择文件" />
          <a-step title="设置参数" />
          <a-step title="元信息设置" />
          <a-step title="确认导入" />
        </a-steps>
        
        <!-- 步骤1: 选择文件 -->
        <div v-if="importStep === 0" class="step-content">
          <div class="upload-area">
            <a-upload-dragger
              v-model:fileList="uploadFileList"
              name="file"
              :multiple="true"
              :before-upload="beforeFileUpload"
              @change="handleFileChange"
            >
              <p class="ant-upload-drag-icon">
                <inbox-outlined />
              </p>
              <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p class="ant-upload-hint">
                支持单个或批量上传，仅支持 .txt 文本文件
              </p>
            </a-upload-dragger>
          </div>
          
          <div class="step-actions">
            <a-button @click="cancelImport">取消</a-button>
            <a-button 
              type="primary" 
              @click="nextImportStep" 
              :disabled="!uploadFileList.length"
            >
              下一步
            </a-button>
          </div>
        </div>
        
        <!-- 步骤2: 设置参数 -->
        <div v-if="importStep === 1" class="step-content">
          <a-form layout="vertical">
            <a-form-item label="分词情况">
              <a-radio-group v-model:value="importParams.segmentation">
                <a-radio value="unsegmented">未分词</a-radio>
                <a-radio value="segmented">已分词</a-radio>
              </a-radio-group>
              <div class="form-item-help">
                <div v-if="importParams.segmentation === 'unsegmented'">
                  文件中的文本没有经过分词处理，系统将自动进行分词
                </div>
                <div v-else>
                  文件中的文本已经进行了分词处理，词与词之间使用空格分隔
                </div>
              </div>
            </a-form-item>
            
            <a-form-item label="词性标注情况">
              <a-radio-group v-model:value="importParams.posTagged">
                <a-radio value="untagged">未标注</a-radio>
                <a-radio value="tagged">已标注</a-radio>
              </a-radio-group>
              <div class="form-item-help">
                <div v-if="importParams.posTagged === 'untagged'">
                  文件中的文本没有词性标注，系统将自动进行词性标注
                </div>
                <div v-else>
                  文件中的文本已经进行了词性标注，格式为"词/词性"
                </div>
              </div>
            </a-form-item>
          </a-form>
          
          <div class="step-actions">
            <a-button @click="prevImportStep">上一步</a-button>
            <a-button type="primary" @click="nextImportStep">下一步</a-button>
          </div>
        </div>
        
        <!-- 步骤3: 元信息设置 -->
        <div v-if="importStep === 2" class="step-content">
          <div class="meta-info-header">
            <div class="meta-info-title">元信息设置</div>
            <a-radio-group v-model:value="importParams.metaInfoMode" size="small">
              <a-radio-button value="individual">单独设置</a-radio-button>
              <a-radio-button value="batch">批量设置</a-radio-button>
            </a-radio-group>
          </div>
          
          <!-- 批量设置 -->
          <div v-if="importParams.metaInfoMode === 'batch'" class="batch-meta-info">
            <a-form layout="vertical">
              <a-form-item label="领域">
                <a-select v-model:value="batchMetaInfo.domain" placeholder="请选择领域">
                  <a-select-option value="general">通用</a-select-option>
                  <a-select-option value="tech">科技</a-select-option>
                  <a-select-option value="medical">医疗</a-select-option>
                  <a-select-option value="legal">法律</a-select-option>
                  <a-select-option value="finance">金融</a-select-option>
                  <a-select-option value="custom">自定义</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item v-if="batchMetaInfo.domain === 'custom'" label="自定义领域">
                <a-input v-model:value="batchMetaInfo.customDomain" placeholder="请输入自定义领域" />
              </a-form-item>
              
              <a-form-item label="来源">
                <a-input v-model:value="batchMetaInfo.source" placeholder="请输入来源" />
              </a-form-item>
              
              <a-form-item label="备注">
                <a-textarea v-model:value="batchMetaInfo.notes" placeholder="请输入备注" :rows="3" />
              </a-form-item>
              
              <a-form-item label="自定义元信息">
                <a-button size="small" @click="addCustomMetaField">
                  <plus-outlined /> 添加字段
                </a-button>
                
                <div v-for="(field, index) in batchMetaInfo.customFields" :key="index" class="custom-meta-field">
                  <a-input v-model:value="field.key" placeholder="字段名" style="width: 40%" />
                  <a-input v-model:value="field.value" placeholder="字段值" style="width: 50%" />
                  <a-button type="text" danger @click="removeCustomMetaField(index)">
                    <delete-outlined />
                  </a-button>
                </div>
              </a-form-item>
            </a-form>
            
            <div class="apply-to-all">
              <a-button type="primary" size="small" @click="applyMetaInfoToAll">
                应用到所有文件
              </a-button>
            </div>
          </div>
          
          <!-- 单独设置 -->
          <div v-else class="individual-meta-info">
            <a-tabs v-model:activeKey="activeFileTab">
              <a-tab-pane v-for="(file, index) in uploadFileList" :key="index" :tab="file.name">
                <a-form layout="vertical">
                  <a-form-item label="领域">
                    <a-select v-model:value="file.metaInfo.domain" placeholder="请选择领域">
                      <a-select-option value="general">通用</a-select-option>
                      <a-select-option value="tech">科技</a-select-option>
                      <a-select-option value="medical">医疗</a-select-option>
                      <a-select-option value="legal">法律</a-select-option>
                      <a-select-option value="finance">金融</a-select-option>
                      <a-select-option value="custom">自定义</a-select-option>
                    </a-select>
                  </a-form-item>
                  
                  <a-form-item v-if="file.metaInfo.domain === 'custom'" label="自定义领域">
                    <a-input v-model:value="file.metaInfo.customDomain" placeholder="请输入自定义领域" />
                  </a-form-item>
                  
                  <a-form-item label="来源">
                    <a-input v-model:value="file.metaInfo.source" placeholder="请输入来源" />
                  </a-form-item>
                  
                  <a-form-item label="备注">
                    <a-textarea v-model:value="file.metaInfo.notes" placeholder="请输入备注" :rows="3" />
                  </a-form-item>
                  
                  <a-form-item label="自定义元信息">
                    <a-button size="small" @click="() => addCustomMetaFieldToFile(index)">
                      <plus-outlined /> 添加字段
                    </a-button>
                    
                    <div v-for="(field, fieldIndex) in file.metaInfo.customFields" :key="fieldIndex" class="custom-meta-field">
                      <a-input v-model:value="field.key" placeholder="字段名" style="width: 40%" />
                      <a-input v-model:value="field.value" placeholder="字段值" style="width: 50%" />
                      <a-button type="text" danger @click="() => removeCustomMetaFieldFromFile(index, fieldIndex)">
                        <delete-outlined />
                      </a-button>
                    </div>
                  </a-form-item>
                </a-form>
              </a-tab-pane>
            </a-tabs>
          </div>
          
          <div class="step-actions">
            <a-button @click="prevImportStep">上一步</a-button>
            <a-button type="primary" @click="nextImportStep">下一步</a-button>
          </div>
        </div>
        
        <!-- 步骤4: 确认导入 -->
        <div v-if="importStep === 3" class="step-content">
          <div class="confirm-import">
            <div class="confirm-title">确认导入信息</div>
            
            <div class="confirm-section">
              <div class="confirm-section-title">文件信息</div>
              <div class="confirm-item">
                <span class="confirm-label">文件数量:</span>
                <span class="confirm-value">{{ uploadFileList.length }} 个文件</span>
              </div>
              <div class="confirm-item">
                <span class="confirm-label">文件列表:</span>
                <div class="confirm-file-list">
                  <div v-for="(file, index) in uploadFileList" :key="index" class="confirm-file-item">
                    {{ file.name }} ({{ formatFileSize(file.size) }})
                  </div>
                </div>
              </div>
            </div>
            
            <div class="confirm-section">
              <div class="confirm-section-title">参数设置</div>
              <div class="confirm-item">
                <span class="confirm-label">分词情况:</span>
                <span class="confirm-value">{{ importParams.segmentation === 'segmented' ? '已分词' : '未分词' }}</span>
              </div>
              <div class="confirm-item">
                <span class="confirm-label">词性标注:</span>
                <span class="confirm-value">{{ importParams.posTagged === 'tagged' ? '已标注' : '未标注' }}</span>
              </div>
              <div class="confirm-item">
                <span class="confirm-label">元信息模式:</span>
                <span class="confirm-value">{{ importParams.metaInfoMode === 'batch' ? '批量设置' : '单独设置' }}</span>
              </div>
            </div>
            
            <div class="confirm-section" v-if="importParams.metaInfoMode === 'batch'">
              <div class="confirm-section-title">批量元信息</div>
              <div class="confirm-item">
                <span class="confirm-label">领域:</span>
                <span class="confirm-value">{{ getBatchDomainDisplay() }}</span>
              </div>
              <div class="confirm-item">
                <span class="confirm-label">来源:</span>
                <span class="confirm-value">{{ batchMetaInfo.source || '未设置' }}</span>
              </div>
              <div class="confirm-item" v-if="batchMetaInfo.notes">
                <span class="confirm-label">备注:</span>
                <span class="confirm-value">{{ batchMetaInfo.notes }}</span>
              </div>
              <div class="confirm-item" v-if="batchMetaInfo.customFields.length">
                <span class="confirm-label">自定义字段:</span>
                <div class="confirm-custom-fields">
                  <div v-for="(field, index) in batchMetaInfo.customFields" :key="index" class="confirm-custom-field">
                    {{ field.key }}: {{ field.value }}
                  </div>
                </div>
              </div>
            </div>
            
            <div class="confirm-notice">
              <info-circle-outlined class="notice-icon" />
              <span v-if="String(selectedCorpus.visibility || '').toLowerCase() === 'private'">
                由于该语料库设置为"仅自己可见"，上传的文件将直接入库，无需审核。
              </span>
              <span v-else>
                上传完成后，文件将提交给管理员审核，审核通过后才会正式入库。
              </span>
            </div>
          </div>
          
          <div class="step-actions">
            <a-button @click="prevImportStep">上一步</a-button>
            <a-button type="primary" :loading="uploading" @click="submitImport">
              {{ uploading ? '上传中...' : '确认导入' }}
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, computed, onMounted, h } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Search,
  Close,
  Plus,
  Edit,
  Delete,
  Upload,
  Download,
  UserFilled,
  Refresh,
  CircleCheck,
  InfoFilled,
  Warning,
  Box
} from '@element-plus/icons-vue';
import { getMonoCorpusList, uploadFile } from '@/api/terminology';

export default defineComponent({
  name: 'CorpusManagement',
  components: {
    Search,
    Close,
    Plus,
    Edit,
    Delete,
    Upload,
    Download,
    UserFilled,
    Refresh,
    CircleCheck,
    InfoFilled,
    Warning,
    Box
  },
  setup() {
    // 语言选项
    const languageOptions = [
      { label: '中文', value: 'zh' },
      { label: '英文', value: 'en' },
      { label: '日文', value: 'ja' },
      { label: '韩文', value: 'ko' },
      { label: '法文', value: 'fr' },
      { label: '德文', value: 'de' },
      { label: '西班牙文', value: 'es' }
    ];
    
    // 加载状态
    const loading = ref(false);
    
    // 获取行样式
    const getRowClassName = (record) => {
      if (record.visibility && record.visibility.toLowerCase() === 'private') {
        return 'private-corpus-row';
      } else if (record.visibility && record.visibility.toLowerCase() === 'team') {
        return 'team-corpus-row';
      } else if (record.visibility && record.visibility.toLowerCase() === 'public') {
        return 'public-corpus-row';
      }
      return '';
    };
    
    // 过滤条件
    const languageFilter = ref('all');
    const visibilityFilter = ref('all');
    const searchText = ref('');
    
    // 语料库数据
    const corpusData = ref([]);
    const selectedCorpus = ref(null);
    
    // 表单相关
    const addCorpusModalVisible = ref(false);
    const editCorpusModalVisible = ref(false);
    const confirmLoading = ref(false);
    const corpusForm = ref({
      name: '',
      language: 'zh',
      isPrivate: true,
      visibility: 'TEAM',
      description: ''
    });
    
    // 表格列定义
    const corpusColumns = [
      {
        title: '术语库名称',
        dataIndex: 'name',
        key: 'name',
        ellipsis: true,
      },
      {
        title: '语言',
        dataIndex: 'language',
        key: 'language',
        width: 80,
        customRender: ({ text, record }) => {
          // 首先尝试通过languageOptions查找
          const lang = languageOptions.find(item => item.value === record.languageCode || item.value === text);
          if (lang) {
            return lang.label;
          }
          
          // 如果找不到匹配项，检查text是否已经是中文名称
          const isChineseName = /[\u4e00-\u9fa5]/.test(text); // 检查是否包含中文字符
          if (isChineseName) {
            return text; // 如果已经是中文名称，直接返回
          }
          
          // 如果是语言代码，尝试映射
          const langMap = {
            'zh': '中文',
            'zh-cn': '中文(简体)',
            'zh-tw': '中文(繁体)',
            'en': '英文',
            'fr': '法文',
            'de': '德文',
            'ru': '俄文',
            'es': '西班牙文',
            'ja': '日文',
            'ko': '韩文'
          };
          
          return langMap[text.toLowerCase()] || text;
        }
      },
      {
        title: '术语数',
        dataIndex: 'termCount',
        key: 'termCount',
        width: 80,
        align: 'right'
      },
      {
        title: '可见性',
        dataIndex: 'visibility',
        key: 'visibility',
        width: 100,
        customRender: ({ text }) => {
          // 处理不同格式的visibility值
          if (typeof text === 'string') {
            const lowerText = text.toLowerCase();
            if (lowerText === 'private') {
              return h('span', { class: 'visibility-tag visibility-private' }, '仅自己可见');
            }
            if (lowerText === 'team') {
              return h('span', { class: 'visibility-tag visibility-team' }, '团队可见');
            }
            if (lowerText === 'public') {
              return h('span', { class: 'visibility-tag visibility-public' }, '公开可见');
            }
          }
          
          // 默认返回
          return '未知';
        }
      }
    ];
    
    // 过滤后的语料库数据
    const filteredCorpusData = computed(() => {
      let result = [...corpusData.value];
      
      // 语言过滤
      if (languageFilter.value !== 'all') {
        result = result.filter(item => item.language === languageFilter.value);
      }
      
      // 类型过滤
      if (visibilityFilter.value !== 'all') {
        result = result.filter(item => item.visibility === visibilityFilter.value);
      }
      
      // 搜索过滤
      if (searchText.value) {
        const searchLower = searchText.value.toLowerCase();
        result = result.filter(item => {
          return item.name.toLowerCase().includes(searchLower);
        });
      }
      
      return result;
    });
    
    // 初始化语料库列表
    onMounted(async () => {
      console.log('[语料库管理] 组件挂载，开始加载数据');
      try {
        await loadCorpusList();
        console.log('[语料库管理] 数据加载完成');
      } catch (error) {
        console.error('[语料库管理] 初始化加载失败:', error);
        ElMessage.error('加载语料库列表失败，请稍后重试');
      }
    });
    
    // 加载语料库列表
    const loadCorpusList = async () => {
      try {
        // 显示加载状态
        loading.value = true;
        
        console.log('[语料库管理] 开始加载语料库列表');
        
        // 调用API获取单语语料库列表
        const response = await getMonoCorpusList();
        
        console.log('[语料库管理] API返回数据:', JSON.stringify(response).substring(0, 500) + '...');
        
        // 检查是否有错误标记
        if (response && response.error) {
          console.error('[语料库管理] API返回错误:', response.errorMessage);
          ElMessage.error(`获取语料库列表失败: ${response.errorMessage || '未知错误'}`);
          
          // 使用模拟数据作为回退
          console.warn('[语料库管理] 使用模拟数据作为回退');
          corpusData.value = generateMockData(3);
          return;
        }
        
        if (response && response.data) {
          // 转换数据格式
          const dataArray = Array.isArray(response.data) ? response.data : 
                          (response.data.data && Array.isArray(response.data.data)) ? response.data.data : 
                          [response.data];
          
          if (dataArray.length === 0) {
            console.warn('[语料库管理] API返回的数据为空数组，使用模拟数据');
            corpusData.value = generateMockData(2);
            return;
          }
          
          corpusData.value = dataArray.map((item, index) => {
            // 确保安全地访问数据，提供默认值
            const id = item?.id ?? `temp-${index}`;
            const name = item?.name ?? `未命名语料库-${index}`;
            const languageCode = item?.language ?? 'zh';
            const isPrivate = String(item?.visibility || '').toLowerCase() === 'private';
            const termCount = parseInt(item?.termCount ?? item?.term_count ?? 0);
            const tokenCount = parseInt(item?.tokenCount ?? item?.token_count ?? 0);
            const createTime = item?.created_at ?? item?.createTime ?? '未知';
            const updateTime = item?.updated_at ?? item?.updateTime ?? '未知';
            const description = item?.description ?? '';
            
            return {
              key: String(id),
              id: id,
              name: name,
              language: languageCode,
              languageCode: languageCode,
              termCount: termCount,
              tokenCount: tokenCount,
              visibility: item?.visibility || (isPrivate ? 'PRIVATE' : 'TEAM'),
              isPrivate: isPrivate,
              createTime: createTime,
              updateTime: updateTime,
              description: description
            };
          });
          
          console.log('[语料库管理] 处理后的语料库数据:', corpusData.value);
          
          if (corpusData.value.length > 0) {
            ElMessage.success(`成功加载 ${corpusData.value.length} 个语料库`);
          }
        } else {
          console.warn('[语料库管理] API返回的数据格式不正确或为空');
          ElMessage.warning('获取语料库列表失败: 返回数据为空');
          
          // 使用模拟数据
          corpusData.value = generateMockData(2);
        }
      } catch (error) {
        console.error('[语料库管理] 获取语料库列表失败:', error);
        ElMessage.error(`获取语料库列表失败: ${error.message || '未知错误'}`);
        
        // 如果API调用失败，使用模拟数据
        corpusData.value = generateMockData(2);
      } finally {
        loading.value = false;
      }
    };
    
    // 生成模拟数据
    const generateMockData = (count) => {
      return Array.from({ length: count }, (_, i) => ({
        key: `mock-${i}`,
        id: `mock-${i}`,
        name: `示例语料库 ${i + 1}`,
        language: 'zh',
        languageCode: 'zh',
        termCount: 100 * (i + 1),
        tokenCount: 1000 * (i + 1),
        visibility: i % 2 === 0 ? 'TEAM' : 'PRIVATE',
        isPrivate: i % 2 === 1,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        description: '这是一个示例语料库，由于API返回的数据为空或出错而创建。'
      }));
    };
    
    // 获取语言标签
    const getLanguageLabel = (value) => {
      const lang = languageOptions.find(item => item.value === value);
      return lang ? lang.label : value;
    };
    
    // 处理语言过滤变更
    const handleLanguageFilterChange = () => {
      // 过滤逻辑已在计算属性中实现
    };
    
    // 处理搜索输入
    const handleSearchInput = () => {
      // 可以在这里添加防抖逻辑
    };
    
    // 处理术语库选择
    const handleCorpusSelect = (record) => {
      selectedCorpus.value = record;
    };
    
    // 显示新建术语库对话框
    const showAddCorpusModal = () => {
      corpusForm.value = {
        name: '',
        language: 'zh',
        isPrivate: true,
        visibility: 'TEAM',
        description: ''
      };
      addCorpusModalVisible.value = true;
    };
    
    // 显示编辑术语库对话框
    const showEditCorpusModal = () => {
      if (!selectedCorpus.value) return;
      
      corpusForm.value = {
        name: selectedCorpus.value.name,
        language: selectedCorpus.value.language,
        isPrivate: selectedCorpus.value.isPrivate,
        visibility: selectedCorpus.value.visibility,
        description: selectedCorpus.value.description || ''
      };
      editCorpusModalVisible.value = true;
    };
    
    // 显示删除确认对话框
    const showDeleteConfirm = () => {
      if (!selectedCorpus.value) return;
      
      ElMessageBox.confirm(
        `确定要删除术语库 "${selectedCorpus.value.name}" 吗？此操作不可恢复。`,
        '确认删除',
        {
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        handleDeleteCorpus();
      }).catch(() => {
        // 用户取消删除
      });
    };
    
    // 处理新建术语库
    const handleAddCorpus = () => {
      confirmLoading.value = true;
      
      // 模拟API请求
      setTimeout(() => {
        const newCorpus = {
          key: `corpus-${corpusData.value.length}`,
          name: corpusForm.value.name,
          language: corpusForm.value.language,
          termCount: 0,
          tokenCount: 0,
          isPrivate: corpusForm.value.isPrivate,
          visibility: corpusForm.value.visibility,
          createTime: new Date().toLocaleString(),
          updateTime: new Date().toLocaleString(),
          description: corpusForm.value.description
        };
        
        corpusData.value = [newCorpus, ...corpusData.value];
        selectedCorpus.value = newCorpus;
        
        ElMessage.success('术语库创建成功');
        addCorpusModalVisible.value = false;
        confirmLoading.value = false;
      }, 1000);
    };
    
    // 处理编辑术语库
    const handleEditCorpus = () => {
      if (!selectedCorpus.value) return;
      
      confirmLoading.value = true;
      
      // 模拟API请求
      setTimeout(() => {
        const index = corpusData.value.findIndex(item => item.key === selectedCorpus.value.key);
        if (index !== -1) {
          const updatedCorpus = {
            ...selectedCorpus.value,
            name: corpusForm.value.name,
            isPrivate: corpusForm.value.isPrivate,
            visibility: corpusForm.value.visibility,
            description: corpusForm.value.description,
            updateTime: new Date().toLocaleString()
          };
          
          corpusData.value[index] = updatedCorpus;
          selectedCorpus.value = updatedCorpus;
        }
        
        ElMessage.success('术语库更新成功');
        editCorpusModalVisible.value = false;
        confirmLoading.value = false;
      }, 1000);
    };
    
    // 处理删除术语库
    const handleDeleteCorpus = () => {
      if (!selectedCorpus.value) return;
      
      // 模拟API请求
      setTimeout(() => {
        corpusData.value = corpusData.value.filter(item => item.key !== selectedCorpus.value.key);
        selectedCorpus.value = null;
        
        ElMessage.success('术语库删除成功');
      }, 1000);
    };
    
    // 文件导入相关
    const importFileModalVisible = ref(false);
    const importStep = ref(0);
    const uploadFileList = ref([]);
    const activeFileTab = ref('0');
    const uploading = ref(false);

    // 导入参数
    const importParams = ref({
      segmentation: 'unsegmented',
      posTagged: 'untagged',
      metaInfoMode: 'batch'
    });

    // 批量元信息
    const batchMetaInfo = ref({
      domain: 'general',
      customDomain: '',
      source: '',
      notes: '',
      customFields: []
    });

    // 显示导入文件对话框
    const showImportTermsModal = () => {
      if (!selectedCorpus.value) {
        ElMessage.warning('请先选择一个语料库');
        return;
      }
      
      // 重置导入状态
      importStep.value = 0;
      uploadFileList.value = [];
      importParams.value = {
        segmentation: 'unsegmented',
        posTagged: 'untagged',
        metaInfoMode: 'batch'
      };
      batchMetaInfo.value = {
        domain: 'general',
        customDomain: '',
        source: '',
        notes: '',
        customFields: []
      };
      
      importFileModalVisible.value = true;
    };

    // 文件上传前检查
    const beforeFileUpload = (file) => {
      // 检查文件类型
      const isTxtFile = file.type === 'text/plain' || file.name.endsWith('.txt');
      if (!isTxtFile) {
        ElMessage.error('只能上传 .txt 文本文件');
      }
      
      // 检查文件大小
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        ElMessage.error('文件大小不能超过 10MB');
      }
      
      // 如果通过检查，为文件添加元信息字段
      if (isTxtFile && isLt10M) {
        file.metaInfo = {
          domain: 'general',
          customDomain: '',
          source: '',
          notes: '',
          customFields: []
        };
      }
      
      // 返回false阻止自动上传，我们将在最后一步手动上传
      return false;
    };

    // 处理文件变更
    const handleFileChange = (info) => {
      uploadFileList.value = info.fileList;
    };

    // 格式化文件大小
    const formatFileSize = (size) => {
      if (size < 1024) {
        return size + ' B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB';
      } else {
        return (size / 1024 / 1024).toFixed(2) + ' MB';
      }
    };

    // 下一步
    const nextImportStep = () => {
      if (importStep.value < 3) {
        importStep.value += 1;
      }
    };

    // 上一步
    const prevImportStep = () => {
      if (importStep.value > 0) {
        importStep.value -= 1;
      }
    };

    // 取消导入
    const cancelImport = () => {
      importFileModalVisible.value = false;
    };

    // 添加自定义元信息字段
    const addCustomMetaField = () => {
      batchMetaInfo.value.customFields.push({ key: '', value: '' });
    };

    // 删除自定义元信息字段
    const removeCustomMetaField = (index) => {
      batchMetaInfo.value.customFields.splice(index, 1);
    };

    // 添加文件自定义元信息字段
    const addCustomMetaFieldToFile = (fileIndex) => {
      if (uploadFileList.value[fileIndex] && uploadFileList.value[fileIndex].metaInfo) {
        uploadFileList.value[fileIndex].metaInfo.customFields.push({ key: '', value: '' });
      }
    };

    // 删除文件自定义元信息字段
    const removeCustomMetaFieldFromFile = (fileIndex, fieldIndex) => {
      if (uploadFileList.value[fileIndex] && uploadFileList.value[fileIndex].metaInfo) {
        uploadFileList.value[fileIndex].metaInfo.customFields.splice(fieldIndex, 1);
      }
    };

    // 应用批量元信息到所有文件
    const applyMetaInfoToAll = () => {
      uploadFileList.value.forEach(file => {
        file.metaInfo = {
          domain: batchMetaInfo.value.domain,
          customDomain: batchMetaInfo.value.customDomain,
          source: batchMetaInfo.value.source,
          notes: batchMetaInfo.value.notes,
          customFields: JSON.parse(JSON.stringify(batchMetaInfo.value.customFields))
        };
      });
      ElMessage.success('已将元信息应用到所有文件');
    };

    // 获取批量领域显示文本
    const getBatchDomainDisplay = () => {
      if (batchMetaInfo.value.domain === 'custom') {
        return batchMetaInfo.value.customDomain || '自定义';
      }
      
      const domainMap = {
        general: '通用',
        tech: '科技',
        medical: '医疗',
        legal: '法律',
        finance: '金融'
      };
      
      return domainMap[batchMetaInfo.value.domain] || batchMetaInfo.value.domain;
    };

    // 提交导入
    const submitImport = async () => {
      if (!selectedCorpus.value || !selectedCorpus.value.id) {
        ElMessage.error('未选择语料库');
        return;
      }
      
      if (uploadFileList.value.length === 0) {
        ElMessage.error('请选择要上传的文件');
        return;
      }
      
      try {
        uploading.value = true;
        
        // 准备元信息
        let metaInfo = {};
        
        if (importParams.value.metaInfoMode === 'batch') {
          // 使用批量元信息
          metaInfo = {
            domain: batchMetaInfo.value.domain === 'custom' ? batchMetaInfo.value.customDomain : batchMetaInfo.value.domain,
            source: batchMetaInfo.value.source,
            notes: batchMetaInfo.value.notes,
            customFields: batchMetaInfo.value.customFields
          };
        }
        
        // 上传每个文件
        for (let i = 0; i < uploadFileList.value.length; i++) {
          const file = uploadFileList.value[i];
          
          // 创建FormData
          const formData = new FormData();
          formData.append('file', file.originFileObj);
          formData.append('segmentation', importParams.value.segmentation);
          formData.append('pos_tagged', importParams.value.posTagged);
          
          // 添加元信息
          if (importParams.value.metaInfoMode === 'batch') {
            formData.append('meta_info', JSON.stringify(metaInfo));
          } else {
            // 使用文件各自的元信息
            const fileMetaInfo = {
              domain: file.metaInfo.domain === 'custom' ? file.metaInfo.customDomain : file.metaInfo.domain,
              source: file.metaInfo.source,
              notes: file.metaInfo.notes,
              customFields: file.metaInfo.customFields
            };
            formData.append('meta_info', JSON.stringify(fileMetaInfo));
          }
          
          // 调用API上传文件
          await uploadFile(formData, selectedCorpus.value.id);
          
          // 更新进度
          ElMessage.success(`上传成功 (${i + 1}/${uploadFileList.value.length}): ${file.name}`);
        }
        
        ElMessage.success('所有文件上传成功');
        
        // 关闭对话框
        importFileModalVisible.value = false;
        
        // 如果是私有库，直接更新语料库数据
        if (String(selectedCorpus.value.visibility || '').toLowerCase() === 'private') {
          await refreshCorpusList();
        } else {
          ElMessage.info('文件已提交，等待管理员审核');
        }
      } catch (error) {
        console.error('[文件上传] 上传失败:', error);
        ElMessage.error(`上传失败: ${error.message || '未知错误'}`);
      } finally {
        uploading.value = false;
      }
    };
    
    // 刷新语料库列表
    const refreshCorpusList = async () => {
      try {
        console.log('[语料库管理] 手动刷新语料库列表');
        ElMessage.info('正在刷新语料库列表...');
        
        // 清空现有数据
        corpusData.value = [];
        
        // 重新加载数据
        await loadCorpusList();
      } catch (error) {
        console.error('[语料库管理] 刷新语料库列表失败:', error);
        ElMessage.error(`刷新语料库列表失败: ${error.message || '未知错误'}`);
      }
    };
    
    // 显示导出术语对话框
    const showExportTermsModal = () => {
      if (!selectedCorpus.value) {
        ElMessage.warning('请先选择一个语料库');
        return;
      }
      
      // 显示导出选项菜单 - 简化为三个按钮选择
      ElMessageBox.confirm(
        '请选择导出格式：\n\n1. 导出原句子 - 导出原始句子，不包含分词和标注信息\n2. 导出带分词 - 导出带分词信息的句子，词与词之间使用空格分隔\n3. 导出带标注 - 导出带词性标注的句子，格式为"词/词性"',
        '导出语料库',
        {
          confirmButtonText: '导出原句子',
          cancelButtonText: '取消',
          type: 'info',
          distinguishCancelAndClose: true
        }
      ).then(() => {
        handleExport('original');
      }).catch((action) => {
        if (action === 'cancel') {
          // 用户点击取消
        }
      });
    };

    // 处理导出
    const handleExport = (type) => {
      if (!selectedCorpus.value || !selectedCorpus.value.id) {
        ElMessage.error('未选择语料库');
        return;
      }
      
      // 构建导出URL
      const baseUrl = '/api/terminology/corpus/export';
      const params = new URLSearchParams({
        corpus_id: selectedCorpus.value.id,
        export_type: type
      });
      
      // 创建下载链接
      const downloadUrl = `${baseUrl}?${params.toString()}`;
      
      // 创建临时链接并触发下载
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.target = '_blank';
      link.download = `${selectedCorpus.value.name}_${type}.txt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      ElMessage.success('导出请求已发送，文件将开始下载');
    };

    // 显示成员管理对话框
    const showMembersModal = () => {
      if (!selectedCorpus.value) {
        ElMessage.warning('请先选择一个语料库');
        return;
      }
      
      // 只有团队库才能管理成员
      if (selectedCorpus.value.visibility !== 'TEAM') {
        ElMessage.info('只有团队可见的语料库才能管理成员');
        return;
      }
      
      ElMessage.info('成员管理功能待实现');
    };

    return {
      // 语言相关
      languageOptions,
      getLanguageLabel,
      
      // 过滤相关
      languageFilter,
      visibilityFilter,
      searchText,
      handleLanguageFilterChange,
      handleSearchInput,
      
      // 数据相关
      corpusData,
      filteredCorpusData,
      selectedCorpus,
      corpusColumns,
      handleCorpusSelect,
      loading,
      getRowClassName,
      
      // 表单相关
      addCorpusModalVisible,
      editCorpusModalVisible,
      confirmLoading,
      corpusForm,
      showAddCorpusModal,
      showEditCorpusModal,
      showDeleteConfirm,
      handleAddCorpus,
      handleEditCorpus,
      
      // 其他功能
      showImportTermsModal,
      showExportTermsModal,
      showMembersModal,
      refreshCorpusList,
      
      // 文件导入相关
      importFileModalVisible,
      importStep,
      uploadFileList,
      activeFileTab,
      uploading,
      importParams,
      batchMetaInfo,
      formatFileSize,
      nextImportStep,
      prevImportStep,
      cancelImport,
      addCustomMetaField,
      removeCustomMetaField,
      addCustomMetaFieldToFile,
      removeCustomMetaFieldFromFile,
      applyMetaInfoToAll,
      getBatchDomainDisplay,
      submitImport
    };
  }
});
</script>

<style scoped>
.corpus-management-container {
  width: 100%;
  height: 100%;
  padding: 16px;
  background-color: #f0f2f5;
}

.corpus-management-layout {
  display: flex;
  gap: 16px;
  height: 100%;
}

.corpus-list-area {
  width: 400px;
  flex-shrink: 0;
}

.corpus-detail-area {
  flex-grow: 1;
  min-width: 0; /* 防止flex子项溢出 */
}

.panel {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 4px 4px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.panel-content {
  padding: 16px;
  flex-grow: 1;
  overflow: auto;
}

.filter-bar {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
}

.search-area {
  flex-grow: 1;
  min-width: 150px;
}

.custom-search-container {
  display: flex;
  align-items: center;
  height: 28px;
  border-radius: 20px;
  border: 1px solid #e8e8e8;
  background-color: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
  transition: all 0.3s;
  padding: 0 8px;
  position: relative;
}

.custom-search-container:hover {
  border-color: #40a9ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.custom-search-container:focus-within {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.search-icon {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  margin-right: 6px;
}

.custom-search-input {
  flex: 1;
  border: none;
  outline: none;
  height: 100%;
  font-size: 13px;
  background: transparent;
  width: 100%;
}

.clear-icon {
  color: rgba(0, 0, 0, 0.25);
  cursor: pointer;
  font-size: 12px;
  transition: color 0.3s;
}

.clear-icon:hover {
  color: rgba(0, 0, 0, 0.45);
}

.team-corpus-row {
  background-color: rgba(230, 247, 255, 0.5);
}

.private-corpus-row {
  background-color: rgba(255, 251, 230, 0.5);
}

.custom-radio-group {
  display: flex;
}

.custom-radio-group :deep(.ant-radio-button-wrapper) {
  background-color: #fff;
  border-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.65);
  font-size: 12px;
  padding: 0 8px;
  height: 28px;
  line-height: 26px;
}

.custom-radio-group :deep(.ant-radio-button-wrapper-checked) {
  background-color: #1890ff;
  border-color: #1890ff;
  color: #fff;
  box-shadow: -1px 0 0 0 #1890ff;
}

.custom-radio-group :deep(.ant-radio-button-wrapper:hover) {
  color: #40a9ff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.detail-item {
  margin-bottom: 16px;
}

.detail-label {
  font-size: 13px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 4px;
}

.detail-value {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

.detail-value.description {
  white-space: pre-line;
  background-color: #f9f9f9;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.empty-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.form-item-help {
  margin-top: 8px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.help-icon {
  margin-right: 4px;
}

.visibility-tag {
  display: inline-block;
  padding: 0 8px;
  height: 20px;
  line-height: 20px;
  border-radius: 10px;
  font-size: 12px;
  margin-left: 8px;
}

.visibility-private {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  color: #d48806;
}

.visibility-team {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  color: #1890ff;
}

.visibility-public {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .corpus-management-layout {
    flex-direction: column;
  }
  
  .corpus-list-area {
    width: 100%;
  }
}

.import-file-container {
  padding: 10px 0;
}

.import-steps {
  margin-bottom: 24px;
}

.step-content {
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

.step-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.upload-area {
  flex-grow: 1;
}

.meta-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.meta-info-title {
  font-size: 16px;
  font-weight: 500;
}

.batch-meta-info {
  margin-bottom: 16px;
}

.custom-meta-field {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.apply-to-all {
  margin-top: 16px;
  text-align: right;
}

.individual-meta-info {
  margin-bottom: 16px;
}

.confirm-import {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.confirm-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
}

.confirm-section {
  margin-bottom: 16px;
}

.confirm-section-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.65);
}

.confirm-item {
  display: flex;
  margin-bottom: 8px;
}

.confirm-label {
  width: 100px;
  color: rgba(0, 0, 0, 0.45);
}

.confirm-value {
  flex: 1;
}

.confirm-file-list {
  max-height: 100px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 8px;
}

.confirm-file-item {
  padding: 4px 0;
  border-bottom: 1px dashed #f0f0f0;
}

.confirm-file-item:last-child {
  border-bottom: none;
}

.confirm-custom-fields {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.confirm-notice {
  margin-top: 16px;
  padding: 8px 12px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.65);
}

.notice-icon {
  color: #1890ff;
  margin-right: 8px;
}

.export-options {
  padding: 16px;
}

.export-title {
  font-size: 14px;
  margin-bottom: 16px;
}

.export-option-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.export-option {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.export-option:hover {
  background-color: #f6f6f6;
  border-color: #d9d9d9;
}

.export-icon {
  font-size: 24px;
  color: #1890ff;
  margin-right: 16px;
}

.export-option-content {
  flex: 1;
}

.export-option-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.export-option-desc {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}
</style> 