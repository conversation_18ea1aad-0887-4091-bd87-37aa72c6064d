/* 现代化图标字体样式 */
@font-face {
  font-family: 'iconfont';
  src: url('//at.alicdn.com/t/font_xxx.woff2') format('woff2'),
       url('//at.alicdn.com/t/font_xxx.woff') format('woff'),
       url('//at.alicdn.com/t/font_xxx.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 图标映射 */
.icon-home:before { content: "\e600"; }
.icon-home-fill:before { content: "\e601"; }
.icon-translate:before { content: "\e602"; }
.icon-translate-fill:before { content: "\e603"; }
.icon-robot:before { content: "\e604"; }
.icon-robot-fill:before { content: "\e605"; }
.icon-chat:before { content: "\e606"; }
.icon-chat-fill:before { content: "\e607"; }
.icon-apps:before { content: "\e608"; }
.icon-apps-fill:before { content: "\e609"; }
.icon-user:before { content: "\e60a"; }
.icon-user-fill:before { content: "\e60b"; }
.icon-document:before { content: "\e60c"; }
.icon-document-fill:before { content: "\e60d"; }
.icon-image:before { content: "\e60e"; }
.icon-image-fill:before { content: "\e60f"; }
.icon-pdf:before { content: "\e610"; }
.icon-video:before { content: "\e611"; }
.icon-audio:before { content: "\e612"; }
.icon-qrcode:before { content: "\e613"; }
.icon-text:before { content: "\e614"; }
.icon-star:before { content: "\e615"; }
.icon-star-fill:before { content: "\e616"; }
.icon-heart:before { content: "\e617"; }
.icon-heart-fill:before { content: "\e618"; }
.icon-bookmark:before { content: "\e619"; }
.icon-bookmark-fill:before { content: "\e61a"; }
.icon-download:before { content: "\e61b"; }
.icon-upload:before { content: "\e61c"; }
.icon-share:before { content: "\e61d"; }
.icon-copy:before { content: "\e61e"; }
.icon-delete:before { content: "\e61f"; }
.icon-edit:before { content: "\e620"; }
.icon-search:before { content: "\e621"; }
.icon-filter:before { content: "\e622"; }
.icon-sort:before { content: "\e623"; }
.icon-menu:before { content: "\e624"; }
.icon-more:before { content: "\e625"; }
.icon-close:before { content: "\e626"; }
.icon-check:before { content: "\e627"; }
.icon-arrow-left:before { content: "\e628"; }
.icon-arrow-right:before { content: "\e629"; }
.icon-arrow-up:before { content: "\e62a"; }
.icon-arrow-down:before { content: "\e62b"; }
.icon-plus:before { content: "\e62c"; }
.icon-minus:before { content: "\e62d"; }
.icon-refresh:before { content: "\e62e"; }
.icon-loading:before { content: "\e62f"; }
.icon-success:before { content: "\e630"; }
.icon-error:before { content: "\e631"; }
.icon-warning:before { content: "\e632"; }
.icon-info:before { content: "\e633"; }
.icon-bell:before { content: "\e634"; }
.icon-bell-fill:before { content: "\e635"; }
.icon-setting:before { content: "\e636"; }
.icon-setting-fill:before { content: "\e637"; }
.icon-crown:before { content: "\e638"; }
.icon-vip:before { content: "\e639"; }
.icon-diamond:before { content: "\e63a"; }
.icon-gift:before { content: "\e63b"; }
.icon-wallet:before { content: "\e63c"; }
.icon-coin:before { content: "\e63d"; }
.icon-calendar:before { content: "\e63e"; }
.icon-clock:before { content: "\e63f"; }
.icon-location:before { content: "\e640"; }
.icon-phone:before { content: "\e641"; }
.icon-email:before { content: "\e642"; }
.icon-link:before { content: "\e643"; }
.icon-wifi:before { content: "\e644"; }
.icon-bluetooth:before { content: "\e645"; }
.icon-camera:before { content: "\e646"; }
.icon-gallery:before { content: "\e647"; }
.icon-mic:before { content: "\e648"; }
.icon-volume:before { content: "\e649"; }
.icon-play:before { content: "\e64a"; }
.icon-pause:before { content: "\e64b"; }
.icon-stop:before { content: "\e64c"; }
.icon-record:before { content: "\e64d"; }
.icon-forward:before { content: "\e64e"; }
.icon-backward:before { content: "\e64f"; }
.icon-fullscreen:before { content: "\e650"; }
.icon-minimize:before { content: "\e651"; }
.icon-maximize:before { content: "\e652"; }
.icon-eye:before { content: "\e653"; }
.icon-eye-close:before { content: "\e654"; }
.icon-lock:before { content: "\e655"; }
.icon-unlock:before { content: "\e656"; }
.icon-shield:before { content: "\e657"; }
.icon-key:before { content: "\e658"; }
.icon-fingerprint:before { content: "\e659"; }
.icon-face:before { content: "\e65a"; }
.icon-scan:before { content: "\e65b"; }
.icon-qrcode-scan:before { content: "\e65c"; }
.icon-barcode:before { content: "\e65d"; }
.icon-print:before { content: "\e65e"; }
.icon-fax:before { content: "\e65f"; }
.icon-folder:before { content: "\e660"; }
.icon-folder-open:before { content: "\e661"; }
.icon-file:before { content: "\e662"; }
.icon-file-text:before { content: "\e663"; }
.icon-file-image:before { content: "\e664"; }
.icon-file-video:before { content: "\e665"; }
.icon-file-audio:before { content: "\e666"; }
.icon-file-zip:before { content: "\e667"; }
.icon-cloud:before { content: "\e668"; }
.icon-cloud-upload:before { content: "\e669"; }
.icon-cloud-download:before { content: "\e66a"; }
.icon-database:before { content: "\e66b"; }
.icon-server:before { content: "\e66c"; }
.icon-network:before { content: "\e66d"; }
.icon-globe:before { content: "\e66e"; }
.icon-language:before { content: "\e66f"; }
.icon-code:before { content: "\e670"; }
.icon-terminal:before { content: "\e671"; }
.icon-bug:before { content: "\e672"; }
.icon-tool:before { content: "\e673"; }
.icon-wrench:before { content: "\e674"; }
.icon-hammer:before { content: "\e675"; }
.icon-scissors:before { content: "\e676"; }
.icon-brush:before { content: "\e677"; }
.icon-palette:before { content: "\e678"; }
.icon-color:before { content: "\e679"; }
.icon-magic:before { content: "\e67a"; }
.icon-wand:before { content: "\e67b"; }
.icon-sparkles:before { content: "\e67c"; }
.icon-fire:before { content: "\e67d"; }
.icon-lightning:before { content: "\e67e"; }
.icon-sun:before { content: "\e67f"; }
.icon-moon:before { content: "\e680"; }
.icon-weather:before { content: "\e681"; }
.icon-rain:before { content: "\e682"; }
.icon-snow:before { content: "\e683"; }
.icon-wind:before { content: "\e684"; }
.icon-leaf:before { content: "\e685"; }
.icon-tree:before { content: "\e686"; }
.icon-flower:before { content: "\e687"; }
.icon-pet:before { content: "\e688"; }
.icon-car:before { content: "\e689"; }
.icon-plane:before { content: "\e68a"; }
.icon-train:before { content: "\e68b"; }
.icon-ship:before { content: "\e68c"; }
.icon-bike:before { content: "\e68d"; }
.icon-walk:before { content: "\e68e"; }
.icon-run:before { content: "\e68f"; }
.icon-sport:before { content: "\e690"; }
.icon-game:before { content: "\e691"; }
.icon-music:before { content: "\e692"; }
.icon-movie:before { content: "\e693"; }
.icon-book:before { content: "\e694"; }
.icon-news:before { content: "\e695"; }
.icon-magazine:before { content: "\e696"; }
.icon-education:before { content: "\e697"; }
.icon-graduation:before { content: "\e698"; }
.icon-medal:before { content: "\e699"; }
.icon-trophy:before { content: "\e69a"; }
.icon-award:before { content: "\e69b"; }
.icon-ribbon:before { content: "\e69c"; }
.icon-flag:before { content: "\e69d"; }
.icon-target:before { content: "\e69e"; }
.icon-bullseye:before { content: "\e69f"; }

/* 图标动画效果 */
.icon-loading {
  animation: icon-spin 1s linear infinite;
}

.icon-heart-fill {
  animation: icon-heartbeat 1.5s ease-in-out infinite;
}

.icon-bell-fill {
  animation: icon-shake 0.5s ease-in-out;
}

@keyframes icon-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes icon-heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes icon-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* 图标尺寸类 */
.icon-xs { font-size: 12rpx; }
.icon-sm { font-size: 16rpx; }
.icon-md { font-size: 20rpx; }
.icon-lg { font-size: 24rpx; }
.icon-xl { font-size: 32rpx; }
.icon-2xl { font-size: 40rpx; }
.icon-3xl { font-size: 48rpx; }

/* 图标颜色类 */
.icon-primary { color: #667eea; }
.icon-success { color: #10B981; }
.icon-warning { color: #F59E0B; }
.icon-danger { color: #EF4444; }
.icon-info { color: #3B82F6; }
.icon-secondary { color: #8E8E93; }
.icon-white { color: #FFFFFF; }
.icon-black { color: #000000; }
