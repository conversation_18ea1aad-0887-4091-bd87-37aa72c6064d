/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 导入自定义样式变量 */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
.audio-translation-page.data-v-5c0b728c {
  min-height: 100vh;
  background-color: #F9FAFB;
}
.page-header.data-v-5c0b728c {
  background-color: #FFFFFF;
  padding: 48rpx 32rpx;
  text-align: center;
  border-bottom: 1px solid #E5E7EB;
}
.page-header .page-title.data-v-5c0b728c {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #111827;
  margin-bottom: 16rpx;
}
.page-header .page-desc.data-v-5c0b728c {
  font-size: 28rpx;
  color: #374151;
}
.language-selector.data-v-5c0b728c {
  background-color: #FFFFFF;
  padding: 32rpx;
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.language-selector .language-item.data-v-5c0b728c {
  flex: 1;
  text-align: center;
}
.language-selector .language-item .language-label.data-v-5c0b728c {
  display: block;
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 16rpx;
}
.language-selector .swap-button.data-v-5c0b728c {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F3F4F6;
  border-radius: 9999rpx;
  margin: 0 32rpx;
}
.language-selector .swap-button.data-v-5c0b728c:active {
  background-color: #E5E7EB;
}
.section-title.data-v-5c0b728c {
  padding: 32rpx 32rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.section-title .title-text.data-v-5c0b728c {
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
}
.section-title .title-desc.data-v-5c0b728c {
  font-size: 24rpx;
  color: #6B7280;
}
.section-title .result-actions.data-v-5c0b728c {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.recording-section.data-v-5c0b728c {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.recording-section .recording-container.data-v-5c0b728c {
  padding: 48rpx 32rpx;
}
.recording-section .recording-container .recording-visualizer.data-v-5c0b728c {
  display: flex;
  align-items: end;
  justify-content: center;
  gap: 4rpx;
  height: 120rpx;
  margin-bottom: 48rpx;
}
.recording-section .recording-container .recording-visualizer .wave-bar.data-v-5c0b728c {
  width: 8rpx;
  background: linear-gradient(to top, #667eea, #764ba2);
  border-radius: 4rpx;
  transition: height 0.1s ease;
}
.recording-section .recording-container .recording-controls.data-v-5c0b728c {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}
.recording-section .recording-container .recording-controls .record-button.data-v-5c0b728c {
  width: 160rpx;
  height: 160rpx;
  border-radius: 9999rpx;
  background-color: #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.recording-section .recording-container .recording-controls .record-button.recording.data-v-5c0b728c {
  background-color: #EF4444;
  animation: pulse-5c0b728c 2s infinite;
}
.recording-section .recording-container .recording-controls .record-button.disabled.data-v-5c0b728c {
  background-color: #9CA3AF;
  pointer-events: none;
}
.recording-section .recording-container .recording-controls .record-button.data-v-5c0b728c:active {
  transform: scale(0.95);
}
.recording-section .recording-container .recording-controls .recording-info.data-v-5c0b728c,
.recording-section .recording-container .recording-controls .processing-info.data-v-5c0b728c {
  text-align: center;
}
.recording-section .recording-container .recording-controls .recording-info .recording-time.data-v-5c0b728c,
.recording-section .recording-container .recording-controls .processing-info .recording-time.data-v-5c0b728c {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #111827;
  margin-bottom: 8rpx;
}
.recording-section .recording-container .recording-controls .recording-info .recording-status.data-v-5c0b728c,
.recording-section .recording-container .recording-controls .recording-info .processing-text.data-v-5c0b728c,
.recording-section .recording-container .recording-controls .processing-info .recording-status.data-v-5c0b728c,
.recording-section .recording-container .recording-controls .processing-info .processing-text.data-v-5c0b728c {
  font-size: 28rpx;
  color: #374151;
}
@keyframes pulse-5c0b728c {
0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
}
70% {
    box-shadow: 0 0 0 20rpx rgba(239, 68, 68, 0);
}
100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
}
}
.upload-section.data-v-5c0b728c {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.upload-section .upload-area.data-v-5c0b728c {
  margin: 0 32rpx 32rpx;
  border: 2px dashed #E5E7EB;
  border-radius: 16rpx;
  padding: 48rpx;
}
.upload-section .upload-area .upload-content.data-v-5c0b728c {
  text-align: center;
}
.upload-section .upload-area .upload-content .upload-text.data-v-5c0b728c {
  display: block;
  font-size: 36rpx;
  font-weight: 500;
  color: #111827;
  margin: 32rpx 0 16rpx;
}
.upload-section .upload-area .upload-content .upload-desc.data-v-5c0b728c {
  font-size: 28rpx;
  color: #374151;
}
.upload-section .upload-area .file-info.data-v-5c0b728c {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.upload-section .upload-area .file-info .file-icon.data-v-5c0b728c {
  width: 80rpx;
  height: 80rpx;
  background-color: #EFF6FF;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-section .upload-area .file-info .file-details.data-v-5c0b728c {
  flex: 1;
}
.upload-section .upload-area .file-info .file-details .file-name.data-v-5c0b728c {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.upload-section .upload-area .file-info .file-details .file-size.data-v-5c0b728c,
.upload-section .upload-area .file-info .file-details .file-duration.data-v-5c0b728c {
  display: block;
  font-size: 24rpx;
  color: #374151;
}
.upload-section .upload-area .file-info .file-actions.data-v-5c0b728c {
  display: flex;
  gap: 16rpx;
}
.upload-section .upload-options.data-v-5c0b728c {
  padding: 0 32rpx 32rpx;
}
.upload-section .upload-options .option-item.data-v-5c0b728c {
  margin-bottom: 32rpx;
}
.upload-section .upload-options .option-item .option-label.data-v-5c0b728c {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 24rpx;
}
.upload-section .translate-button.data-v-5c0b728c {
  padding: 0 32rpx 32rpx;
}
.translation-result.data-v-5c0b728c {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.translation-result .result-content.data-v-5c0b728c {
  padding: 0 32rpx 32rpx;
}
.translation-result .result-content .text-result .original-text.data-v-5c0b728c,
.translation-result .result-content .text-result .translated-text.data-v-5c0b728c {
  margin-bottom: 32rpx;
}
.translation-result .result-content .text-result .original-text .result-label.data-v-5c0b728c,
.translation-result .result-content .text-result .translated-text .result-label.data-v-5c0b728c {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}
.translation-result .result-content .text-result .original-text .result-text.data-v-5c0b728c,
.translation-result .result-content .text-result .translated-text .result-text.data-v-5c0b728c {
  display: block;
  font-size: 28rpx;
  line-height: 1.625;
  color: #111827;
  background-color: #F3F4F6;
  padding: 24rpx;
  border-radius: 12rpx;
  word-break: break-word;
}
.translation-result .result-content .segment-result .segment-item.data-v-5c0b728c {
  background-color: #F3F4F6;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}
.translation-result .result-content .segment-result .segment-item .segment-time.data-v-5c0b728c {
  font-size: 20rpx;
  color: #6B7280;
  margin-bottom: 16rpx;
}
.translation-result .result-content .segment-result .segment-item .segment-original.data-v-5c0b728c {
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 16rpx;
}
.translation-result .result-content .segment-result .segment-item .segment-translated.data-v-5c0b728c {
  font-size: 28rpx;
  color: #111827;
  font-weight: 500;
}
.translation-result .result-content .audio-result .audio-player.data-v-5c0b728c {
  display: flex;
  align-items: center;
  gap: 32rpx;
  background-color: #F3F4F6;
  border-radius: 16rpx;
  padding: 32rpx;
}
.translation-result .result-content .audio-result .audio-player .audio-info.data-v-5c0b728c {
  flex: 1;
}
.translation-result .result-content .audio-result .audio-player .audio-info .audio-title.data-v-5c0b728c {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
}
.translation-result .result-content .audio-result .audio-player .audio-info .audio-duration.data-v-5c0b728c {
  font-size: 24rpx;
  color: #374151;
}
.translation-history.data-v-5c0b728c {
  background-color: #FFFFFF;
}
.translation-history .history-list.data-v-5c0b728c {
  padding: 0 32rpx 32rpx;
}
.translation-history .history-list .history-item.data-v-5c0b728c {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  background-color: #F3F4F6;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}
.translation-history .history-list .history-item.data-v-5c0b728c:last-child {
  margin-bottom: 0;
}
.translation-history .history-list .history-item.data-v-5c0b728c:active {
  background-color: #E5E7EB;
}
.translation-history .history-list .history-item .history-icon.data-v-5c0b728c {
  width: 64rpx;
  height: 64rpx;
  background-color: #EFF6FF;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.translation-history .history-list .history-item .history-content.data-v-5c0b728c {
  flex: 1;
}
.translation-history .history-list .history-item .history-content .history-title.data-v-5c0b728c {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.translation-history .history-list .history-item .history-content .history-desc.data-v-5c0b728c {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.translation-history .history-list .history-item .history-content .history-time.data-v-5c0b728c {
  font-size: 20rpx;
  color: #6B7280;
}
.translation-history .history-list .history-item .history-actions.data-v-5c0b728c {
  padding: 16rpx;
}
@media screen and (max-width: 480px) {
.recording-container.data-v-5c0b728c {
    padding: 32rpx;
}
.recording-container .record-button.data-v-5c0b728c {
    width: 120rpx;
    height: 120rpx;
}
.recording-container .recording-visualizer.data-v-5c0b728c {
    height: 80rpx;
}
.language-selector.data-v-5c0b728c {
    flex-direction: column;
    gap: 24rpx;
}
.language-selector .swap-button.data-v-5c0b728c {
    transform: rotate(90deg);
}
}
@media (prefers-color-scheme: dark) {
.audio-translation-page.data-v-5c0b728c {
    background-color: #1a1a1a;
}
.page-header.data-v-5c0b728c,
.language-selector.data-v-5c0b728c,
.recording-section.data-v-5c0b728c,
.upload-section.data-v-5c0b728c,
.translation-result.data-v-5c0b728c,
.translation-history.data-v-5c0b728c {
    background-color: #2d2d2d;
    border-color: #404040;
}
.upload-area.data-v-5c0b728c,
.result-text.data-v-5c0b728c,
.segment-item.data-v-5c0b728c,
.audio-player.data-v-5c0b728c,
.history-item.data-v-5c0b728c {
    background-color: #404040;
    border-color: #555555;
}
.swap-button.data-v-5c0b728c,
.file-icon.data-v-5c0b728c,
.history-icon.data-v-5c0b728c {
    background-color: #404040;
}
.wave-bar.data-v-5c0b728c {
    background: linear-gradient(to top, #3b82f6, #60a5fa);
}
}