{"version": 3, "file": "index.js", "sources": ["pages/translation/video/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdHJhbnNsYXRpb24vdmlkZW8vaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"video-translation-page\">\n    <!-- 页面标题 -->\n    <view class=\"page-header\">\n      <text class=\"page-title\">视频翻译</text>\n      <text class=\"page-desc\">支持视频字幕提取、翻译和生成</text>\n    </view>\n\n    <!-- 语言选择 -->\n    <view class=\"language-selector\">\n      <view class=\"language-item\">\n        <text class=\"language-label\">源语言</text>\n        <u-button \n          type=\"text\" \n          :text=\"sourceLanguage?.name || '自动检测'\" \n          @click=\"showSourceLanguagePicker = true\"\n          :custom-style=\"{ color: '#2563EB' }\"\n        >\n          <u-icon name=\"arrow-down\" size=\"12\" color=\"#2563EB\" margin-left=\"4\"></u-icon>\n        </u-button>\n      </view>\n      \n      <view class=\"swap-button\" @click=\"swapLanguages\">\n        <u-icon name=\"arrow-left-right\" size=\"20\" color=\"#6B7280\"></u-icon>\n      </view>\n      \n      <view class=\"language-item\">\n        <text class=\"language-label\">目标语言</text>\n        <u-button \n          type=\"text\" \n          :text=\"targetLanguage?.name || '中文'\" \n          @click=\"showTargetLanguagePicker = true\"\n          :custom-style=\"{ color: '#2563EB' }\"\n        >\n          <u-icon name=\"arrow-down\" size=\"12\" color=\"#2563EB\" margin-left=\"4\"></u-icon>\n        </u-button>\n      </view>\n    </view>\n\n    <!-- 视频上传区域 -->\n    <view class=\"upload-section\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">上传视频文件</text>\n        <text class=\"title-desc\">支持MP4、AVI、MOV等格式，最大500MB</text>\n      </view>\n      \n      <view class=\"upload-area\" @click=\"chooseVideoFile\">\n        <view class=\"upload-content\" v-if=\"!selectedVideoFile\">\n          <u-icon name=\"video\" size=\"48\" color=\"#2563EB\"></u-icon>\n          <text class=\"upload-text\">选择视频文件</text>\n          <text class=\"upload-desc\">支持 MP4、AVI、MOV、MKV 格式</text>\n          <text class=\"upload-limit\">文件大小限制：500MB</text>\n        </view>\n        \n        <view class=\"file-info\" v-else>\n          <view class=\"video-preview\">\n            <video \n              class=\"preview-video\"\n              :src=\"selectedVideoFile.path\"\n              :poster=\"selectedVideoFile.thumbnail\"\n              controls\n              @loadedmetadata=\"onVideoLoaded\"\n            ></video>\n          </view>\n          <view class=\"file-details\">\n            <text class=\"file-name\">{{ selectedVideoFile.name }}</text>\n            <text class=\"file-size\">{{ formatFileSize(selectedVideoFile.size) }}</text>\n            <text class=\"file-duration\" v-if=\"selectedVideoFile.duration\">\n              时长: {{ formatDuration(selectedVideoFile.duration) }}\n            </text>\n            <text class=\"file-resolution\" v-if=\"selectedVideoFile.resolution\">\n              分辨率: {{ selectedVideoFile.resolution }}\n            </text>\n          </view>\n          <view class=\"file-actions\">\n            <u-icon name=\"close-circle\" size=\"24\" color=\"#EF4444\" @click.stop=\"removeVideoFile\"></u-icon>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 翻译选项 -->\n    <view class=\"translation-options\" v-if=\"selectedVideoFile\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">翻译选项</text>\n      </view>\n      \n      <view class=\"options-list\">\n        <!-- 字幕提取方式 -->\n        <view class=\"option-item\">\n          <text class=\"option-label\">字幕提取方式</text>\n          <u-radio-group v-model=\"extractionMode\">\n            <u-radio name=\"auto\" label=\"自动识别\"></u-radio>\n            <u-radio name=\"existing\" label=\"现有字幕\"></u-radio>\n          </u-radio-group>\n        </view>\n        \n        <!-- 输出格式 -->\n        <view class=\"option-item\">\n          <text class=\"option-label\">输出格式</text>\n          <u-radio-group v-model=\"outputFormat\">\n            <u-radio name=\"srt\" label=\"SRT字幕\"></u-radio>\n            <u-radio name=\"vtt\" label=\"VTT字幕\"></u-radio>\n            <u-radio name=\"ass\" label=\"ASS字幕\"></u-radio>\n            <u-radio name=\"video\" label=\"嵌入视频\"></u-radio>\n          </u-radio-group>\n        </view>\n        \n        <!-- 字幕样式 -->\n        <view class=\"option-item\" v-if=\"outputFormat === 'video'\">\n          <text class=\"option-label\">字幕样式</text>\n          <view class=\"subtitle-style-options\">\n            <view class=\"style-preview\">\n              <text class=\"preview-text\" :style=\"subtitleStyle\">示例字幕文本</text>\n            </view>\n            <view class=\"style-controls\">\n              <view class=\"control-item\">\n                <text class=\"control-label\">字体大小</text>\n                <u-slider v-model=\"subtitleConfig.fontSize\" :min=\"12\" :max=\"32\" @change=\"updateSubtitleStyle\"></u-slider>\n              </view>\n              <view class=\"control-item\">\n                <text class=\"control-label\">字体颜色</text>\n                <view class=\"color-picker\">\n                  <view \n                    class=\"color-option\"\n                    v-for=\"color in colorOptions\"\n                    :key=\"color\"\n                    :style=\"{ backgroundColor: color }\"\n                    :class=\"{ active: subtitleConfig.color === color }\"\n                    @click=\"selectColor(color)\"\n                  ></view>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"translate-button\">\n        <u-button \n          type=\"primary\" \n          size=\"large\"\n          :loading=\"isTranslating\"\n          :disabled=\"!canTranslate\"\n          @click=\"startTranslation\"\n          :custom-style=\"{ width: '100%' }\"\n        >\n          {{ isTranslating ? '处理中...' : '开始翻译' }}\n        </u-button>\n      </view>\n    </view>\n\n    <!-- 翻译进度 -->\n    <view class=\"translation-progress\" v-if=\"isTranslating\">\n      <view class=\"progress-header\">\n        <text class=\"progress-title\">正在处理视频</text>\n        <text class=\"progress-desc\">{{ progressStatus }}</text>\n      </view>\n      \n      <view class=\"progress-bar\">\n        <u-line-progress \n          :percent=\"translationProgress\" \n          :show-percent=\"true\"\n          active-color=\"#2563EB\"\n        ></u-line-progress>\n      </view>\n      \n      <view class=\"progress-steps\">\n        <view \n          class=\"step-item\"\n          v-for=\"(step, index) in progressSteps\"\n          :key=\"index\"\n          :class=\"{ \n            active: currentStep === index,\n            completed: currentStep > index \n          }\"\n        >\n          <view class=\"step-icon\">\n            <u-icon \n              :name=\"currentStep > index ? 'checkmark' : step.icon\" \n              size=\"16\" \n              :color=\"currentStep >= index ? '#2563EB' : '#9CA3AF'\"\n            ></u-icon>\n          </view>\n          <text class=\"step-text\">{{ step.text }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 翻译结果 -->\n    <view class=\"translation-result\" v-if=\"translationResult\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">翻译结果</text>\n        <view class=\"result-actions\">\n          <u-icon name=\"download\" size=\"18\" color=\"#2563EB\" @click=\"downloadResult\"></u-icon>\n          <u-icon name=\"share\" size=\"18\" color=\"#2563EB\" margin-left=\"12\" @click=\"shareResult\"></u-icon>\n        </view>\n      </view>\n      \n      <view class=\"result-content\">\n        <!-- 视频预览 -->\n        <view class=\"video-result\" v-if=\"translationResult.type === 'video'\">\n          <video \n            class=\"result-video\"\n            :src=\"translationResult.videoUrl\"\n            controls\n            :poster=\"translationResult.thumbnail\"\n          ></video>\n          <view class=\"video-info\">\n            <text class=\"video-title\">{{ translationResult.filename }}</text>\n            <text class=\"video-size\">{{ formatFileSize(translationResult.size) }}</text>\n          </view>\n        </view>\n        \n        <!-- 字幕文件 -->\n        <view class=\"subtitle-result\" v-if=\"translationResult.type === 'subtitle'\">\n          <view class=\"subtitle-preview\">\n            <view \n              class=\"subtitle-item\"\n              v-for=\"(subtitle, index) in translationResult.subtitles.slice(0, 5)\"\n              :key=\"index\"\n            >\n              <view class=\"subtitle-time\">\n                {{ formatTime(subtitle.startTime) }} --> {{ formatTime(subtitle.endTime) }}\n              </view>\n              <view class=\"subtitle-original\">{{ subtitle.originalText }}</view>\n              <view class=\"subtitle-translated\">{{ subtitle.translatedText }}</view>\n            </view>\n            \n            <view class=\"subtitle-more\" v-if=\"translationResult.subtitles.length > 5\">\n              <text class=\"more-text\">还有 {{ translationResult.subtitles.length - 5 }} 条字幕...</text>\n            </view>\n          </view>\n          \n          <view class=\"subtitle-download\">\n            <u-button \n              type=\"primary\" \n              size=\"small\"\n              @click=\"downloadSubtitle\"\n            >\n              下载字幕文件\n            </u-button>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 翻译历史 -->\n    <view class=\"translation-history\" v-if=\"videoHistory.length > 0\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">翻译历史</text>\n        <u-button \n          type=\"text\" \n          text=\"查看全部\" \n          size=\"small\"\n          @click=\"viewAllHistory\"\n          :custom-style=\"{ color: '#2563EB', fontSize: '24rpx' }\"\n        ></u-button>\n      </view>\n      \n      <view class=\"history-list\">\n        <view \n          class=\"history-item\"\n          v-for=\"item in videoHistory.slice(0, 3)\"\n          :key=\"item.id\"\n          @click=\"viewHistoryItem(item)\"\n        >\n          <view class=\"history-thumbnail\">\n            <image class=\"thumbnail-image\" :src=\"item.thumbnail\" mode=\"aspectFill\"></image>\n            <view class=\"play-overlay\">\n              <u-icon name=\"play\" size=\"16\" color=\"#fff\"></u-icon>\n            </view>\n          </view>\n          <view class=\"history-content\">\n            <text class=\"history-title\">{{ item.filename }}</text>\n            <text class=\"history-desc\">{{ item.sourceLang }} → {{ item.targetLang }}</text>\n            <text class=\"history-time\">{{ formatTime(item.createdAt) }}</text>\n          </view>\n          <view class=\"history-actions\">\n            <u-icon name=\"download\" size=\"16\" color=\"#6B7280\"></u-icon>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 语言选择器 -->\n    <u-picker\n      v-model=\"showSourceLanguagePicker\"\n      :columns=\"sourceLanguageColumns\"\n      @confirm=\"onSourceLanguageConfirm\"\n      @cancel=\"showSourceLanguagePicker = false\"\n    ></u-picker>\n\n    <u-picker\n      v-model=\"showTargetLanguagePicker\"\n      :columns=\"targetLanguageColumns\"\n      @confirm=\"onTargetLanguageConfirm\"\n      @cancel=\"showTargetLanguagePicker = false\"\n    ></u-picker>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\nimport { useTranslationStore } from '@/store/modules/translation'\nimport dayjs from 'dayjs'\n\n// Store\nconst translationStore = useTranslationStore()\n\n// 响应式数据\nconst selectedVideoFile = ref<any>(null)\nconst isTranslating = ref<boolean>(false)\nconst translationProgress = ref<number>(0)\nconst progressStatus = ref<string>('')\nconst currentStep = ref<number>(0)\nconst translationResult = ref<any>(null)\nconst videoHistory = ref<any[]>([])\nconst showSourceLanguagePicker = ref<boolean>(false)\nconst showTargetLanguagePicker = ref<boolean>(false)\nconst extractionMode = ref<string>('auto')\nconst outputFormat = ref<string>('srt')\nconst subtitleConfig = ref({\n  fontSize: 18,\n  color: '#FFFFFF',\n  backgroundColor: 'rgba(0,0,0,0.7)',\n  position: 'bottom'\n})\n\n// 进度步骤\nconst progressSteps = ref([\n  { icon: 'video', text: '视频解析' },\n  { icon: 'mic', text: '语音识别' },\n  { icon: 'translate', text: '文本翻译' },\n  { icon: 'subtitles', text: '字幕生成' },\n  { icon: 'checkmark', text: '完成处理' }\n])\n\n// 颜色选项\nconst colorOptions = ref([\n  '#FFFFFF', '#000000', '#FF0000', '#00FF00',\n  '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'\n])\n\n// 计算属性\nconst {\n  languages,\n  currentSourceLang,\n  currentTargetLang,\n  sourceLanguage,\n  targetLanguage\n} = translationStore\n\nconst canTranslate = computed(() => {\n  return selectedVideoFile.value && currentSourceLang.value && currentTargetLang.value\n})\n\nconst sourceLanguageColumns = computed(() => {\n  const autoDetect = [{ text: '自动检测', value: 'auto' }]\n  const languageOptions = languages.map(lang => ({\n    text: lang.name,\n    value: lang.code\n  }))\n  return [autoDetect.concat(languageOptions)]\n})\n\nconst targetLanguageColumns = computed(() => {\n  const languageOptions = languages.filter(lang => lang.code !== 'auto').map(lang => ({\n    text: lang.name,\n    value: lang.code\n  }))\n  return [languageOptions]\n})\n\nconst subtitleStyle = computed(() => {\n  return {\n    fontSize: subtitleConfig.value.fontSize + 'px',\n    color: subtitleConfig.value.color,\n    backgroundColor: subtitleConfig.value.backgroundColor,\n    padding: '8px 12px',\n    borderRadius: '4px',\n    display: 'inline-block'\n  }\n})\n\n// 页面加载\nonMounted(async () => {\n  await translationStore.loadLanguages()\n  loadVideoHistory()\n})\n\n// 选择视频文件\nconst chooseVideoFile = () => {\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  uni.chooseVideo({\n    sourceType: ['album', 'camera'],\n    maxDuration: 300, // 5分钟\n    success: (res) => {\n      handleSelectedFile({\n        name: res.tempFilePath.split('/').pop(),\n        size: res.size,\n        path: res.tempFilePath,\n        duration: res.duration,\n        width: res.width,\n        height: res.height\n      })\n    },\n    fail: (error) => {\n      uni.__f__('error','at pages/translation/video/index.vue:422','选择视频失败:', error)\n    }\n  })\n\n}\n\n// 处理选中的文件\nconst handleSelectedFile = (file: any) => {\n  // 检查文件大小\n  if (file.size > 500 * 1024 * 1024) {\n    uni.showToast({\n      title: '文件大小不能超过500MB',\n      icon: 'error'\n    })\n    return\n  }\n\n  selectedVideoFile.value = {\n    name: file.name,\n    size: file.size,\n    path: file.path || URL.createObjectURL(file),\n    duration: file.duration,\n    resolution: file.width && file.height ? `${file.width}x${file.height}` : null,\n    thumbnail: null\n  }\n\n  // 生成缩略图\n  generateThumbnail(selectedVideoFile.value.path)\n}\n\n// 生成视频缩略图\nconst generateThumbnail = (videoPath: string) => {\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n}\n\n// 视频加载完成\nconst onVideoLoaded = (e: any) => {\n  if (selectedVideoFile.value && !selectedVideoFile.value.duration) {\n    selectedVideoFile.value.duration = e.target.duration\n  }\n}\n\n// 移除视频文件\nconst removeVideoFile = () => {\n  selectedVideoFile.value = null\n  translationResult.value = null\n}\n\n// 开始翻译\nconst startTranslation = async () => {\n  if (!canTranslate.value) return\n\n  try {\n    isTranslating.value = true\n    translationProgress.value = 0\n    currentStep.value = 0\n\n    // 模拟翻译进度\n    const steps = [\n      { status: '正在解析视频文件...', duration: 2000 },\n      { status: '正在识别语音内容...', duration: 3000 },\n      { status: '正在翻译文本...', duration: 2000 },\n      { status: '正在生成字幕...', duration: 2000 },\n      { status: '正在合成视频...', duration: 3000 }\n    ]\n\n    for (let i = 0; i < steps.length; i++) {\n      currentStep.value = i\n      progressStatus.value = steps[i].status\n\n      // 模拟进度更新\n      const stepProgress = 100 / steps.length\n      const startProgress = i * stepProgress\n\n      await new Promise(resolve => {\n        const interval = setInterval(() => {\n          translationProgress.value += 2\n          if (translationProgress.value >= (i + 1) * stepProgress) {\n            clearInterval(interval)\n            resolve(true)\n          }\n        }, steps[i].duration / 50)\n      })\n    }\n\n    // 生成模拟结果\n    const mockResult = outputFormat.value === 'video' ? {\n      type: 'video',\n      filename: `translated_${selectedVideoFile.value.name}`,\n      videoUrl: selectedVideoFile.value.path,\n      thumbnail: selectedVideoFile.value.thumbnail,\n      size: selectedVideoFile.value.size * 1.2,\n      duration: selectedVideoFile.value.duration\n    } : {\n      type: 'subtitle',\n      filename: `${selectedVideoFile.value.name.split('.')[0]}.${outputFormat.value}`,\n      subtitles: [\n        {\n          startTime: 0,\n          endTime: 3,\n          originalText: '这是第一句原文',\n          translatedText: 'This is the first original sentence'\n        },\n        {\n          startTime: 3,\n          endTime: 6,\n          originalText: '这是第二句原文',\n          translatedText: 'This is the second original sentence'\n        },\n        {\n          startTime: 6,\n          endTime: 9,\n          originalText: '这是第三句原文',\n          translatedText: 'This is the third original sentence'\n        }\n      ]\n    }\n\n    translationResult.value = mockResult\n\n    // 添加到历史记录\n    const historyItem = {\n      id: Date.now().toString(),\n      filename: selectedVideoFile.value.name,\n      thumbnail: selectedVideoFile.value.thumbnail,\n      sourceLang: currentSourceLang.value,\n      targetLang: currentTargetLang.value,\n      outputFormat: outputFormat.value,\n      size: selectedVideoFile.value.size,\n      duration: selectedVideoFile.value.duration,\n      createdAt: new Date().toISOString()\n    }\n\n    videoHistory.value.unshift(historyItem)\n    saveVideoHistory()\n\n    uni.showToast({\n      title: '翻译完成',\n      icon: 'success'\n    })\n\n  } catch (error) {\n    uni.__f__('error','at pages/translation/video/index.vue:579','翻译失败:', error)\n    uni.showToast({\n      title: '翻译失败',\n      icon: 'error'\n    })\n  } finally {\n    isTranslating.value = false\n    currentStep.value = progressSteps.value.length\n  }\n}\n\n// 交换语言\nconst swapLanguages = () => {\n  translationStore.swapLanguages()\n}\n\n// 语言选择确认\nconst onSourceLanguageConfirm = (value: any) => {\n  translationStore.setSourceLanguage(value[0].value)\n  showSourceLanguagePicker.value = false\n}\n\nconst onTargetLanguageConfirm = (value: any) => {\n  translationStore.setTargetLanguage(value[0].value)\n  showTargetLanguagePicker.value = false\n}\n\n// 更新字幕样式\nconst updateSubtitleStyle = () => {\n  // 字幕样式更新逻辑\n}\n\n// 选择颜色\nconst selectColor = (color: string) => {\n  subtitleConfig.value.color = color\n  updateSubtitleStyle()\n}\n\n// 下载结果\nconst downloadResult = () => {\n  if (!translationResult.value) return\n\n  uni.showToast({\n    title: '开始下载',\n    icon: 'success'\n  })\n}\n\n// 分享结果\nconst shareResult = () => {\n  if (!translationResult.value) return\n\n  uni.share({\n    provider: 'weixin',\n    scene: 'WXSceneSession',\n    type: 2,\n    imageUrl: translationResult.value.thumbnail,\n    title: '视频翻译完成',\n    summary: `已完成视频翻译: ${translationResult.value.filename}`,\n    success: () => {\n      uni.showToast({\n        title: '分享成功',\n        icon: 'success'\n      })\n    }\n  })\n}\n\n// 下载字幕\nconst downloadSubtitle = () => {\n  if (!translationResult.value?.subtitles) return\n\n  // 生成字幕文件内容\n  let subtitleContent = ''\n\n  if (outputFormat.value === 'srt') {\n    subtitleContent = translationResult.value.subtitles.map((sub: any, index: number) => {\n      return `${index + 1}\\n${formatSRTTime(sub.startTime)} --> ${formatSRTTime(sub.endTime)}\\n${sub.translatedText}\\n`\n    }).join('\\n')\n  }\n\n  // 下载文件\n  uni.showToast({\n    title: '字幕文件已生成',\n    icon: 'success'\n  })\n}\n\n// 工具函数\nconst formatTime = (seconds: number) => {\n  if (typeof seconds === 'string') {\n    return dayjs(seconds).format('MM-DD HH:mm')\n  }\n\n  const mins = Math.floor(seconds / 60)\n  const secs = Math.floor(seconds % 60)\n  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n}\n\nconst formatDuration = (seconds: number) => {\n  if (!seconds) return '00:00'\n  return formatTime(seconds)\n}\n\nconst formatSRTTime = (seconds: number) => {\n  const hours = Math.floor(seconds / 3600)\n  const mins = Math.floor((seconds % 3600) / 60)\n  const secs = Math.floor(seconds % 60)\n  const ms = Math.floor((seconds % 1) * 1000)\n\n  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`\n}\n\nconst formatFileSize = (bytes: number) => {\n  if (bytes === 0) return '0 B'\n  const k = 1024\n  const sizes = ['B', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// 历史记录相关\nconst saveVideoHistory = () => {\n  uni.setStorageSync('video_translation_history', videoHistory.value)\n}\n\nconst loadVideoHistory = () => {\n  const history = uni.getStorageSync('video_translation_history') || []\n  videoHistory.value = history\n}\n\nconst viewAllHistory = () => {\n  uni.navigateTo({\n    url: '/pages/translation/video/history/index'\n  })\n}\n\nconst viewHistoryItem = (item: any) => {\n  uni.navigateTo({\n    url: `/pages/translation/video/detail/index?id=${item.id}`\n  })\n}\n\n// 页面下拉刷新\nconst onPullDownRefresh = () => {\n  loadVideoHistory()\n  uni.stopPullDownRefresh()\n}\n\n// 导出方法供页面使用\ndefineExpose({\n  onPullDownRefresh\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.video-translation-page {\n  min-height: 100vh;\n  background-color: $bg-secondary;\n}\n\n// 页面头部\n.page-header {\n  background-color: $bg-primary;\n  padding: $spacing-6 $spacing-4;\n  text-align: center;\n  border-bottom: 1px solid $border-primary;\n\n  .page-title {\n    display: block;\n    font-size: $font-size-2xl;\n    font-weight: $font-weight-bold;\n    color: $text-primary;\n    margin-bottom: $spacing-2;\n  }\n\n  .page-desc {\n    font-size: $font-size-base;\n    color: $text-secondary;\n  }\n}\n\n// 语言选择器\n.language-selector {\n  background-color: $bg-primary;\n  padding: $spacing-4;\n  border-bottom: 1px solid $border-primary;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  .language-item {\n    flex: 1;\n    text-align: center;\n\n    .language-label {\n      display: block;\n      font-size: $font-size-sm;\n      color: $text-tertiary;\n      margin-bottom: $spacing-2;\n    }\n  }\n\n  .swap-button {\n    width: 80rpx;\n    height: 80rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background-color: $bg-tertiary;\n    border-radius: $radius-full;\n    margin: 0 $spacing-4;\n\n    &:active {\n      background-color: $gray-200;\n    }\n  }\n}\n\n// 通用区块标题\n.section-title {\n  padding: $spacing-4 $spacing-4 $spacing-3;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  .title-text {\n    font-size: $font-size-lg;\n    font-weight: $font-weight-semibold;\n    color: $text-primary;\n  }\n\n  .title-desc {\n    font-size: $font-size-sm;\n    color: $text-tertiary;\n  }\n\n  .result-actions {\n    display: flex;\n    align-items: center;\n    gap: $spacing-2;\n  }\n}\n\n// 文件上传区域\n.upload-section {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .upload-area {\n    margin: 0 $spacing-4 $spacing-4;\n    border: 2px dashed $border-primary;\n    border-radius: $radius-lg;\n    padding: $spacing-6;\n\n    .upload-content {\n      text-align: center;\n\n      .upload-text {\n        display: block;\n        font-size: $font-size-lg;\n        font-weight: $font-weight-medium;\n        color: $text-primary;\n        margin: $spacing-4 0 $spacing-2;\n      }\n\n      .upload-desc {\n        display: block;\n        font-size: $font-size-base;\n        color: $text-secondary;\n        margin-bottom: $spacing-2;\n      }\n\n      .upload-limit {\n        font-size: $font-size-sm;\n        color: $text-tertiary;\n      }\n    }\n\n    .file-info {\n      display: flex;\n      flex-direction: column;\n      gap: $spacing-3;\n\n      .video-preview {\n        .preview-video {\n          width: 100%;\n          height: 400rpx;\n          border-radius: $radius-lg;\n          background-color: $bg-tertiary;\n        }\n      }\n\n      .file-details {\n        flex: 1;\n\n        .file-name {\n          display: block;\n          font-size: $font-size-base;\n          font-weight: $font-weight-medium;\n          color: $text-primary;\n          margin-bottom: $spacing-1;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n\n        .file-size,\n        .file-duration,\n        .file-resolution {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          margin-bottom: 4rpx;\n        }\n      }\n\n      .file-actions {\n        display: flex;\n        justify-content: flex-end;\n        gap: $spacing-2;\n      }\n    }\n  }\n}\n\n// 翻译选项\n.translation-options {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .options-list {\n    padding: 0 $spacing-4;\n\n    .option-item {\n      padding: $spacing-4 0;\n      border-bottom: 1px solid $border-primary;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      .option-label {\n        display: block;\n        font-size: $font-size-base;\n        font-weight: $font-weight-medium;\n        color: $text-primary;\n        margin-bottom: $spacing-3;\n      }\n\n      .subtitle-style-options {\n        .style-preview {\n          background-color: $bg-tertiary;\n          border-radius: $radius-md;\n          padding: $spacing-4;\n          margin-bottom: $spacing-3;\n          text-align: center;\n\n          .preview-text {\n            font-weight: $font-weight-medium;\n          }\n        }\n\n        .style-controls {\n          .control-item {\n            margin-bottom: $spacing-3;\n\n            .control-label {\n              display: block;\n              font-size: $font-size-sm;\n              color: $text-secondary;\n              margin-bottom: $spacing-2;\n            }\n\n            .color-picker {\n              display: flex;\n              gap: $spacing-2;\n\n              .color-option {\n                width: 60rpx;\n                height: 60rpx;\n                border-radius: $radius-md;\n                border: 2px solid transparent;\n\n                &.active {\n                  border-color: $primary;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .translate-button {\n    padding: 0 $spacing-4 $spacing-4;\n  }\n}\n\n// 翻译进度\n.translation-progress {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n  padding: $spacing-4;\n\n  .progress-header {\n    text-align: center;\n    margin-bottom: $spacing-4;\n\n    .progress-title {\n      display: block;\n      font-size: $font-size-lg;\n      font-weight: $font-weight-semibold;\n      color: $text-primary;\n      margin-bottom: $spacing-1;\n    }\n\n    .progress-desc {\n      font-size: $font-size-sm;\n      color: $text-secondary;\n    }\n  }\n\n  .progress-bar {\n    margin-bottom: $spacing-6;\n  }\n\n  .progress-steps {\n    display: flex;\n    justify-content: space-between;\n\n    .step-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      flex: 1;\n\n      .step-icon {\n        width: 64rpx;\n        height: 64rpx;\n        border-radius: $radius-full;\n        background-color: $bg-tertiary;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-bottom: $spacing-2;\n\n        &.active,\n        &.completed {\n          background-color: $primary-50;\n        }\n      }\n\n      .step-text {\n        font-size: $font-size-xs;\n        color: $text-tertiary;\n        text-align: center;\n\n        &.active {\n          color: $text-primary;\n          font-weight: $font-weight-medium;\n        }\n      }\n    }\n  }\n}\n\n// 翻译结果\n.translation-result {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .result-content {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .video-result {\n      .result-video {\n        width: 100%;\n        height: 400rpx;\n        border-radius: $radius-lg;\n        background-color: $bg-tertiary;\n        margin-bottom: $spacing-3;\n      }\n\n      .video-info {\n        text-align: center;\n\n        .video-title {\n          display: block;\n          font-size: $font-size-base;\n          font-weight: $font-weight-medium;\n          color: $text-primary;\n          margin-bottom: $spacing-1;\n        }\n\n        .video-size {\n          font-size: $font-size-sm;\n          color: $text-secondary;\n        }\n      }\n    }\n\n    .subtitle-result {\n      .subtitle-preview {\n        background-color: $bg-tertiary;\n        border-radius: $radius-lg;\n        padding: $spacing-4;\n        margin-bottom: $spacing-4;\n        max-height: 600rpx;\n        overflow-y: auto;\n\n        .subtitle-item {\n          margin-bottom: $spacing-4;\n\n          &:last-child {\n            margin-bottom: 0;\n          }\n\n          .subtitle-time {\n            font-size: $font-size-xs;\n            color: $text-tertiary;\n            margin-bottom: $spacing-1;\n          }\n\n          .subtitle-original {\n            font-size: $font-size-sm;\n            color: $text-secondary;\n            margin-bottom: $spacing-1;\n          }\n\n          .subtitle-translated {\n            font-size: $font-size-base;\n            color: $text-primary;\n            font-weight: $font-weight-medium;\n          }\n        }\n\n        .subtitle-more {\n          text-align: center;\n          padding: $spacing-3;\n\n          .more-text {\n            font-size: $font-size-sm;\n            color: $text-tertiary;\n          }\n        }\n      }\n\n      .subtitle-download {\n        text-align: center;\n      }\n    }\n  }\n}\n\n// 翻译历史\n.translation-history {\n  background-color: $bg-primary;\n\n  .history-list {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .history-item {\n      display: flex;\n      align-items: center;\n      gap: $spacing-3;\n      padding: $spacing-3;\n      background-color: $bg-tertiary;\n      border-radius: $radius-lg;\n      margin-bottom: $spacing-2;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      &:active {\n        background-color: $gray-200;\n      }\n\n      .history-thumbnail {\n        position: relative;\n        width: 120rpx;\n        height: 80rpx;\n        border-radius: $radius-md;\n        overflow: hidden;\n\n        .thumbnail-image {\n          width: 100%;\n          height: 100%;\n          background-color: $bg-quaternary;\n        }\n\n        .play-overlay {\n          position: absolute;\n          top: 50%;\n          left: 50%;\n          transform: translate(-50%, -50%);\n          width: 48rpx;\n          height: 48rpx;\n          background-color: rgba(0, 0, 0, 0.6);\n          border-radius: $radius-full;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n      }\n\n      .history-content {\n        flex: 1;\n\n        .history-title {\n          display: block;\n          font-size: $font-size-base;\n          font-weight: $font-weight-medium;\n          color: $text-primary;\n          margin-bottom: $spacing-1;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n\n        .history-desc {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          margin-bottom: $spacing-1;\n        }\n\n        .history-time {\n          font-size: $font-size-xs;\n          color: $text-tertiary;\n        }\n      }\n\n      .history-actions {\n        padding: $spacing-2;\n      }\n    }\n  }\n}\n\n// 响应式适配\n@media screen and (max-width: 480px) {\n  .language-selector {\n    flex-direction: column;\n    gap: $spacing-3;\n\n    .swap-button {\n      transform: rotate(90deg);\n    }\n  }\n\n  .progress-steps {\n    flex-wrap: wrap;\n    gap: $spacing-3;\n\n    .step-item {\n      flex: 0 0 calc(50% - #{$spacing-3});\n    }\n  }\n\n  .upload-area .file-info {\n    .video-preview .preview-video {\n      height: 300rpx;\n    }\n  }\n\n  .result-content .video-result .result-video {\n    height: 300rpx;\n  }\n}\n\n// 暗色模式适配\n@media (prefers-color-scheme: dark) {\n  .video-translation-page {\n    background-color: #1a1a1a;\n  }\n\n  .page-header,\n  .language-selector,\n  .upload-section,\n  .translation-options,\n  .translation-progress,\n  .translation-result,\n  .translation-history {\n    background-color: #2d2d2d;\n    border-color: #404040;\n  }\n\n  .upload-area,\n  .style-preview,\n  .subtitle-preview,\n  .history-item {\n    background-color: #404040;\n    border-color: #555555;\n  }\n\n  .swap-button,\n  .step-icon {\n    background-color: #404040;\n  }\n\n  .preview-video,\n  .result-video {\n    background-color: #555555;\n  }\n\n  .thumbnail-image {\n    background-color: #666666;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/AI_system/mobile-uniapp/src/pages/translation/video/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useTranslationStore", "ref", "computed", "onMounted", "uni", "dayjs"], "mappings": ";;;;;;;;;;;;;;;;AAoTA,UAAM,mBAAmBA,0BAAAA;AAGnB,UAAA,oBAAoBC,kBAAS,IAAI;AACjC,UAAA,gBAAgBA,kBAAa,KAAK;AAClC,UAAA,sBAAsBA,kBAAY,CAAC;AACnC,UAAA,iBAAiBA,kBAAY,EAAE;AAC/B,UAAA,cAAcA,kBAAY,CAAC;AAC3B,UAAA,oBAAoBA,kBAAS,IAAI;AACjC,UAAA,eAAeA,kBAAW,CAAA,CAAE;AAC5B,UAAA,2BAA2BA,kBAAa,KAAK;AAC7C,UAAA,2BAA2BA,kBAAa,KAAK;AAC7C,UAAA,iBAAiBA,kBAAY,MAAM;AACnC,UAAA,eAAeA,kBAAY,KAAK;AACtC,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB,UAAU;AAAA,MACV,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,UAAU;AAAA,IAAA,CACX;AAGD,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB,EAAE,MAAM,SAAS,MAAM,OAAO;AAAA,MAC9B,EAAE,MAAM,OAAO,MAAM,OAAO;AAAA,MAC5B,EAAE,MAAM,aAAa,MAAM,OAAO;AAAA,MAClC,EAAE,MAAM,aAAa,MAAM,OAAO;AAAA,MAClC,EAAE,MAAM,aAAa,MAAM,OAAO;AAAA,IAAA,CACnC;AAGD,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACvB;AAAA,MAAW;AAAA,MAAW;AAAA,MAAW;AAAA,MACjC;AAAA,MAAW;AAAA,MAAW;AAAA,MAAW;AAAA,IAAA,CAClC;AAGK,UAAA;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACE,IAAA;AAEE,UAAA,eAAeC,cAAAA,SAAS,MAAM;AAClC,aAAO,kBAAkB,SAAS,kBAAkB,SAAS,kBAAkB;AAAA,IAAA,CAChF;AAEK,UAAA,wBAAwBA,cAAAA,SAAS,MAAM;AAC3C,YAAM,aAAa,CAAC,EAAE,MAAM,QAAQ,OAAO,QAAQ;AAC7C,YAAA,kBAAkB,UAAU,IAAI,CAAS,UAAA;AAAA,QAC7C,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,MACZ,EAAA;AACF,aAAO,CAAC,WAAW,OAAO,eAAe,CAAC;AAAA,IAAA,CAC3C;AAEK,UAAA,wBAAwBA,cAAAA,SAAS,MAAM;AACrC,YAAA,kBAAkB,UAAU,OAAO,CAAA,SAAQ,KAAK,SAAS,MAAM,EAAE,IAAI,CAAS,UAAA;AAAA,QAClF,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,MACZ,EAAA;AACF,aAAO,CAAC,eAAe;AAAA,IAAA,CACxB;AAEK,UAAA,gBAAgBA,cAAAA,SAAS,MAAM;AAC5B,aAAA;AAAA,QACL,UAAU,eAAe,MAAM,WAAW;AAAA,QAC1C,OAAO,eAAe,MAAM;AAAA,QAC5B,iBAAiB,eAAe,MAAM;AAAA,QACtC,SAAS;AAAA,QACT,cAAc;AAAA,QACd,SAAS;AAAA,MAAA;AAAA,IACX,CACD;AAGDC,kBAAAA,UAAU,YAAY;AACpB,YAAM,iBAAiB;AACN;IAAA,CAClB;AAGD,UAAM,kBAAkB,MAAM;AAe5BC,oBAAAA,MAAI,YAAY;AAAA,QACd,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,aAAa;AAAA;AAAA,QACb,SAAS,CAAC,QAAQ;AACG,6BAAA;AAAA,YACjB,MAAM,IAAI,aAAa,MAAM,GAAG,EAAE,IAAI;AAAA,YACtC,MAAM,IAAI;AAAA,YACV,MAAM,IAAI;AAAA,YACV,UAAU,IAAI;AAAA,YACd,OAAO,IAAI;AAAA,YACX,QAAQ,IAAI;AAAA,UAAA,CACb;AAAA,QACH;AAAA,QACA,MAAM,CAAC,UAAU;AACfA,wBAAA,MAAI,MAAM,SAAQ,4CAA2C,WAAW,KAAK;AAAA,QAC/E;AAAA,MAAA,CACD;AAAA,IAAA;AAKG,UAAA,qBAAqB,CAAC,SAAc;AAExC,UAAI,KAAK,OAAO,MAAM,OAAO,MAAM;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MACF;AAEA,wBAAkB,QAAQ;AAAA,QACxB,MAAM,KAAK;AAAA,QACX,MAAM,KAAK;AAAA,QACX,MAAM,KAAK,QAAQ,IAAI,gBAAgB,IAAI;AAAA,QAC3C,UAAU,KAAK;AAAA,QACf,YAAY,KAAK,SAAS,KAAK,SAAS,GAAG,KAAK,KAAK,IAAI,KAAK,MAAM,KAAK;AAAA,QACzE,WAAW;AAAA,MAAA;AAIK,wBAAA,kBAAkB,MAAM,IAAI;AAAA,IAAA;AAI1C,UAAA,oBAAoB,CAAC,cAAsB;AAAA,IAAA;AAoB3C,UAAA,gBAAgB,CAAC,MAAW;AAChC,UAAI,kBAAkB,SAAS,CAAC,kBAAkB,MAAM,UAAU;AAC9C,0BAAA,MAAM,WAAW,EAAE,OAAO;AAAA,MAC9C;AAAA,IAAA;AAIF,UAAM,kBAAkB,MAAM;AAC5B,wBAAkB,QAAQ;AAC1B,wBAAkB,QAAQ;AAAA,IAAA;AAI5B,UAAM,mBAAmB,YAAY;AACnC,UAAI,CAAC,aAAa;AAAO;AAErB,UAAA;AACF,sBAAc,QAAQ;AACtB,4BAAoB,QAAQ;AAC5B,oBAAY,QAAQ;AAGpB,cAAM,QAAQ;AAAA,UACZ,EAAE,QAAQ,eAAe,UAAU,IAAK;AAAA,UACxC,EAAE,QAAQ,eAAe,UAAU,IAAK;AAAA,UACxC,EAAE,QAAQ,aAAa,UAAU,IAAK;AAAA,UACtC,EAAE,QAAQ,aAAa,UAAU,IAAK;AAAA,UACtC,EAAE,QAAQ,aAAa,UAAU,IAAK;AAAA,QAAA;AAGxC,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,sBAAY,QAAQ;AACL,yBAAA,QAAQ,MAAM,CAAC,EAAE;AAG1B,gBAAA,eAAe,MAAM,MAAM;AACjC,gBAAM,gBAAgB,IAAI;AAEpB,gBAAA,IAAI,QAAQ,CAAW,YAAA;AACrB,kBAAA,WAAW,YAAY,MAAM;AACjC,kCAAoB,SAAS;AAC7B,kBAAI,oBAAoB,UAAU,IAAI,KAAK,cAAc;AACvD,8BAAc,QAAQ;AACtB,wBAAQ,IAAI;AAAA,cACd;AAAA,YACC,GAAA,MAAM,CAAC,EAAE,WAAW,EAAE;AAAA,UAAA,CAC1B;AAAA,QACH;AAGM,cAAA,aAAa,aAAa,UAAU,UAAU;AAAA,UAClD,MAAM;AAAA,UACN,UAAU,cAAc,kBAAkB,MAAM,IAAI;AAAA,UACpD,UAAU,kBAAkB,MAAM;AAAA,UAClC,WAAW,kBAAkB,MAAM;AAAA,UACnC,MAAM,kBAAkB,MAAM,OAAO;AAAA,UACrC,UAAU,kBAAkB,MAAM;AAAA,QAAA,IAChC;AAAA,UACF,MAAM;AAAA,UACN,UAAU,GAAG,kBAAkB,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC,IAAI,aAAa,KAAK;AAAA,UAC7E,WAAW;AAAA,YACT;AAAA,cACE,WAAW;AAAA,cACX,SAAS;AAAA,cACT,cAAc;AAAA,cACd,gBAAgB;AAAA,YAClB;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,SAAS;AAAA,cACT,cAAc;AAAA,cACd,gBAAgB;AAAA,YAClB;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,SAAS;AAAA,cACT,cAAc;AAAA,cACd,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,QAAA;AAGF,0BAAkB,QAAQ;AAG1B,cAAM,cAAc;AAAA,UAClB,IAAI,KAAK,IAAI,EAAE,SAAS;AAAA,UACxB,UAAU,kBAAkB,MAAM;AAAA,UAClC,WAAW,kBAAkB,MAAM;AAAA,UACnC,YAAY,kBAAkB;AAAA,UAC9B,YAAY,kBAAkB;AAAA,UAC9B,cAAc,aAAa;AAAA,UAC3B,MAAM,kBAAkB,MAAM;AAAA,UAC9B,UAAU,kBAAkB,MAAM;AAAA,UAClC,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAAA;AAGvB,qBAAA,MAAM,QAAQ,WAAW;AACrB;AAEjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,eAEM,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,4CAA2C,SAAS,KAAK;AAC3EA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,sBAAc,QAAQ;AACV,oBAAA,QAAQ,cAAc,MAAM;AAAA,MAC1C;AAAA,IAAA;AAIF,UAAM,gBAAgB,MAAM;AAC1B,uBAAiB,cAAc;AAAA,IAAA;AAI3B,UAAA,0BAA0B,CAAC,UAAe;AAC9C,uBAAiB,kBAAkB,MAAM,CAAC,EAAE,KAAK;AACjD,+BAAyB,QAAQ;AAAA,IAAA;AAG7B,UAAA,0BAA0B,CAAC,UAAe;AAC9C,uBAAiB,kBAAkB,MAAM,CAAC,EAAE,KAAK;AACjD,+BAAyB,QAAQ;AAAA,IAAA;AAInC,UAAM,sBAAsB,MAAM;AAAA,IAAA;AAK5B,UAAA,cAAc,CAAC,UAAkB;AACrC,qBAAe,MAAM,QAAQ;AAAA,IACT;AAItB,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,kBAAkB;AAAO;AAE9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIH,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,kBAAkB;AAAO;AAE9BA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU,kBAAkB,MAAM;AAAA,QAClC,OAAO;AAAA,QACP,SAAS,YAAY,kBAAkB,MAAM,QAAQ;AAAA,QACrD,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,mBAAmB,MAAM;;AACzB,UAAA,GAAC,uBAAkB,UAAlB,mBAAyB;AAAW;AAKrC,UAAA,aAAa,UAAU,OAAO;AACd,0BAAkB,MAAM,UAAU,IAAI,CAAC,KAAU,UAAkB;AAC5E,iBAAA,GAAG,QAAQ,CAAC;AAAA,EAAK,cAAc,IAAI,SAAS,CAAC,QAAQ,cAAc,IAAI,OAAO,CAAC;AAAA,EAAK,IAAI,cAAc;AAAA;AAAA,QAAA,CAC9G,EAAE,KAAK,IAAI;AAAA,MACd;AAGAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIG,UAAA,aAAa,CAAC,YAAoB;AAClC,UAAA,OAAO,YAAY,UAAU;AAC/B,eAAOC,cAAM,MAAA,OAAO,EAAE,OAAO,aAAa;AAAA,MAC5C;AAEA,YAAM,OAAO,KAAK,MAAM,UAAU,EAAE;AACpC,YAAM,OAAO,KAAK,MAAM,UAAU,EAAE;AACpC,aAAO,GAAG,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IAAA;AAG1E,UAAA,iBAAiB,CAAC,YAAoB;AAC1C,UAAI,CAAC;AAAgB,eAAA;AACrB,aAAO,WAAW,OAAO;AAAA,IAAA;AAGrB,UAAA,gBAAgB,CAAC,YAAoB;AACzC,YAAM,QAAQ,KAAK,MAAM,UAAU,IAAI;AACvC,YAAM,OAAO,KAAK,MAAO,UAAU,OAAQ,EAAE;AAC7C,YAAM,OAAO,KAAK,MAAM,UAAU,EAAE;AACpC,YAAM,KAAK,KAAK,MAAO,UAAU,IAAK,GAAI;AAE1C,aAAO,GAAG,MAAM,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,SAAW,EAAA,SAAS,GAAG,GAAG,CAAC,IAAI,GAAG,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IAAA;AAGjJ,UAAA,iBAAiB,CAAC,UAAkB;AACxC,UAAI,UAAU;AAAU,eAAA;AACxB,YAAM,IAAI;AACV,YAAM,QAAQ,CAAC,KAAK,MAAM,MAAM,IAAI;AAC9B,YAAA,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,CAAC;AAClD,aAAO,YAAY,QAAQ,KAAK,IAAI,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC;AAAA,IAAA;AAIxE,UAAM,mBAAmB,MAAM;AACzBD,oBAAAA,MAAA,eAAe,6BAA6B,aAAa,KAAK;AAAA,IAAA;AAGpE,UAAM,mBAAmB,MAAM;AAC7B,YAAM,UAAUA,cAAA,MAAI,eAAe,2BAA2B,KAAK,CAAA;AACnE,mBAAa,QAAQ;AAAA,IAAA;AAGvB,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MAAA,CACN;AAAA,IAAA;AAGG,UAAA,kBAAkB,CAAC,SAAc;AACrCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4CAA4C,KAAK,EAAE;AAAA,MAAA,CACzD;AAAA,IAAA;AAIH,UAAM,oBAAoB,MAAM;AACb;AACjBA,oBAAA,MAAI,oBAAoB;AAAA,IAAA;AAIb,aAAA;AAAA,MACX;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACztBD,GAAG,WAAW,eAAe;"}