<template>
  <view class="document-translation-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">文档翻译</text>
      <text class="page-desc">支持多种文档格式的智能翻译</text>
    </view>

    <!-- 语言选择 -->
    <view class="language-selector">
      <view class="language-item">
        <text class="language-label">源语言</text>
        <u-button 
          type="text" 
          :text="sourceLanguage?.name || '自动检测'" 
          @click="showSourceLanguagePicker = true"
          :custom-style="{ color: '#2563EB' }"
        >
          <u-icon name="arrow-down" size="12" color="#2563EB" margin-left="4"></u-icon>
        </u-button>
      </view>
      
      <view class="swap-button" @click="swapLanguages">
        <u-icon name="arrow-left-right" size="20" color="#6B7280"></u-icon>
      </view>
      
      <view class="language-item">
        <text class="language-label">目标语言</text>
        <u-button 
          type="text" 
          :text="targetLanguage?.name || '中文'" 
          @click="showTargetLanguagePicker = true"
          :custom-style="{ color: '#2563EB' }"
        >
          <u-icon name="arrow-down" size="12" color="#2563EB" margin-left="4"></u-icon>
        </u-button>
      </view>
    </view>

    <!-- 文档上传区域 -->
    <view class="upload-section">
      <view class="section-title">
        <text class="title-text">上传文档</text>
        <text class="title-desc">支持PDF、Word、Excel、PPT等格式，最大100MB</text>
      </view>
      
      <view class="upload-area" @click="chooseDocument">
        <view class="upload-content" v-if="!selectedDocument">
          <u-icon name="file-document" size="48" color="#2563EB"></u-icon>
          <text class="upload-text">选择文档文件</text>
          <text class="upload-desc">支持 PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX</text>
          <text class="upload-limit">文件大小限制：100MB</text>
        </view>
        
        <view class="file-info" v-else>
          <view class="file-icon">
            <u-icon :name="getFileIcon(selectedDocument.type)" size="32" :color="getFileColor(selectedDocument.type)"></u-icon>
          </view>
          <view class="file-details">
            <text class="file-name">{{ selectedDocument.name }}</text>
            <text class="file-size">{{ formatFileSize(selectedDocument.size) }}</text>
            <text class="file-type">{{ selectedDocument.type.toUpperCase() }} 文档</text>
            <text class="file-pages" v-if="selectedDocument.pages">
              {{ selectedDocument.pages }} 页
            </text>
          </view>
          <view class="file-actions">
            <u-icon name="eye" size="20" color="#2563EB" @click.stop="previewDocument"></u-icon>
            <u-icon name="close-circle" size="20" color="#EF4444" @click.stop="removeDocument"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 翻译选项 -->
    <view class="translation-options" v-if="selectedDocument">
      <view class="section-title">
        <text class="title-text">翻译选项</text>
      </view>
      
      <view class="options-list">
        <!-- 翻译模式 -->
        <view class="option-item">
          <text class="option-label">翻译模式</text>
          <u-radio-group v-model="translationMode">
            <u-radio name="full" label="完整翻译"></u-radio>
            <u-radio name="partial" label="部分翻译"></u-radio>
          </u-radio-group>
        </view>
        
        <!-- 页面范围 -->
        <view class="option-item" v-if="translationMode === 'partial' && selectedDocument.pages">
          <text class="option-label">页面范围</text>
          <input 
            class="page-range-input"
            v-model="pageRange"
            placeholder="例如：1-5,8,10-12"
          />
        </view>
        
        <!-- 保持格式 -->
        <view class="option-item">
          <text class="option-label">格式保持</text>
          <u-switch v-model="keepFormat" active-color="#2563EB"></u-switch>
          <text class="option-desc">保持原文档的格式和布局</text>
        </view>
        
        <!-- 输出格式 -->
        <view class="option-item">
          <text class="option-label">输出格式</text>
          <u-radio-group v-model="outputFormat">
            <u-radio name="same" label="与原文档相同"></u-radio>
            <u-radio name="pdf" label="PDF格式"></u-radio>
            <u-radio name="docx" label="Word格式"></u-radio>
          </u-radio-group>
        </view>
        
        <!-- 翻译质量 -->
        <view class="option-item">
          <text class="option-label">翻译质量</text>
          <u-radio-group v-model="translationQuality">
            <u-radio name="fast" label="快速翻译"></u-radio>
            <u-radio name="balanced" label="平衡模式"></u-radio>
            <u-radio name="accurate" label="精准翻译"></u-radio>
          </u-radio-group>
        </view>
      </view>
      
      <view class="translate-button">
        <u-button 
          type="primary" 
          size="large"
          :loading="isTranslating"
          :disabled="!canTranslate"
          @click="startTranslation"
          :custom-style="{ width: '100%' }"
        >
          {{ isTranslating ? '翻译中...' : '开始翻译' }}
        </u-button>
      </view>
    </view>

    <!-- 翻译进度 -->
    <view class="translation-progress" v-if="isTranslating">
      <view class="progress-header">
        <text class="progress-title">正在翻译文档</text>
        <text class="progress-desc">{{ progressStatus }}</text>
      </view>
      
      <view class="progress-bar">
        <u-line-progress 
          :percent="translationProgress" 
          :show-percent="true"
          active-color="#2563EB"
        ></u-line-progress>
      </view>
      
      <view class="progress-details">
        <text class="progress-text">已处理 {{ processedPages }}/{{ totalPages }} 页</text>
        <text class="progress-time">预计剩余时间: {{ estimatedTime }}</text>
      </view>
    </view>

    <!-- 翻译结果 -->
    <view class="translation-result" v-if="translationResult">
      <view class="section-title">
        <text class="title-text">翻译完成</text>
        <view class="result-actions">
          <u-icon name="download" size="18" color="#2563EB" @click="downloadResult"></u-icon>
          <u-icon name="share" size="18" color="#2563EB" margin-left="12" @click="shareResult"></u-icon>
        </view>
      </view>
      
      <view class="result-content">
        <view class="result-summary">
          <view class="summary-item">
            <u-icon name="checkmark-circle" size="20" color="#10B981"></u-icon>
            <text class="summary-text">翻译完成</text>
          </view>
          <view class="summary-stats">
            <text class="stat-item">处理页数: {{ translationResult.processedPages }}</text>
            <text class="stat-item">翻译字数: {{ translationResult.wordCount }}</text>
            <text class="stat-item">用时: {{ translationResult.duration }}</text>
          </view>
        </view>
        
        <view class="result-file">
          <view class="file-preview">
            <u-icon :name="getFileIcon(translationResult.format)" size="32" :color="getFileColor(translationResult.format)"></u-icon>
            <view class="preview-info">
              <text class="preview-name">{{ translationResult.filename }}</text>
              <text class="preview-size">{{ formatFileSize(translationResult.size) }}</text>
            </view>
          </view>
          
          <view class="file-actions">
            <u-button 
              type="primary" 
              size="small"
              @click="downloadResult"
            >
              下载文档
            </u-button>
            <u-button 
              type="text" 
              size="small"
              @click="previewResult"
              :custom-style="{ color: '#2563EB' }"
            >
              预览
            </u-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 翻译历史 -->
    <view class="translation-history" v-if="documentHistory.length > 0">
      <view class="section-title">
        <text class="title-text">翻译历史</text>
        <u-button 
          type="text" 
          text="查看全部" 
          size="small"
          @click="viewAllHistory"
          :custom-style="{ color: '#2563EB', fontSize: '24rpx' }"
        ></u-button>
      </view>
      
      <view class="history-list">
        <view 
          class="history-item"
          v-for="item in documentHistory.slice(0, 3)"
          :key="item.id"
          @click="viewHistoryItem(item)"
        >
          <view class="history-icon">
            <u-icon :name="getFileIcon(item.fileType)" size="20" :color="getFileColor(item.fileType)"></u-icon>
          </view>
          <view class="history-content">
            <text class="history-title">{{ item.filename }}</text>
            <text class="history-desc">{{ item.sourceLang }} → {{ item.targetLang }}</text>
            <text class="history-time">{{ formatTime(item.createdAt) }}</text>
          </view>
          <view class="history-status">
            <u-icon 
              :name="item.status === 'completed' ? 'checkmark-circle' : 'time'" 
              size="16" 
              :color="item.status === 'completed' ? '#10B981' : '#F59E0B'"
            ></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 语言选择器 -->
    <u-picker
      v-model="showSourceLanguagePicker"
      :columns="sourceLanguageColumns"
      @confirm="onSourceLanguageConfirm"
      @cancel="showSourceLanguagePicker = false"
    ></u-picker>

    <u-picker
      v-model="showTargetLanguagePicker"
      :columns="targetLanguageColumns"
      @confirm="onTargetLanguageConfirm"
      @cancel="showTargetLanguagePicker = false"
    ></u-picker>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useTranslationStore } from '@/store/modules/translation'
import dayjs from 'dayjs'

// Store
const translationStore = useTranslationStore()

// 响应式数据
const selectedDocument = ref<any>(null)
const isTranslating = ref<boolean>(false)
const translationProgress = ref<number>(0)
const progressStatus = ref<string>('')
const processedPages = ref<number>(0)
const totalPages = ref<number>(0)
const estimatedTime = ref<string>('--:--')
const translationResult = ref<any>(null)
const documentHistory = ref<any[]>([])
const showSourceLanguagePicker = ref<boolean>(false)
const showTargetLanguagePicker = ref<boolean>(false)
const translationMode = ref<string>('full')
const pageRange = ref<string>('')
const keepFormat = ref<boolean>(true)
const outputFormat = ref<string>('same')
const translationQuality = ref<string>('balanced')

// 计算属性
const {
  languages,
  currentSourceLang,
  currentTargetLang,
  sourceLanguage,
  targetLanguage
} = translationStore

const canTranslate = computed(() => {
  return selectedDocument.value && currentSourceLang.value && currentTargetLang.value
})

const sourceLanguageColumns = computed(() => {
  const autoDetect = [{ text: '自动检测', value: 'auto' }]
  const languageOptions = languages.map(lang => ({
    text: lang.name,
    value: lang.code
  }))
  return [autoDetect.concat(languageOptions)]
})

const targetLanguageColumns = computed(() => {
  const languageOptions = languages.filter(lang => lang.code !== 'auto').map(lang => ({
    text: lang.name,
    value: lang.code
  }))
  return [languageOptions]
})

// 页面加载
onMounted(async () => {
  await translationStore.loadLanguages()
  loadDocumentHistory()
})

// 选择文档
const chooseDocument = () => {
  // #ifdef H5
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx'
  input.onchange = (e: any) => {
    const file = e.target.files[0]
    if (file) {
      handleSelectedFile(file)
    }
  }
  input.click()
  // #endif

  // #ifdef APP-PLUS || MP
  uni.chooseFile({
    count: 1,
    type: 'file',
    extension: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],
    success: (res) => {
      const file = res.tempFiles[0]
      handleSelectedFile(file)
    },
    fail: (error) => {
      console.error('选择文件失败:', error)
    }
  })
  // #endif
}

// 处理选中的文件
const handleSelectedFile = (file: any) => {
  // 检查文件大小
  if (file.size > 100 * 1024 * 1024) {
    uni.showToast({
      title: '文件大小不能超过100MB',
      icon: 'error'
    })
    return
  }

  const fileExtension = getFileExtension(file.name)

  selectedDocument.value = {
    name: file.name,
    size: file.size,
    type: fileExtension,
    path: file.path || URL.createObjectURL(file),
    pages: getEstimatedPages(fileExtension, file.size)
  }

  // 重置结果
  translationResult.value = null
}

// 获取文件扩展名
const getFileExtension = (filename: string) => {
  return filename.split('.').pop()?.toLowerCase() || ''
}

// 估算页数
const getEstimatedPages = (fileType: string, fileSize: number) => {
  // 根据文件类型和大小估算页数
  const avgPageSize = {
    pdf: 100 * 1024, // 100KB per page
    doc: 50 * 1024,  // 50KB per page
    docx: 30 * 1024, // 30KB per page
    xls: 20 * 1024,  // 20KB per page
    xlsx: 15 * 1024, // 15KB per page
    ppt: 200 * 1024, // 200KB per page
    pptx: 150 * 1024 // 150KB per page
  }

  const avgSize = avgPageSize[fileType as keyof typeof avgPageSize] || 50 * 1024
  return Math.max(1, Math.ceil(fileSize / avgSize))
}

// 获取文件图标
const getFileIcon = (fileType: string) => {
  const iconMap: Record<string, string> = {
    pdf: 'file-pdf',
    doc: 'file-word',
    docx: 'file-word',
    xls: 'file-excel',
    xlsx: 'file-excel',
    ppt: 'file-powerpoint',
    pptx: 'file-powerpoint'
  }

  return iconMap[fileType] || 'file-document'
}

// 获取文件颜色
const getFileColor = (fileType: string) => {
  const colorMap: Record<string, string> = {
    pdf: '#EF4444',
    doc: '#2563EB',
    docx: '#2563EB',
    xls: '#10B981',
    xlsx: '#10B981',
    ppt: '#F59E0B',
    pptx: '#F59E0B'
  }

  return colorMap[fileType] || '#6B7280'
}

// 移除文档
const removeDocument = () => {
  selectedDocument.value = null
  translationResult.value = null
}

// 预览文档
const previewDocument = () => {
  if (!selectedDocument.value) return

  uni.showToast({
    title: '预览功能开发中',
    icon: 'none'
  })
}

// 开始翻译
const startTranslation = async () => {
  if (!canTranslate.value) return

  try {
    isTranslating.value = true
    translationProgress.value = 0
    processedPages.value = 0
    totalPages.value = selectedDocument.value.pages || 1

    // 模拟翻译进度
    const totalSteps = totalPages.value
    const stepDuration = 2000 // 每页2秒

    for (let i = 0; i < totalSteps; i++) {
      progressStatus.value = `正在翻译第 ${i + 1} 页...`
      processedPages.value = i + 1

      // 计算剩余时间
      const remainingPages = totalSteps - (i + 1)
      const remainingSeconds = remainingPages * (stepDuration / 1000)
      estimatedTime.value = formatDuration(remainingSeconds)

      // 更新进度
      await new Promise(resolve => {
        const interval = setInterval(() => {
          translationProgress.value += 2
          if (translationProgress.value >= ((i + 1) / totalSteps) * 100) {
            clearInterval(interval)
            resolve(true)
          }
        }, stepDuration / 50)
      })
    }

    // 生成翻译结果
    const outputExt = outputFormat.value === 'same' ? selectedDocument.value.type : outputFormat.value
    const mockResult = {
      filename: `translated_${selectedDocument.value.name.split('.')[0]}.${outputExt}`,
      format: outputExt,
      size: selectedDocument.value.size * 1.1,
      processedPages: totalPages.value,
      wordCount: Math.floor(selectedDocument.value.size / 10),
      duration: formatDuration(totalSteps * 2),
      downloadUrl: 'mock-download-url'
    }

    translationResult.value = mockResult

    // 添加到历史记录
    const historyItem = {
      id: Date.now().toString(),
      filename: selectedDocument.value.name,
      fileType: selectedDocument.value.type,
      sourceLang: currentSourceLang.value,
      targetLang: currentTargetLang.value,
      status: 'completed',
      size: selectedDocument.value.size,
      pages: totalPages.value,
      createdAt: new Date().toISOString()
    }

    documentHistory.value.unshift(historyItem)
    saveDocumentHistory()

    uni.showToast({
      title: '翻译完成',
      icon: 'success'
    })

  } catch (error) {
    console.error('翻译失败:', error)
    uni.showToast({
      title: '翻译失败',
      icon: 'error'
    })
  } finally {
    isTranslating.value = false
    estimatedTime.value = '00:00'
  }
}

// 交换语言
const swapLanguages = () => {
  translationStore.swapLanguages()
}

// 语言选择确认
const onSourceLanguageConfirm = (value: any) => {
  translationStore.setSourceLanguage(value[0].value)
  showSourceLanguagePicker.value = false
}

const onTargetLanguageConfirm = (value: any) => {
  translationStore.setTargetLanguage(value[0].value)
  showTargetLanguagePicker.value = false
}

// 下载结果
const downloadResult = () => {
  if (!translationResult.value) return

  uni.showToast({
    title: '开始下载',
    icon: 'success'
  })
}

// 分享结果
const shareResult = () => {
  if (!translationResult.value) return

  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    summary: `文档翻译完成: ${translationResult.value.filename}`,
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    }
  })
}

// 预览结果
const previewResult = () => {
  if (!translationResult.value) return

  uni.showToast({
    title: '预览功能开发中',
    icon: 'none'
  })
}

// 工具函数
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

// 历史记录相关
const saveDocumentHistory = () => {
  uni.setStorageSync('document_translation_history', documentHistory.value)
}

const loadDocumentHistory = () => {
  const history = uni.getStorageSync('document_translation_history') || []
  documentHistory.value = history
}

const viewAllHistory = () => {
  uni.navigateTo({
    url: '/pages/translation/document/history/index'
  })
}

const viewHistoryItem = (item: any) => {
  uni.navigateTo({
    url: `/pages/translation/document/detail/index?id=${item.id}`
  })
}

// 页面下拉刷新
const onPullDownRefresh = () => {
  loadDocumentHistory()
  uni.stopPullDownRefresh()
}

// 导出方法供页面使用
defineExpose({
  onPullDownRefresh
})
</script>

<style lang="scss" scoped>
.document-translation-page {
  min-height: 100vh;
  background-color: $bg-secondary;
}

// 页面头部
.page-header {
  background-color: $bg-primary;
  padding: $spacing-6 $spacing-4;
  text-align: center;
  border-bottom: 1px solid $border-primary;

  .page-title {
    display: block;
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: $spacing-2;
  }

  .page-desc {
    font-size: $font-size-base;
    color: $text-secondary;
  }
}

// 语言选择器
.language-selector {
  background-color: $bg-primary;
  padding: $spacing-4;
  border-bottom: 1px solid $border-primary;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .language-item {
    flex: 1;
    text-align: center;

    .language-label {
      display: block;
      font-size: $font-size-sm;
      color: $text-tertiary;
      margin-bottom: $spacing-2;
    }
  }

  .swap-button {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $bg-tertiary;
    border-radius: $radius-full;
    margin: 0 $spacing-4;

    &:active {
      background-color: $gray-200;
    }
  }
}

// 通用区块标题
.section-title {
  padding: $spacing-4 $spacing-4 $spacing-3;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title-text {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
  }

  .title-desc {
    font-size: $font-size-sm;
    color: $text-tertiary;
  }

  .result-actions {
    display: flex;
    align-items: center;
    gap: $spacing-2;
  }
}

// 文档上传区域
.upload-section {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .upload-area {
    margin: 0 $spacing-4 $spacing-4;
    border: 2px dashed $border-primary;
    border-radius: $radius-lg;
    padding: $spacing-6;

    .upload-content {
      text-align: center;

      .upload-text {
        display: block;
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin: $spacing-4 0 $spacing-2;
      }

      .upload-desc {
        display: block;
        font-size: $font-size-base;
        color: $text-secondary;
        margin-bottom: $spacing-2;
      }

      .upload-limit {
        font-size: $font-size-sm;
        color: $text-tertiary;
      }
    }

    .file-info {
      display: flex;
      align-items: center;
      gap: $spacing-3;

      .file-icon {
        width: 80rpx;
        height: 80rpx;
        background-color: $bg-tertiary;
        border-radius: $radius-lg;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .file-details {
        flex: 1;

        .file-name {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-size,
        .file-type,
        .file-pages {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: 4rpx;
        }
      }

      .file-actions {
        display: flex;
        gap: $spacing-2;
      }
    }
  }
}

// 翻译选项
.translation-options {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .options-list {
    padding: 0 $spacing-4;

    .option-item {
      padding: $spacing-4 0;
      border-bottom: 1px solid $border-primary;

      &:last-child {
        border-bottom: none;
      }

      .option-label {
        display: block;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-3;
      }

      .option-desc {
        font-size: $font-size-sm;
        color: $text-tertiary;
        margin-left: $spacing-2;
      }

      .page-range-input {
        width: 100%;
        padding: $spacing-2 $spacing-3;
        border: 1px solid $border-primary;
        border-radius: $radius-md;
        font-size: $font-size-base;
        color: $text-primary;
        background-color: $bg-tertiary;

        &::placeholder {
          color: $text-quaternary;
        }
      }
    }
  }

  .translate-button {
    padding: 0 $spacing-4 $spacing-4;
  }
}

// 翻译进度
.translation-progress {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;
  padding: $spacing-4;

  .progress-header {
    text-align: center;
    margin-bottom: $spacing-4;

    .progress-title {
      display: block;
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-primary;
      margin-bottom: $spacing-1;
    }

    .progress-desc {
      font-size: $font-size-sm;
      color: $text-secondary;
    }
  }

  .progress-bar {
    margin-bottom: $spacing-4;
  }

  .progress-details {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .progress-text,
    .progress-time {
      font-size: $font-size-sm;
      color: $text-tertiary;
    }
  }
}

// 翻译结果
.translation-result {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .result-content {
    padding: 0 $spacing-4 $spacing-4;

    .result-summary {
      background-color: $success-50;
      border-radius: $radius-lg;
      padding: $spacing-4;
      margin-bottom: $spacing-4;

      .summary-item {
        display: flex;
        align-items: center;
        gap: $spacing-2;
        margin-bottom: $spacing-3;

        .summary-text {
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $success-700;
        }
      }

      .summary-stats {
        display: flex;
        flex-wrap: wrap;
        gap: $spacing-4;

        .stat-item {
          font-size: $font-size-sm;
          color: $success-600;
        }
      }
    }

    .result-file {
      background-color: $bg-tertiary;
      border-radius: $radius-lg;
      padding: $spacing-4;

      .file-preview {
        display: flex;
        align-items: center;
        gap: $spacing-3;
        margin-bottom: $spacing-4;

        .preview-info {
          flex: 1;

          .preview-name {
            display: block;
            font-size: $font-size-base;
            font-weight: $font-weight-medium;
            color: $text-primary;
            margin-bottom: $spacing-1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .preview-size {
            font-size: $font-size-sm;
            color: $text-secondary;
          }
        }
      }

      .file-actions {
        display: flex;
        gap: $spacing-2;
      }
    }
  }
}

// 翻译历史
.translation-history {
  background-color: $bg-primary;

  .history-list {
    padding: 0 $spacing-4 $spacing-4;

    .history-item {
      display: flex;
      align-items: center;
      gap: $spacing-3;
      padding: $spacing-3;
      background-color: $bg-tertiary;
      border-radius: $radius-lg;
      margin-bottom: $spacing-2;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        background-color: $gray-200;
      }

      .history-icon {
        width: 64rpx;
        height: 64rpx;
        background-color: $bg-quaternary;
        border-radius: $radius-lg;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .history-content {
        flex: 1;

        .history-title {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .history-desc {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-1;
        }

        .history-time {
          font-size: $font-size-xs;
          color: $text-tertiary;
        }
      }

      .history-status {
        padding: $spacing-2;
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 480px) {
  .language-selector {
    flex-direction: column;
    gap: $spacing-3;

    .swap-button {
      transform: rotate(90deg);
    }
  }

  .summary-stats {
    flex-direction: column;
    gap: $spacing-2;
  }

  .file-actions {
    flex-direction: column;
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .document-translation-page {
    background-color: #1a1a1a;
  }

  .page-header,
  .language-selector,
  .upload-section,
  .translation-options,
  .translation-progress,
  .translation-result,
  .translation-history {
    background-color: #2d2d2d;
    border-color: #404040;
  }

  .upload-area,
  .result-file,
  .history-item {
    background-color: #404040;
    border-color: #555555;
  }

  .swap-button,
  .file-icon,
  .history-icon {
    background-color: #404040;
  }

  .result-summary {
    background-color: #1e3a2e;
  }

  .page-range-input {
    background-color: #555555;
    border-color: #666666;
    color: #ffffff;

    &::placeholder {
      color: #888888;
    }
  }
}
</style>
