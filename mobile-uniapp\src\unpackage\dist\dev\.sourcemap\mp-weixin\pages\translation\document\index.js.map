{"version": 3, "file": "index.js", "sources": ["pages/translation/document/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdHJhbnNsYXRpb24vZG9jdW1lbnQvaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"document-translation-page\">\n    <!-- 页面标题 -->\n    <view class=\"page-header\">\n      <text class=\"page-title\">文档翻译</text>\n      <text class=\"page-desc\">支持多种文档格式的智能翻译</text>\n    </view>\n\n    <!-- 语言选择 -->\n    <view class=\"language-selector\">\n      <view class=\"language-item\">\n        <text class=\"language-label\">源语言</text>\n        <u-button \n          type=\"text\" \n          :text=\"sourceLanguage?.name || '自动检测'\" \n          @click=\"showSourceLanguagePicker = true\"\n          :custom-style=\"{ color: '#2563EB' }\"\n        >\n          <u-icon name=\"arrow-down\" size=\"12\" color=\"#2563EB\" margin-left=\"4\"></u-icon>\n        </u-button>\n      </view>\n      \n      <view class=\"swap-button\" @click=\"swapLanguages\">\n        <u-icon name=\"arrow-left-right\" size=\"20\" color=\"#6B7280\"></u-icon>\n      </view>\n      \n      <view class=\"language-item\">\n        <text class=\"language-label\">目标语言</text>\n        <u-button \n          type=\"text\" \n          :text=\"targetLanguage?.name || '中文'\" \n          @click=\"showTargetLanguagePicker = true\"\n          :custom-style=\"{ color: '#2563EB' }\"\n        >\n          <u-icon name=\"arrow-down\" size=\"12\" color=\"#2563EB\" margin-left=\"4\"></u-icon>\n        </u-button>\n      </view>\n    </view>\n\n    <!-- 文档上传区域 -->\n    <view class=\"upload-section\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">上传文档</text>\n        <text class=\"title-desc\">支持PDF、Word、Excel、PPT等格式，最大100MB</text>\n      </view>\n      \n      <view class=\"upload-area\" @click=\"chooseDocument\">\n        <view class=\"upload-content\" v-if=\"!selectedDocument\">\n          <u-icon name=\"file-document\" size=\"48\" color=\"#2563EB\"></u-icon>\n          <text class=\"upload-text\">选择文档文件</text>\n          <text class=\"upload-desc\">支持 PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX</text>\n          <text class=\"upload-limit\">文件大小限制：100MB</text>\n        </view>\n        \n        <view class=\"file-info\" v-else>\n          <view class=\"file-icon\">\n            <u-icon :name=\"getFileIcon(selectedDocument.type)\" size=\"32\" :color=\"getFileColor(selectedDocument.type)\"></u-icon>\n          </view>\n          <view class=\"file-details\">\n            <text class=\"file-name\">{{ selectedDocument.name }}</text>\n            <text class=\"file-size\">{{ formatFileSize(selectedDocument.size) }}</text>\n            <text class=\"file-type\">{{ selectedDocument.type.toUpperCase() }} 文档</text>\n            <text class=\"file-pages\" v-if=\"selectedDocument.pages\">\n              {{ selectedDocument.pages }} 页\n            </text>\n          </view>\n          <view class=\"file-actions\">\n            <u-icon name=\"eye\" size=\"20\" color=\"#2563EB\" @click.stop=\"previewDocument\"></u-icon>\n            <u-icon name=\"close-circle\" size=\"20\" color=\"#EF4444\" @click.stop=\"removeDocument\"></u-icon>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 翻译选项 -->\n    <view class=\"translation-options\" v-if=\"selectedDocument\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">翻译选项</text>\n      </view>\n      \n      <view class=\"options-list\">\n        <!-- 翻译模式 -->\n        <view class=\"option-item\">\n          <text class=\"option-label\">翻译模式</text>\n          <u-radio-group v-model=\"translationMode\">\n            <u-radio name=\"full\" label=\"完整翻译\"></u-radio>\n            <u-radio name=\"partial\" label=\"部分翻译\"></u-radio>\n          </u-radio-group>\n        </view>\n        \n        <!-- 页面范围 -->\n        <view class=\"option-item\" v-if=\"translationMode === 'partial' && selectedDocument.pages\">\n          <text class=\"option-label\">页面范围</text>\n          <input \n            class=\"page-range-input\"\n            v-model=\"pageRange\"\n            placeholder=\"例如：1-5,8,10-12\"\n          />\n        </view>\n        \n        <!-- 保持格式 -->\n        <view class=\"option-item\">\n          <text class=\"option-label\">格式保持</text>\n          <u-switch v-model=\"keepFormat\" active-color=\"#2563EB\"></u-switch>\n          <text class=\"option-desc\">保持原文档的格式和布局</text>\n        </view>\n        \n        <!-- 输出格式 -->\n        <view class=\"option-item\">\n          <text class=\"option-label\">输出格式</text>\n          <u-radio-group v-model=\"outputFormat\">\n            <u-radio name=\"same\" label=\"与原文档相同\"></u-radio>\n            <u-radio name=\"pdf\" label=\"PDF格式\"></u-radio>\n            <u-radio name=\"docx\" label=\"Word格式\"></u-radio>\n          </u-radio-group>\n        </view>\n        \n        <!-- 翻译质量 -->\n        <view class=\"option-item\">\n          <text class=\"option-label\">翻译质量</text>\n          <u-radio-group v-model=\"translationQuality\">\n            <u-radio name=\"fast\" label=\"快速翻译\"></u-radio>\n            <u-radio name=\"balanced\" label=\"平衡模式\"></u-radio>\n            <u-radio name=\"accurate\" label=\"精准翻译\"></u-radio>\n          </u-radio-group>\n        </view>\n      </view>\n      \n      <view class=\"translate-button\">\n        <u-button \n          type=\"primary\" \n          size=\"large\"\n          :loading=\"isTranslating\"\n          :disabled=\"!canTranslate\"\n          @click=\"startTranslation\"\n          :custom-style=\"{ width: '100%' }\"\n        >\n          {{ isTranslating ? '翻译中...' : '开始翻译' }}\n        </u-button>\n      </view>\n    </view>\n\n    <!-- 翻译进度 -->\n    <view class=\"translation-progress\" v-if=\"isTranslating\">\n      <view class=\"progress-header\">\n        <text class=\"progress-title\">正在翻译文档</text>\n        <text class=\"progress-desc\">{{ progressStatus }}</text>\n      </view>\n      \n      <view class=\"progress-bar\">\n        <u-line-progress \n          :percent=\"translationProgress\" \n          :show-percent=\"true\"\n          active-color=\"#2563EB\"\n        ></u-line-progress>\n      </view>\n      \n      <view class=\"progress-details\">\n        <text class=\"progress-text\">已处理 {{ processedPages }}/{{ totalPages }} 页</text>\n        <text class=\"progress-time\">预计剩余时间: {{ estimatedTime }}</text>\n      </view>\n    </view>\n\n    <!-- 翻译结果 -->\n    <view class=\"translation-result\" v-if=\"translationResult\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">翻译完成</text>\n        <view class=\"result-actions\">\n          <u-icon name=\"download\" size=\"18\" color=\"#2563EB\" @click=\"downloadResult\"></u-icon>\n          <u-icon name=\"share\" size=\"18\" color=\"#2563EB\" margin-left=\"12\" @click=\"shareResult\"></u-icon>\n        </view>\n      </view>\n      \n      <view class=\"result-content\">\n        <view class=\"result-summary\">\n          <view class=\"summary-item\">\n            <u-icon name=\"checkmark-circle\" size=\"20\" color=\"#10B981\"></u-icon>\n            <text class=\"summary-text\">翻译完成</text>\n          </view>\n          <view class=\"summary-stats\">\n            <text class=\"stat-item\">处理页数: {{ translationResult.processedPages }}</text>\n            <text class=\"stat-item\">翻译字数: {{ translationResult.wordCount }}</text>\n            <text class=\"stat-item\">用时: {{ translationResult.duration }}</text>\n          </view>\n        </view>\n        \n        <view class=\"result-file\">\n          <view class=\"file-preview\">\n            <u-icon :name=\"getFileIcon(translationResult.format)\" size=\"32\" :color=\"getFileColor(translationResult.format)\"></u-icon>\n            <view class=\"preview-info\">\n              <text class=\"preview-name\">{{ translationResult.filename }}</text>\n              <text class=\"preview-size\">{{ formatFileSize(translationResult.size) }}</text>\n            </view>\n          </view>\n          \n          <view class=\"file-actions\">\n            <u-button \n              type=\"primary\" \n              size=\"small\"\n              @click=\"downloadResult\"\n            >\n              下载文档\n            </u-button>\n            <u-button \n              type=\"text\" \n              size=\"small\"\n              @click=\"previewResult\"\n              :custom-style=\"{ color: '#2563EB' }\"\n            >\n              预览\n            </u-button>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 翻译历史 -->\n    <view class=\"translation-history\" v-if=\"documentHistory.length > 0\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">翻译历史</text>\n        <u-button \n          type=\"text\" \n          text=\"查看全部\" \n          size=\"small\"\n          @click=\"viewAllHistory\"\n          :custom-style=\"{ color: '#2563EB', fontSize: '24rpx' }\"\n        ></u-button>\n      </view>\n      \n      <view class=\"history-list\">\n        <view \n          class=\"history-item\"\n          v-for=\"item in documentHistory.slice(0, 3)\"\n          :key=\"item.id\"\n          @click=\"viewHistoryItem(item)\"\n        >\n          <view class=\"history-icon\">\n            <u-icon :name=\"getFileIcon(item.fileType)\" size=\"20\" :color=\"getFileColor(item.fileType)\"></u-icon>\n          </view>\n          <view class=\"history-content\">\n            <text class=\"history-title\">{{ item.filename }}</text>\n            <text class=\"history-desc\">{{ item.sourceLang }} → {{ item.targetLang }}</text>\n            <text class=\"history-time\">{{ formatTime(item.createdAt) }}</text>\n          </view>\n          <view class=\"history-status\">\n            <u-icon \n              :name=\"item.status === 'completed' ? 'checkmark-circle' : 'time'\" \n              size=\"16\" \n              :color=\"item.status === 'completed' ? '#10B981' : '#F59E0B'\"\n            ></u-icon>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 语言选择器 -->\n    <u-picker\n      v-model=\"showSourceLanguagePicker\"\n      :columns=\"sourceLanguageColumns\"\n      @confirm=\"onSourceLanguageConfirm\"\n      @cancel=\"showSourceLanguagePicker = false\"\n    ></u-picker>\n\n    <u-picker\n      v-model=\"showTargetLanguagePicker\"\n      :columns=\"targetLanguageColumns\"\n      @confirm=\"onTargetLanguageConfirm\"\n      @cancel=\"showTargetLanguagePicker = false\"\n    ></u-picker>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\nimport { useTranslationStore } from '@/store/modules/translation'\nimport dayjs from 'dayjs'\n\n// Store\nconst translationStore = useTranslationStore()\n\n// 响应式数据\nconst selectedDocument = ref<any>(null)\nconst isTranslating = ref<boolean>(false)\nconst translationProgress = ref<number>(0)\nconst progressStatus = ref<string>('')\nconst processedPages = ref<number>(0)\nconst totalPages = ref<number>(0)\nconst estimatedTime = ref<string>('--:--')\nconst translationResult = ref<any>(null)\nconst documentHistory = ref<any[]>([])\nconst showSourceLanguagePicker = ref<boolean>(false)\nconst showTargetLanguagePicker = ref<boolean>(false)\nconst translationMode = ref<string>('full')\nconst pageRange = ref<string>('')\nconst keepFormat = ref<boolean>(true)\nconst outputFormat = ref<string>('same')\nconst translationQuality = ref<string>('balanced')\n\n// 计算属性\nconst {\n  languages,\n  currentSourceLang,\n  currentTargetLang,\n  sourceLanguage,\n  targetLanguage\n} = translationStore\n\nconst canTranslate = computed(() => {\n  return selectedDocument.value && currentSourceLang.value && currentTargetLang.value\n})\n\nconst sourceLanguageColumns = computed(() => {\n  const autoDetect = [{ text: '自动检测', value: 'auto' }]\n  const languageOptions = languages.map(lang => ({\n    text: lang.name,\n    value: lang.code\n  }))\n  return [autoDetect.concat(languageOptions)]\n})\n\nconst targetLanguageColumns = computed(() => {\n  const languageOptions = languages.filter(lang => lang.code !== 'auto').map(lang => ({\n    text: lang.name,\n    value: lang.code\n  }))\n  return [languageOptions]\n})\n\n// 页面加载\nonMounted(async () => {\n  await translationStore.loadLanguages()\n  loadDocumentHistory()\n})\n\n// 选择文档\nconst chooseDocument = () => {\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  uni.chooseFile({\n    count: 1,\n    type: 'file',\n    extension: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],\n    success: (res) => {\n      const file = res.tempFiles[0]\n      handleSelectedFile(file)\n    },\n    fail: (error) => {\n      uni.__f__('error','at pages/translation/document/index.vue:360','选择文件失败:', error)\n    }\n  })\n\n}\n\n// 处理选中的文件\nconst handleSelectedFile = (file: any) => {\n  // 检查文件大小\n  if (file.size > 100 * 1024 * 1024) {\n    uni.showToast({\n      title: '文件大小不能超过100MB',\n      icon: 'error'\n    })\n    return\n  }\n\n  const fileExtension = getFileExtension(file.name)\n\n  selectedDocument.value = {\n    name: file.name,\n    size: file.size,\n    type: fileExtension,\n    path: file.path || URL.createObjectURL(file),\n    pages: getEstimatedPages(fileExtension, file.size)\n  }\n\n  // 重置结果\n  translationResult.value = null\n}\n\n// 获取文件扩展名\nconst getFileExtension = (filename: string) => {\n  return filename.split('.').pop()?.toLowerCase() || ''\n}\n\n// 估算页数\nconst getEstimatedPages = (fileType: string, fileSize: number) => {\n  // 根据文件类型和大小估算页数\n  const avgPageSize = {\n    pdf: 100 * 1024, // 100KB per page\n    doc: 50 * 1024,  // 50KB per page\n    docx: 30 * 1024, // 30KB per page\n    xls: 20 * 1024,  // 20KB per page\n    xlsx: 15 * 1024, // 15KB per page\n    ppt: 200 * 1024, // 200KB per page\n    pptx: 150 * 1024 // 150KB per page\n  }\n\n  const avgSize = avgPageSize[fileType as keyof typeof avgPageSize] || 50 * 1024\n  return Math.max(1, Math.ceil(fileSize / avgSize))\n}\n\n// 获取文件图标\nconst getFileIcon = (fileType: string) => {\n  const iconMap: Record<string, string> = {\n    pdf: 'file-pdf',\n    doc: 'file-word',\n    docx: 'file-word',\n    xls: 'file-excel',\n    xlsx: 'file-excel',\n    ppt: 'file-powerpoint',\n    pptx: 'file-powerpoint'\n  }\n\n  return iconMap[fileType] || 'file-document'\n}\n\n// 获取文件颜色\nconst getFileColor = (fileType: string) => {\n  const colorMap: Record<string, string> = {\n    pdf: '#EF4444',\n    doc: '#2563EB',\n    docx: '#2563EB',\n    xls: '#10B981',\n    xlsx: '#10B981',\n    ppt: '#F59E0B',\n    pptx: '#F59E0B'\n  }\n\n  return colorMap[fileType] || '#6B7280'\n}\n\n// 移除文档\nconst removeDocument = () => {\n  selectedDocument.value = null\n  translationResult.value = null\n}\n\n// 预览文档\nconst previewDocument = () => {\n  if (!selectedDocument.value) return\n\n  uni.showToast({\n    title: '预览功能开发中',\n    icon: 'none'\n  })\n}\n\n// 开始翻译\nconst startTranslation = async () => {\n  if (!canTranslate.value) return\n\n  try {\n    isTranslating.value = true\n    translationProgress.value = 0\n    processedPages.value = 0\n    totalPages.value = selectedDocument.value.pages || 1\n\n    // 模拟翻译进度\n    const totalSteps = totalPages.value\n    const stepDuration = 2000 // 每页2秒\n\n    for (let i = 0; i < totalSteps; i++) {\n      progressStatus.value = `正在翻译第 ${i + 1} 页...`\n      processedPages.value = i + 1\n\n      // 计算剩余时间\n      const remainingPages = totalSteps - (i + 1)\n      const remainingSeconds = remainingPages * (stepDuration / 1000)\n      estimatedTime.value = formatDuration(remainingSeconds)\n\n      // 更新进度\n      await new Promise(resolve => {\n        const interval = setInterval(() => {\n          translationProgress.value += 2\n          if (translationProgress.value >= ((i + 1) / totalSteps) * 100) {\n            clearInterval(interval)\n            resolve(true)\n          }\n        }, stepDuration / 50)\n      })\n    }\n\n    // 生成翻译结果\n    const outputExt = outputFormat.value === 'same' ? selectedDocument.value.type : outputFormat.value\n    const mockResult = {\n      filename: `translated_${selectedDocument.value.name.split('.')[0]}.${outputExt}`,\n      format: outputExt,\n      size: selectedDocument.value.size * 1.1,\n      processedPages: totalPages.value,\n      wordCount: Math.floor(selectedDocument.value.size / 10),\n      duration: formatDuration(totalSteps * 2),\n      downloadUrl: 'mock-download-url'\n    }\n\n    translationResult.value = mockResult\n\n    // 添加到历史记录\n    const historyItem = {\n      id: Date.now().toString(),\n      filename: selectedDocument.value.name,\n      fileType: selectedDocument.value.type,\n      sourceLang: currentSourceLang.value,\n      targetLang: currentTargetLang.value,\n      status: 'completed',\n      size: selectedDocument.value.size,\n      pages: totalPages.value,\n      createdAt: new Date().toISOString()\n    }\n\n    documentHistory.value.unshift(historyItem)\n    saveDocumentHistory()\n\n    uni.showToast({\n      title: '翻译完成',\n      icon: 'success'\n    })\n\n  } catch (error) {\n    uni.__f__('error','at pages/translation/document/index.vue:530','翻译失败:', error)\n    uni.showToast({\n      title: '翻译失败',\n      icon: 'error'\n    })\n  } finally {\n    isTranslating.value = false\n    estimatedTime.value = '00:00'\n  }\n}\n\n// 交换语言\nconst swapLanguages = () => {\n  translationStore.swapLanguages()\n}\n\n// 语言选择确认\nconst onSourceLanguageConfirm = (value: any) => {\n  translationStore.setSourceLanguage(value[0].value)\n  showSourceLanguagePicker.value = false\n}\n\nconst onTargetLanguageConfirm = (value: any) => {\n  translationStore.setTargetLanguage(value[0].value)\n  showTargetLanguagePicker.value = false\n}\n\n// 下载结果\nconst downloadResult = () => {\n  if (!translationResult.value) return\n\n  uni.showToast({\n    title: '开始下载',\n    icon: 'success'\n  })\n}\n\n// 分享结果\nconst shareResult = () => {\n  if (!translationResult.value) return\n\n  uni.share({\n    provider: 'weixin',\n    scene: 'WXSceneSession',\n    type: 0,\n    summary: `文档翻译完成: ${translationResult.value.filename}`,\n    success: () => {\n      uni.showToast({\n        title: '分享成功',\n        icon: 'success'\n      })\n    }\n  })\n}\n\n// 预览结果\nconst previewResult = () => {\n  if (!translationResult.value) return\n\n  uni.showToast({\n    title: '预览功能开发中',\n    icon: 'none'\n  })\n}\n\n// 工具函数\nconst formatFileSize = (bytes: number) => {\n  if (bytes === 0) return '0 B'\n  const k = 1024\n  const sizes = ['B', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\nconst formatDuration = (seconds: number) => {\n  const mins = Math.floor(seconds / 60)\n  const secs = Math.floor(seconds % 60)\n  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n}\n\nconst formatTime = (time: string) => {\n  return dayjs(time).format('MM-DD HH:mm')\n}\n\n// 历史记录相关\nconst saveDocumentHistory = () => {\n  uni.setStorageSync('document_translation_history', documentHistory.value)\n}\n\nconst loadDocumentHistory = () => {\n  const history = uni.getStorageSync('document_translation_history') || []\n  documentHistory.value = history\n}\n\nconst viewAllHistory = () => {\n  uni.navigateTo({\n    url: '/pages/translation/document/history/index'\n  })\n}\n\nconst viewHistoryItem = (item: any) => {\n  uni.navigateTo({\n    url: `/pages/translation/document/detail/index?id=${item.id}`\n  })\n}\n\n// 页面下拉刷新\nconst onPullDownRefresh = () => {\n  loadDocumentHistory()\n  uni.stopPullDownRefresh()\n}\n\n// 导出方法供页面使用\ndefineExpose({\n  onPullDownRefresh\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.document-translation-page {\n  min-height: 100vh;\n  background-color: $bg-secondary;\n}\n\n// 页面头部\n.page-header {\n  background-color: $bg-primary;\n  padding: $spacing-6 $spacing-4;\n  text-align: center;\n  border-bottom: 1px solid $border-primary;\n\n  .page-title {\n    display: block;\n    font-size: $font-size-2xl;\n    font-weight: $font-weight-bold;\n    color: $text-primary;\n    margin-bottom: $spacing-2;\n  }\n\n  .page-desc {\n    font-size: $font-size-base;\n    color: $text-secondary;\n  }\n}\n\n// 语言选择器\n.language-selector {\n  background-color: $bg-primary;\n  padding: $spacing-4;\n  border-bottom: 1px solid $border-primary;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  .language-item {\n    flex: 1;\n    text-align: center;\n\n    .language-label {\n      display: block;\n      font-size: $font-size-sm;\n      color: $text-tertiary;\n      margin-bottom: $spacing-2;\n    }\n  }\n\n  .swap-button {\n    width: 80rpx;\n    height: 80rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background-color: $bg-tertiary;\n    border-radius: $radius-full;\n    margin: 0 $spacing-4;\n\n    &:active {\n      background-color: $gray-200;\n    }\n  }\n}\n\n// 通用区块标题\n.section-title {\n  padding: $spacing-4 $spacing-4 $spacing-3;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  .title-text {\n    font-size: $font-size-lg;\n    font-weight: $font-weight-semibold;\n    color: $text-primary;\n  }\n\n  .title-desc {\n    font-size: $font-size-sm;\n    color: $text-tertiary;\n  }\n\n  .result-actions {\n    display: flex;\n    align-items: center;\n    gap: $spacing-2;\n  }\n}\n\n// 文档上传区域\n.upload-section {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .upload-area {\n    margin: 0 $spacing-4 $spacing-4;\n    border: 2px dashed $border-primary;\n    border-radius: $radius-lg;\n    padding: $spacing-6;\n\n    .upload-content {\n      text-align: center;\n\n      .upload-text {\n        display: block;\n        font-size: $font-size-lg;\n        font-weight: $font-weight-medium;\n        color: $text-primary;\n        margin: $spacing-4 0 $spacing-2;\n      }\n\n      .upload-desc {\n        display: block;\n        font-size: $font-size-base;\n        color: $text-secondary;\n        margin-bottom: $spacing-2;\n      }\n\n      .upload-limit {\n        font-size: $font-size-sm;\n        color: $text-tertiary;\n      }\n    }\n\n    .file-info {\n      display: flex;\n      align-items: center;\n      gap: $spacing-3;\n\n      .file-icon {\n        width: 80rpx;\n        height: 80rpx;\n        background-color: $bg-tertiary;\n        border-radius: $radius-lg;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n\n      .file-details {\n        flex: 1;\n\n        .file-name {\n          display: block;\n          font-size: $font-size-base;\n          font-weight: $font-weight-medium;\n          color: $text-primary;\n          margin-bottom: $spacing-1;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n\n        .file-size,\n        .file-type,\n        .file-pages {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          margin-bottom: 4rpx;\n        }\n      }\n\n      .file-actions {\n        display: flex;\n        gap: $spacing-2;\n      }\n    }\n  }\n}\n\n// 翻译选项\n.translation-options {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .options-list {\n    padding: 0 $spacing-4;\n\n    .option-item {\n      padding: $spacing-4 0;\n      border-bottom: 1px solid $border-primary;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      .option-label {\n        display: block;\n        font-size: $font-size-base;\n        font-weight: $font-weight-medium;\n        color: $text-primary;\n        margin-bottom: $spacing-3;\n      }\n\n      .option-desc {\n        font-size: $font-size-sm;\n        color: $text-tertiary;\n        margin-left: $spacing-2;\n      }\n\n      .page-range-input {\n        width: 100%;\n        padding: $spacing-2 $spacing-3;\n        border: 1px solid $border-primary;\n        border-radius: $radius-md;\n        font-size: $font-size-base;\n        color: $text-primary;\n        background-color: $bg-tertiary;\n\n        &::placeholder {\n          color: $text-quaternary;\n        }\n      }\n    }\n  }\n\n  .translate-button {\n    padding: 0 $spacing-4 $spacing-4;\n  }\n}\n\n// 翻译进度\n.translation-progress {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n  padding: $spacing-4;\n\n  .progress-header {\n    text-align: center;\n    margin-bottom: $spacing-4;\n\n    .progress-title {\n      display: block;\n      font-size: $font-size-lg;\n      font-weight: $font-weight-semibold;\n      color: $text-primary;\n      margin-bottom: $spacing-1;\n    }\n\n    .progress-desc {\n      font-size: $font-size-sm;\n      color: $text-secondary;\n    }\n  }\n\n  .progress-bar {\n    margin-bottom: $spacing-4;\n  }\n\n  .progress-details {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .progress-text,\n    .progress-time {\n      font-size: $font-size-sm;\n      color: $text-tertiary;\n    }\n  }\n}\n\n// 翻译结果\n.translation-result {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .result-content {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .result-summary {\n      background-color: $success-50;\n      border-radius: $radius-lg;\n      padding: $spacing-4;\n      margin-bottom: $spacing-4;\n\n      .summary-item {\n        display: flex;\n        align-items: center;\n        gap: $spacing-2;\n        margin-bottom: $spacing-3;\n\n        .summary-text {\n          font-size: $font-size-base;\n          font-weight: $font-weight-medium;\n          color: $success-700;\n        }\n      }\n\n      .summary-stats {\n        display: flex;\n        flex-wrap: wrap;\n        gap: $spacing-4;\n\n        .stat-item {\n          font-size: $font-size-sm;\n          color: $success-600;\n        }\n      }\n    }\n\n    .result-file {\n      background-color: $bg-tertiary;\n      border-radius: $radius-lg;\n      padding: $spacing-4;\n\n      .file-preview {\n        display: flex;\n        align-items: center;\n        gap: $spacing-3;\n        margin-bottom: $spacing-4;\n\n        .preview-info {\n          flex: 1;\n\n          .preview-name {\n            display: block;\n            font-size: $font-size-base;\n            font-weight: $font-weight-medium;\n            color: $text-primary;\n            margin-bottom: $spacing-1;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            white-space: nowrap;\n          }\n\n          .preview-size {\n            font-size: $font-size-sm;\n            color: $text-secondary;\n          }\n        }\n      }\n\n      .file-actions {\n        display: flex;\n        gap: $spacing-2;\n      }\n    }\n  }\n}\n\n// 翻译历史\n.translation-history {\n  background-color: $bg-primary;\n\n  .history-list {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .history-item {\n      display: flex;\n      align-items: center;\n      gap: $spacing-3;\n      padding: $spacing-3;\n      background-color: $bg-tertiary;\n      border-radius: $radius-lg;\n      margin-bottom: $spacing-2;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      &:active {\n        background-color: $gray-200;\n      }\n\n      .history-icon {\n        width: 64rpx;\n        height: 64rpx;\n        background-color: $bg-quaternary;\n        border-radius: $radius-lg;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n\n      .history-content {\n        flex: 1;\n\n        .history-title {\n          display: block;\n          font-size: $font-size-base;\n          font-weight: $font-weight-medium;\n          color: $text-primary;\n          margin-bottom: $spacing-1;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n\n        .history-desc {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          margin-bottom: $spacing-1;\n        }\n\n        .history-time {\n          font-size: $font-size-xs;\n          color: $text-tertiary;\n        }\n      }\n\n      .history-status {\n        padding: $spacing-2;\n      }\n    }\n  }\n}\n\n// 响应式适配\n@media screen and (max-width: 480px) {\n  .language-selector {\n    flex-direction: column;\n    gap: $spacing-3;\n\n    .swap-button {\n      transform: rotate(90deg);\n    }\n  }\n\n  .summary-stats {\n    flex-direction: column;\n    gap: $spacing-2;\n  }\n\n  .file-actions {\n    flex-direction: column;\n  }\n}\n\n// 暗色模式适配\n@media (prefers-color-scheme: dark) {\n  .document-translation-page {\n    background-color: #1a1a1a;\n  }\n\n  .page-header,\n  .language-selector,\n  .upload-section,\n  .translation-options,\n  .translation-progress,\n  .translation-result,\n  .translation-history {\n    background-color: #2d2d2d;\n    border-color: #404040;\n  }\n\n  .upload-area,\n  .result-file,\n  .history-item {\n    background-color: #404040;\n    border-color: #555555;\n  }\n\n  .swap-button,\n  .file-icon,\n  .history-icon {\n    background-color: #404040;\n  }\n\n  .result-summary {\n    background-color: #1e3a2e;\n  }\n\n  .page-range-input {\n    background-color: #555555;\n    border-color: #666666;\n    color: #ffffff;\n\n    &::placeholder {\n      color: #888888;\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/AI_system/mobile-uniapp/src/pages/translation/document/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useTranslationStore", "ref", "computed", "onMounted", "uni", "dayjs"], "mappings": ";;;;;;;;;;;;;;;;AAsRA,UAAM,mBAAmBA,0BAAAA;AAGnB,UAAA,mBAAmBC,kBAAS,IAAI;AAChC,UAAA,gBAAgBA,kBAAa,KAAK;AAClC,UAAA,sBAAsBA,kBAAY,CAAC;AACnC,UAAA,iBAAiBA,kBAAY,EAAE;AAC/B,UAAA,iBAAiBA,kBAAY,CAAC;AAC9B,UAAA,aAAaA,kBAAY,CAAC;AAC1B,UAAA,gBAAgBA,kBAAY,OAAO;AACnC,UAAA,oBAAoBA,kBAAS,IAAI;AACjC,UAAA,kBAAkBA,kBAAW,CAAA,CAAE;AAC/B,UAAA,2BAA2BA,kBAAa,KAAK;AAC7C,UAAA,2BAA2BA,kBAAa,KAAK;AAC7C,UAAA,kBAAkBA,kBAAY,MAAM;AACpC,UAAA,YAAYA,kBAAY,EAAE;AAC1B,UAAA,aAAaA,kBAAa,IAAI;AAC9B,UAAA,eAAeA,kBAAY,MAAM;AACjC,UAAA,qBAAqBA,kBAAY,UAAU;AAG3C,UAAA;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACE,IAAA;AAEE,UAAA,eAAeC,cAAAA,SAAS,MAAM;AAClC,aAAO,iBAAiB,SAAS,kBAAkB,SAAS,kBAAkB;AAAA,IAAA,CAC/E;AAEK,UAAA,wBAAwBA,cAAAA,SAAS,MAAM;AAC3C,YAAM,aAAa,CAAC,EAAE,MAAM,QAAQ,OAAO,QAAQ;AAC7C,YAAA,kBAAkB,UAAU,IAAI,CAAS,UAAA;AAAA,QAC7C,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,MACZ,EAAA;AACF,aAAO,CAAC,WAAW,OAAO,eAAe,CAAC;AAAA,IAAA,CAC3C;AAEK,UAAA,wBAAwBA,cAAAA,SAAS,MAAM;AACrC,YAAA,kBAAkB,UAAU,OAAO,CAAA,SAAQ,KAAK,SAAS,MAAM,EAAE,IAAI,CAAS,UAAA;AAAA,QAClF,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,MACZ,EAAA;AACF,aAAO,CAAC,eAAe;AAAA,IAAA,CACxB;AAGDC,kBAAAA,UAAU,YAAY;AACpB,YAAM,iBAAiB;AACH;IAAA,CACrB;AAGD,UAAM,iBAAiB,MAAM;AAe3BC,oBAAAA,MAAI,WAAW;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW,CAAC,QAAQ,QAAQ,SAAS,QAAQ,SAAS,QAAQ,OAAO;AAAA,QACrE,SAAS,CAAC,QAAQ;AACV,gBAAA,OAAO,IAAI,UAAU,CAAC;AAC5B,6BAAmB,IAAI;AAAA,QACzB;AAAA,QACA,MAAM,CAAC,UAAU;AACfA,wBAAA,MAAI,MAAM,SAAQ,+CAA8C,WAAW,KAAK;AAAA,QAClF;AAAA,MAAA,CACD;AAAA,IAAA;AAKG,UAAA,qBAAqB,CAAC,SAAc;AAExC,UAAI,KAAK,OAAO,MAAM,OAAO,MAAM;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MACF;AAEM,YAAA,gBAAgB,iBAAiB,KAAK,IAAI;AAEhD,uBAAiB,QAAQ;AAAA,QACvB,MAAM,KAAK;AAAA,QACX,MAAM,KAAK;AAAA,QACX,MAAM;AAAA,QACN,MAAM,KAAK,QAAQ,IAAI,gBAAgB,IAAI;AAAA,QAC3C,OAAO,kBAAkB,eAAe,KAAK,IAAI;AAAA,MAAA;AAInD,wBAAkB,QAAQ;AAAA,IAAA;AAItB,UAAA,mBAAmB,CAAC,aAAqB;;AAC7C,eAAO,cAAS,MAAM,GAAG,EAAE,UAApB,mBAA2B,kBAAiB;AAAA,IAAA;AAI/C,UAAA,oBAAoB,CAAC,UAAkB,aAAqB;AAEhE,YAAM,cAAc;AAAA,QAClB,KAAK,MAAM;AAAA;AAAA,QACX,KAAK,KAAK;AAAA;AAAA,QACV,MAAM,KAAK;AAAA;AAAA,QACX,KAAK,KAAK;AAAA;AAAA,QACV,MAAM,KAAK;AAAA;AAAA,QACX,KAAK,MAAM;AAAA;AAAA,QACX,MAAM,MAAM;AAAA;AAAA,MAAA;AAGd,YAAM,UAAU,YAAY,QAAoC,KAAK,KAAK;AAC1E,aAAO,KAAK,IAAI,GAAG,KAAK,KAAK,WAAW,OAAO,CAAC;AAAA,IAAA;AAI5C,UAAA,cAAc,CAAC,aAAqB;AACxC,YAAM,UAAkC;AAAA,QACtC,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MAAA;AAGD,aAAA,QAAQ,QAAQ,KAAK;AAAA,IAAA;AAIxB,UAAA,eAAe,CAAC,aAAqB;AACzC,YAAM,WAAmC;AAAA,QACvC,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MAAA;AAGD,aAAA,SAAS,QAAQ,KAAK;AAAA,IAAA;AAI/B,UAAM,iBAAiB,MAAM;AAC3B,uBAAiB,QAAQ;AACzB,wBAAkB,QAAQ;AAAA,IAAA;AAI5B,UAAM,kBAAkB,MAAM;AAC5B,UAAI,CAAC,iBAAiB;AAAO;AAE7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIH,UAAM,mBAAmB,YAAY;AACnC,UAAI,CAAC,aAAa;AAAO;AAErB,UAAA;AACF,sBAAc,QAAQ;AACtB,4BAAoB,QAAQ;AAC5B,uBAAe,QAAQ;AACZ,mBAAA,QAAQ,iBAAiB,MAAM,SAAS;AAGnD,cAAM,aAAa,WAAW;AAC9B,cAAM,eAAe;AAErB,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACpB,yBAAA,QAAQ,SAAS,IAAI,CAAC;AACrC,yBAAe,QAAQ,IAAI;AAGrB,gBAAA,iBAAiB,cAAc,IAAI;AACnC,gBAAA,mBAAmB,kBAAkB,eAAe;AAC5C,wBAAA,QAAQ,eAAe,gBAAgB;AAG/C,gBAAA,IAAI,QAAQ,CAAW,YAAA;AACrB,kBAAA,WAAW,YAAY,MAAM;AACjC,kCAAoB,SAAS;AAC7B,kBAAI,oBAAoB,UAAW,IAAI,KAAK,aAAc,KAAK;AAC7D,8BAAc,QAAQ;AACtB,wBAAQ,IAAI;AAAA,cACd;AAAA,YAAA,GACC,eAAe,EAAE;AAAA,UAAA,CACrB;AAAA,QACH;AAGA,cAAM,YAAY,aAAa,UAAU,SAAS,iBAAiB,MAAM,OAAO,aAAa;AAC7F,cAAM,aAAa;AAAA,UACjB,UAAU,cAAc,iBAAiB,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC,IAAI,SAAS;AAAA,UAC9E,QAAQ;AAAA,UACR,MAAM,iBAAiB,MAAM,OAAO;AAAA,UACpC,gBAAgB,WAAW;AAAA,UAC3B,WAAW,KAAK,MAAM,iBAAiB,MAAM,OAAO,EAAE;AAAA,UACtD,UAAU,eAAe,aAAa,CAAC;AAAA,UACvC,aAAa;AAAA,QAAA;AAGf,0BAAkB,QAAQ;AAG1B,cAAM,cAAc;AAAA,UAClB,IAAI,KAAK,IAAI,EAAE,SAAS;AAAA,UACxB,UAAU,iBAAiB,MAAM;AAAA,UACjC,UAAU,iBAAiB,MAAM;AAAA,UACjC,YAAY,kBAAkB;AAAA,UAC9B,YAAY,kBAAkB;AAAA,UAC9B,QAAQ;AAAA,UACR,MAAM,iBAAiB,MAAM;AAAA,UAC7B,OAAO,WAAW;AAAA,UAClB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAAA;AAGpB,wBAAA,MAAM,QAAQ,WAAW;AACrB;AAEpBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,eAEM,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,+CAA8C,SAAS,KAAK;AAC9EA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,sBAAc,QAAQ;AACtB,sBAAc,QAAQ;AAAA,MACxB;AAAA,IAAA;AAIF,UAAM,gBAAgB,MAAM;AAC1B,uBAAiB,cAAc;AAAA,IAAA;AAI3B,UAAA,0BAA0B,CAAC,UAAe;AAC9C,uBAAiB,kBAAkB,MAAM,CAAC,EAAE,KAAK;AACjD,+BAAyB,QAAQ;AAAA,IAAA;AAG7B,UAAA,0BAA0B,CAAC,UAAe;AAC9C,uBAAiB,kBAAkB,MAAM,CAAC,EAAE,KAAK;AACjD,+BAAyB,QAAQ;AAAA,IAAA;AAInC,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,kBAAkB;AAAO;AAE9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIH,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,kBAAkB;AAAO;AAE9BA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,SAAS,WAAW,kBAAkB,MAAM,QAAQ;AAAA,QACpD,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,gBAAgB,MAAM;AAC1B,UAAI,CAAC,kBAAkB;AAAO;AAE9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIG,UAAA,iBAAiB,CAAC,UAAkB;AACxC,UAAI,UAAU;AAAU,eAAA;AACxB,YAAM,IAAI;AACV,YAAM,QAAQ,CAAC,KAAK,MAAM,MAAM,IAAI;AAC9B,YAAA,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,CAAC;AAClD,aAAO,YAAY,QAAQ,KAAK,IAAI,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC;AAAA,IAAA;AAGlE,UAAA,iBAAiB,CAAC,YAAoB;AAC1C,YAAM,OAAO,KAAK,MAAM,UAAU,EAAE;AACpC,YAAM,OAAO,KAAK,MAAM,UAAU,EAAE;AACpC,aAAO,GAAG,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IAAA;AAG1E,UAAA,aAAa,CAAC,SAAiB;AACnC,aAAOC,cAAM,MAAA,IAAI,EAAE,OAAO,aAAa;AAAA,IAAA;AAIzC,UAAM,sBAAsB,MAAM;AAC5BD,oBAAAA,MAAA,eAAe,gCAAgC,gBAAgB,KAAK;AAAA,IAAA;AAG1E,UAAM,sBAAsB,MAAM;AAChC,YAAM,UAAUA,cAAA,MAAI,eAAe,8BAA8B,KAAK,CAAA;AACtE,sBAAgB,QAAQ;AAAA,IAAA;AAG1B,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MAAA,CACN;AAAA,IAAA;AAGG,UAAA,kBAAkB,CAAC,SAAc;AACrCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,+CAA+C,KAAK,EAAE;AAAA,MAAA,CAC5D;AAAA,IAAA;AAIH,UAAM,oBAAoB,MAAM;AACV;AACpBA,oBAAA,MAAI,oBAAoB;AAAA,IAAA;AAIb,aAAA;AAAA,MACX;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnoBD,GAAG,WAAW,eAAe;"}