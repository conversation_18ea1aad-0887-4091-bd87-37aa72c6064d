{"version": 3, "file": "index.js", "sources": ["pages/user/profile/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9wcm9maWxlL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"profile-page\">\n    <!-- 用户信息头部 -->\n    <view class=\"profile-header\">\n      <view class=\"header-bg\"></view>\n      <view class=\"user-info\">\n        <view class=\"avatar-container\" @click=\"changeAvatar\">\n          <image \n            class=\"user-avatar\" \n            :src=\"userInfo?.avatar || '/static/images/default-avatar.png'\"\n            mode=\"aspectFill\"\n          ></image>\n          <view class=\"avatar-edit\">\n            <u-icon name=\"camera\" size=\"16\" color=\"#fff\"></u-icon>\n          </view>\n        </view>\n        \n        <view class=\"user-details\">\n          <text class=\"user-name\">{{ userDisplayName }}</text>\n          <text class=\"user-email\" v-if=\"userInfo?.email\">{{ userInfo.email }}</text>\n          <view class=\"user-stats\">\n            <view class=\"stat-item\">\n              <text class=\"stat-number\">{{ userStats?.total_translations || 0 }}</text>\n              <text class=\"stat-label\">翻译次数</text>\n            </view>\n            <view class=\"stat-item\">\n              <text class=\"stat-number\">{{ userStats?.total_conversations || 0 }}</text>\n              <text class=\"stat-label\">对话次数</text>\n            </view>\n            <view class=\"stat-item\">\n              <text class=\"stat-number\">{{ userStats?.total_agents || 0 }}</text>\n              <text class=\"stat-label\">智能体</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 快捷功能 -->\n    <view class=\"quick-actions\">\n      <view \n        class=\"action-item\"\n        v-for=\"action in quickActions\"\n        :key=\"action.id\"\n        @click=\"handleQuickAction(action)\"\n      >\n        <view class=\"action-icon\" :style=\"{ backgroundColor: action.color }\">\n          <u-icon :name=\"action.icon\" color=\"#fff\" size=\"20\"></u-icon>\n        </view>\n        <text class=\"action-title\">{{ action.title }}</text>\n        <view class=\"action-badge\" v-if=\"action.badge\">\n          <text class=\"badge-text\">{{ action.badge }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 功能菜单 -->\n    <view class=\"menu-sections\">\n      <!-- 我的内容 -->\n      <view class=\"menu-section\">\n        <view class=\"section-title\">\n          <text class=\"title-text\">我的内容</text>\n        </view>\n        <view class=\"menu-list\">\n          <view \n            class=\"menu-item\"\n            v-for=\"item in myContentMenus\"\n            :key=\"item.id\"\n            @click=\"navigateTo(item.path)\"\n          >\n            <view class=\"menu-icon\">\n              <u-icon :name=\"item.icon\" :color=\"item.color\" size=\"20\"></u-icon>\n            </view>\n            <text class=\"menu-title\">{{ item.title }}</text>\n            <view class=\"menu-extra\" v-if=\"item.count !== undefined\">\n              <text class=\"extra-text\">{{ item.count }}</text>\n            </view>\n            <u-icon name=\"arrow-right\" color=\"#C4C4C4\" size=\"16\"></u-icon>\n          </view>\n        </view>\n      </view>\n\n      <!-- 应用设置 -->\n      <view class=\"menu-section\">\n        <view class=\"section-title\">\n          <text class=\"title-text\">应用设置</text>\n        </view>\n        <view class=\"menu-list\">\n          <view \n            class=\"menu-item\"\n            v-for=\"item in settingsMenus\"\n            :key=\"item.id\"\n            @click=\"handleSettingsMenu(item)\"\n          >\n            <view class=\"menu-icon\">\n              <u-icon :name=\"item.icon\" :color=\"item.color\" size=\"20\"></u-icon>\n            </view>\n            <text class=\"menu-title\">{{ item.title }}</text>\n            <view class=\"menu-extra\" v-if=\"item.extra\">\n              <text class=\"extra-text\">{{ item.extra }}</text>\n            </view>\n            <u-icon name=\"arrow-right\" color=\"#C4C4C4\" size=\"16\"></u-icon>\n          </view>\n        </view>\n      </view>\n\n      <!-- 帮助与反馈 -->\n      <view class=\"menu-section\">\n        <view class=\"section-title\">\n          <text class=\"title-text\">帮助与反馈</text>\n        </view>\n        <view class=\"menu-list\">\n          <view \n            class=\"menu-item\"\n            v-for=\"item in helpMenus\"\n            :key=\"item.id\"\n            @click=\"handleHelpMenu(item)\"\n          >\n            <view class=\"menu-icon\">\n              <u-icon :name=\"item.icon\" :color=\"item.color\" size=\"20\"></u-icon>\n            </view>\n            <text class=\"menu-title\">{{ item.title }}</text>\n            <view class=\"menu-extra\" v-if=\"item.badge\">\n              <view class=\"badge-dot\"></view>\n            </view>\n            <u-icon name=\"arrow-right\" color=\"#C4C4C4\" size=\"16\"></u-icon>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 退出登录 -->\n    <view class=\"logout-section\">\n      <u-button \n        type=\"error\" \n        size=\"large\"\n        :custom-style=\"{ width: '100%', marginTop: '40rpx' }\"\n        @click=\"handleLogout\"\n      >\n        退出登录\n      </u-button>\n    </view>\n\n    <!-- 版本信息 -->\n    <view class=\"version-info\">\n      <text class=\"version-text\">版本 {{ appVersion }}</text>\n    </view>\n\n    <!-- 头像选择弹窗 -->\n    <u-action-sheet \n      v-model=\"showAvatarSheet\"\n      :actions=\"avatarActions\"\n      @click=\"handleAvatarAction\"\n    ></u-action-sheet>\n\n    <!-- 语言选择弹窗 -->\n    <u-picker\n      v-model=\"showLanguagePicker\"\n      :columns=\"languageColumns\"\n      @confirm=\"onLanguageConfirm\"\n      @cancel=\"showLanguagePicker = false\"\n    ></u-picker>\n\n    <!-- 主题选择弹窗 -->\n    <u-action-sheet \n      v-model=\"showThemeSheet\"\n      :actions=\"themeActions\"\n      @click=\"handleThemeAction\"\n    ></u-action-sheet>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\nimport { useUserStore } from '@/store/modules/user'\n\n// Store\nconst userStore = useUserStore()\n\n// 响应式数据\nconst userStats = ref<any>(null)\nconst showAvatarSheet = ref<boolean>(false)\nconst showLanguagePicker = ref<boolean>(false)\nconst showThemeSheet = ref<boolean>(false)\nconst appVersion = ref<string>('1.0.0')\n\n// 计算属性\nconst { userInfo, userDisplayName } = userStore\n\n// 快捷功能\nconst quickActions = ref([\n  {\n    id: 'favorites',\n    title: '我的收藏',\n    icon: 'heart-fill',\n    color: '#EF4444',\n    badge: '12',\n    path: '/pages/user/favorites/index'\n  },\n  {\n    id: 'history',\n    title: '使用历史',\n    icon: 'clock-fill',\n    color: '#2563EB',\n    path: '/pages/user/history/index'\n  },\n  {\n    id: 'downloads',\n    title: '我的下载',\n    icon: 'download',\n    color: '#10B981',\n    path: '/pages/user/downloads/index'\n  },\n  {\n    id: 'wallet',\n    title: '我的钱包',\n    icon: 'wallet',\n    color: '#F59E0B',\n    path: '/pages/user/wallet/index'\n  }\n])\n\n// 我的内容菜单\nconst myContentMenus = ref([\n  {\n    id: 'translations',\n    title: '翻译记录',\n    icon: 'globe',\n    color: '#2563EB',\n    count: 156,\n    path: '/pages/user/translations/index'\n  },\n  {\n    id: 'digital-humans',\n    title: '数字人对话',\n    icon: 'account-circle',\n    color: '#10B981',\n    count: 23,\n    path: '/pages/user/digital-humans/index'\n  },\n  {\n    id: 'ai-agents',\n    title: 'AI智能体',\n    icon: 'robot',\n    color: '#F59E0B',\n    count: 8,\n    path: '/pages/user/ai-agents/index'\n  },\n  {\n    id: 'documents',\n    title: '文档转换',\n    icon: 'file-document',\n    color: '#8B5CF6',\n    count: 45,\n    path: '/pages/user/documents/index'\n  }\n])\n\n// 设置菜单\nconst settingsMenus = ref([\n  {\n    id: 'account',\n    title: '账号设置',\n    icon: 'account-settings',\n    color: '#6B7280',\n    path: '/pages/user/account/index'\n  },\n  {\n    id: 'privacy',\n    title: '隐私设置',\n    icon: 'shield-check',\n    color: '#059669',\n    path: '/pages/user/privacy/index'\n  },\n  {\n    id: 'notifications',\n    title: '消息通知',\n    icon: 'bell',\n    color: '#DC2626',\n    extra: '开启'\n  },\n  {\n    id: 'language',\n    title: '语言设置',\n    icon: 'translate',\n    color: '#2563EB',\n    extra: '中文'\n  },\n  {\n    id: 'theme',\n    title: '主题设置',\n    icon: 'palette',\n    color: '#7C3AED',\n    extra: '跟随系统'\n  },\n  {\n    id: 'cache',\n    title: '清理缓存',\n    icon: 'delete-sweep',\n    color: '#EF4444',\n    extra: '125MB'\n  }\n])\n\n// 帮助菜单\nconst helpMenus = ref([\n  {\n    id: 'help',\n    title: '使用帮助',\n    icon: 'help-circle',\n    color: '#2563EB',\n    path: '/pages/help/index'\n  },\n  {\n    id: 'feedback',\n    title: '意见反馈',\n    icon: 'message-text',\n    color: '#10B981',\n    path: '/pages/feedback/index'\n  },\n  {\n    id: 'about',\n    title: '关于我们',\n    icon: 'information',\n    color: '#6B7280',\n    path: '/pages/about/index'\n  },\n  {\n    id: 'update',\n    title: '检查更新',\n    icon: 'update',\n    color: '#F59E0B',\n    badge: true\n  }\n])\n\n// 头像操作选项\nconst avatarActions = ref([\n  { name: '拍照', value: 'camera' },\n  { name: '从相册选择', value: 'album' },\n  { name: '查看头像', value: 'view' }\n])\n\n// 语言选项\nconst languageColumns = ref([[\n  { text: '中文', value: 'zh-CN' },\n  { text: 'English', value: 'en-US' },\n  { text: '日本語', value: 'ja-JP' },\n  { text: '한국어', value: 'ko-KR' }\n]])\n\n// 主题选项\nconst themeActions = ref([\n  { name: '跟随系统', value: 'auto' },\n  { name: '浅色模式', value: 'light' },\n  { name: '深色模式', value: 'dark' }\n])\n\n// 页面加载\nonMounted(async () => {\n  await loadUserStats()\n\n  // 获取应用版本信息\n\n\n\n\n\n})\n\n// 加载用户统计信息\nconst loadUserStats = async () => {\n  try {\n    const stats = await userStore.getUserStats()\n    if (stats) {\n      userStats.value = stats\n    }\n  } catch (error) {\n    uni.__f__('error','at pages/user/profile/index.vue:379','加载用户统计失败:', error)\n  }\n}\n\n// 处理快捷功能\nconst handleQuickAction = (action: any) => {\n  if (action.path) {\n    navigateTo(action.path)\n  }\n}\n\n// 处理设置菜单\nconst handleSettingsMenu = (item: any) => {\n  switch (item.id) {\n    case 'notifications':\n      toggleNotifications()\n      break\n    case 'language':\n      showLanguagePicker.value = true\n      break\n    case 'theme':\n      showThemeSheet.value = true\n      break\n    case 'cache':\n      clearCache()\n      break\n    default:\n      if (item.path) {\n        navigateTo(item.path)\n      }\n  }\n}\n\n// 处理帮助菜单\nconst handleHelpMenu = (item: any) => {\n  switch (item.id) {\n    case 'update':\n      checkForUpdate()\n      break\n    default:\n      if (item.path) {\n        navigateTo(item.path)\n      }\n  }\n}\n\n// 更换头像\nconst changeAvatar = () => {\n  showAvatarSheet.value = true\n}\n\n// 处理头像操作\nconst handleAvatarAction = (action: any) => {\n  showAvatarSheet.value = false\n\n  switch (action.value) {\n    case 'camera':\n      chooseAvatar('camera')\n      break\n    case 'album':\n      chooseAvatar('album')\n      break\n    case 'view':\n      previewAvatar()\n      break\n  }\n}\n\n// 选择头像\nconst chooseAvatar = (sourceType: 'camera' | 'album') => {\n  uni.chooseImage({\n    count: 1,\n    sourceType: [sourceType],\n    success: async (res) => {\n      const filePath = res.tempFilePaths[0]\n\n      try {\n        uni.showLoading({ title: '上传中...' })\n\n        const result = await userStore.uploadAvatar(filePath)\n\n        uni.hideLoading()\n\n        if (result.success) {\n          uni.showToast({\n            title: '头像更新成功',\n            icon: 'success'\n          })\n        }\n      } catch (error) {\n        uni.hideLoading()\n        uni.__f__('error','at pages/user/profile/index.vue:470','上传头像失败:', error)\n      }\n    },\n    fail: (error) => {\n      uni.__f__('error','at pages/user/profile/index.vue:474','选择图片失败:', error)\n    }\n  })\n}\n\n// 预览头像\nconst previewAvatar = () => {\n  if (userInfo.value?.avatar) {\n    uni.previewImage({\n      urls: [userInfo.value.avatar],\n      current: 0\n    })\n  }\n}\n\n// 切换通知设置\nconst toggleNotifications = () => {\n  uni.showModal({\n    title: '消息通知',\n    content: '是否开启消息通知？',\n    success: (res) => {\n      if (res.confirm) {\n        // 处理通知设置\n        uni.showToast({\n          title: '设置成功',\n          icon: 'success'\n        })\n      }\n    }\n  })\n}\n\n// 语言选择确认\nconst onLanguageConfirm = (value: any) => {\n  const language = value[0].value\n\n  // 更新语言设置\n  uni.setStorageSync('app_language', language)\n\n  // 更新菜单显示\n  const languageMenu = settingsMenus.value.find(m => m.id === 'language')\n  if (languageMenu) {\n    languageMenu.extra = value[0].text\n  }\n\n  showLanguagePicker.value = false\n\n  uni.showToast({\n    title: '语言设置成功',\n    icon: 'success'\n  })\n}\n\n// 处理主题选择\nconst handleThemeAction = (action: any) => {\n  showThemeSheet.value = false\n\n  // 更新主题设置\n  uni.setStorageSync('app_theme', action.value)\n\n  // 更新菜单显示\n  const themeMenu = settingsMenus.value.find(m => m.id === 'theme')\n  if (themeMenu) {\n    themeMenu.extra = action.name\n  }\n\n  uni.showToast({\n    title: '主题设置成功',\n    icon: 'success'\n  })\n}\n\n// 清理缓存\nconst clearCache = () => {\n  uni.showModal({\n    title: '清理缓存',\n    content: '确定要清理应用缓存吗？这将删除临时文件和图片缓存。',\n    success: (res) => {\n      if (res.confirm) {\n        uni.showLoading({ title: '清理中...' })\n\n        // 模拟清理过程\n        setTimeout(() => {\n          uni.hideLoading()\n\n          // 更新缓存大小显示\n          const cacheMenu = settingsMenus.value.find(m => m.id === 'cache')\n          if (cacheMenu) {\n            cacheMenu.extra = '0MB'\n          }\n\n          uni.showToast({\n            title: '清理完成',\n            icon: 'success'\n          })\n        }, 2000)\n      }\n    }\n  })\n}\n\n// 检查更新\nconst checkForUpdate = () => {\n  uni.showLoading({ title: '检查中...' })\n\n  // 模拟检查更新\n  setTimeout(() => {\n    uni.hideLoading()\n\n    uni.showModal({\n      title: '检查更新',\n      content: '当前已是最新版本',\n      showCancel: false,\n      confirmText: '确定'\n    })\n  }, 1500)\n}\n\n// 退出登录\nconst handleLogout = () => {\n  uni.showModal({\n    title: '退出登录',\n    content: '确定要退出当前账号吗？',\n    success: async (res) => {\n      if (res.confirm) {\n        try {\n          await userStore.logout()\n        } catch (error) {\n          uni.__f__('error','at pages/user/profile/index.vue:602','退出登录失败:', error)\n        }\n      }\n    }\n  })\n}\n\n// 页面导航\nconst navigateTo = (path: string) => {\n  uni.navigateTo({\n    url: path,\n    fail: (err) => {\n      uni.__f__('error','at pages/user/profile/index.vue:614','页面跳转失败:', err)\n      uni.showToast({\n        title: '页面跳转失败',\n        icon: 'error'\n      })\n    }\n  })\n}\n\n// 页面下拉刷新\nconst onPullDownRefresh = async () => {\n  try {\n    await loadUserStats()\n    uni.showToast({\n      title: '刷新成功',\n      icon: 'success'\n    })\n  } catch (error) {\n    uni.showToast({\n      title: '刷新失败',\n      icon: 'error'\n    })\n  } finally {\n    uni.stopPullDownRefresh()\n  }\n}\n\n// 导出方法供页面使用\ndefineExpose({\n  onPullDownRefresh\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.profile-page {\n  min-height: 100vh;\n  background-color: $bg-secondary;\n}\n\n// 用户信息头部\n.profile-header {\n  position: relative;\n  background-color: $bg-primary;\n  padding-bottom: $spacing-6;\n\n  .header-bg {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 300rpx;\n    background: linear-gradient(135deg, #667eea, #764ba2);\n  }\n\n  .user-info {\n    position: relative;\n    z-index: 1;\n    padding: $spacing-6 $spacing-4;\n    display: flex;\n    align-items: center;\n    gap: $spacing-4;\n\n    .avatar-container {\n      position: relative;\n\n      .user-avatar {\n        width: 160rpx;\n        height: 160rpx;\n        border-radius: $radius-full;\n        border: 4px solid rgba(255, 255, 255, 0.2);\n      }\n\n      .avatar-edit {\n        position: absolute;\n        bottom: 8rpx;\n        right: 8rpx;\n        width: 48rpx;\n        height: 48rpx;\n        background-color: #667eea;\n        border-radius: $radius-full;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        border: 2px solid white;\n      }\n    }\n\n    .user-details {\n      flex: 1;\n      color: white;\n\n      .user-name {\n        display: block;\n        font-size: $font-size-xl;\n        font-weight: $font-weight-bold;\n        margin-bottom: $spacing-1;\n      }\n\n      .user-email {\n        display: block;\n        font-size: $font-size-sm;\n        opacity: 0.8;\n        margin-bottom: $spacing-3;\n      }\n\n      .user-stats {\n        display: flex;\n        gap: $spacing-6;\n\n        .stat-item {\n          text-align: center;\n\n          .stat-number {\n            display: block;\n            font-size: $font-size-lg;\n            font-weight: $font-weight-bold;\n            margin-bottom: 4rpx;\n          }\n\n          .stat-label {\n            font-size: $font-size-xs;\n            opacity: 0.8;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 快捷功能\n.quick-actions {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: $spacing-4;\n  padding: $spacing-4;\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .action-item {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    text-align: center;\n    position: relative;\n\n    .action-icon {\n      width: 96rpx;\n      height: 96rpx;\n      border-radius: $radius-2xl;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: $spacing-2;\n    }\n\n    .action-title {\n      font-size: $font-size-sm;\n      color: $text-primary;\n    }\n\n    .action-badge {\n      position: absolute;\n      top: -8rpx;\n      right: 16rpx;\n      background-color: $error;\n      border-radius: $radius-full;\n      padding: 4rpx 12rpx;\n      min-width: 32rpx;\n\n      .badge-text {\n        font-size: $font-size-xs;\n        color: white;\n        text-align: center;\n      }\n    }\n  }\n}\n\n// 菜单区域\n.menu-sections {\n  .menu-section {\n    background-color: $bg-primary;\n    margin-bottom: $spacing-3;\n\n    .section-title {\n      padding: $spacing-4 $spacing-4 $spacing-2;\n\n      .title-text {\n        font-size: $font-size-base;\n        font-weight: $font-weight-semibold;\n        color: $text-secondary;\n      }\n    }\n\n    .menu-list {\n      .menu-item {\n        display: flex;\n        align-items: center;\n        padding: $spacing-4;\n        border-bottom: 1px solid $border-primary;\n\n        &:last-child {\n          border-bottom: none;\n        }\n\n        &:active {\n          background-color: $bg-tertiary;\n        }\n\n        .menu-icon {\n          width: 64rpx;\n          height: 64rpx;\n          background-color: $bg-tertiary;\n          border-radius: $radius-lg;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin-right: $spacing-3;\n        }\n\n        .menu-title {\n          flex: 1;\n          font-size: $font-size-base;\n          color: $text-primary;\n        }\n\n        .menu-extra {\n          margin-right: $spacing-2;\n\n          .extra-text {\n            font-size: $font-size-sm;\n            color: $text-tertiary;\n          }\n\n          .badge-dot {\n            width: 16rpx;\n            height: 16rpx;\n            background-color: $error;\n            border-radius: $radius-full;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 退出登录区域\n.logout-section {\n  padding: $spacing-4;\n}\n\n// 版本信息\n.version-info {\n  text-align: center;\n  padding: $spacing-4;\n\n  .version-text {\n    font-size: $font-size-sm;\n    color: $text-quaternary;\n  }\n}\n\n// 响应式适配\n@media screen and (max-width: 480px) {\n  .profile-header .user-info {\n    flex-direction: column;\n    text-align: center;\n\n    .user-details .user-stats {\n      justify-content: center;\n    }\n  }\n\n  .quick-actions {\n    grid-template-columns: repeat(2, 1fr);\n    gap: $spacing-6;\n\n    .action-item .action-icon {\n      width: 120rpx;\n      height: 120rpx;\n    }\n  }\n}\n\n// 暗色模式适配\n@media (prefers-color-scheme: dark) {\n  .profile-page {\n    background-color: #1a1a1a;\n  }\n\n  .profile-header {\n    background-color: #2d2d2d;\n\n    .header-bg {\n      background: linear-gradient(135deg, #1e40af, #3b82f6);\n    }\n  }\n\n  .quick-actions,\n  .menu-section {\n    background-color: #2d2d2d;\n    border-color: #404040;\n  }\n\n  .menu-item {\n    border-color: #404040;\n\n    &:active {\n      background-color: #404040;\n    }\n  }\n\n  .menu-icon {\n    background-color: #404040;\n  }\n\n  .user-details {\n    color: #ffffff;\n  }\n\n  .menu-title {\n    color: #ffffff;\n  }\n\n  .extra-text {\n    color: #cccccc;\n  }\n\n  .version-text {\n    color: #888888;\n  }\n}\n\n// 安全区域适配\n.profile-header {\n  padding-top: calc($spacing-6 + env(safe-area-inset-top));\n}\n\n.logout-section {\n  padding-bottom: calc($spacing-4 + env(safe-area-inset-bottom));\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/AI_system/mobile-uniapp/src/pages/user/profile/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "onMounted", "uni"], "mappings": ";;;;;;;;;;;;;AAiLA,UAAM,YAAYA,mBAAAA;AAGZ,UAAA,YAAYC,kBAAS,IAAI;AACzB,UAAA,kBAAkBA,kBAAa,KAAK;AACpC,UAAA,qBAAqBA,kBAAa,KAAK;AACvC,UAAA,iBAAiBA,kBAAa,KAAK;AACnC,UAAA,aAAaA,kBAAY,OAAO;AAGhC,UAAA,EAAE,UAAU,gBAAoB,IAAA;AAGtC,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACvB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,IAAA,CACD;AAGD,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,IAAA,CACD;AAGD,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,IAAA,CACD;AAGD,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,IAAA,CACD;AAGD,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB,EAAE,MAAM,MAAM,OAAO,SAAS;AAAA,MAC9B,EAAE,MAAM,SAAS,OAAO,QAAQ;AAAA,MAChC,EAAE,MAAM,QAAQ,OAAO,OAAO;AAAA,IAAA,CAC/B;AAGK,UAAA,kBAAkBA,cAAAA,IAAI,CAAC;AAAA,MAC3B,EAAE,MAAM,MAAM,OAAO,QAAQ;AAAA,MAC7B,EAAE,MAAM,WAAW,OAAO,QAAQ;AAAA,MAClC,EAAE,MAAM,OAAO,OAAO,QAAQ;AAAA,MAC9B,EAAE,MAAM,OAAO,OAAO,QAAQ;AAAA,IAC/B,CAAA,CAAC;AAGF,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACvB,EAAE,MAAM,QAAQ,OAAO,OAAO;AAAA,MAC9B,EAAE,MAAM,QAAQ,OAAO,QAAQ;AAAA,MAC/B,EAAE,MAAM,QAAQ,OAAO,OAAO;AAAA,IAAA,CAC/B;AAGDC,kBAAAA,UAAU,YAAY;AACpB,YAAM,cAAc;AAAA,IAAA,CAQrB;AAGD,UAAM,gBAAgB,YAAY;AAC5B,UAAA;AACI,cAAA,QAAQ,MAAM,UAAU;AAC9B,YAAI,OAAO;AACT,oBAAU,QAAQ;AAAA,QACpB;AAAA,eACO,OAAO;AACdC,sBAAA,MAAI,MAAM,SAAQ,uCAAsC,aAAa,KAAK;AAAA,MAC5E;AAAA,IAAA;AAII,UAAA,oBAAoB,CAAC,WAAgB;AACzC,UAAI,OAAO,MAAM;AACf,mBAAW,OAAO,IAAI;AAAA,MACxB;AAAA,IAAA;AAII,UAAA,qBAAqB,CAAC,SAAc;AACxC,cAAQ,KAAK,IAAI;AAAA,QACf,KAAK;AACiB;AACpB;AAAA,QACF,KAAK;AACH,6BAAmB,QAAQ;AAC3B;AAAA,QACF,KAAK;AACH,yBAAe,QAAQ;AACvB;AAAA,QACF,KAAK;AACQ;AACX;AAAA,QACF;AACE,cAAI,KAAK,MAAM;AACb,uBAAW,KAAK,IAAI;AAAA,UACtB;AAAA,MACJ;AAAA,IAAA;AAII,UAAA,iBAAiB,CAAC,SAAc;AACpC,cAAQ,KAAK,IAAI;AAAA,QACf,KAAK;AACY;AACf;AAAA,QACF;AACE,cAAI,KAAK,MAAM;AACb,uBAAW,KAAK,IAAI;AAAA,UACtB;AAAA,MACJ;AAAA,IAAA;AAIF,UAAM,eAAe,MAAM;AACzB,sBAAgB,QAAQ;AAAA,IAAA;AAIpB,UAAA,qBAAqB,CAAC,WAAgB;AAC1C,sBAAgB,QAAQ;AAExB,cAAQ,OAAO,OAAO;AAAA,QACpB,KAAK;AACH,uBAAa,QAAQ;AACrB;AAAA,QACF,KAAK;AACH,uBAAa,OAAO;AACpB;AAAA,QACF,KAAK;AACW;AACd;AAAA,MACJ;AAAA,IAAA;AAII,UAAA,eAAe,CAAC,eAAmC;AACvDA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,YAAY,CAAC,UAAU;AAAA,QACvB,SAAS,OAAO,QAAQ;AAChB,gBAAA,WAAW,IAAI,cAAc,CAAC;AAEhC,cAAA;AACFA,0BAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAEnC,kBAAM,SAAS,MAAM,UAAU,aAAa,QAAQ;AAEpDA,0BAAA,MAAI,YAAY;AAEhB,gBAAI,OAAO,SAAS;AAClBA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACP;AAAA,YACH;AAAA,mBACO,OAAO;AACdA,0BAAA,MAAI,YAAY;AAChBA,0BAAA,MAAI,MAAM,SAAQ,uCAAsC,WAAW,KAAK;AAAA,UAC1E;AAAA,QACF;AAAA,QACA,MAAM,CAAC,UAAU;AACfA,wBAAA,MAAI,MAAM,SAAQ,uCAAsC,WAAW,KAAK;AAAA,QAC1E;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,gBAAgB,MAAM;;AACtB,WAAA,cAAS,UAAT,mBAAgB,QAAQ;AAC1BA,sBAAAA,MAAI,aAAa;AAAA,UACf,MAAM,CAAC,SAAS,MAAM,MAAM;AAAA,UAC5B,SAAS;AAAA,QAAA,CACV;AAAA,MACH;AAAA,IAAA;AAIF,UAAM,sBAAsB,MAAM;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAAA,CACP;AAAA,UACH;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IAAA;AAIG,UAAA,oBAAoB,CAAC,UAAe;AAClC,YAAA,WAAW,MAAM,CAAC,EAAE;AAGtBA,oBAAAA,MAAA,eAAe,gBAAgB,QAAQ;AAG3C,YAAM,eAAe,cAAc,MAAM,KAAK,CAAK,MAAA,EAAE,OAAO,UAAU;AACtE,UAAI,cAAc;AACH,qBAAA,QAAQ,MAAM,CAAC,EAAE;AAAA,MAChC;AAEA,yBAAmB,QAAQ;AAE3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIG,UAAA,oBAAoB,CAAC,WAAgB;AACzC,qBAAe,QAAQ;AAGnBA,oBAAAA,MAAA,eAAe,aAAa,OAAO,KAAK;AAG5C,YAAM,YAAY,cAAc,MAAM,KAAK,CAAK,MAAA,EAAE,OAAO,OAAO;AAChE,UAAI,WAAW;AACb,kBAAU,QAAQ,OAAO;AAAA,MAC3B;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIH,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAGnC,uBAAW,MAAM;AACfA,4BAAA,MAAI,YAAY;AAGhB,oBAAM,YAAY,cAAc,MAAM,KAAK,CAAK,MAAA,EAAE,OAAO,OAAO;AAChE,kBAAI,WAAW;AACb,0BAAU,QAAQ;AAAA,cACpB;AAEAA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACP;AAAA,eACA,GAAI;AAAA,UACT;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAGnC,iBAAW,MAAM;AACfA,sBAAA,MAAI,YAAY;AAEhBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,aAAa;AAAA,QAAA,CACd;AAAA,SACA,IAAI;AAAA,IAAA;AAIT,UAAM,eAAe,MAAM;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACX,gBAAA;AACF,oBAAM,UAAU;qBACT,OAAO;AACdA,4BAAA,MAAI,MAAM,SAAQ,uCAAsC,WAAW,KAAK;AAAA,YAC1E;AAAA,UACF;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IAAA;AAIG,UAAA,aAAa,CAAC,SAAiB;AACnCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAI,MAAM,SAAQ,uCAAsC,WAAW,GAAG;AACtEA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,oBAAoB,YAAY;AAChC,UAAA;AACF,cAAM,cAAc;AACpBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,eACM,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACAA,sBAAA,MAAI,oBAAoB;AAAA,MAC1B;AAAA,IAAA;AAIW,aAAA;AAAA,MACX;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACloBD,GAAG,WAAW,eAAe;"}