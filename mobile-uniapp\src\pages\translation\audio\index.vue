<template>
  <view class="audio-translation-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">音频翻译</text>
      <text class="page-desc">支持实时录音和音频文件翻译</text>
    </view>

    <!-- 语言选择 -->
    <view class="language-selector">
      <view class="language-item">
        <text class="language-label">源语言</text>
        <u-button 
          type="text" 
          :text="sourceLanguage?.name || '自动检测'" 
          @click="showSourceLanguagePicker = true"
          :custom-style="{ color: '#2563EB' }"
        >
          <u-icon name="arrow-down" size="12" color="#2563EB" margin-left="4"></u-icon>
        </u-button>
      </view>
      
      <view class="swap-button" @click="swapLanguages">
        <u-icon name="arrow-left-right" size="20" color="#6B7280"></u-icon>
      </view>
      
      <view class="language-item">
        <text class="language-label">目标语言</text>
        <u-button 
          type="text" 
          :text="targetLanguage?.name || '中文'" 
          @click="showTargetLanguagePicker = true"
          :custom-style="{ color: '#2563EB' }"
        >
          <u-icon name="arrow-down" size="12" color="#2563EB" margin-left="4"></u-icon>
        </u-button>
      </view>
    </view>

    <!-- 录音模式 -->
    <view class="recording-section">
      <view class="section-title">
        <text class="title-text">实时录音翻译</text>
        <text class="title-desc">点击开始录音，实时翻译语音内容</text>
      </view>
      
      <view class="recording-container">
        <view class="recording-visualizer" v-if="isRecording">
          <view 
            class="wave-bar"
            v-for="(height, index) in waveData"
            :key="index"
            :style="{ height: height + 'rpx' }"
          ></view>
        </view>
        
        <view class="recording-controls">
          <view 
            class="record-button"
            :class="{ recording: isRecording, disabled: isProcessing }"
            @click="toggleRecording"
          >
            <u-icon 
              :name="isRecording ? 'stop' : 'mic'" 
              size="32" 
              color="#fff"
            ></u-icon>
          </view>
          
          <view class="recording-info" v-if="isRecording">
            <text class="recording-time">{{ formatTime(recordingTime) }}</text>
            <text class="recording-status">正在录音...</text>
          </view>
          
          <view class="processing-info" v-if="isProcessing">
            <u-loading-icon mode="flower"></u-loading-icon>
            <text class="processing-text">正在处理音频...</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 文件上传模式 -->
    <view class="upload-section">
      <view class="section-title">
        <text class="title-text">音频文件翻译</text>
        <text class="title-desc">支持MP3、WAV、M4A等格式，最大100MB</text>
      </view>
      
      <view class="upload-area" @click="chooseAudioFile">
        <view class="upload-content" v-if="!selectedAudioFile">
          <u-icon name="cloud-upload" size="48" color="#2563EB"></u-icon>
          <text class="upload-text">选择音频文件</text>
          <text class="upload-desc">支持 MP3、WAV、M4A、AAC 格式</text>
        </view>
        
        <view class="file-info" v-else>
          <view class="file-icon">
            <u-icon name="music" size="32" color="#2563EB"></u-icon>
          </view>
          <view class="file-details">
            <text class="file-name">{{ selectedAudioFile.name }}</text>
            <text class="file-size">{{ formatFileSize(selectedAudioFile.size) }}</text>
            <text class="file-duration" v-if="selectedAudioFile.duration">
              时长: {{ formatDuration(selectedAudioFile.duration) }}
            </text>
          </view>
          <view class="file-actions">
            <u-icon name="play-circle" size="24" color="#2563EB" @click.stop="playAudio"></u-icon>
            <u-icon name="close-circle" size="24" color="#EF4444" @click.stop="removeAudioFile"></u-icon>
          </view>
        </view>
      </view>
      
      <view class="upload-options" v-if="selectedAudioFile">
        <view class="option-item">
          <text class="option-label">翻译模式</text>
          <u-radio-group v-model="translationMode">
            <u-radio name="full" label="完整翻译"></u-radio>
            <u-radio name="segment" label="分段翻译"></u-radio>
          </u-radio-group>
        </view>
        
        <view class="option-item">
          <text class="option-label">输出格式</text>
          <u-radio-group v-model="outputFormat">
            <u-radio name="text" label="纯文本"></u-radio>
            <u-radio name="srt" label="字幕文件"></u-radio>
            <u-radio name="audio" label="语音合成"></u-radio>
          </u-radio-group>
        </view>
      </view>
      
      <view class="translate-button" v-if="selectedAudioFile">
        <u-button 
          type="primary" 
          size="large"
          :loading="isTranslating"
          :disabled="!canTranslate"
          @click="translateAudioFile"
          :custom-style="{ width: '100%' }"
        >
          {{ isTranslating ? '翻译中...' : '开始翻译' }}
        </u-button>
      </view>
    </view>

    <!-- 翻译结果 -->
    <view class="translation-result" v-if="translationResult">
      <view class="section-title">
        <text class="title-text">翻译结果</text>
        <view class="result-actions">
          <u-icon name="copy" size="18" color="#2563EB" @click="copyResult"></u-icon>
          <u-icon name="share" size="18" color="#2563EB" margin-left="12" @click="shareResult"></u-icon>
          <u-icon name="download" size="18" color="#2563EB" margin-left="12" @click="downloadResult"></u-icon>
        </view>
      </view>
      
      <view class="result-content">
        <!-- 文本结果 -->
        <view class="text-result" v-if="translationResult.type === 'text'">
          <view class="original-text">
            <text class="result-label">原文:</text>
            <text class="result-text" selectable>{{ translationResult.originalText }}</text>
          </view>
          <view class="translated-text">
            <text class="result-label">译文:</text>
            <text class="result-text" selectable>{{ translationResult.translatedText }}</text>
          </view>
        </view>
        
        <!-- 分段结果 -->
        <view class="segment-result" v-if="translationResult.type === 'segments'">
          <view 
            class="segment-item"
            v-for="(segment, index) in translationResult.segments"
            :key="index"
          >
            <view class="segment-time">{{ formatTime(segment.startTime) }} - {{ formatTime(segment.endTime) }}</view>
            <view class="segment-original">{{ segment.originalText }}</view>
            <view class="segment-translated">{{ segment.translatedText }}</view>
          </view>
        </view>
        
        <!-- 音频结果 -->
        <view class="audio-result" v-if="translationResult.type === 'audio'">
          <view class="audio-player">
            <u-icon 
              :name="isPlayingResult ? 'pause-circle-fill' : 'play-circle-fill'" 
              size="48" 
              color="#2563EB"
              @click="toggleResultAudio"
            ></u-icon>
            <view class="audio-info">
              <text class="audio-title">翻译语音</text>
              <text class="audio-duration">{{ formatDuration(translationResult.duration) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 翻译历史 -->
    <view class="translation-history" v-if="audioHistory.length > 0">
      <view class="section-title">
        <text class="title-text">翻译历史</text>
        <u-button 
          type="text" 
          text="查看全部" 
          size="small"
          @click="viewAllHistory"
          :custom-style="{ color: '#2563EB', fontSize: '24rpx' }"
        ></u-button>
      </view>
      
      <view class="history-list">
        <view 
          class="history-item"
          v-for="item in audioHistory.slice(0, 3)"
          :key="item.id"
          @click="viewHistoryItem(item)"
        >
          <view class="history-icon">
            <u-icon name="music" size="20" color="#2563EB"></u-icon>
          </view>
          <view class="history-content">
            <text class="history-title">{{ item.filename || '录音翻译' }}</text>
            <text class="history-desc">{{ item.originalText.substring(0, 50) }}...</text>
            <text class="history-time">{{ formatTime(item.createdAt) }}</text>
          </view>
          <view class="history-actions">
            <u-icon name="play-circle" size="16" color="#6B7280"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 语言选择器 -->
    <u-picker
      v-model="showSourceLanguagePicker"
      :columns="sourceLanguageColumns"
      @confirm="onSourceLanguageConfirm"
      @cancel="showSourceLanguagePicker = false"
    ></u-picker>

    <u-picker
      v-model="showTargetLanguagePicker"
      :columns="targetLanguageColumns"
      @confirm="onTargetLanguageConfirm"
      @cancel="showTargetLanguagePicker = false"
    ></u-picker>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useTranslationStore } from '@/store/modules/translation'
import dayjs from 'dayjs'

// Store
const translationStore = useTranslationStore()

// 响应式数据
const isRecording = ref<boolean>(false)
const isProcessing = ref<boolean>(false)
const isTranslating = ref<boolean>(false)
const isPlayingResult = ref<boolean>(false)
const recordingTime = ref<number>(0)
const waveData = ref<number[]>(Array(20).fill(20))
const selectedAudioFile = ref<any>(null)
const translationResult = ref<any>(null)
const audioHistory = ref<any[]>([])
const showSourceLanguagePicker = ref<boolean>(false)
const showTargetLanguagePicker = ref<boolean>(false)
const translationMode = ref<string>('full')
const outputFormat = ref<string>('text')

// 录音相关
let recordingTimer: NodeJS.Timeout | null = null
let waveTimer: NodeJS.Timeout | null = null
let audioContext: any = null
let mediaRecorder: any = null

// 计算属性
const {
  languages,
  currentSourceLang,
  currentTargetLang,
  sourceLanguage,
  targetLanguage
} = translationStore

const canTranslate = computed(() => {
  return selectedAudioFile.value && currentSourceLang.value && currentTargetLang.value
})

const sourceLanguageColumns = computed(() => {
  const autoDetect = [{ text: '自动检测', value: 'auto' }]
  const languageOptions = languages.map(lang => ({
    text: lang.name,
    value: lang.code
  }))
  return [autoDetect.concat(languageOptions)]
})

const targetLanguageColumns = computed(() => {
  const languageOptions = languages.filter(lang => lang.code !== 'auto').map(lang => ({
    text: lang.name,
    value: lang.code
  }))
  return [languageOptions]
})

// 页面加载
onMounted(async () => {
  await translationStore.loadLanguages()
  loadAudioHistory()
})

// 页面卸载
onUnmounted(() => {
  stopRecording()
  clearTimers()
})

// 切换录音状态
const toggleRecording = async () => {
  if (isProcessing.value) return

  if (isRecording.value) {
    await stopRecording()
  } else {
    await startRecording()
  }
}

// 开始录音
const startRecording = async () => {
  try {
    // #ifdef H5
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })

    mediaRecorder = new MediaRecorder(stream)
    const audioChunks: Blob[] = []

    mediaRecorder.ondataavailable = (event: any) => {
      audioChunks.push(event.data)
    }

    mediaRecorder.onstop = async () => {
      const audioBlob = new Blob(audioChunks, { type: 'audio/wav' })
      await processRecordedAudio(audioBlob)
    }

    mediaRecorder.start()
    // #endif

    // #ifdef APP-PLUS
    plus.audio.getRecorder().record({
      filename: '_doc/audio_record.wav',
      format: 'wav'
    }, (path) => {
      processRecordedAudioFile(path)
    }, (error) => {
      console.error('录音失败:', error)
      uni.showToast({
        title: '录音失败',
        icon: 'error'
      })
    })
    // #endif

    isRecording.value = true
    recordingTime.value = 0

    // 开始计时
    recordingTimer = setInterval(() => {
      recordingTime.value++
    }, 1000)

    // 开始波形动画
    startWaveAnimation()

    uni.showToast({
      title: '开始录音',
      icon: 'success'
    })

  } catch (error) {
    console.error('开始录音失败:', error)
    uni.showToast({
      title: '录音权限被拒绝',
      icon: 'error'
    })
  }
}

// 停止录音
const stopRecording = async () => {
  isRecording.value = false
  isProcessing.value = true

  clearTimers()

  // #ifdef H5
  if (mediaRecorder && mediaRecorder.state !== 'inactive') {
    mediaRecorder.stop()

    // 停止所有音频轨道
    if (mediaRecorder.stream) {
      mediaRecorder.stream.getTracks().forEach((track: any) => track.stop())
    }
  }
  // #endif

  // #ifdef APP-PLUS
  plus.audio.getRecorder().stop()
  // #endif

  uni.showToast({
    title: '录音结束',
    icon: 'success'
  })
}

// 处理录音音频
const processRecordedAudio = async (audioBlob: Blob) => {
  try {
    // 这里应该调用语音识别API
    // 模拟处理过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    const mockResult = {
      type: 'text',
      originalText: '这是录音识别的原文内容',
      translatedText: 'This is the original text content recognized from the recording',
      confidence: 0.95,
      duration: recordingTime.value
    }

    translationResult.value = mockResult

    // 添加到历史记录
    const historyItem = {
      id: Date.now().toString(),
      type: 'recording',
      originalText: mockResult.originalText,
      translatedText: mockResult.translatedText,
      sourceLang: currentSourceLang.value,
      targetLang: currentTargetLang.value,
      duration: recordingTime.value,
      createdAt: new Date().toISOString()
    }

    audioHistory.value.unshift(historyItem)
    saveAudioHistory()

  } catch (error) {
    console.error('处理录音失败:', error)
    uni.showToast({
      title: '处理录音失败',
      icon: 'error'
    })
  } finally {
    isProcessing.value = false
  }
}

// 处理录音文件 (App)
const processRecordedAudioFile = async (filePath: string) => {
  // App环境下处理录音文件
  await processRecordedAudio(new Blob())
}

// 开始波形动画
const startWaveAnimation = () => {
  waveTimer = setInterval(() => {
    waveData.value = waveData.value.map(() =>
      Math.floor(Math.random() * 60) + 20
    )
  }, 100)
}

// 清除定时器
const clearTimers = () => {
  if (recordingTimer) {
    clearInterval(recordingTimer)
    recordingTimer = null
  }

  if (waveTimer) {
    clearInterval(waveTimer)
    waveTimer = null
  }
}

// 选择音频文件
const chooseAudioFile = () => {
  // #ifdef H5
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'audio/*'
  input.onchange = (e: any) => {
    const file = e.target.files[0]
    if (file) {
      handleSelectedFile(file)
    }
  }
  input.click()
  // #endif

  // #ifdef APP-PLUS || MP
  uni.chooseFile({
    count: 1,
    type: 'file',
    extension: ['.mp3', '.wav', '.m4a', '.aac'],
    success: (res) => {
      const file = res.tempFiles[0]
      handleSelectedFile(file)
    },
    fail: (error) => {
      console.error('选择文件失败:', error)
    }
  })
  // #endif
}

// 处理选中的文件
const handleSelectedFile = (file: any) => {
  // 检查文件大小
  if (file.size > 100 * 1024 * 1024) {
    uni.showToast({
      title: '文件大小不能超过100MB',
      icon: 'error'
    })
    return
  }

  selectedAudioFile.value = {
    name: file.name,
    size: file.size,
    path: file.path || URL.createObjectURL(file),
    duration: null // 这里可以获取音频时长
  }

  // 获取音频时长
  getAudioDuration(selectedAudioFile.value.path)
}

// 获取音频时长
const getAudioDuration = (audioPath: string) => {
  // #ifdef H5
  const audio = new Audio(audioPath)
  audio.onloadedmetadata = () => {
    if (selectedAudioFile.value) {
      selectedAudioFile.value.duration = audio.duration
    }
  }
  // #endif
}

// 移除音频文件
const removeAudioFile = () => {
  selectedAudioFile.value = null
  translationResult.value = null
}

// 播放音频
const playAudio = () => {
  if (!selectedAudioFile.value) return

  // #ifdef H5
  const audio = new Audio(selectedAudioFile.value.path)
  audio.play()
  // #endif

  // #ifdef APP-PLUS
  uni.playBackgroundAudio({
    dataUrl: selectedAudioFile.value.path,
    title: selectedAudioFile.value.name
  })
  // #endif
}

// 翻译音频文件
const translateAudioFile = async () => {
  if (!canTranslate.value) return

  try {
    isTranslating.value = true

    // 模拟翻译过程
    await new Promise(resolve => setTimeout(resolve, 3000))

    const mockResult = {
      type: outputFormat.value === 'segment' ? 'segments' : outputFormat.value,
      originalText: '这是音频文件中识别的原文内容',
      translatedText: 'This is the original text content recognized from the audio file',
      segments: outputFormat.value === 'segment' ? [
        {
          startTime: 0,
          endTime: 5,
          originalText: '第一段原文',
          translatedText: 'First segment translation'
        },
        {
          startTime: 5,
          endTime: 10,
          originalText: '第二段原文',
          translatedText: 'Second segment translation'
        }
      ] : null,
      duration: selectedAudioFile.value.duration,
      audioUrl: outputFormat.value === 'audio' ? 'mock-audio-url' : null
    }

    translationResult.value = mockResult

    // 添加到历史记录
    const historyItem = {
      id: Date.now().toString(),
      type: 'file',
      filename: selectedAudioFile.value.name,
      originalText: mockResult.originalText,
      translatedText: mockResult.translatedText,
      sourceLang: currentSourceLang.value,
      targetLang: currentTargetLang.value,
      duration: selectedAudioFile.value.duration,
      createdAt: new Date().toISOString()
    }

    audioHistory.value.unshift(historyItem)
    saveAudioHistory()

    uni.showToast({
      title: '翻译完成',
      icon: 'success'
    })

  } catch (error) {
    console.error('翻译失败:', error)
    uni.showToast({
      title: '翻译失败',
      icon: 'error'
    })
  } finally {
    isTranslating.value = false
  }
}

// 交换语言
const swapLanguages = () => {
  translationStore.swapLanguages()
}

// 语言选择确认
const onSourceLanguageConfirm = (value: any) => {
  translationStore.setSourceLanguage(value[0].value)
  showSourceLanguagePicker.value = false
}

const onTargetLanguageConfirm = (value: any) => {
  translationStore.setTargetLanguage(value[0].value)
  showTargetLanguagePicker.value = false
}

// 复制结果
const copyResult = () => {
  if (!translationResult.value) return

  let textToCopy = ''
  if (translationResult.value.type === 'text') {
    textToCopy = `原文: ${translationResult.value.originalText}\n译文: ${translationResult.value.translatedText}`
  } else if (translationResult.value.type === 'segments') {
    textToCopy = translationResult.value.segments.map((seg: any) =>
      `${formatTime(seg.startTime)}-${formatTime(seg.endTime)}\n${seg.originalText}\n${seg.translatedText}`
    ).join('\n\n')
  }

  uni.setClipboardData({
    data: textToCopy,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      })
    }
  })
}

// 分享结果
const shareResult = () => {
  if (!translationResult.value) return

  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    summary: `音频翻译结果: ${translationResult.value.translatedText}`,
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    }
  })
}

// 下载结果
const downloadResult = () => {
  if (!translationResult.value) return

  uni.showToast({
    title: '下载功能开发中',
    icon: 'none'
  })
}

// 播放结果音频
const toggleResultAudio = () => {
  if (!translationResult.value?.audioUrl) return

  if (isPlayingResult.value) {
    uni.stopBackgroundAudio()
    isPlayingResult.value = false
  } else {
    uni.playBackgroundAudio({
      dataUrl: translationResult.value.audioUrl,
      title: '翻译语音',
      success: () => {
        isPlayingResult.value = true
      }
    })
  }
}

// 工具函数
const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const formatDuration = (seconds: number) => {
  if (!seconds) return '00:00'
  return formatTime(seconds)
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 历史记录相关
const saveAudioHistory = () => {
  uni.setStorageSync('audio_translation_history', audioHistory.value)
}

const loadAudioHistory = () => {
  const history = uni.getStorageSync('audio_translation_history') || []
  audioHistory.value = history
}

const viewAllHistory = () => {
  uni.navigateTo({
    url: '/pages/translation/audio/history/index'
  })
}

const viewHistoryItem = (item: any) => {
  translationResult.value = {
    type: 'text',
    originalText: item.originalText,
    translatedText: item.translatedText
  }
}

// 页面下拉刷新
const onPullDownRefresh = () => {
  loadAudioHistory()
  uni.stopPullDownRefresh()
}

// 导出方法供页面使用
defineExpose({
  onPullDownRefresh
})
</script>

<style lang="scss" scoped>
.audio-translation-page {
  min-height: 100vh;
  background-color: $bg-secondary;
}

// 页面头部
.page-header {
  background-color: $bg-primary;
  padding: $spacing-6 $spacing-4;
  text-align: center;
  border-bottom: 1px solid $border-primary;

  .page-title {
    display: block;
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: $spacing-2;
  }

  .page-desc {
    font-size: $font-size-base;
    color: $text-secondary;
  }
}

// 语言选择器
.language-selector {
  background-color: $bg-primary;
  padding: $spacing-4;
  border-bottom: 1px solid $border-primary;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .language-item {
    flex: 1;
    text-align: center;

    .language-label {
      display: block;
      font-size: $font-size-sm;
      color: $text-tertiary;
      margin-bottom: $spacing-2;
    }
  }

  .swap-button {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $bg-tertiary;
    border-radius: $radius-full;
    margin: 0 $spacing-4;

    &:active {
      background-color: $gray-200;
    }
  }
}

// 通用区块标题
.section-title {
  padding: $spacing-4 $spacing-4 $spacing-3;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title-text {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
  }

  .title-desc {
    font-size: $font-size-sm;
    color: $text-tertiary;
  }

  .result-actions {
    display: flex;
    align-items: center;
    gap: $spacing-2;
  }
}

// 录音区域
.recording-section {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .recording-container {
    padding: $spacing-6 $spacing-4;

    .recording-visualizer {
      display: flex;
      align-items: end;
      justify-content: center;
      gap: 4rpx;
      height: 120rpx;
      margin-bottom: $spacing-6;

      .wave-bar {
        width: 8rpx;
        background: linear-gradient(to top, $primary, $primary-light);
        border-radius: 4rpx;
        transition: height 0.1s ease;
      }
    }

    .recording-controls {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: $spacing-4;

      .record-button {
        width: 160rpx;
        height: 160rpx;
        border-radius: $radius-full;
        background-color: $primary;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &.recording {
          background-color: $error;
          animation: pulse 2s infinite;
        }

        &.disabled {
          background-color: $gray-400;
          pointer-events: none;
        }

        &:active {
          transform: scale(0.95);
        }
      }

      .recording-info,
      .processing-info {
        text-align: center;

        .recording-time {
          display: block;
          font-size: $font-size-xl;
          font-weight: $font-weight-bold;
          color: $text-primary;
          margin-bottom: $spacing-1;
        }

        .recording-status,
        .processing-text {
          font-size: $font-size-base;
          color: $text-secondary;
        }
      }
    }
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 20rpx rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

// 文件上传区域
.upload-section {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .upload-area {
    margin: 0 $spacing-4 $spacing-4;
    border: 2px dashed $border-primary;
    border-radius: $radius-lg;
    padding: $spacing-6;

    .upload-content {
      text-align: center;

      .upload-text {
        display: block;
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin: $spacing-4 0 $spacing-2;
      }

      .upload-desc {
        font-size: $font-size-base;
        color: $text-secondary;
      }
    }

    .file-info {
      display: flex;
      align-items: center;
      gap: $spacing-3;

      .file-icon {
        width: 80rpx;
        height: 80rpx;
        background-color: $primary-50;
        border-radius: $radius-lg;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .file-details {
        flex: 1;

        .file-name {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-size,
        .file-duration {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
        }
      }

      .file-actions {
        display: flex;
        gap: $spacing-2;
      }
    }
  }

  .upload-options {
    padding: 0 $spacing-4 $spacing-4;

    .option-item {
      margin-bottom: $spacing-4;

      .option-label {
        display: block;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-3;
      }
    }
  }

  .translate-button {
    padding: 0 $spacing-4 $spacing-4;
  }
}

// 翻译结果
.translation-result {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .result-content {
    padding: 0 $spacing-4 $spacing-4;

    .text-result {
      .original-text,
      .translated-text {
        margin-bottom: $spacing-4;

        .result-label {
          display: block;
          font-size: $font-size-sm;
          font-weight: $font-weight-medium;
          color: $text-secondary;
          margin-bottom: $spacing-2;
        }

        .result-text {
          display: block;
          font-size: $font-size-base;
          line-height: $line-height-relaxed;
          color: $text-primary;
          background-color: $bg-tertiary;
          padding: $spacing-3;
          border-radius: $radius-md;
          word-break: break-word;
        }
      }
    }

    .segment-result {
      .segment-item {
        background-color: $bg-tertiary;
        border-radius: $radius-md;
        padding: $spacing-3;
        margin-bottom: $spacing-3;

        .segment-time {
          font-size: $font-size-xs;
          color: $text-tertiary;
          margin-bottom: $spacing-2;
        }

        .segment-original {
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-2;
        }

        .segment-translated {
          font-size: $font-size-base;
          color: $text-primary;
          font-weight: $font-weight-medium;
        }
      }
    }

    .audio-result {
      .audio-player {
        display: flex;
        align-items: center;
        gap: $spacing-4;
        background-color: $bg-tertiary;
        border-radius: $radius-lg;
        padding: $spacing-4;

        .audio-info {
          flex: 1;

          .audio-title {
            display: block;
            font-size: $font-size-base;
            font-weight: $font-weight-medium;
            color: $text-primary;
            margin-bottom: $spacing-1;
          }

          .audio-duration {
            font-size: $font-size-sm;
            color: $text-secondary;
          }
        }
      }
    }
  }
}

// 翻译历史
.translation-history {
  background-color: $bg-primary;

  .history-list {
    padding: 0 $spacing-4 $spacing-4;

    .history-item {
      display: flex;
      align-items: center;
      gap: $spacing-3;
      padding: $spacing-3;
      background-color: $bg-tertiary;
      border-radius: $radius-lg;
      margin-bottom: $spacing-2;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        background-color: $gray-200;
      }

      .history-icon {
        width: 64rpx;
        height: 64rpx;
        background-color: $primary-50;
        border-radius: $radius-lg;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .history-content {
        flex: 1;

        .history-title {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .history-desc {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .history-time {
          font-size: $font-size-xs;
          color: $text-tertiary;
        }
      }

      .history-actions {
        padding: $spacing-2;
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 480px) {
  .recording-container {
    padding: $spacing-4;

    .record-button {
      width: 120rpx;
      height: 120rpx;
    }

    .recording-visualizer {
      height: 80rpx;
    }
  }

  .language-selector {
    flex-direction: column;
    gap: $spacing-3;

    .swap-button {
      transform: rotate(90deg);
    }
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .audio-translation-page {
    background-color: #1a1a1a;
  }

  .page-header,
  .language-selector,
  .recording-section,
  .upload-section,
  .translation-result,
  .translation-history {
    background-color: #2d2d2d;
    border-color: #404040;
  }

  .upload-area,
  .result-text,
  .segment-item,
  .audio-player,
  .history-item {
    background-color: #404040;
    border-color: #555555;
  }

  .swap-button,
  .file-icon,
  .history-icon {
    background-color: #404040;
  }

  .wave-bar {
    background: linear-gradient(to top, #3b82f6, #60a5fa);
  }
}
</style>
