"use strict";
const common_vendor = require("../../common/vendor.js");
const store_modules_user = require("../../store/modules/user.js");
if (!Array) {
  const _component_u_button = common_vendor.resolveComponent("u-button");
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  (_component_u_button + _component_u_icon)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props, { expose: __expose }) {
    store_modules_user.useUserStore();
    const banners = common_vendor.ref([
      {
        id: 1,
        title: "AI翻译服务",
        description: "支持200+语言，专业翻译",
        image: "/static/images/banner1.jpg",
        buttonText: "立即体验",
        path: "/pages/translation/text/index"
      },
      {
        id: 2,
        title: "数字人对话",
        description: "逼真AI数字人，智能交互",
        image: "/static/images/banner2.jpg",
        buttonText: "开始对话",
        path: "/pages/digital-human/chat/index"
      },
      {
        id: 3,
        title: "AI智能体",
        description: "专业AI助手，提升效率",
        image: "/static/images/banner3.jpg",
        buttonText: "探索更多",
        path: "/pages/ai-agent/market/index"
      }
    ]);
    const quickActions = common_vendor.ref([
      {
        title: "文本翻译",
        description: "快速翻译",
        icon: "edit-pen",
        color: "#2563EB",
        path: "/pages/translation/text/index"
      },
      {
        title: "语音翻译",
        description: "实时语音",
        icon: "mic",
        color: "#10B981",
        path: "/pages/translation/audio/index"
      },
      {
        title: "数字人",
        description: "AI对话",
        icon: "account",
        color: "#F59E0B",
        path: "/pages/digital-human/chat/index"
      },
      {
        title: "智能体",
        description: "AI助手",
        icon: "robot",
        color: "#EF4444",
        path: "/pages/ai-agent/market/index"
      }
    ]);
    const featureModules = common_vendor.ref([
      {
        title: "翻译服务",
        description: "支持文本、文档、音频、视频等多种翻译方式",
        icon: "globe",
        color: "#2563EB",
        badge: "热门",
        features: ["文本翻译", "文档翻译", "音频翻译", "视频翻译"],
        path: "/pages/translation/text/index"
      },
      {
        title: "数字人对话",
        description: "与AI数字人进行自然对话，体验未来交互方式",
        icon: "account-circle",
        color: "#10B981",
        badge: "新功能",
        features: ["智能对话", "表情生动", "多语言支持"],
        path: "/pages/digital-human/chat/index"
      },
      {
        title: "AI智能体市场",
        description: "丰富的AI智能体，满足各种专业需求",
        icon: "store",
        color: "#F59E0B",
        features: ["专业助手", "定制服务", "智能推荐"],
        path: "/pages/ai-agent/market/index"
      },
      {
        title: "实用工具",
        description: "文档转换、音频处理、图片编辑等实用工具",
        icon: "wrench",
        color: "#8B5CF6",
        features: ["格式转换", "批量处理", "高效便捷"],
        path: "/pages/utilities/conversion/document/index"
      }
    ]);
    const recentItems = common_vendor.ref([]);
    common_vendor.onMounted(async () => {
      await loadRecentItems();
    });
    const loadRecentItems = async () => {
      try {
        const recent = common_vendor.index.getStorageSync("recent_items") || [];
        recentItems.value = recent.slice(0, 10);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:249", "加载最近使用记录失败:", error);
      }
    };
    const navigateTo = (path) => {
      if (!path)
        return;
      common_vendor.index.navigateTo({
        url: path,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/index/index.vue:260", "页面跳转失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "error"
          });
        }
      });
    };
    const handleBannerClick = (banner) => {
      navigateTo(banner.path);
    };
    const handleRecentClick = (item) => {
      navigateTo(item.path);
    };
    const formatTime = (time) => {
      return common_vendor.dayjs(time).fromNow();
    };
    const onPullDownRefresh = async () => {
      try {
        await loadRecentItems();
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "刷新失败",
          icon: "error"
        });
      } finally {
        common_vendor.index.stopPullDownRefresh();
      }
    };
    __expose({
      onPullDownRefresh
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(banners.value, (banner, index, i0) => {
          return {
            a: common_vendor.t(banner.title),
            b: common_vendor.t(banner.description),
            c: common_vendor.t(banner.buttonText),
            d: common_vendor.o(($event) => handleBannerClick(banner), index),
            e: "1cf27b2a-0-" + i0,
            f: `url(${banner.image})`,
            g: index
          };
        }),
        b: common_vendor.p({
          type: "primary",
          size: "small",
          shape: "round"
        }),
        c: common_vendor.f(quickActions.value, (action, index, i0) => {
          return {
            a: "1cf27b2a-1-" + i0,
            b: common_vendor.p({
              name: action.icon,
              color: "#fff",
              size: "24"
            }),
            c: action.color,
            d: common_vendor.t(action.title),
            e: common_vendor.t(action.description),
            f: index,
            g: common_vendor.o(($event) => navigateTo(action.path), index)
          };
        }),
        d: common_vendor.f(featureModules.value, (module, index, i0) => {
          return common_vendor.e({
            a: "1cf27b2a-2-" + i0,
            b: common_vendor.p({
              name: module.icon,
              color: module.color,
              size: "28"
            }),
            c: module.badge
          }, module.badge ? {
            d: common_vendor.t(module.badge)
          } : {}, {
            e: common_vendor.t(module.title),
            f: common_vendor.t(module.description),
            g: common_vendor.f(module.features, (feature, idx, i1) => {
              return {
                a: common_vendor.t(feature),
                b: idx
              };
            }),
            h: "1cf27b2a-3-" + i0,
            i: index,
            j: common_vendor.o(($event) => navigateTo(module.path), index)
          });
        }),
        e: common_vendor.p({
          name: "arrow-right",
          color: "#9CA3AF",
          size: "16"
        }),
        f: recentItems.value.length > 0
      }, recentItems.value.length > 0 ? {
        g: common_vendor.f(recentItems.value, (item, index, i0) => {
          return {
            a: "1cf27b2a-4-" + i0,
            b: common_vendor.p({
              name: item.icon,
              color: "#2563EB",
              size: "20"
            }),
            c: common_vendor.t(item.title),
            d: common_vendor.t(formatTime(item.time)),
            e: index,
            f: common_vendor.o(($event) => handleRecentClick(item), index)
          };
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
