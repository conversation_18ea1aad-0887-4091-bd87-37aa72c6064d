/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 导入自定义样式变量 */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
.document-converter.data-v-7998750f {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
  padding: 32rpx;
}
.header-section.data-v-7998750f {
  margin-bottom: 48rpx;
}
.header-section .header-content.data-v-7998750f {
  text-align: center;
}
.header-section .header-content .page-title.data-v-7998750f {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #1D1D1F;
  margin-bottom: 16rpx;
}
.header-section .header-content .page-subtitle.data-v-7998750f {
  font-size: 28rpx;
  color: #8E8E93;
  line-height: 1.4;
}
.converter-section.data-v-7998750f {
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.converter-section .format-selector.data-v-7998750f {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.converter-section .format-selector .format-item.data-v-7998750f {
  flex: 1;
}
.converter-section .format-selector .format-item .format-label.data-v-7998750f {
  display: block;
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 16rpx;
}
.converter-section .format-selector .format-item .format-picker.data-v-7998750f {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #F2F2F7;
  border-radius: 16rpx;
  padding: 24rpx;
}
.converter-section .format-selector .format-item .format-picker .format-display.data-v-7998750f {
  display: flex;
  align-items: center;
}
.converter-section .format-selector .format-item .format-picker .format-display .format-icon.data-v-7998750f {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}
.converter-section .format-selector .format-item .format-picker .format-display .format-icon .iconfont.data-v-7998750f {
  font-size: 24rpx;
  color: #FFFFFF;
}
.converter-section .format-selector .format-item .format-picker .format-display .format-name.data-v-7998750f {
  font-size: 28rpx;
  font-weight: 500;
  color: #1D1D1F;
}
.converter-section .format-selector .convert-arrow.data-v-7998750f {
  margin: 0 32rpx;
}
.converter-section .format-selector .convert-arrow .arrow-icon.data-v-7998750f {
  width: 64rpx;
  height: 64rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.converter-section .format-selector .convert-arrow .arrow-icon.data-v-7998750f:active {
  transform: scale(0.95);
}
.upload-section.data-v-7998750f {
  margin-bottom: 32rpx;
}
.upload-section .upload-area.data-v-7998750f {
  background: #FFFFFF;
  border: 2rpx dashed #D1D5DB;
  border-radius: 24rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.upload-section .upload-area.data-v-7998750f:active {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.02);
}
.upload-section .upload-area .upload-content .upload-icon.data-v-7998750f {
  margin-bottom: 24rpx;
}
.upload-section .upload-area .upload-content .upload-title.data-v-7998750f {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 12rpx;
}
.upload-section .upload-area .upload-content .upload-desc.data-v-7998750f {
  display: block;
  font-size: 26rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}
.upload-section .upload-area .upload-content .upload-limit.data-v-7998750f {
  font-size: 22rpx;
  color: #C7C7CC;
}
.upload-section .upload-area .file-preview.data-v-7998750f {
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: left;
}
.upload-section .upload-area .file-preview .file-info.data-v-7998750f {
  display: flex;
  align-items: center;
  flex: 1;
}
.upload-section .upload-area .file-preview .file-info .file-icon.data-v-7998750f {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.upload-section .upload-area .file-preview .file-info .file-icon .iconfont.data-v-7998750f {
  font-size: 32rpx;
  color: #FFFFFF;
}
.upload-section .upload-area .file-preview .file-info .file-details .file-name.data-v-7998750f {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 8rpx;
}
.upload-section .upload-area .file-preview .file-info .file-details .file-size.data-v-7998750f {
  display: block;
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}
.upload-section .upload-area .file-preview .file-info .file-details .file-status.data-v-7998750f {
  font-size: 22rpx;
  color: #8E8E93;
}
.upload-section .upload-area .file-preview .file-info .file-details .file-status.success.data-v-7998750f {
  color: #10B981;
}
.upload-section .upload-area .file-preview .file-info .file-details .file-status.error.data-v-7998750f {
  color: #EF4444;
}
.upload-section .upload-area .file-preview .file-actions .action-btn.data-v-7998750f {
  width: 56rpx;
  height: 56rpx;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-section .upload-area .file-preview .file-actions .action-btn.remove.data-v-7998750f {
  background: rgba(239, 68, 68, 0.1);
}
.options-section.data-v-7998750f {
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.options-section .section-title.data-v-7998750f {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 32rpx;
}
.options-section .option-list .option-item.data-v-7998750f {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F2F2F7;
}
.options-section .option-list .option-item.data-v-7998750f:last-child {
  border-bottom: none;
}
.options-section .option-list .option-item .option-info.data-v-7998750f {
  flex: 1;
  margin-right: 32rpx;
}
.options-section .option-list .option-item .option-info .option-name.data-v-7998750f {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #1D1D1F;
  margin-bottom: 8rpx;
}
.options-section .option-list .option-item .option-info .option-desc.data-v-7998750f {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
}
.action-section.data-v-7998750f {
  text-align: center;
  margin-bottom: 48rpx;
}
.action-section .convert-btn.data-v-7998750f {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 28rpx;
  padding: 32rpx 64rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}
.action-section .convert-btn.data-v-7998750f:active:not(.disabled):not(.loading) {
  transform: scale(0.98);
}
.action-section .convert-btn.disabled.data-v-7998750f {
  opacity: 0.5;
  box-shadow: none;
}
.action-section .convert-btn.loading.data-v-7998750f {
  pointer-events: none;
}
.action-section .convert-btn .btn-content.data-v-7998750f {
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-section .convert-btn .btn-content .btn-text.data-v-7998750f {
  font-size: 32rpx;
  font-weight: 600;
  color: #FFFFFF;
  margin-left: 16rpx;
}
.action-section .convert-btn .btn-content .loading-icon.data-v-7998750f {
  animation: spin-7998750f 1s linear infinite;
}
.action-section .convert-tip.data-v-7998750f {
  display: block;
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 24rpx;
}
.history-section .section-title.data-v-7998750f {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 32rpx;
}
.history-section .history-list .history-item.data-v-7998750f {
  display: flex;
  align-items: center;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}
.history-section .history-list .history-item.data-v-7998750f:active {
  transform: scale(0.98);
}
.history-section .history-list .history-item .history-icon.data-v-7998750f {
  width: 64rpx;
  height: 64rpx;
  background: #F2F2F7;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.history-section .history-list .history-item .history-icon .iconfont.data-v-7998750f {
  font-size: 24rpx;
  color: #8E8E93;
}
.history-section .history-list .history-item .history-content.data-v-7998750f {
  flex: 1;
}
.history-section .history-list .history-item .history-content .history-name.data-v-7998750f {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #1D1D1F;
  margin-bottom: 8rpx;
}
.history-section .history-list .history-item .history-content .history-desc.data-v-7998750f {
  display: block;
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}
.history-section .history-list .history-item .history-content .history-time.data-v-7998750f {
  font-size: 22rpx;
  color: #C7C7CC;
}
.history-section .history-list .history-item .history-action.data-v-7998750f {
  padding: 16rpx;
}
.bottom-safe-area.data-v-7998750f {
  height: calc(120rpx + env(safe-area-inset-bottom));
}
@keyframes spin-7998750f {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
@media (prefers-color-scheme: dark) {
.document-converter.data-v-7998750f {
    background: linear-gradient(180deg, #1c1c1e 0%, #000000 100%);
}
.converter-section.data-v-7998750f,
.upload-area.data-v-7998750f,
.options-section.data-v-7998750f,
.history-item.data-v-7998750f {
    background: #2c2c2e;
    border-color: #38383a;
}
.page-title.data-v-7998750f,
.section-title.data-v-7998750f,
.format-name.data-v-7998750f,
.option-name.data-v-7998750f,
.history-name.data-v-7998750f {
    color: #ffffff !important;
}
.format-picker.data-v-7998750f {
    background: #38383a !important;
}
.history-icon.data-v-7998750f {
    background: #38383a;
}
}