#!/usr/bin/env python3
"""
增强的MuseTalk推理脚本
集成MediaPipe面部关键点检测和更精确的唇形同步
"""

import os
import sys
import argparse
import logging
import cv2
import numpy as np
import torch
from pathlib import Path
import tempfile
import librosa
import subprocess

# 添加MuseTalk目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(current_dir / "scripts"))

# 设置环境变量
os.environ['CUDA_VISIBLE_DEVICES'] = '0' if torch.cuda.is_available() else ''

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='增强的MuseTalk数字人生成')
    parser.add_argument('--source_image', required=True, help='源图像路径')
    parser.add_argument('--driving_audio', required=True, help='驱动音频路径')
    parser.add_argument('--output', required=True, help='输出视频路径')
    parser.add_argument('--device', default='cuda', choices=['cuda', 'cpu'], help='设备类型')
    parser.add_argument('--fps', type=int, default=25, help='输出视频帧率')
    parser.add_argument('--quality', default='high', choices=['low', 'medium', 'high'], help='输出质量')
    return parser.parse_args()

def check_dependencies():
    """检查依赖库"""
    try:
        import mediapipe as mp
        logger.info("✅ MediaPipe已安装")
        return True
    except ImportError:
        logger.warning("❌ MediaPipe未安装，将使用基础方法")
        return False

def extract_audio_features(audio_path, fps=25):
    """提取音频特征"""
    try:
        # 加载音频
        audio, sr = librosa.load(audio_path, sr=16000)
        
        if len(audio) == 0:
            raise ValueError("音频文件为空")
        
        duration = len(audio) / sr
        total_frames = int(duration * fps)
        
        logger.info(f"音频时长: {duration:.2f}秒, 总帧数: {total_frames}")
        
        # 提取MFCC特征
        mfcc = librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=13, hop_length=int(sr/fps))
        
        # 提取其他特征
        rms = librosa.feature.rms(y=audio, hop_length=int(sr/fps))[0]
        spectral_centroid = librosa.feature.spectral_centroid(y=audio, sr=sr, hop_length=int(sr/fps))[0]
        zcr = librosa.feature.zero_crossing_rate(audio, hop_length=int(sr/fps))[0]
        
        # 确保所有特征长度一致
        min_length = min(mfcc.shape[1], len(rms), len(spectral_centroid), len(zcr))
        
        features = []
        for i in range(min_length):
            frame_features = {
                'mfcc': mfcc[:, i],
                'rms': rms[i],
                'spectral_centroid': spectral_centroid[i],
                'zcr': zcr[i],
                'frame_index': i
            }
            features.append(frame_features)
        
        logger.info(f"✅ 提取了 {len(features)} 帧音频特征")
        return features
        
    except Exception as e:
        logger.error(f"音频特征提取失败: {e}")
        raise

def detect_face_landmarks(image):
    """使用MediaPipe检测面部关键点"""
    try:
        import mediapipe as mp
        
        mp_face_mesh = mp.solutions.face_mesh
        mp_drawing = mp.solutions.drawing_utils
        
        with mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5
        ) as face_mesh:
            
            # 转换为RGB
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            results = face_mesh.process(rgb_image)
            
            if results.multi_face_landmarks:
                landmarks = results.multi_face_landmarks[0]
                
                # 提取嘴部关键点 (MediaPipe面部网格的嘴部索引)
                mouth_indices = [
                    61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
                    78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308
                ]
                
                mouth_landmarks = []
                h, w = image.shape[:2]
                
                for idx in mouth_indices:
                    if idx < len(landmarks.landmark):
                        landmark = landmarks.landmark[idx]
                        x = int(landmark.x * w)
                        y = int(landmark.y * h)
                        mouth_landmarks.append((x, y))
                
                return mouth_landmarks
                
    except ImportError:
        logger.warning("MediaPipe不可用，使用OpenCV面部检测")
        return detect_face_opencv(image)
    except Exception as e:
        logger.warning(f"MediaPipe面部检测失败: {e}")
        return detect_face_opencv(image)
    
    return None

def detect_face_opencv(image):
    """使用OpenCV检测面部"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    faces = face_cascade.detectMultiScale(gray, 1.1, 4)
    
    if len(faces) > 0:
        # 选择最大的面部
        face = max(faces, key=lambda x: x[2] * x[3])
        fx, fy, fw, fh = face
        
        # 估算嘴部区域
        mouth_x = fx + int(fw * 0.3)
        mouth_y = fy + int(fh * 0.65)
        mouth_w = int(fw * 0.4)
        mouth_h = int(fh * 0.2)
        
        # 返回嘴部矩形的四个角点
        return [
            (mouth_x, mouth_y),
            (mouth_x + mouth_w, mouth_y),
            (mouth_x + mouth_w, mouth_y + mouth_h),
            (mouth_x, mouth_y + mouth_h)
        ]
    
    return None

def calculate_lip_movement(audio_features):
    """基于音频特征计算唇形运动"""
    lip_movements = []
    
    for i, features in enumerate(audio_features):
        mfcc = features['mfcc']
        rms = features['rms']
        spectral_centroid = features['spectral_centroid']
        zcr = features['zcr']
        
        # 计算嘴部开合程度 (基于RMS能量)
        mouth_open = np.clip(rms * 4.0, 0, 1)
        
        # 计算嘴部宽度 (基于谱质心)
        mouth_width = np.clip(spectral_centroid / 3000.0, 0.7, 1.3)
        
        # 计算说话强度 (基于多个特征)
        speech_intensity = np.clip((zcr * 8 + rms * 3), 0, 1)
        
        # 添加自然变化
        natural_variation = np.sin(i * 0.2) * 0.05
        
        lip_movement = {
            'mouth_open': mouth_open + natural_variation,
            'mouth_width': mouth_width,
            'speech_intensity': speech_intensity,
            'frame_index': i
        }
        
        lip_movements.append(lip_movement)
    
    return lip_movements

def apply_enhanced_lip_sync(frame, mouth_landmarks, lip_movement):
    """应用增强的唇形同步效果"""
    if not mouth_landmarks or len(mouth_landmarks) < 4:
        return frame
    
    try:
        mouth_open = np.clip(lip_movement['mouth_open'], 0, 1)
        speech_intensity = np.clip(lip_movement['speech_intensity'], 0, 1)
        
        # 如果有MediaPipe关键点，使用精确的嘴部区域
        if len(mouth_landmarks) > 10:  # MediaPipe关键点
            # 计算嘴部边界框
            xs = [p[0] for p in mouth_landmarks]
            ys = [p[1] for p in mouth_landmarks]
            
            mouth_x_start = max(0, min(xs) - 10)
            mouth_y_start = max(0, min(ys) - 10)
            mouth_x_end = min(frame.shape[1], max(xs) + 10)
            mouth_y_end = min(frame.shape[0], max(ys) + 10)
        else:  # OpenCV矩形
            mouth_x_start = mouth_landmarks[0][0]
            mouth_y_start = mouth_landmarks[0][1]
            mouth_x_end = mouth_landmarks[2][0]
            mouth_y_end = mouth_landmarks[2][1]
        
        # 提取嘴部区域
        mouth_region = frame[mouth_y_start:mouth_y_end, mouth_x_start:mouth_x_end].copy()
        
        if mouth_region.size > 0 and speech_intensity > 0.1:
            # 应用更自然的变化
            # 1. 垂直拉伸 (模拟嘴部张开)
            if mouth_open > 0.2:
                scale_factor = 1.0 + mouth_open * 0.2
                new_height = int(mouth_region.shape[0] * scale_factor)
                mouth_region = cv2.resize(mouth_region, (mouth_region.shape[1], new_height))
                
                # 如果拉伸后超出原区域，裁剪到原大小
                if new_height > (mouth_y_end - mouth_y_start):
                    excess = new_height - (mouth_y_end - mouth_y_start)
                    mouth_region = mouth_region[excess//2:new_height-excess//2, :]
            
            # 2. 亮度和对比度调整
            brightness = int(speech_intensity * 10)
            contrast = 1.0 + speech_intensity * 0.1
            mouth_region = cv2.convertScaleAbs(mouth_region, alpha=contrast, beta=brightness)
            
            # 3. 色彩饱和度调整
            hsv = cv2.cvtColor(mouth_region, cv2.COLOR_BGR2HSV)
            hsv[:, :, 1] = np.clip(hsv[:, :, 1] * (1.0 + speech_intensity * 0.15), 0, 255)
            mouth_region = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
            
            # 将处理后的区域放回原图
            region_h, region_w = mouth_region.shape[:2]
            target_h = mouth_y_end - mouth_y_start
            target_w = mouth_x_end - mouth_x_start
            
            if region_h == target_h and region_w == target_w:
                frame[mouth_y_start:mouth_y_end, mouth_x_start:mouth_x_end] = mouth_region
        
        return frame
        
    except Exception as e:
        logger.warning(f"唇形同步应用失败: {e}")
        return frame

def generate_enhanced_video(source_image, audio_features, lip_movements, output_path, fps=25):
    """生成增强的数字人视频"""
    try:
        # 读取源图像
        image = cv2.imread(source_image)
        if image is None:
            raise ValueError(f"无法读取图像: {source_image}")
        
        # 检测面部关键点
        logger.info("🔍 检测面部关键点...")
        mouth_landmarks = detect_face_landmarks(image)
        
        if not mouth_landmarks:
            logger.warning("未检测到面部关键点，使用基础方法")
            mouth_landmarks = [(100, 100), (200, 100), (200, 150), (100, 150)]  # 默认区域
        
        logger.info(f"✅ 检测到 {len(mouth_landmarks)} 个嘴部关键点")
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            frames_dir = Path(temp_dir) / "frames"
            frames_dir.mkdir()
            
            total_frames = len(lip_movements)
            logger.info(f"🎬 生成 {total_frames} 帧增强视频...")
            
            # 生成每一帧
            for i, lip_movement in enumerate(lip_movements):
                # 应用唇形同步
                frame = apply_enhanced_lip_sync(image.copy(), mouth_landmarks, lip_movement)
                
                # 保存帧
                frame_path = frames_dir / f"frame_{i:06d}.png"
                cv2.imwrite(str(frame_path), frame)
                
                if i % 25 == 0:
                    progress = (i / total_frames) * 100
                    logger.info(f"生成进度: {i}/{total_frames} ({progress:.1f}%)")
            
            # 使用FFmpeg合成视频
            logger.info("🎥 合成最终视频...")
            return create_final_video(frames_dir, audio_features, output_path, fps)
            
    except Exception as e:
        logger.error(f"增强视频生成失败: {e}")
        raise

def create_final_video(frames_dir, audio_features, output_path, fps):
    """创建最终视频"""
    try:
        # 构建FFmpeg命令
        cmd = [
            'ffmpeg', '-y',
            '-framerate', str(fps),
            '-i', str(frames_dir / 'frame_%06d.png'),
            '-c:v', 'libx264',
            '-pix_fmt', 'yuv420p',
            '-preset', 'medium',
            '-crf', '20',
            '-r', str(fps),
            str(output_path)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            file_size = Path(output_path).stat().st_size
            logger.info(f"✅ 增强视频创建成功: {output_path}")
            logger.info(f"视频文件大小: {file_size} bytes")
            return True
        else:
            logger.error(f"FFmpeg错误: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"视频创建失败: {e}")
        return False

def main():
    """主函数"""
    args = parse_args()
    
    logger.info("🎭 开始增强的MuseTalk数字人生成...")
    logger.info(f"源图像: {args.source_image}")
    logger.info(f"驱动音频: {args.driving_audio}")
    logger.info(f"输出视频: {args.output}")
    logger.info(f"设备: {args.device}")
    logger.info(f"质量: {args.quality}")
    
    try:
        # 检查依赖
        has_mediapipe = check_dependencies()
        
        # 提取音频特征
        logger.info("🎵 提取音频特征...")
        audio_features = extract_audio_features(args.driving_audio, args.fps)
        
        # 计算唇形运动
        logger.info("👄 计算唇形运动...")
        lip_movements = calculate_lip_movement(audio_features)
        
        # 生成增强视频
        success = generate_enhanced_video(
            args.source_image, 
            audio_features, 
            lip_movements, 
            args.output, 
            args.fps
        )
        
        if success:
            logger.info("🎉 增强的MuseTalk数字人生成完成！")
            logger.info(f"输出文件: {args.output}")
        else:
            raise Exception("视频生成失败")
            
    except Exception as e:
        logger.error(f"增强的MuseTalk推理失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
