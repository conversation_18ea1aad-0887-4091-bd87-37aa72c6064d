/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 导入自定义样式变量 */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
.translation-page.data-v-0df80e11 {
  min-height: 100vh;
  background-color: #F9FAFB;
}
.language-bar.data-v-0df80e11 {
  background-color: #FFFFFF;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #E5E7EB;
}
.language-bar .language-selector.data-v-0df80e11 {
  flex: 1;
  text-align: center;
}
.language-bar .swap-button.data-v-0df80e11 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F3F4F6;
  border-radius: 9999rpx;
  margin: 0 32rpx;
}
.language-bar .swap-button.data-v-0df80e11:active {
  background-color: #E5E7EB;
}
.quick-pairs.data-v-0df80e11 {
  background-color: #FFFFFF;
  padding: 16rpx 0;
  border-bottom: 1px solid #E5E7EB;
}
.quick-pairs .pairs-scroll.data-v-0df80e11 {
  white-space: nowrap;
}
.quick-pairs .pairs-scroll .pairs-list.data-v-0df80e11 {
  display: flex;
  padding: 0 32rpx;
  gap: 16rpx;
}
.quick-pairs .pairs-scroll .pairs-list .pair-item.data-v-0df80e11 {
  flex-shrink: 0;
  background-color: #F3F4F6;
  border-radius: 9999rpx;
  padding: 8rpx 24rpx;
}
.quick-pairs .pairs-scroll .pairs-list .pair-item .pair-text.data-v-0df80e11 {
  font-size: 20rpx;
  color: #374151;
  white-space: nowrap;
}
.quick-pairs .pairs-scroll .pairs-list .pair-item.data-v-0df80e11:active {
  background-color: #E5E7EB;
}
.translation-container.data-v-0df80e11 {
  padding: 32rpx;
  gap: 32rpx;
  display: flex;
  flex-direction: column;
}
.input-section.data-v-0df80e11 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.input-section .section-header.data-v-0df80e11 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background-color: #F3F4F6;
  border-bottom: 1px solid #E5E7EB;
}
.input-section .section-header .section-title.data-v-0df80e11 {
  font-size: 24rpx;
  font-weight: 500;
  color: #374151;
}
.input-section .section-header .header-actions.data-v-0df80e11 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.input-section .text-input.data-v-0df80e11 {
  width: 100%;
  min-height: 200rpx;
  padding: 32rpx;
  font-size: 28rpx;
  line-height: 1.625;
  color: #111827;
  background-color: transparent;
  border: none;
  outline: none;
  resize: none;
}
.input-section .text-input.data-v-0df80e11::-webkit-input-placeholder {
  color: #9CA3AF;
}
.input-section .text-input.data-v-0df80e11::placeholder {
  color: #9CA3AF;
}
.input-section .input-footer.data-v-0df80e11 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background-color: #F3F4F6;
  border-top: 1px solid #E5E7EB;
}
.input-section .input-footer .char-count.data-v-0df80e11 {
  font-size: 20rpx;
  color: #6B7280;
}
.result-section.data-v-0df80e11 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.result-section .section-header.data-v-0df80e11 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background-color: #EFF6FF;
  border-bottom: 1px solid #BFDBFE;
}
.result-section .section-header .section-title.data-v-0df80e11 {
  font-size: 24rpx;
  font-weight: 500;
  color: #1D4ED8;
}
.result-section .section-header .header-actions.data-v-0df80e11 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.result-section .result-content.data-v-0df80e11 {
  padding: 32rpx;
}
.result-section .result-content .result-text.data-v-0df80e11 {
  font-size: 28rpx;
  line-height: 1.625;
  color: #111827;
  word-break: break-word;
}
.result-section .result-footer.data-v-0df80e11 {
  padding: 24rpx 32rpx;
  background-color: #F3F4F6;
  border-top: 1px solid #E5E7EB;
}
.result-section .result-footer .result-info.data-v-0df80e11 {
  font-size: 20rpx;
  color: #6B7280;
}
.history-section.data-v-0df80e11 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.history-section .section-header.data-v-0df80e11 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background-color: #F3F4F6;
  border-bottom: 1px solid #E5E7EB;
}
.history-section .section-header .section-title.data-v-0df80e11 {
  font-size: 24rpx;
  font-weight: 500;
  color: #374151;
}
.history-section .history-list .history-item.data-v-0df80e11 {
  padding: 32rpx;
  border-bottom: 1px solid #E5E7EB;
}
.history-section .history-list .history-item.data-v-0df80e11:last-child {
  border-bottom: none;
}
.history-section .history-list .history-item.data-v-0df80e11:active {
  background-color: #F3F4F6;
}
.history-section .history-list .history-item .history-content.data-v-0df80e11 {
  margin-bottom: 16rpx;
}
.history-section .history-list .history-item .history-content .history-source.data-v-0df80e11 {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.history-section .history-list .history-item .history-content .history-target.data-v-0df80e11 {
  display: block;
  font-size: 28rpx;
  color: #111827;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.history-section .history-list .history-item .history-meta.data-v-0df80e11 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.history-section .history-list .history-item .history-meta .history-langs.data-v-0df80e11 {
  font-size: 20rpx;
  color: #6B7280;
}
.history-section .history-list .history-item .history-meta .history-time.data-v-0df80e11 {
  font-size: 20rpx;
  color: #9CA3AF;
}
.history-modal .history-scroll .history-modal-item.data-v-0df80e11 {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  border-bottom: 1px solid #E5E7EB;
}
.history-modal .history-scroll .history-modal-item.data-v-0df80e11:last-child {
  border-bottom: none;
}
.history-modal .history-scroll .history-modal-item.data-v-0df80e11:active {
  background-color: #F3F4F6;
}
.history-modal .history-scroll .history-modal-item .modal-item-content.data-v-0df80e11 {
  flex: 1;
  margin-right: 24rpx;
}
.history-modal .history-scroll .history-modal-item .modal-item-content .modal-source.data-v-0df80e11 {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 8rpx;
  line-height: 1.375;
}
.history-modal .history-scroll .history-modal-item .modal-item-content .modal-target.data-v-0df80e11 {
  display: block;
  font-size: 28rpx;
  color: #111827;
  margin-bottom: 16rpx;
  line-height: 1.375;
}
.history-modal .history-scroll .history-modal-item .modal-item-content .modal-meta.data-v-0df80e11 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.history-modal .history-scroll .history-modal-item .modal-item-content .modal-meta .modal-langs.data-v-0df80e11 {
  font-size: 20rpx;
  color: #6B7280;
}
.history-modal .history-scroll .history-modal-item .modal-item-content .modal-meta .modal-time.data-v-0df80e11 {
  font-size: 20rpx;
  color: #9CA3AF;
}
@media screen and (max-width: 480px) {
.translation-container.data-v-0df80e11 {
    padding: 24rpx;
}
.input-section .text-input.data-v-0df80e11 {
    min-height: 160rpx;
}
.language-bar.data-v-0df80e11 {
    padding: 24rpx;
}
}
@media (prefers-color-scheme: dark) {
.translation-page.data-v-0df80e11 {
    background-color: #1a1a1a;
}
.input-section.data-v-0df80e11,
.result-section.data-v-0df80e11,
.history-section.data-v-0df80e11 {
    background-color: #2d2d2d;
    border-color: #404040;
}
.section-header.data-v-0df80e11 {
    background-color: #404040;
}
.text-input.data-v-0df80e11 {
    color: #ffffff;
}
.text-input.data-v-0df80e11::-webkit-input-placeholder {
    color: #888888;
}
.text-input.data-v-0df80e11::placeholder {
    color: #888888;
}
}