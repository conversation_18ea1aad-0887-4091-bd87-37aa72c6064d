/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 导入自定义样式变量 */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
.ai-agents-market.data-v-4fe86039 {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
}
.header-section.data-v-4fe86039 {
  background: #FFFFFF;
  padding: 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}
.header-section .search-container.data-v-4fe86039 {
  margin-bottom: 32rpx;
}
.header-section .search-container .search-box.data-v-4fe86039 {
  display: flex;
  align-items: center;
  background: #F2F2F7;
  border-radius: 20rpx;
  padding: 24rpx 32rpx;
}
.header-section .search-container .search-box .search-input.data-v-4fe86039 {
  flex: 1;
  font-size: 28rpx;
  color: #1D1D1F;
  margin-left: 16rpx;
}
.header-section .search-container .search-box .search-input.data-v-4fe86039::-webkit-input-placeholder {
  color: #8E8E93;
}
.header-section .search-container .search-box .search-input.data-v-4fe86039::placeholder {
  color: #8E8E93;
}
.header-section .search-container .search-box .filter-btn.data-v-4fe86039 {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
}
.header-section .category-scroll.data-v-4fe86039 {
  white-space: nowrap;
}
.header-section .category-scroll .category-list.data-v-4fe86039 {
  display: flex;
  gap: 16rpx;
}
.header-section .category-scroll .category-list .category-item.data-v-4fe86039 {
  flex-shrink: 0;
  padding: 16rpx 32rpx;
  background: #F2F2F7;
  border-radius: 40rpx;
  transition: all 0.3s ease;
}
.header-section .category-scroll .category-list .category-item.active.data-v-4fe86039 {
  background: linear-gradient(135deg, #667eea, #764ba2);
}
.header-section .category-scroll .category-list .category-item.active .category-text.data-v-4fe86039 {
  color: #FFFFFF;
}
.header-section .category-scroll .category-list .category-item .category-text.data-v-4fe86039 {
  font-size: 26rpx;
  color: #8E8E93;
  font-weight: 500;
}
.agents-content.data-v-4fe86039 {
  flex: 1;
  padding: 32rpx;
}
.section.data-v-4fe86039 {
  margin-bottom: 48rpx;
}
.section .section-header.data-v-4fe86039 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}
.section .section-header .section-title.data-v-4fe86039 {
  font-size: 36rpx;
  font-weight: 700;
  color: #1D1D1F;
}
.section .section-header .section-more.data-v-4fe86039,
.section .section-header .section-count.data-v-4fe86039 {
  font-size: 26rpx;
  color: #667eea;
}
.agents-grid.data-v-4fe86039 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}
.agents-grid .agent-card.data-v-4fe86039 {
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
.agents-grid .agent-card.data-v-4fe86039:active {
  transform: scale(0.98);
}
.agents-grid .agent-card.featured.data-v-4fe86039 {
  border: 2rpx solid rgba(102, 126, 234, 0.2);
}
.agents-grid .agent-card .card-header.data-v-4fe86039 {
  position: relative;
  margin-bottom: 24rpx;
}
.agents-grid .agent-card .card-header .agent-avatar.data-v-4fe86039 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
}
.agents-grid .agent-card .card-header .agent-badge.data-v-4fe86039 {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  color: #FFFFFF;
  font-size: 18rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}
.agents-grid .agent-card .card-header .agent-rating.data-v-4fe86039 {
  position: absolute;
  bottom: -8rpx;
  right: 0;
  display: flex;
  align-items: center;
  background: rgba(255, 215, 0, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
}
.agents-grid .agent-card .card-header .agent-rating .rating-text.data-v-4fe86039 {
  font-size: 20rpx;
  color: #FFD700;
  margin-left: 4rpx;
  font-weight: 600;
}
.agents-grid .agent-card .card-content.data-v-4fe86039 {
  margin-bottom: 24rpx;
}
.agents-grid .agent-card .card-content .agent-name.data-v-4fe86039 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 8rpx;
}
.agents-grid .agent-card .card-content .agent-desc.data-v-4fe86039 {
  display: block;
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
  margin-bottom: 16rpx;
}
.agents-grid .agent-card .card-content .agent-tags.data-v-4fe86039 {
  display: flex;
  gap: 8rpx;
  margin-bottom: 16rpx;
}
.agents-grid .agent-card .card-content .agent-tags .tag.data-v-4fe86039 {
  font-size: 20rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}
.agents-grid .agent-card .card-content .agent-stats.data-v-4fe86039 {
  display: flex;
  gap: 16rpx;
}
.agents-grid .agent-card .card-content .agent-stats .stat-item.data-v-4fe86039 {
  display: flex;
  align-items: center;
}
.agents-grid .agent-card .card-content .agent-stats .stat-item .stat-text.data-v-4fe86039 {
  font-size: 20rpx;
  color: #8E8E93;
  margin-left: 4rpx;
}
.agents-grid .agent-card .card-action.data-v-4fe86039 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.agents-grid .agent-card .card-action .price-info .price.data-v-4fe86039 {
  font-size: 24rpx;
  font-weight: 600;
  color: #1D1D1F;
}
.agents-grid .agent-card .card-action .price-info .price.free.data-v-4fe86039 {
  color: #10B981;
}
.agents-grid .agent-card .card-action .action-btn.data-v-4fe86039 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #FFFFFF;
  font-size: 22rpx;
  font-weight: 500;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
}
.agents-grid .agent-card .card-action .action-btn .btn-text.data-v-4fe86039 {
  color: #FFFFFF;
}
.agents-list .agent-item.data-v-4fe86039 {
  display: flex;
  align-items: center;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}
.agents-list .agent-item.data-v-4fe86039:active {
  transform: scale(0.98);
}
.agents-list .agent-item .item-avatar.data-v-4fe86039 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  margin-right: 24rpx;
}
.agents-list .agent-item .item-content.data-v-4fe86039 {
  flex: 1;
}
.agents-list .agent-item .item-content .item-header.data-v-4fe86039 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}
.agents-list .agent-item .item-content .item-header .item-name.data-v-4fe86039 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1D1D1F;
}
.agents-list .agent-item .item-content .item-header .item-rating.data-v-4fe86039 {
  display: flex;
  align-items: center;
}
.agents-list .agent-item .item-content .item-header .item-rating .rating-text.data-v-4fe86039 {
  font-size: 20rpx;
  color: #FFD700;
  margin-left: 4rpx;
}
.agents-list .agent-item .item-content .item-desc.data-v-4fe86039 {
  display: block;
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
  margin-bottom: 16rpx;
}
.agents-list .agent-item .item-content .item-footer.data-v-4fe86039 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.agents-list .agent-item .item-content .item-footer .item-tags.data-v-4fe86039 {
  display: flex;
  gap: 8rpx;
}
.agents-list .agent-item .item-content .item-footer .item-tags .mini-tag.data-v-4fe86039 {
  font-size: 18rpx;
  color: #8E8E93;
  background: #F2F2F7;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}
.agents-list .agent-item .item-content .item-footer .item-price .price-text.data-v-4fe86039 {
  font-size: 22rpx;
  font-weight: 600;
  color: #1D1D1F;
}
.agents-list .agent-item .item-content .item-footer .item-price .price-text.free.data-v-4fe86039 {
  color: #10B981;
}
.agents-list .agent-item .item-action .action-btn.data-v-4fe86039 {
  width: 64rpx;
  height: 64rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.agents-list .agent-item .item-action .action-btn.mini.data-v-4fe86039 {
  width: 56rpx;
  height: 56rpx;
  border-radius: 28rpx;
}
.bottom-safe-area.data-v-4fe86039 {
  height: calc(120rpx + env(safe-area-inset-bottom));
}
@media (prefers-color-scheme: dark) {
.ai-agents-market.data-v-4fe86039 {
    background: linear-gradient(180deg, #1c1c1e 0%, #000000 100%);
}
.header-section.data-v-4fe86039 {
    background: #1c1c1e;
    border-bottom-color: #38383a;
}
.agent-card.data-v-4fe86039,
.agent-item.data-v-4fe86039 {
    background: #2c2c2e;
    border-color: #38383a;
}
.agent-name.data-v-4fe86039,
.item-name.data-v-4fe86039 {
    color: #ffffff !important;
}
.search-box.data-v-4fe86039 {
    background: #38383a !important;
}
.category-item.data-v-4fe86039 {
    background: #38383a;
}
.category-item:not(.active) .category-text.data-v-4fe86039 {
    color: #8e8e93;
}
}