"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const currentTab = common_vendor.ref(0);
    const tabList = [
      {
        text: "首页",
        icon: "icon-home",
        selectedIcon: "icon-home-fill",
        pagePath: "/pages/index/index"
      },
      {
        text: "翻译",
        icon: "icon-translate",
        selectedIcon: "icon-translate-fill",
        pagePath: "/pages/translation/text/index"
      },
      {
        text: "AI助手",
        icon: "icon-robot",
        selectedIcon: "icon-robot-fill",
        pagePath: "/pages/digital-human/chat/index",
        badge: "New"
      },
      {
        text: "智能体",
        icon: "icon-apps",
        selectedIcon: "icon-apps-fill",
        pagePath: "/pages/ai-agents/market/index"
      },
      {
        text: "我的",
        icon: "icon-user",
        selectedIcon: "icon-user-fill",
        pagePath: "/pages/user/profile/index"
      }
    ];
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        const currentRoute = "/" + currentPage.route;
        const tabIndex = tabList.findIndex((item) => item.pagePath === currentRoute);
        if (tabIndex !== -1) {
          currentTab.value = tabIndex;
        }
      }
    });
    const switchTab = (index) => {
      if (currentTab.value === index)
        return;
      currentTab.value = index;
      const targetPage = tabList[index].pagePath;
      common_vendor.index.switchTab({
        url: targetPage,
        fail: (err) => {
          common_vendor.index.__f__("error", "at components/TabBar/index.vue:91", "Tab switch failed:", err);
          common_vendor.index.reLaunch({
            url: targetPage
          });
        }
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(tabList, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.n(currentTab.value === index ? item.selectedIcon : item.icon),
            b: item.badge
          }, item.badge ? {
            c: common_vendor.t(item.badge)
          } : {}, {
            d: currentTab.value === index ? 1 : "",
            e: common_vendor.t(item.text),
            f: currentTab.value === index ? 1 : "",
            g: currentTab.value === index
          }, currentTab.value === index ? {} : {}, {
            h: index,
            i: currentTab.value === index ? 1 : "",
            j: common_vendor.o(($event) => switchTab(index), index)
          });
        })
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5b533c2d"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/TabBar/index.js.map
