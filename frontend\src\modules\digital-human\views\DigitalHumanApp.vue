<template>
  <div class="digital-human-app-center">
    <!-- 认证状态通知 -->
    <auth-status-notification />
    
    <!-- 错误边界组件 -->
    <error-boundary>
      <!-- 组件加载检查 -->
      <div v-if="!componentReady" class="loading-container" v-loading="true" element-loading-text="正在初始化组件...">
        <div style="height: 200px;"></div>
      </div>
      
      <template v-else>
        <!-- 顶部导航 -->
        <div class="app-center-nav">
          <div class="nav-left">
            <h2 class="platform-title">数字人应用平台</h2>
          </div>
          <div class="nav-right">
            <!-- 简洁美观的搜索框 -->
            <div class="search-container">
              <el-input
                v-model="searchQuery"
                placeholder="搜索数字人应用..."
                class="search-input"
                @input="onSearch"
                clearable
              >
                <template #suffix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
            
            <!-- 改进的用户下拉菜单 -->
            <el-dropdown @command="handleUserMenuClick">
              <span class="user-dropdown" :class="{ 'not-logged-in': !isAuthenticated }">
                <el-avatar :size="32" class="user-avatar" :class="{ 'guest-avatar': !isAuthenticated }">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="username">{{ displayUsername }}</span>
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-if="!isAuthenticated" command="login">
                    <el-icon><User /></el-icon>
                    登录账号
                  </el-dropdown-item>
                  <template v-else>
                    <el-dropdown-item command="profile">
                      <el-icon><User /></el-icon>
                      个人中心
                    </el-dropdown-item>
                    <el-dropdown-item command="settings">
                      <el-icon><Setting /></el-icon>
                      账号设置
                    </el-dropdown-item>
                    <el-dropdown-item divided command="logout">
                      <el-icon><SwitchButton /></el-icon>
                      退出登录
                    </el-dropdown-item>
                  </template>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="app-center-main">
          <!-- 侧边栏导航 -->
          <div class="app-sidebar">
            <div class="sidebar-section">
              <h3 class="sidebar-title">我的工作台</h3>
              <el-menu
                mode="vertical"
                :default-active="currentCategory"
                @select="handleMenuSelect"
              >
                <el-menu-item index="recent">
                  <el-icon><Clock /></el-icon>
                  <span>最近使用</span>
                </el-menu-item>
                <el-menu-item index="favorites">
                  <el-icon><Star /></el-icon>
                  <span>我的收藏</span>
                </el-menu-item>
                <el-menu-item index="my-creations">
                  <el-icon><User /></el-icon>
                  <span>我的创建</span>
                </el-menu-item>

                <!-- 创建数字人选项 -->
                <el-menu-item index="create-new" class="create-dh-menu-item">
                  <el-icon><Plus /></el-icon>
                  <span>创建数字人</span>
                </el-menu-item>
              </el-menu>
            </div>

            <el-divider style="margin: 12px 0" />

            <div class="sidebar-section">
              <h3 class="sidebar-title">应用分类</h3>
              <el-menu
                mode="vertical"
                :default-active="currentCategory"
                @select="handleMenuSelect"
                class="category-menu"
              >
                <el-menu-item index="all">
                  <el-icon><Grid /></el-icon>
                  <span>全部应用</span>
                </el-menu-item>
                <el-menu-item index="assistant">
                  <el-icon><User /></el-icon>
                  <span>智能助手</span>
                </el-menu-item>
                <el-menu-item index="customer_service">
                  <el-icon><Headset /></el-icon>
                  <span>客服专员</span>
                </el-menu-item>
                <el-menu-item index="broadcaster">
                  <el-icon><VideoCamera /></el-icon>
                  <span>主播讲解员</span>
                </el-menu-item>
                <el-menu-item index="teacher">
                  <el-icon><Reading /></el-icon>
                  <span>教育培训</span>
                </el-menu-item>
              </el-menu>
            </div>
          </div>

          <!-- 应用内容区域 -->
          <div class="app-content">
            <!-- 推荐应用轮播 -->
            <div class="featured-apps" v-if="currentCategory === 'all'">
              <h2 class="section-title">推荐应用</h2>
              <el-carousel :autoplay="true" class="featured-carousel" height="280px">
                <el-carousel-item v-for="(item, index) in featuredApps" :key="index">
                  <div class="carousel-content" :style="{ backgroundImage: `url(${item.backgroundUrl})` }">
                    <div class="carousel-info">
                      <h3>{{ item.title }}</h3>
                      <p>{{ item.description }}</p>
                      <el-button type="primary" @click="openApp(item)">立即使用</el-button>
                    </div>
                    <div class="carousel-image">
                      <img :src="item.imageUrl" :alt="item.title" />
                    </div>
                  </div>
                </el-carousel-item>
              </el-carousel>
            </div>

            <!-- 最近使用/我的收藏标题 -->
            <div v-if="['recent', 'favorites', 'my-creations'].includes(currentCategory)">
              <h2 class="section-title">
                {{ 
                  currentCategory === 'recent' ? '最近使用' : 
                  currentCategory === 'favorites' ? '我的收藏' : '我的创建'
                }}
              </h2>
            </div>

            <!-- 应用类别标题 -->
            <div v-if="['assistant', 'customer_service', 'broadcaster', 'teacher'].includes(currentCategory)">
              <h2 class="section-title">
                {{ 
                  currentCategory === 'assistant' ? '智能助手' : 
                  currentCategory === 'customer_service' ? '客服专员' :
                  currentCategory === 'broadcaster' ? '主播讲解员' : '教育培训'
                }}
              </h2>
            </div>

            <!-- 应用列表 -->
            <div class="app-list" v-if="paginatedApps && paginatedApps.length > 0">
              <el-row :gutter="24">
                <el-col :xs="24" :sm="12" :md="8" :lg="8" v-for="app in paginatedApps" :key="app.id" class="app-col">
                  <div class="app-card" :class="`type-${app.type}`">
                    <div class="app-media">
                      <!-- 视频预览 (如果有) -->
                      <video
                        v-if="app.video_url || app.videoUrl"
                        class="app-video"
                        :src="app.video_url || app.videoUrl"
                        loop
                        muted
                        autoplay
                        @click.stop="previewApp(app)"
                      ></video>
                      <!-- 图片展示 (如果没有视频) -->
                      <img
                        v-else
                        class="app-image"
                        :src="app.thumbnail_url || app.avatar_url || app.iconUrl || app.image_url || getDefaultAvatar(app)"
                        :alt="app.name"
                        @click.stop="previewApp(app)"
                        @error="$event.target.src = getDefaultAvatar(app)"
                      />
                      
                      <!-- 播放按钮 -->
                      <div class="play-btn-wrapper" v-if="app.videoUrl" @click.stop="previewApp(app)">
                        <el-button class="play-button" type="primary" circle>
                          <el-icon><VideoPlay /></el-icon>
                        </el-button>
                      </div>
                      
                      <!-- 视频控制覆盖层 -->
                      <div class="media-overlay">
                        <div class="overlay-top">
                          <!-- 类型标识 -->
                          <div class="type-badge" :class="`type-${app.type}`">
                            <span class="type-icon">{{ getTypeIcon(app.type) }}</span>
                            <span class="type-name">{{ getTypeName(app.type) }}</span>
                          </div>

                          <el-tooltip content="收藏">
                            <el-button
                              text
                              circle
                              class="favorite-btn"
                              :class="{ 'favorited-btn': app.isFavorite }"
                              @click.stop="toggleFavorite(app.id)"
                            >
                              <el-icon>
                                <Star v-if="!app.isFavorite" />
                                <StarFilled v-else />
                              </el-icon>
                            </el-button>
                          </el-tooltip>
                        </div>
                        
                        <div class="overlay-bottom">
                          <h3 class="app-name">{{ app.name }}</h3>
                          <p class="app-desc">{{ app.description }}</p>
                          <div class="app-meta">
                            <span class="app-uses">
                              <team-outlined /> {{ app.usageCount || 0 }}次使用
                            </span>
                            <span class="app-id" v-if="app.digital_human_id">
                              ID: {{ app.digital_human_id }}
                            </span>
                          </div>
                          <div class="overlay-actions">
                            <el-button type="primary" round size="small" @click.stop="openApp(app)">
                              <el-icon><ChatDotRound /></el-icon> 开始对话
                            </el-button>
                            <el-button round size="small" @click.stop="viewAppDetails(app.digital_human_id || app.id)">
                              <el-icon><Edit /></el-icon> 编辑
                            </el-button>
                          </div>
                        </div>
                      </div>
                      
                      <!-- 类型标签 - 始终可见 -->
                      <div class="app-badge" v-if="app.type">
                        {{ getCategoryName(app.type || 'other') }}
                      </div>
                      
                      <!-- 名称显示 - 非悬停状态 -->
                      <div class="app-name-overlay">
                        <h3 class="app-name-visible">{{ app.name }}</h3>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 分页组件 -->
            <div v-if="!isLoading && totalItems > pageSize" class="pagination-container">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :total="totalItems"
                :page-sizes="[6, 12, 24, 48]"
                layout="total, sizes, prev, pager, next, jumper"
                class="pagination"
                @current-change="onPageChange"
                @size-change="onPageSizeChange"
              />
            </div>

            <!-- 加载状态显示 -->
            <div v-if="isLoading" class="loading-state">
              <div v-loading="true" element-loading-text="正在加载数字人应用..." style="height: 200px;">
              </div>
            </div>

            <!-- 空状态展示 -->
            <div v-if="!isLoading && filteredApps && filteredApps.length === 0" class="empty-state">
              <el-empty :description="getEmptyStateDescription()">
                <el-button type="primary" @click="createNew">创建数字人</el-button>
              </el-empty>
            </div>
          </div>
        </div>
        <CreateDigitalHumanModal
          v-model:visible="state.showCreateModal"
          @onComplete="onSubmitComplete"
        />
        
        <!-- 应用预览模态框 -->
        <el-dialog
          v-model="state.previewVisible"
          :title="state.previewApp?.name || '数字人预览'"
          class="app-preview-modal"
          width="700px"
          @cancel="closePreview"
        >
          <div class="preview-content" v-if="state.previewApp">
            <div class="preview-media">
              <!-- 视频预览 -->
              <video 
                v-if="state.previewApp.videoUrl" 
                class="preview-video"
                :src="state.previewApp.videoUrl" 
                controls
                autoplay
                loop
              ></video>
              <!-- 图片预览 -->
              <img 
                v-else
                class="preview-image" 
                :src="state.previewApp.iconUrl || getDefaultAvatar(state.previewApp)" 
                :alt="state.previewApp.name" 
              />
            </div>
            <div class="preview-info">
              <h2>{{ state.previewApp.name }}</h2>
              <p class="preview-desc">{{ state.previewApp.description }}</p>
              <div class="preview-meta">
                <p><strong>类别:</strong> {{ getCategoryName(state.previewApp.category) }}</p>
                <p><strong>创建时间:</strong> {{ formatDate(state.previewApp.createTime) }}</p>
                <p v-if="state.previewApp.digital_human_id"><strong>数字人ID:</strong> {{ state.previewApp.digital_human_id }}</p>
              </div>
            </div>
            <div class="preview-actions">
              <el-button type="primary" size="large" @click="openApp(state.previewApp)">
                <el-icon><ChatDotRound /></el-icon>
                开始对话
              </el-button>
              <el-button type="success" size="large" @click="startRealtimeChat(state.previewApp)">
                <el-icon><VideoCamera /></el-icon>
                实时视频对话
              </el-button>
              <el-button size="large" @click="viewAppDetails(state.previewApp.digital_human_id || state.previewApp.id)">
                <el-icon><Edit /></el-icon>
                编辑数字人
              </el-button>
              <el-button size="large" @click="closePreview">关闭</el-button>
            </div>
          </div>
          <div class="empty-preview" v-else>
            <el-empty description="无法加载预览内容" />
          </div>
        </el-dialog>
      </template>
    </error-boundary>
  </div>
</template>

<script>
import { defineComponent, ref, computed, onMounted, watch, reactive, toRefs, nextTick } from 'vue';
import { useRouter } from 'vue-router';
// Element Plus icons are already imported below
import { getStaticResourceUrl } from '../../../utils/asset-path';
import { useDigitalHumanStore } from '../../../stores/digitalHuman';
import { useAuthStore } from '../../../auth';
import { storeToRefs } from 'pinia';
import * as digitalHumanApi from '../../../api/digital-human';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Grid,
  User,
  Headset,
  VideoCamera,
  Reading,
  VideoPlay,
  Star,
  StarFilled,
  ChatDotRound,
  Edit,
  ArrowDown,
  Clock,
  Service,
  Shop,
  PriceTag,
  UserFilled,
  Plus,
  InfoFilled,
  Grid as AppstoreIcon,
  View,
  Message,
  Check,
  Search,
  Right,
  SwitchButton,
  Setting
} from '@element-plus/icons-vue';
import AuthStatusNotification from '../../../components/common/AuthStatusNotification.vue';
import CreateDigitalHumanModal from '../components/CreateDigitalHumanModal.vue';
import ErrorBoundary from '../../../components/common/ErrorBoundary.vue';
import { getDigitalHumanApiId, normalizeDigitalHumanId, ensureStringId, isDigitalHumanId } from '../../../utils/digital-human-helper';

export default defineComponent({
  name: 'DigitalHumanAppCenter',
  
  components: {
    User,
    ArrowDown,
    Clock,
    Star,
    Service,
    VideoCamera,
    Reading,
    Shop,
    PriceTag,
    UserFilled,
    Plus,
    InfoFilled,
    AppstoreIcon,
    AuthStatusNotification,
    View,
    VideoPlay,
    Edit,
    CreateDigitalHumanModal,
    ErrorBoundary,
    Message,
    Check,
    Search,
    Right,
    SwitchButton,
    Setting
  },
  
  setup() {
    const router = useRouter();
    const authStore = useAuthStore();
    const username = ref('');
    const searchQuery = ref('');
    const currentCategory = ref('all');
    const searchFocused = ref(false);

    // 分页相关
    const currentPage = ref(1);
    const pageSize = ref(12); // 每页显示12个数字人
    const totalItems = ref(0);

    // 组件就绪状态
    const componentReady = ref(false);
    
    // 使用数字人store
    const digitalHumanStore = useDigitalHumanStore();
    const { digitalHumans, loading: isLoading, error } = storeToRefs(digitalHumanStore);
    
    // 认证状态相关
    const tokenHealth = ref({
      isHealthy: true, // 默认认为token是健康的
      expiresIn: null
    });
    
    // 使用auth系统的登录状态
    const isAuthenticated = computed(() => {
      return authStore.isAuthenticated;
    });

    // 显示的用户名
    const displayUsername = computed(() => {
      if (isAuthenticated.value) {
        // 优先使用authStore中的用户信息
        const user = authStore.user || authStore.currentUser;
        if (user?.username) {
          return user.username;
        }

        // 备用：使用组件内的username
        if (username.value && username.value !== '用户') {
          return username.value;
        }

        return '用户';
      }
      return '未登录';
    });
    
    // 收藏状态 (实际应用中这个应该从服务器获取)
    const favoriteDigitalHumans = ref([]);
    const recentDigitalHumans = ref([]);
    
    // 响应式状态管理
    const state = reactive({
      digitalHumanList: [],
      loading: false,
      selectedCategory: 'all',
      searchQuery: '',
      featuredApps: [],
      showCreateModal: false,
      previewVisible: false,
      previewApp: null,
    });
    
    // 映射类型到分类的函数
    const mapTypeToCategory = (type) => {
      const typeMap = {
        'assistant': 'assistant',
        'customer-service': 'customer_service',
        'customer_service': 'customer_service',
        'broadcaster': 'broadcaster',
        'teacher': 'teacher',
        'education': 'teacher'
      };
      
      return typeMap[type] || 'other';
    };
    
    // 获取分类名称
    const getCategoryName = (category) => {
      const categoryMap = {
        'assistant': '智能助手',
        'customer_service': '客服专员',
        'broadcaster': '主播讲解员',
        'teacher': '教育培训'
      };
      
      return categoryMap[category] || '其他类型';
    };
    
    // 过滤后的应用列表
    const filteredApps = computed(() => {
      if (!digitalHumans.value) return [];
      
      // 首先映射数据格式
      let apps = digitalHumans.value.map(dh => {
        // 标准化ID处理
        const humanIdStr = dh.id ? String(dh.id) : null;
        const originalIdStr = dh.original_id ? String(dh.original_id) : null;
        const digitalHumanIdStr = dh.digital_human_id ? String(dh.digital_human_id) : null;
        
        if (!humanIdStr && !originalIdStr && !digitalHumanIdStr) {
          console.warn('数字人缺少有效ID:', dh);
          return null;
        }
        
        return {
          id: humanIdStr,
          original_id: originalIdStr,
          digital_human_id: digitalHumanIdStr,
          name: dh.name || '未命名数字人',
          description: dh.description || '智能数字人助手',
          iconUrl: dh.thumbnail_url || dh.avatar_url,
          videoUrl: dh.video_url || null,
          thumbnailUrl: dh.thumbnail_url,
          avatar_url: dh.avatar_url,
          thumbnail_url: dh.thumbnail_url,
          type: dh.type || 'assistant',
          category: mapTypeToCategory(dh.type || 'assistant'),
          usageCount: dh.usage_count || 0,
          isFavorite: favoriteDigitalHumans.value.includes(humanIdStr) || 
                      (originalIdStr && favoriteDigitalHumans.value.includes(originalIdStr)) || 
                      (digitalHumanIdStr && favoriteDigitalHumans.value.includes(digitalHumanIdStr)),
          isRecent: recentDigitalHumans.value.includes(humanIdStr) || 
                    (originalIdStr && recentDigitalHumans.value.includes(originalIdStr)) || 
                    (digitalHumanIdStr && recentDigitalHumans.value.includes(digitalHumanIdStr)),
          gender: dh.gender || 'neutral',
          createdAt: dh.created_at,
          updatedAt: dh.updated_at,
          voice_id: dh.voice_id
        };
      }).filter(app => app !== null);
      
      // 根据当前分类或搜索条件过滤
      const filtered = apps.filter(app => {
        // 搜索过滤
        if (searchQuery.value) {
          const query = searchQuery.value.toLowerCase();
          return app.name.toLowerCase().includes(query) ||
                 (app.description && app.description.toLowerCase().includes(query));
        }

        // 分类过滤
        if (currentCategory.value === 'all') {
          return true; // 显示所有应用
        } else if (currentCategory.value === 'recent') {
          return app.isRecent;
        } else if (currentCategory.value === 'favorites') {
          return app.isFavorite;
        } else if (currentCategory.value === 'my-creations') {
          return true; // 所有都是用户创建的
        } else {
          return app.category === currentCategory.value;
        }
      });

      // 更新总数 - 使用后端返回的总数
      totalItems.value = digitalHumanStore.pagination?.total || filtered.length;

      return filtered;
    });

    // 直接使用过滤后的应用列表（后端已经分页）
    const paginatedApps = computed(() => {
      return filteredApps.value;
    });
    
    // 轮播图推荐应用 - 从数字人列表中选择推荐
    const featuredApps = computed(() => {
      // 从数字人列表中选择最多3个作为推荐
      const featured = digitalHumans.value.slice(0, 3).map(dh => {
        // 确保每个数字人都有有效ID
        const humanId = dh.id || dh.digital_human_id || `unknown-${Math.random().toString(36).substring(2, 10)}`;
        
        return {
          id: humanId,
          title: dh.name || '智能数字人',
          description: dh.description || '智能数字人，为您提供专业服务',
          backgroundUrl: 'linear-gradient(135deg, #1890ff 0%, #36cfc9 100%)',
          imageUrl: dh.avatar_url || getStaticResourceUrl('/images/digital-human/avatar1.png')
        };
      });
      
      // 如果没有足够的数字人，添加默认推荐
      if (featured.length < 3) {
        const defaultFeatured = [
          {
            id: 'feat1',
            title: '智能客服数字人',
            description: '7×24小时在线，自动响应客户问题，提升客户满意度',
            backgroundUrl: 'linear-gradient(135deg, #1890ff 0%, #36cfc9 100%)',
            imageUrl: getStaticResourceUrl('/images/digital-human/avatar1.png')
          },
          {
            id: 'feat2',
            title: '数字主播系统',
            description: '为电商直播、产品发布会提供专业数字主播服务',
            backgroundUrl: 'linear-gradient(135deg, #722ed1 0%, #eb2f96 100%)',
            imageUrl: getStaticResourceUrl('/images/digital-human/avatar2.png')
          },
          {
            id: 'feat3',
            title: '教育培训助手',
            description: '智能教学助手，提供个性化教学体验',
            backgroundUrl: 'linear-gradient(135deg, #52c41a 0%, #13c2c2 100%)',
            imageUrl: getStaticResourceUrl('/images/digital-human/avatar3.png')
          }
        ];
        
        // 添加缺失的默认推荐
        while (featured.length < 3) {
          featured.push(defaultFeatured[featured.length]);
        }
      }
      
      return featured;
    });
    
    // 数据状态监控 - 开发调试用
    watch(
      [digitalHumans, () => filteredApps.value?.length || 0, currentCategory],
      ([newDigitalHumans, newFilteredCount, newCategory], [oldDigitalHumans, oldFilteredCount, oldCategory]) => {
        console.log(
          `数据状态更新: 总数字人: ${newDigitalHumans?.length || 0} (之前: ${oldDigitalHumans?.length || 0}), ` +
          `过滤后: ${newFilteredCount} (之前: ${oldFilteredCount}), ` +
          `类别: ${newCategory} (之前: ${oldCategory})`
        );
        
        if (newFilteredCount === 0 && (newDigitalHumans?.length || 0) > 0) {
          console.log('警告: 有数据但过滤后为空');
          
          // 打印所有数据的类型分布，帮助诊断类别过滤问题
          if (newCategory !== 'all') {
            const typeDistribution = {};
            newDigitalHumans.forEach(dh => {
              const type = dh.type || 'unknown';
              typeDistribution[type] = (typeDistribution[type] || 0) + 1;
            });
            console.log('数据类型分布:', typeDistribution);
            
            const categoryDistribution = {};
            newDigitalHumans.forEach(dh => {
              const category = mapTypeToCategory(dh.type || 'assistant');
              categoryDistribution[category] = (categoryDistribution[category] || 0) + 1;
            });
            console.log('数据类别分布:', categoryDistribution);
          }
        }
      },
      { immediate: true }
    );
    
    // 获取默认头像
    const getDefaultAvatar = (app) => {
      // 添加空值检查
      if (!app) {
        console.warn('getDefaultAvatar: 应用对象为空');
        return getStaticResourceUrl('/images/digital-human/avatar3.png'); // 返回一个默认头像
      }
      
      const gender = app.gender || 'neutral';
      const type = app.type || 'assistant';
      
      if (gender === 'female') {
        return getStaticResourceUrl('/images/digital-human/avatar1.png');
      } else if (gender === 'male') {
        return getStaticResourceUrl('/images/digital-human/avatar2.png');
      } else {
        return getStaticResourceUrl('/images/digital-human/avatar3.png');
      }
    };
    
    // 获取空状态描述
    const getEmptyStateDescription = () => {
      if (isLoading.value) {
        return '正在加载数字人应用...';
      }
      
      // 添加更多诊断信息
      if (error.value) {
        return `加载失败: ${error.value}`;
      }
      
      if (digitalHumans.value?.length === 0) {
        return '数据库中暂无数字人应用，请创建新的数字人';
      }
      
      if (currentCategory.value === 'recent') {
        return '暂无最近使用的应用';
      } else if (currentCategory.value === 'favorites') {
        return '暂无收藏的应用';
      } else if (currentCategory.value === 'my-creations') {
        return '您还没有创建任何数字人应用';
      } else if (currentCategory.value !== 'all') {
        return `暂无${getCategoryName(currentCategory.value)}类别的应用`;
      } else if (searchQuery.value) {
        return `没有找到包含"${searchQuery.value}"的应用`;
      } else {
        return '暂无符合条件的应用，请尝试其他筛选条件';
      }
    };
    
    // 获取空状态图片URL - 新增方法
    const getEmptyImage = () => {
      // 使用本地已有的图片资源
      return '/images/digital-human/avatar-placeholder.png';
    };
    
    // 处理菜单点击
    const handleMenuClick = (e) => {
      if (e.key === 'create-new') {
        createNewWithAuth();
        return;
      }
      currentCategory.value = e.key;
      // 切换分类时重置到第一页
      currentPage.value = 1;
    };

    // 处理Element Plus菜单选择
    const handleMenuSelect = (key) => {
      if (key === 'create-new') {
        createNewWithAuth();
        return;
      }
      currentCategory.value = key;
      // 切换分类时重置到第一页
      currentPage.value = 1;
    };

    // 分页相关方法
    const onPageChange = async (page) => {
      console.log(`切换到第${page}页，每页${pageSize.value}条`);
      currentPage.value = page;
      // 重新获取数据
      await digitalHumanStore.fetchDigitalHumans({
        page: currentPage.value,
        page_size: pageSize.value,
        category: 'all'
      });
      console.log(`第${page}页数据获取完成，当前数字人数量:`, digitalHumans.value?.length);
      console.log('第一个数字人的ID:', digitalHumans.value?.[0]?.id);
      console.log('最后一个数字人的ID:', digitalHumans.value?.[digitalHumans.value.length - 1]?.id);
      console.log('过滤后的应用数量:', filteredApps.value?.length);
      console.log('分页后的应用数量:', paginatedApps.value?.length);
      console.log('第一个应用:', filteredApps.value?.[0]);
      // 滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const onPageSizeChange = async (current, size) => {
      pageSize.value = size;
      currentPage.value = 1; // 重置到第一页
      // 重新获取数据
      await digitalHumanStore.fetchDigitalHumans({
        page: currentPage.value,
        page_size: pageSize.value,
        category: 'all'
      });
      // 滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    // 获取类型图标
    const getTypeIcon = (type) => {
      const icons = {
        assistant: '🤖',
        customer_service: '👨‍💼',
        broadcaster: '📺',
        teacher: '👩‍🏫',
        entertainer: '🎭',
        consultant: '💼'
      };
      return icons[type] || '🤖';
    };

    // 获取类型名称
    const getTypeName = (type) => {
      const names = {
        assistant: '智能助手',
        customer_service: '客服专员',
        broadcaster: '主播讲解',
        teacher: '教育培训',
        entertainer: '娱乐互动',
        consultant: '专业顾问'
      };
      return names[type] || '智能助手';
    };
    
    // 处理用户菜单点击
    const handleUserMenuClick = (e) => {
      if (e.key === 'logout') {
        // 清除认证信息
        localStorage.removeItem('token');
        sessionStorage.removeItem('token');
        localStorage.removeItem('auth_token');
        sessionStorage.removeItem('auth_token');
        localStorage.removeItem('user_info');
        ElMessage.success('退出登录成功');
        
        // 刷新页面或跳转到登录页
        router.push('/user/login');
      } else if (e.key === 'login') {
        router.push('/user/login');
      } else if (e.key === 'profile') {
        router.push('/user/profile');
      } else if (e.key === 'settings') {
        router.push('/user/settings');
      }
    };
    
    // 搜索应用
    const onSearch = (value) => {
      searchQuery.value = value;
    };
    
    // 打开应用
    const openApp = (app) => {
      console.log('打开应用:', app);
      
      // 检查app对象是否有效
      if (!app || (!app.id && !app.digital_human_id)) {
        console.error('无效的应用对象:', app);
        ElMessage.error('无法打开应用，数据无效');
        return;
      }
      
      // 使用辅助工具获取API ID
      let routeId = getDigitalHumanApiId(app);
      if (!routeId) {
        console.error('无法获取有效的数字人ID:', app);
        ElMessage.error('无法打开应用，ID无效');
        return;
      }
      
      console.log(`跳转到聊天页面，使用ID: ${routeId} (digital_human_id: ${app.digital_human_id}, id: ${app.id})`);

      // 跳转到数字人聊天页面
      router.push({
        name: 'DigitalHumanChat',
        params: { id: routeId }
      });
    };
    
    // 查看应用详情
    const viewAppDetails = (id) => {
      // 检查ID是否有效
      if (!id) {
        console.error('无效的数字人ID:', id);
        ElMessage.error('无法查看详情，ID无效');
        return;
      }

      // 确保id是字符串类型
      const idStr = ensureStringId(id);
      
      // 获取完整的应用数据
      let app = null;
      if (digitalHumans.value && digitalHumans.value.length > 0) {
        // 使用标准化ID比较找到匹配的数字人
        app = digitalHumans.value.find(dh => {
          const dhId = ensureStringId(dh.id);
          const dhDigitalHumanId = ensureStringId(dh.digital_human_id);
          const normalizedInputId = normalizeDigitalHumanId(idStr);
          const normalizedDhId = normalizeDigitalHumanId(dhId);
          const normalizedDhDigitalHumanId = normalizeDigitalHumanId(dhDigitalHumanId);
          
          return normalizedInputId === normalizedDhId || normalizedInputId === normalizedDhDigitalHumanId;
        });
      }
      
      // 优先使用digital_human_id
      let routeId = idStr;
      if (app && app.digital_human_id) {
        routeId = app.digital_human_id;
        console.log(`使用digital_human_id跳转: ${routeId}`);
      } else {
        console.log(`使用原始ID跳转: ${routeId}`);
      }
      
      // 如果是无效ID格式，进行处理
      if (routeId.startsWith('unknown-')) {
        console.error('无效的数字人ID:', routeId);
        ElMessage.error('无法查看详情，ID无效');
        return;
      }
      
      console.log(`查看应用详情: ${routeId}`);
      // 跳转到数字人编辑页面
      router.push({
        name: 'DigitalHumanEdit',
        params: { id: routeId }
      });
    };
    
    // 切换收藏状态
    let toggleFavorite = async (id) => {
      try {
        // 检查ID是否有效
        if (!id) {
          console.error('无效的数字人ID:', id);
          ElMessage.error('无法添加收藏，ID无效');
          return;
        }
        
        // 确保id是字符串类型
        const idStr = String(id);
        if (idStr.startsWith('unknown-')) {
          console.error('无效的数字人ID:', id);
          ElMessage.error('无法添加收藏，ID无效');
          return;
        }
        
        const isFavorite = !favoriteDigitalHumans.value.includes(idStr);
        await digitalHumanApi.toggleFavoriteDigitalHuman(idStr, isFavorite);
        
        if (isFavorite) {
          favoriteDigitalHumans.value.push(idStr);
          ElMessage.success(`已将数字人添加到收藏`);
        } else {
          const index = favoriteDigitalHumans.value.indexOf(idStr);
          if (index > -1) {
            favoriteDigitalHumans.value.splice(index, 1);
          }
          ElMessage.success(`已将数字人从收藏中移除`);
        }
      } catch (error) {
        console.error('收藏操作失败:', error);
        ElMessage.error('操作失败，请稍后重试');
      }
    };
    
    // 创建新数字人
    let createNew = () => {
      // 跳转到创建页面
      router.push({ name: 'DigitalHumanCreate' });
    };
    
    // 加载用户信息
    const loadUserInfo = async () => {
      try {
        console.log('[用户信息] 开始加载用户信息');

        // 1. 检查authStore中是否已有用户信息
        if (authStore.user?.username) {
          console.log('[用户信息] 从authStore获取用户名:', authStore.user.username);
          username.value = authStore.user.username;
          return;
        }

        // 2. 尝试从token解析用户信息
        const token = authStore.getToken();
        if (token) {
          try {
            const { getUserInfoFromToken } = await import('../../../auth/utils');
            const tokenInfo = getUserInfoFromToken(token);
            if (tokenInfo?.username) {
              console.log('[用户信息] 从token解析用户名:', tokenInfo.username);
              username.value = tokenInfo.username;
              // 更新authStore
              authStore.user = tokenInfo;
              return;
            }
          } catch (parseError) {
            console.warn('[用户信息] token解析失败:', parseError);
          }
        }

        // 3. 尝试从localStorage获取用户信息
        const storedUserInfo = localStorage.getItem('userInfo');
        if (storedUserInfo) {
          try {
            const userInfo = JSON.parse(storedUserInfo);
            if (userInfo.username) {
              console.log('[用户信息] 从localStorage获取用户名:', userInfo.username);
              username.value = userInfo.username;
              authStore.user = userInfo;
              return;
            }
          } catch (parseError) {
            console.warn('[用户信息] localStorage解析失败:', parseError);
          }
        }

        // 4. 尝试从API获取用户信息
        try {
          const auth = await import('../../../auth');
          const userInfo = await auth.fetchUserInfo();

          if (userInfo?.username) {
            console.log('[用户信息] 从API获取用户名:', userInfo.username);
            username.value = userInfo.username;
            authStore.user = userInfo;
            return;
          }
        } catch (apiError) {
          console.warn('[用户信息] API获取失败:', apiError);
        }

        // 5. 最后的备用方案 - 使用默认值
        console.log('[用户信息] 所有方法都失败，使用默认用户名');
        username.value = '用户';

      } catch (error) {
        console.error('[用户信息] 加载用户信息过程中出错:', error);
        username.value = '用户';
      }
    };

    // 加载数据
    onMounted(async () => {
      try {
        console.log('组件挂载，开始加载数据');

        // 简化的用户信息加载
        await loadUserInfo();

        // 强制刷新用户信息显示
        nextTick(() => {
          console.log('[用户信息] 最终用户名:', username.value);
          console.log('[用户信息] authStore.user:', authStore.user);
          console.log('[用户信息] displayUsername:', displayUsername.value);
        });

        // 从API加载数字人列表
        console.log('调用 fetchDigitalHumans');
        await digitalHumanStore.fetchDigitalHumans({
          page: currentPage.value,
          page_size: pageSize.value,
          category: 'all'
        });

        console.log(`数据加载完成，获取到 ${digitalHumans.value?.length || 0} 个数字人`);
        
        // 尝试获取token健康状态
        try {
          const token = localStorage.getItem('token') || sessionStorage.getItem('token');
          if (token) {
            // 简单检查token是否存在且不为空
            tokenHealth.value.isHealthy = token && token.length > 0;
          } else {
            tokenHealth.value.isHealthy = false;
          }
        } catch (tokenError) {
          console.warn('检查token健康状态失败:', tokenError);
          tokenHealth.value.isHealthy = false;
        }
        
        // 获取用户信息
        try {
          // 使用auth store获取用户信息
          if (authStore.currentUser && authStore.currentUser.username) {
            username.value = authStore.currentUser.username;
          } else {
            username.value = '用户';
          }
        } catch (userError) {
          console.warn('获取用户信息失败:', userError);
          username.value = '用户';
        }
        
        // 加载收藏和最近使用的数据
        // 实际应用中应该从API加载这些数据
        if (digitalHumans.value && digitalHumans.value.length > 0) {
          // 获取所有有效的ID（优先使用id，其次使用digital_human_id）
          const ids = digitalHumans.value.map(dh => {
            const id = dh.id || dh.digital_human_id;
            if (!id || id === 'unknown') {
              console.warn('数字人缺少有效ID:', dh);
              return null;
            }
            return id;
          }).filter(id => id !== null);
          
          console.log('收集到的有效ID列表:', ids);
          
          // 随机选择一些作为收藏和最近使用
          if (ids.length > 0) {
            favoriteDigitalHumans.value = ids.slice(0, Math.min(2, ids.length));
            recentDigitalHumans.value = ids.slice(0, Math.min(3, ids.length));
          } else {
            console.warn('没有找到有效的数字人ID');
          }
        }
        
        // 设置组件就绪状态为true
        componentReady.value = true;
      } catch (err) {
        console.error('加载数据失败:', err);
        ElMessage.error('加载数字人应用失败，请刷新页面重试');
      }
    });
    
    // 处理需要认证的操作
    const handleAuthRequiredAction = (action, ...args) => {
      if (!tokenHealth.value.isHealthy) {
        ElMessage.warning('此操作需要登录，请先登录您的账号');
        digitalHumanStore.handleAuthIssue();
        return false;
      }
      
      // 执行原始操作
      return action(...args);
    };
    
    // 修改需要认证的函数 - 使用包装函数而不是重新赋值
    const toggleFavoriteWithAuth = (id) => handleAuthRequiredAction(toggleFavorite, id);
    const createNewWithAuth = () => handleAuthRequiredAction(createNew);
    
    // 打开预览模态框
    const previewApp = (app) => {
      // 添加健壮性检查
      if (!app) {
        console.error('预览应用失败：应用对象为空');
        ElMessage.error('无法预览，数据无效');
        return;
      }
      
      try {
        // 克隆app对象以避免引用问题，确保所有属性都被转换为安全的类型
        state.previewApp = {
          ...app,
          id: app.id ? String(app.id) : '',
          digital_human_id: app.digital_human_id ? String(app.digital_human_id) : '',
          name: app.name || '未命名数字人',
          description: app.description || '暂无描述信息',
          category: app.category || 'other',
          gender: app.gender || 'neutral',
          type: app.type || 'assistant',
          videoUrl: app.videoUrl || null
        };
        
        state.previewVisible = true;
      } catch (error) {
        console.error('打开预览时出错:', error);
        ElMessage.error('预览加载失败');
      }
    };

    // 关闭预览
    const closePreview = () => {
      state.previewVisible = false;
      setTimeout(() => {
        state.previewApp = null;
      }, 300); // 添加延迟以避免关闭动画时内容闪烁
    };
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '未知时间';
      
      try {
        const date = new Date(dateString);
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return '日期格式无效';
        }
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      } catch (error) {
        console.warn('日期格式化失败:', error);
        return '日期格式错误';
      }
    };
    
    // 添加onSubmitComplete函数
    const onSubmitComplete = (result) => {
      // 实现提交完成后的逻辑
      console.log('数字人创建完成', result);
      
      // 刷新数字人列表
      digitalHumanStore.fetchDigitalHumans().then(() => {
        ElMessage.success('创建成功并已刷新列表');
      }).catch(err => {
        console.error('创建成功但刷新列表失败', err);
      });
      
      // 关闭模态框
      state.showCreateModal = false;
    };
    
    // 播放视频
    const playVideo = (event) => {
      if (event.target && event.target.play) {
        event.target.play().catch(err => {
          console.error('视频播放失败:', err);
        });
      }
    };
    
    // 暂停视频
    const pauseVideo = (event) => {
      if (event.target && event.target.pause) {
        event.target.pause();
      }
    };

    // 开始实时聊天
    const startRealtimeChat = (app) => {
      if (!app) {
        ElMessage.error('无法启动实时聊天，数字人信息无效');
        return;
      }

      // 检查是否需要认证
      if (!tokenHealth.value.isHealthy) {
        ElMessage.warning('实时聊天需要登录，请先登录您的账号');
        digitalHumanStore.handleAuthIssue();
        return;
      }

      try {
        // 跳转到实时聊天页面，并传递数字人ID
        const digitalHumanId = app.digital_human_id || app.id;
        if (!digitalHumanId) {
          ElMessage.error('数字人ID无效，无法启动实时聊天');
          return;
        }

        // 关闭预览窗口
        closePreview();

        // 跳转到实时聊天页面
        router.push({
          name: 'RealtimeDigitalHumanChat',
          query: {
            digitalHumanId: digitalHumanId,
            name: app.name || '数字人'
          }
        });

        ElMessage.success(`正在启动与 ${app.name || '数字人'} 的实时对话...`);

      } catch (error) {
        console.error('启动实时聊天失败:', error);
        ElMessage.error('启动实时聊天失败，请重试');
      }
    };
    
    return {
      username,
      displayUsername,
      isAuthenticated,
      currentCategory,
      featuredApps,
      filteredApps,
      paginatedApps,
      searchQuery,
      currentPage,
      pageSize,
      totalItems,
      searchFocused,
      getCategoryName,
      getEmptyStateDescription,
      handleMenuClick,
      handleMenuSelect,
      handleUserMenuClick,
      onSearch,
      openApp,
      viewAppDetails,
      toggleFavorite: toggleFavoriteWithAuth,
      createNew: createNewWithAuth,
      getStaticResourceUrl,
      getDefaultAvatar,
      isLoading,
      getEmptyImage,
      tokenHealth,
      handleAuthRequiredAction,
      previewApp,
      closePreview,
      state,
      formatDate,
      onSubmitComplete,
      componentReady,
      playVideo,
      pauseVideo,
      startRealtimeChat,
      onPageChange,
      onPageSizeChange,
      getTypeIcon,
      getTypeName
    };
  }
});
</script>

<style scoped>
/* 添加loading-container样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  width: 100%;
}

.digital-human-app-center {
  min-height: 100vh;
  background: #f5f7fa;
  position: relative;
}

/* 顶部导航样式 */
.app-center-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.platform-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a237e;
  background: linear-gradient(90deg, #1a237e, #3949ab);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* 美化的搜索框样式 */
.search-container {
  position: relative;
  animation: slideInDown 0.8s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-input {
  width: 250px;
  border-radius: 4px;
  transition: all 0.3s;
}

.search-input:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.search-input :deep(.ant-input) {
  border-radius: 20px;
  padding-left: 12px;
}

.search-input :deep(.ant-input-search-button) {
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
  background: #1a237e;
  border-color: #1a237e;
}

.search-input :deep(.ant-input-search-button:hover) {
  background: #3949ab;
  border-color: #3949ab;
}

.search-icon {
  color: #1a237e;
  opacity: 0.7;
}

/* 用户下拉菜单样式 */
.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #333;
  padding: 6px 10px;
  border-radius: 20px;
  transition: all 0.3s;
  background: #f5f7fa;
}

.user-dropdown:hover {
  background-color: #e8eaf6;
}

.user-dropdown.not-logged-in {
  color: #757575;
  background: #f0f0f0;
}

.user-avatar {
  background: linear-gradient(135deg, #1a237e, #3949ab);
}

.guest-avatar {
  background: #bdbdbd;
}

.username {
  font-weight: 500;
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 主体内容区域样式 */
.app-center-main {
  display: flex;
  min-height: calc(100vh - 64px);
}

/* 侧边栏样式 */
.app-sidebar {
  width: 240px;
  background: white;
  padding: 20px 0;
  border-right: 1px solid #eee;
  height: 100%;
  animation: slideInLeft 0.8s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.sidebar-section {
  margin-bottom: 24px;
}

.sidebar-title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
  padding: 0 24px;
  margin-bottom: 8px;
}

/* 应用内容区域样式 */
.app-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title:after {
  content: '';
  flex: 1;
  height: 1px;
  background: #eee;
  margin-left: 16px;
}

/* 推荐应用轮播 */
.featured-apps {
  margin-bottom: 32px;
}

.featured-carousel {
  height: 280px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.carousel-item {
  height: 280px;
}

.carousel-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 60px;
  background-size: cover;
  background-position: center;
  position: relative;
}

.carousel-content:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(10, 10, 50, 0.8), rgba(10, 10, 50, 0.4));
  z-index: 1;
}

.carousel-info {
  max-width: 50%;
  z-index: 2;
  color: white;
}

.carousel-info h3 {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 16px;
  color: white;
}

.carousel-info p {
  font-size: 16px;
  margin-bottom: 24px;
  opacity: 0.9;
}

.carousel-image {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  z-index: 2;
}

.carousel-image img {
  height: 80%;
  max-height: 240px;
  object-fit: contain;
  filter: drop-shadow(0 0 20px rgba(0, 0, 0, 0.3));
}

/* 应用列表样式 */
.app-list {
  margin-bottom: 40px;
}

.app-col {
  margin-bottom: 40px; /* 增加底部间距，为按钮留出空间 */
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin: 40px 0 20px 0;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.pagination {
  display: flex;
  align-items: center;
  gap: 16px;
}

:deep(.el-pagination) {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-pager li) {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

:deep(.el-pager li:hover) {
  border-color: #409eff;
  transform: translateY(-1px);
}

:deep(.el-pager li.is-active) {
  background: linear-gradient(135deg, #409eff, #36cfc9);
  border-color: #409eff;
  color: white;
}

:deep(.el-pagination__prev),
:deep(.el-pagination__next) {
  border-radius: 8px;
  transition: all 0.3s;
}

:deep(.el-pagination__prev:hover),
:deep(.el-pagination__next:hover) {
  border-color: #409eff;
  transform: translateY(-1px);
}

:deep(.el-pagination__sizes) {
  margin-left: 16px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-pagination__total) {
  color: #666;
  font-size: 14px;
}

/* 应用卡片类型边框 */
.app-card.type-assistant {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #1890ff, #36cfc9) border-box;
}

.app-card.type-customer_service {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #52c41a, #73d13d) border-box;
}

.app-card.type-broadcaster {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #f5222d, #ff7875) border-box;
}

.app-card.type-teacher {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #faad14, #ffd666) border-box;
}

.app-card.type-entertainer {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #722ed1, #b37feb) border-box;
}

.app-card.type-consultant {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #13c2c2, #5cdbd3) border-box;
}

/* 应用卡片样式 - 优化卡片布局 */
.app-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden; /* 恢复隐藏溢出 */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  min-height: 380px; /* 适中的高度，避免过多空白 */
  background: #fff;
  transform: translateY(0);
  animation: cardFadeIn 0.6s ease-out;
}

@keyframes cardFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.app-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 卡片加载动画延迟 */
.app-card:nth-child(1) { animation-delay: 0.1s; }
.app-card:nth-child(2) { animation-delay: 0.2s; }
.app-card:nth-child(3) { animation-delay: 0.3s; }
.app-card:nth-child(4) { animation-delay: 0.4s; }
.app-card:nth-child(5) { animation-delay: 0.5s; }
.app-card:nth-child(6) { animation-delay: 0.6s; }

.app-media {
  position: relative;
  width: 100%;
  height: 100%; /* 填满整个卡片 */
  min-height: 380px; /* 匹配卡片最小高度 */
  overflow: hidden; /* 隐藏溢出，保持整洁 */
}

.app-video, .app-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.app-card:hover .app-video,
.app-card:hover .app-image {
  transform: scale(1.05);
}

.media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0; /* 使用right而不是width，确保不超出 */
  bottom: 0; /* 使用bottom而不是height，确保填满 */
  background: linear-gradient(to bottom, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0.8) 70%, rgba(0,0,0,0.9) 100%);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 16px; /* 统一padding */
  transition: opacity 0.3s ease;
  opacity: 0;
  z-index: 10;
}

.app-card:hover .media-overlay {
  opacity: 1;
}

.overlay-top {
  position: absolute;
  top: 16px;
  right: 16px;
  left: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 2;
}

/* 类型标识样式 */
.type-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
  color: white;
  background: rgba(255,255,255,0.1);
  transition: all 0.3s ease;
}

.type-badge .type-icon {
  font-size: 14px;
}

.type-badge .type-name {
  font-size: 11px;
  white-space: nowrap;
}

/* 不同类型的特色颜色 */
.type-badge.type-assistant {
  background: rgba(24, 144, 255, 0.8);
  border-color: rgba(24, 144, 255, 0.9);
}

.type-badge.type-customer_service {
  background: rgba(82, 196, 26, 0.8);
  border-color: rgba(82, 196, 26, 0.9);
}

.type-badge.type-broadcaster {
  background: rgba(245, 34, 45, 0.8);
  border-color: rgba(245, 34, 45, 0.9);
}

.type-badge.type-teacher {
  background: rgba(250, 173, 20, 0.8);
  border-color: rgba(250, 173, 20, 0.9);
}

.type-badge.type-entertainer {
  background: rgba(114, 46, 209, 0.8);
  border-color: rgba(114, 46, 209, 0.9);
}

.type-badge.type-consultant {
  background: rgba(19, 194, 194, 0.8);
  border-color: rgba(19, 194, 194, 0.9);
}

.favorite-btn {
  color: white;
  border: none;
  background: rgba(0, 0, 0, 0.5);
  width: 36px;
  height: 36px;
  transition: all 0.3s ease;
}

.favorite-btn:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

.favorited-btn {
  color: #faad14 !important;
}

.overlay-bottom {
  z-index: 2;
}

.media-overlay .app-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
  color: white;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.media-overlay .app-desc {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4px;
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transition: all 0.3s ease;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.app-card:hover .media-overlay .app-desc {
  max-height: 40px;
  opacity: 1;
  margin-bottom: 8px;
}

.app-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.app-card:hover .app-meta {
  opacity: 1;
  transform: translateY(0);
}

.app-uses {
  display: flex;
  align-items: center;
  gap: 4px;
}

.app-id {
  font-family: monospace;
  padding: 1px 4px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.overlay-actions {
  display: flex;
  gap: 8px;
  opacity: 0; /* 恢复隐藏，悬停时显示 */
  transform: translateY(10px);
  transition: all 0.3s ease 0.1s;
  margin-top: 8px;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

.app-card:hover .overlay-actions {
  opacity: 1;
  transform: translateY(0);
}

.play-btn-wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.7;
  transition: opacity 0.3s ease, transform 0.3s ease;
  z-index: 3;
}

.app-card:hover .play-btn-wrapper {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

.play-button {
  width: 58px;
  height: 58px;
  font-size: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(24, 144, 255, 0.85);
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

.play-button:hover {
  background: #1890ff;
  transform: scale(1.05);
}

.app-badge {
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
  position: absolute;
  left: 16px;
  top: 16px;
  z-index: 3;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.app-name-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
  padding: 16px;
  padding-top: 40px;
  z-index: 2;
  pointer-events: none;
}

.app-card:hover .app-name-overlay {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.app-name-visible {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

/* 创建数字人菜单项样式 */
.create-dh-menu-item {
  margin-top: 16px;
  border-top: 1px dashed #eee;
  padding-top: 16px;
  color: #1a237e !important;
  font-weight: 600;
}

.create-dh-menu-item .anticon {
  color: #1a237e;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .app-center-main {
    flex-direction: column;
  }
  
  .app-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #eee;
    padding: 12px 0;
  }
  
  .sidebar-title {
    padding: 0 16px;
  }
  
  .carousel-content {
    padding: 0 24px;
    flex-direction: column;
    justify-content: center;
    text-align: center;
  }
  
  .carousel-info {
    max-width: 100%;
    margin-bottom: 20px;
  }
  
  .carousel-image {
    justify-content: center;
  }
  
  .carousel-image img {
    height: 40%;
  }
}

@media (max-width: 768px) {
  .app-center-nav {
    padding: 12px 16px;
  }
  
  .platform-title {
    font-size: 18px;
  }
  
  .nav-right {
    gap: 12px;
  }
  
  .username {
    display: none;
  }
  
  .app-content {
    padding: 16px;
  }
  
  .section-title {
    font-size: 18px;
    margin-bottom: 16px;
  }
  
  .featured-carousel {
    height: 240px;
  }
  
  .carousel-item {
    height: 240px;
  }
  
  .carousel-info h3 {
    font-size: 22px;
    margin-bottom: 8px;
  }
  
  .carousel-info p {
    font-size: 14px;
    margin-bottom: 16px;
  }
  
  .search-input {
    width: 180px;
  }
}

/* 预览模态框样式 */
.preview-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.preview-media {
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9宽高比 */
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: #f5f7fa;
  position: relative;
}

.preview-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
}

.preview-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.preview-info {
  width: 100%;
  text-align: center;
  margin-bottom: 24px;
  
  h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
  }
  
  .preview-desc {
    font-size: 16px;
    color: #666;
    margin-bottom: 20px;
    white-space: pre-line;
    line-height: 1.6;
  }
  
  .preview-meta {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 16px;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 8px;
    
    p {
      margin: 0;
      padding: 4px 0;
      flex: 0 0 48%;
    }
  }
}

.preview-actions {
  display: flex;
  gap: 16px;
  
  .ant-btn {
    min-width: 120px;
  }
}

:deep(.app-preview-modal) {
  .ant-modal-content {
    border-radius: 12px;
    overflow: hidden;
  }
  
  .ant-modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .ant-modal-body {
    padding: 24px;
  }
  
  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 12px 24px;
  }
}

/* 清除默认样式 */
.ant-card-body {
  padding: 0;
}

.app-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
}

.left-actions {
  display: flex;
}

.left-actions .ant-btn {
  color: #666;
  font-size: 18px;
  width: 40px;
  height: 40px;
}

.left-actions .ant-btn:hover {
  color: #1890ff;
  background: #f0f8ff;
}

.left-actions .favorited-btn {
  color: #faad14;
}

.left-actions .favorited-btn:hover {
  color: #d48806;
  background: #fffbe6;
}

.right-actions {
  display: flex;
  gap: 8px;
}

.right-actions .action-chat {
  background: #1890ff;
}

.right-actions .action-chat:hover {
  background: #096dd9;
}

.right-actions .action-edit {
  color: #666;
}

.right-actions .action-edit:hover {
  color: #333;
  border-color: #d9d9d9;
}
</style> 