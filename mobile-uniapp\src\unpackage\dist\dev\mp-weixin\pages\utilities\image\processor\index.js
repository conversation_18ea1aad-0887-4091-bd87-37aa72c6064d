"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  const _component_u_slider = common_vendor.resolveComponent("u-slider");
  const _component_u_radio = common_vendor.resolveComponent("u-radio");
  const _component_u_radio_group = common_vendor.resolveComponent("u-radio-group");
  const _component_u_button = common_vendor.resolveComponent("u-button");
  const _component_u_line_progress = common_vendor.resolveComponent("u-line-progress");
  (_component_u_icon + _component_u_slider + _component_u_radio + _component_u_radio_group + _component_u_button + _component_u_line_progress)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props, { expose: __expose }) {
    const selectedImage = common_vendor.ref(null);
    const selectedTool = common_vendor.ref("");
    const selectedFilter = common_vendor.ref("none");
    const isProcessing = common_vendor.ref(false);
    const processingProgress = common_vendor.ref(0);
    const progressStatus = common_vendor.ref("");
    const processingResult = common_vendor.ref(null);
    const imageHistory = common_vendor.ref([]);
    const convertOptions = common_vendor.ref({
      format: "jpg"
    });
    const compressOptions = common_vendor.ref({
      quality: 80,
      maxSize: "1024",
      customSize: ""
    });
    const cropOptions = common_vendor.ref({
      ratio: "free",
      width: "",
      height: ""
    });
    const adjustOptions = common_vendor.ref({
      brightness: 0,
      contrast: 0,
      saturation: 0
    });
    const processingTools = common_vendor.ref([
      {
        id: "convert",
        name: "格式转换",
        description: "转换图片格式",
        icon: "refresh",
        color: "#2563EB"
      },
      {
        id: "compress",
        name: "图片压缩",
        description: "减小文件大小",
        icon: "archive",
        color: "#10B981"
      },
      {
        id: "crop",
        name: "裁剪调整",
        description: "裁剪和调整尺寸",
        icon: "crop",
        color: "#F59E0B"
      },
      {
        id: "filter",
        name: "滤镜效果",
        description: "添加滤镜效果",
        icon: "color-filter",
        color: "#8B5CF6"
      },
      {
        id: "adjust",
        name: "色彩调整",
        description: "调整亮度对比度",
        icon: "tune",
        color: "#EF4444"
      }
    ]);
    const outputFormats = common_vendor.ref([
      { label: "JPG", value: "jpg" },
      { label: "PNG", value: "png" },
      { label: "WebP", value: "webp" },
      { label: "GIF", value: "gif" }
    ]);
    const filterOptions = common_vendor.ref([
      { name: "无滤镜", value: "none", css: "none" },
      { name: "黑白", value: "grayscale", css: "grayscale(100%)" },
      { name: "复古", value: "sepia", css: "sepia(100%)" },
      { name: "模糊", value: "blur", css: "blur(2px)" },
      { name: "高对比", value: "contrast", css: "contrast(150%)" },
      { name: "高饱和", value: "saturate", css: "saturate(150%)" }
    ]);
    const canProcess = common_vendor.computed(() => {
      return selectedImage.value && selectedTool.value;
    });
    common_vendor.onMounted(() => {
      loadImageHistory();
    });
    const chooseImage = () => {
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: ["album", "camera"],
        success: (res) => {
          const filePath = res.tempFilePaths[0];
          common_vendor.index.getImageInfo({
            src: filePath,
            success: (info) => {
              selectedImage.value = {
                name: `image_${Date.now()}.jpg`,
                path: filePath,
                size: info.size || 0,
                width: info.width,
                height: info.height,
                type: info.type || "jpg"
              };
              selectedTool.value = "";
              processingResult.value = null;
            },
            fail: (error) => {
              common_vendor.index.__f__("error", "at pages/utilities/image/processor/index.vue:431", "获取图片信息失败:", error);
              common_vendor.index.showToast({
                title: "获取图片信息失败",
                icon: "error"
              });
            }
          });
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/utilities/image/processor/index.vue:440", "选择图片失败:", error);
        }
      });
    };
    const onImageLoad = (e) => {
      common_vendor.index.__f__("log", "at pages/utilities/image/processor/index.vue:447", "图片加载完成:", e);
    };
    const removeImage = () => {
      selectedImage.value = null;
      selectedTool.value = "";
      processingResult.value = null;
    };
    const selectTool = (toolId) => {
      selectedTool.value = toolId;
      processingResult.value = null;
    };
    const startProcessing = async () => {
      if (!canProcess.value)
        return;
      try {
        isProcessing.value = true;
        processingProgress.value = 0;
        progressStatus.value = "正在处理图片...";
        const progressInterval = setInterval(() => {
          if (processingProgress.value < 90) {
            processingProgress.value += Math.random() * 10;
            if (processingProgress.value < 30) {
              progressStatus.value = "正在分析图片...";
            } else if (processingProgress.value < 60) {
              progressStatus.value = "正在应用处理...";
            } else {
              progressStatus.value = "正在生成结果...";
            }
          }
        }, 200);
        await new Promise((resolve) => setTimeout(resolve, 3e3));
        clearInterval(progressInterval);
        processingProgress.value = 100;
        progressStatus.value = "处理完成";
        const mockResult = {
          imageUrl: selectedImage.value.path,
          // 实际应该是处理后的图片URL
          size: calculateProcessedSize(),
          format: getOutputFormat(),
          filename: generateResultFilename()
        };
        processingResult.value = mockResult;
        const historyItem = {
          id: Date.now().toString(),
          filename: selectedImage.value.name,
          thumbnail: selectedImage.value.path,
          tool: selectedTool.value,
          format: mockResult.format,
          originalSize: selectedImage.value.size,
          processedSize: mockResult.size,
          createdAt: (/* @__PURE__ */ new Date()).toISOString()
        };
        imageHistory.value.unshift(historyItem);
        saveImageHistory();
        common_vendor.index.showToast({
          title: "处理完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/utilities/image/processor/index.vue:525", "处理失败:", error);
        common_vendor.index.showToast({
          title: "处理失败",
          icon: "error"
        });
      } finally {
        isProcessing.value = false;
      }
    };
    const calculateProcessedSize = () => {
      const originalSize = selectedImage.value.size;
      switch (selectedTool.value) {
        case "compress":
          const quality = compressOptions.value.quality / 100;
          return Math.floor(originalSize * quality);
        case "convert":
          const formatMultiplier = {
            jpg: 0.8,
            png: 1.2,
            webp: 0.6,
            gif: 1
          };
          return Math.floor(originalSize * (formatMultiplier[convertOptions.value.format] || 1));
        default:
          return originalSize;
      }
    };
    const getOutputFormat = () => {
      switch (selectedTool.value) {
        case "convert":
          return convertOptions.value.format.toUpperCase();
        default:
          return selectedImage.value.type.toUpperCase();
      }
    };
    const generateResultFilename = () => {
      const baseName = selectedImage.value.name.split(".")[0];
      const toolName = getToolName(selectedTool.value);
      const format = getOutputFormat().toLowerCase();
      return `${baseName}_${toolName}.${format}`;
    };
    const getToolName = (toolId) => {
      const tool = processingTools.value.find((t) => t.id === toolId);
      return (tool == null ? void 0 : tool.name) || "未知工具";
    };
    const calculateCompressionRatio = () => {
      if (!processingResult.value || !selectedImage.value)
        return 0;
      const originalSize = selectedImage.value.size;
      const processedSize = processingResult.value.size;
      return Math.round((originalSize - processedSize) / originalSize * 100);
    };
    const downloadResult = () => {
      if (!processingResult.value)
        return;
      common_vendor.index.showToast({
        title: "开始下载",
        icon: "success"
      });
    };
    const shareResult = () => {
      if (!processingResult.value)
        return;
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 1,
        imageUrl: processingResult.value.imageUrl,
        success: () => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        }
      });
    };
    const formatFileSize = (bytes) => {
      if (bytes === 0)
        return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };
    const formatTime = (time) => {
      return common_vendor.dayjs(time).format("MM-DD HH:mm");
    };
    const saveImageHistory = () => {
      common_vendor.index.setStorageSync("image_processing_history", imageHistory.value);
    };
    const loadImageHistory = () => {
      const history = common_vendor.index.getStorageSync("image_processing_history") || [];
      imageHistory.value = history;
    };
    const clearHistory = () => {
      common_vendor.index.showModal({
        title: "确认清空",
        content: "确定要清空所有处理历史吗？",
        success: (res) => {
          if (res.confirm) {
            imageHistory.value = [];
            saveImageHistory();
            common_vendor.index.showToast({
              title: "清空成功",
              icon: "success"
            });
          }
        }
      });
    };
    const viewHistoryItem = (item) => {
      common_vendor.index.showToast({
        title: "查看历史记录",
        icon: "none"
      });
    };
    const onPullDownRefresh = () => {
      loadImageHistory();
      common_vendor.index.stopPullDownRefresh();
    };
    __expose({
      onPullDownRefresh
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: !selectedImage.value
      }, !selectedImage.value ? {
        b: common_vendor.p({
          name: "image",
          size: "48",
          color: "#2563EB"
        })
      } : common_vendor.e({
        c: selectedImage.value.path,
        d: common_vendor.o(onImageLoad),
        e: common_vendor.t(selectedImage.value.name),
        f: common_vendor.t(formatFileSize(selectedImage.value.size)),
        g: selectedImage.value.width && selectedImage.value.height
      }, selectedImage.value.width && selectedImage.value.height ? {
        h: common_vendor.t(selectedImage.value.width),
        i: common_vendor.t(selectedImage.value.height)
      } : {}, {
        j: common_vendor.o(removeImage),
        k: common_vendor.p({
          name: "close-circle",
          size: "20",
          color: "#EF4444"
        })
      }), {
        l: common_vendor.o(chooseImage),
        m: selectedImage.value
      }, selectedImage.value ? {
        n: common_vendor.f(processingTools.value, (tool, k0, i0) => {
          return {
            a: "fb23d7ac-2-" + i0,
            b: common_vendor.p({
              name: tool.icon,
              color: "#fff",
              size: "20"
            }),
            c: tool.color,
            d: common_vendor.t(tool.name),
            e: common_vendor.t(tool.description),
            f: tool.id,
            g: selectedTool.value === tool.id ? 1 : "",
            h: common_vendor.o(($event) => selectTool(tool.id), tool.id)
          };
        })
      } : {}, {
        o: selectedImage.value && selectedTool.value
      }, selectedImage.value && selectedTool.value ? common_vendor.e({
        p: common_vendor.t(getToolName(selectedTool.value)),
        q: selectedTool.value === "convert"
      }, selectedTool.value === "convert" ? {
        r: common_vendor.f(outputFormats.value, (format, k0, i0) => {
          return {
            a: common_vendor.t(format.label),
            b: format.value,
            c: convertOptions.value.format === format.value ? 1 : "",
            d: common_vendor.o(($event) => convertOptions.value.format = format.value, format.value)
          };
        })
      } : {}, {
        s: selectedTool.value === "compress"
      }, selectedTool.value === "compress" ? common_vendor.e({
        t: common_vendor.t(compressOptions.value.quality),
        v: common_vendor.o(($event) => compressOptions.value.quality = $event),
        w: common_vendor.p({
          min: 10,
          max: 100,
          step: 10,
          ["active-color"]: "#2563EB",
          modelValue: compressOptions.value.quality
        }),
        x: common_vendor.p({
          name: "1024",
          label: "1024KB"
        }),
        y: common_vendor.p({
          name: "512",
          label: "512KB"
        }),
        z: common_vendor.p({
          name: "256",
          label: "256KB"
        }),
        A: common_vendor.p({
          name: "custom",
          label: "自定义"
        }),
        B: common_vendor.o(($event) => compressOptions.value.maxSize = $event),
        C: common_vendor.p({
          modelValue: compressOptions.value.maxSize
        }),
        D: compressOptions.value.maxSize === "custom"
      }, compressOptions.value.maxSize === "custom" ? {
        E: compressOptions.value.customSize,
        F: common_vendor.o(($event) => compressOptions.value.customSize = $event.detail.value)
      } : {}) : {}, {
        G: selectedTool.value === "crop"
      }, selectedTool.value === "crop" ? {
        H: common_vendor.p({
          name: "free",
          label: "自由裁剪"
        }),
        I: common_vendor.p({
          name: "1:1",
          label: "1:1 正方形"
        }),
        J: common_vendor.p({
          name: "4:3",
          label: "4:3"
        }),
        K: common_vendor.p({
          name: "16:9",
          label: "16:9"
        }),
        L: common_vendor.o(($event) => cropOptions.value.ratio = $event),
        M: common_vendor.p({
          modelValue: cropOptions.value.ratio
        }),
        N: cropOptions.value.width,
        O: common_vendor.o(($event) => cropOptions.value.width = $event.detail.value),
        P: cropOptions.value.height,
        Q: common_vendor.o(($event) => cropOptions.value.height = $event.detail.value)
      } : {}, {
        R: selectedTool.value === "filter"
      }, selectedTool.value === "filter" ? {
        S: common_vendor.f(filterOptions.value, (filter, k0, i0) => {
          return {
            a: filter.css,
            b: common_vendor.t(filter.name),
            c: filter.value,
            d: selectedFilter.value === filter.value ? 1 : "",
            e: common_vendor.o(($event) => selectedFilter.value = filter.value, filter.value)
          };
        }),
        T: selectedImage.value.path
      } : {}, {
        U: selectedTool.value === "adjust"
      }, selectedTool.value === "adjust" ? {
        V: common_vendor.t(adjustOptions.value.brightness),
        W: common_vendor.o(($event) => adjustOptions.value.brightness = $event),
        X: common_vendor.p({
          min: -100,
          max: 100,
          ["active-color"]: "#2563EB",
          modelValue: adjustOptions.value.brightness
        }),
        Y: common_vendor.t(adjustOptions.value.contrast),
        Z: common_vendor.o(($event) => adjustOptions.value.contrast = $event),
        aa: common_vendor.p({
          min: -100,
          max: 100,
          ["active-color"]: "#2563EB",
          modelValue: adjustOptions.value.contrast
        }),
        ab: common_vendor.t(adjustOptions.value.saturation),
        ac: common_vendor.o(($event) => adjustOptions.value.saturation = $event),
        ad: common_vendor.p({
          min: -100,
          max: 100,
          ["active-color"]: "#2563EB",
          modelValue: adjustOptions.value.saturation
        })
      } : {}, {
        ae: common_vendor.t(isProcessing.value ? "处理中..." : "开始处理"),
        af: common_vendor.o(startProcessing),
        ag: common_vendor.p({
          type: "primary",
          size: "large",
          loading: isProcessing.value,
          disabled: !canProcess.value,
          ["custom-style"]: {
            width: "100%"
          }
        })
      }) : {}, {
        ah: isProcessing.value
      }, isProcessing.value ? {
        ai: common_vendor.t(progressStatus.value),
        aj: common_vendor.p({
          percent: processingProgress.value,
          ["show-percent"]: true,
          ["active-color"]: "#2563EB"
        })
      } : {}, {
        ak: processingResult.value
      }, processingResult.value ? {
        al: common_vendor.o(downloadResult),
        am: common_vendor.p({
          name: "download",
          size: "18",
          color: "#2563EB"
        }),
        an: common_vendor.o(shareResult),
        ao: common_vendor.p({
          name: "share",
          size: "18",
          color: "#2563EB",
          ["margin-left"]: "12"
        }),
        ap: selectedImage.value.path,
        aq: common_vendor.t(formatFileSize(selectedImage.value.size)),
        ar: common_vendor.p({
          name: "arrow-right",
          size: "20",
          color: "#6B7280"
        }),
        as: processingResult.value.imageUrl,
        at: common_vendor.t(formatFileSize(processingResult.value.size)),
        av: common_vendor.t(getToolName(selectedTool.value)),
        aw: common_vendor.t(formatFileSize(processingResult.value.size)),
        ax: common_vendor.t(calculateCompressionRatio())
      } : {}, {
        ay: imageHistory.value.length > 0
      }, imageHistory.value.length > 0 ? {
        az: common_vendor.o(clearHistory),
        aA: common_vendor.p({
          type: "text",
          text: "清空",
          size: "small",
          ["custom-style"]: {
            color: "#EF4444",
            fontSize: "24rpx"
          }
        }),
        aB: common_vendor.f(imageHistory.value.slice(0, 5), (item, k0, i0) => {
          return {
            a: item.thumbnail,
            b: common_vendor.t(item.filename),
            c: common_vendor.t(getToolName(item.tool)),
            d: common_vendor.t(item.format),
            e: common_vendor.t(formatTime(item.createdAt)),
            f: "fb23d7ac-23-" + i0,
            g: item.id,
            h: common_vendor.o(($event) => viewHistoryItem(), item.id)
          };
        }),
        aC: common_vendor.p({
          name: "download",
          size: "16",
          color: "#6B7280"
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-fb23d7ac"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pages/utilities/image/processor/index.js.map
