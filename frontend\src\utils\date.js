/**
 * 日期时间工具函数
 * 提供各种日期格式化和处理功能
 */

/**
 * 格式化日期
 * @param {Date|string|number} date - 日期对象、字符串或时间戳
 * @param {string} format - 格式字符串
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

/**
 * 格式化相对时间
 * @param {Date|string|number} date - 日期
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(date) {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const now = new Date();
  const diffMs = now.getTime() - d.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffSeconds < 60) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return formatDate(d, 'MM-DD');
  }
}

/**
 * 格式化时间段
 * @param {number} seconds - 秒数
 * @returns {string} 格式化的时间段
 */
export function formatDuration(seconds) {
  if (!seconds || seconds < 0) return '0秒';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟${secs}秒`;
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`;
  } else {
    return `${secs}秒`;
  }
}

/**
 * 格式化简短时间段
 * @param {number} seconds - 秒数
 * @returns {string} 简短的时间段格式
 */
export function formatShortDuration(seconds) {
  if (!seconds || seconds < 0) return '0:00';
  
  const minutes = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  
  return `${minutes}:${String(secs).padStart(2, '0')}`;
}

/**
 * 检查日期是否为今天
 * @param {Date|string|number} date - 日期
 * @returns {boolean} 是否为今天
 */
export function isToday(date) {
  if (!date) return false;
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return false;
  
  const today = new Date();
  return d.toDateString() === today.toDateString();
}

/**
 * 检查日期是否为昨天
 * @param {Date|string|number} date - 日期
 * @returns {boolean} 是否为昨天
 */
export function isYesterday(date) {
  if (!date) return false;
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return false;
  
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return d.toDateString() === yesterday.toDateString();
}

/**
 * 检查日期是否为本周
 * @param {Date|string|number} date - 日期
 * @returns {boolean} 是否为本周
 */
export function isThisWeek(date) {
  if (!date) return false;
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return false;
  
  const now = new Date();
  const startOfWeek = new Date(now);
  startOfWeek.setDate(now.getDate() - now.getDay());
  startOfWeek.setHours(0, 0, 0, 0);
  
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6);
  endOfWeek.setHours(23, 59, 59, 999);
  
  return d >= startOfWeek && d <= endOfWeek;
}

/**
 * 获取友好的日期显示
 * @param {Date|string|number} date - 日期
 * @returns {string} 友好的日期字符串
 */
export function getFriendlyDate(date) {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  if (isToday(d)) {
    return `今天 ${formatDate(d, 'HH:mm')}`;
  } else if (isYesterday(d)) {
    return `昨天 ${formatDate(d, 'HH:mm')}`;
  } else if (isThisWeek(d)) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    return `${weekdays[d.getDay()]} ${formatDate(d, 'HH:mm')}`;
  } else {
    return formatDate(d, 'MM-DD HH:mm');
  }
}

/**
 * 解析日期字符串
 * @param {string} dateString - 日期字符串
 * @returns {Date|null} 解析后的日期对象
 */
export function parseDate(dateString) {
  if (!dateString) return null;
  
  const date = new Date(dateString);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * 获取日期范围
 * @param {string} range - 范围类型 ('today', 'yesterday', 'week', 'month')
 * @returns {Object} 包含开始和结束日期的对象
 */
export function getDateRange(range) {
  const now = new Date();
  const start = new Date(now);
  const end = new Date(now);
  
  switch (range) {
    case 'today':
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      break;
      
    case 'yesterday':
      start.setDate(now.getDate() - 1);
      start.setHours(0, 0, 0, 0);
      end.setDate(now.getDate() - 1);
      end.setHours(23, 59, 59, 999);
      break;
      
    case 'week':
      start.setDate(now.getDate() - now.getDay());
      start.setHours(0, 0, 0, 0);
      end.setDate(start.getDate() + 6);
      end.setHours(23, 59, 59, 999);
      break;
      
    case 'month':
      start.setDate(1);
      start.setHours(0, 0, 0, 0);
      end.setMonth(now.getMonth() + 1, 0);
      end.setHours(23, 59, 59, 999);
      break;
      
    default:
      return { start: now, end: now };
  }
  
  return { start, end };
}

/**
 * 计算两个日期之间的天数差
 * @param {Date|string|number} date1 - 第一个日期
 * @param {Date|string|number} date2 - 第二个日期
 * @returns {number} 天数差
 */
export function daysBetween(date1, date2) {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  
  if (isNaN(d1.getTime()) || isNaN(d2.getTime())) return 0;
  
  const diffTime = Math.abs(d2.getTime() - d1.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * 添加天数到日期
 * @param {Date|string|number} date - 原始日期
 * @param {number} days - 要添加的天数
 * @returns {Date} 新的日期对象
 */
export function addDays(date, days) {
  const d = new Date(date);
  if (isNaN(d.getTime())) return new Date();
  
  d.setDate(d.getDate() + days);
  return d;
}

/**
 * 获取月份名称
 * @param {number} month - 月份 (0-11)
 * @param {boolean} short - 是否使用简短格式
 * @returns {string} 月份名称
 */
export function getMonthName(month, short = false) {
  const months = short 
    ? ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    : ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
  
  return months[month] || '';
}

/**
 * 获取星期名称
 * @param {number} day - 星期 (0-6, 0为周日)
 * @param {boolean} short - 是否使用简短格式
 * @returns {string} 星期名称
 */
export function getDayName(day, short = false) {
  const days = short 
    ? ['日', '一', '二', '三', '四', '五', '六']
    : ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  
  return days[day] || '';
}

/**
 * 验证日期格式
 * @param {string} dateString - 日期字符串
 * @param {string} format - 期望的格式
 * @returns {boolean} 是否符合格式
 */
export function isValidDateFormat(dateString, format = 'YYYY-MM-DD') {
  if (!dateString) return false;
  
  // 简单的格式验证
  const formatRegex = {
    'YYYY-MM-DD': /^\d{4}-\d{2}-\d{2}$/,
    'YYYY-MM-DD HH:mm:ss': /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/,
    'MM-DD': /^\d{2}-\d{2}$/,
    'HH:mm': /^\d{2}:\d{2}$/
  };
  
  const regex = formatRegex[format];
  if (!regex) return false;
  
  return regex.test(dateString) && !isNaN(new Date(dateString).getTime());
}
