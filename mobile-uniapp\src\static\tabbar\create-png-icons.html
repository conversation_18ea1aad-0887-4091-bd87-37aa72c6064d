<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建TabBar PNG图标</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .icon-item {
            text-align: center;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .icon-preview {
            width: 40px;
            height: 40px;
            margin: 0 auto 10px;
            background: #8E8E93;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }
        .icon-preview.active {
            background: #667eea;
        }
        .icon-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }
        .download-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #5a67d8;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .instructions h3 {
            color: #1976d2;
            margin-top: 0;
        }
        .instructions ol {
            color: #333;
            line-height: 1.6;
        }
        canvas {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 TabBar图标生成器</h1>
        
        <div class="icon-grid">
            <div class="icon-item">
                <div class="icon-preview">🏠</div>
                <div class="icon-name">首页</div>
                <button class="download-btn" onclick="downloadIcon('home', '🏠', false)">下载普通</button>
                <button class="download-btn" onclick="downloadIcon('home-active', '🏠', true)">下载选中</button>
            </div>
            
            <div class="icon-item">
                <div class="icon-preview">🌍</div>
                <div class="icon-name">翻译</div>
                <button class="download-btn" onclick="downloadIcon('translate', '🌍', false)">下载普通</button>
                <button class="download-btn" onclick="downloadIcon('translate-active', '🌍', true)">下载选中</button>
            </div>
            
            <div class="icon-item">
                <div class="icon-preview">💬</div>
                <div class="icon-name">对话</div>
                <button class="download-btn" onclick="downloadIcon('chat', '💬', false)">下载普通</button>
                <button class="download-btn" onclick="downloadIcon('chat-active', '💬', true)">下载选中</button>
            </div>
            
            <div class="icon-item">
                <div class="icon-preview">🏪</div>
                <div class="icon-name">智能体</div>
                <button class="download-btn" onclick="downloadIcon('market', '🏪', false)">下载普通</button>
                <button class="download-btn" onclick="downloadIcon('market-active', '🏪', true)">下载选中</button>
            </div>
            
            <div class="icon-item">
                <div class="icon-preview">👤</div>
                <div class="icon-name">我的</div>
                <button class="download-btn" onclick="downloadIcon('user', '👤', false)">下载普通</button>
                <button class="download-btn" onclick="downloadIcon('user-active', '👤', true)">下载选中</button>
            </div>
        </div>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li>点击上方的"下载"按钮，为每个图标下载普通和选中两个版本</li>
                <li>将下载的PNG文件放到 <code>src/static/tabbar/</code> 目录下</li>
                <li>确保文件名与pages.json中配置的一致</li>
                <li>重新编译项目即可看到图标</li>
            </ol>
            
            <h3>🎨 自定义图标</h3>
            <p>如果您想使用自定义图标，请确保：</p>
            <ul>
                <li>图标尺寸：40x40px</li>
                <li>格式：PNG（支持透明背景）</li>
                <li>普通状态颜色：#8E8E93</li>
                <li>选中状态颜色：#667eea</li>
            </ul>
        </div>
    </div>
    
    <canvas id="canvas" width="40" height="40"></canvas>
    
    <script>
        function downloadIcon(name, emoji, isActive) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, 40, 40);
            
            // 设置背景色（可选）
            ctx.fillStyle = 'transparent';
            ctx.fillRect(0, 0, 40, 40);
            
            // 设置文字样式
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // 设置颜色
            if (isActive) {
                ctx.fillStyle = '#667eea';
            } else {
                ctx.fillStyle = '#8E8E93';
            }
            
            // 绘制emoji（简单方案）
            ctx.fillText(emoji, 20, 20);
            
            // 转换为blob并下载
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = name + '.png';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }
    </script>
</body>
</html>
