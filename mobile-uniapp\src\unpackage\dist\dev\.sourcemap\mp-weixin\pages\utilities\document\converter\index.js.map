{"version": 3, "file": "index.js", "sources": ["pages/utilities/document/converter/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXRpbGl0aWVzL2RvY3VtZW50L2NvbnZlcnRlci9pbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"document-converter\">\n    <!-- 头部区域 -->\n    <view class=\"header-section\">\n      <view class=\"header-content\">\n        <text class=\"page-title\">文档转换</text>\n        <text class=\"page-subtitle\">支持多种格式互转，保持原有排版</text>\n      </view>\n    </view>\n    \n    <!-- 转换配置区域 -->\n    <view class=\"converter-section\">\n      <view class=\"format-selector\">\n        <view class=\"format-item\">\n          <text class=\"format-label\">源格式</text>\n          <view class=\"format-picker\" @click=\"showSourcePicker\">\n            <view class=\"format-display\">\n              <view class=\"format-icon\" :style=\"{ background: sourceFormat.color }\">\n                <text class=\"iconfont\" :class=\"sourceFormat.icon\"></text>\n              </view>\n              <text class=\"format-name\">{{ sourceFormat.name }}</text>\n            </view>\n            <u-icon name=\"arrow-down\" size=\"16\" color=\"#8E8E93\"></u-icon>\n          </view>\n        </view>\n        \n        <view class=\"convert-arrow\">\n          <view class=\"arrow-icon\" @click=\"swapFormats\">\n            <u-icon name=\"arrow-right\" size=\"20\" color=\"#667eea\"></u-icon>\n          </view>\n        </view>\n        \n        <view class=\"format-item\">\n          <text class=\"format-label\">目标格式</text>\n          <view class=\"format-picker\" @click=\"showTargetPicker\">\n            <view class=\"format-display\">\n              <view class=\"format-icon\" :style=\"{ background: targetFormat.color }\">\n                <text class=\"iconfont\" :class=\"targetFormat.icon\"></text>\n              </view>\n              <text class=\"format-name\">{{ targetFormat.name }}</text>\n            </view>\n            <u-icon name=\"arrow-down\" size=\"16\" color=\"#8E8E93\"></u-icon>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 文件上传区域 -->\n    <view class=\"upload-section\">\n      <view class=\"upload-area\" @click=\"chooseFile\">\n        <view class=\"upload-content\" v-if=\"!selectedFile\">\n          <view class=\"upload-icon\">\n            <u-icon name=\"cloud-upload\" size=\"48\" color=\"#667eea\"></u-icon>\n          </view>\n          <text class=\"upload-title\">选择文件或拖拽上传</text>\n          <text class=\"upload-desc\">支持 PDF、Word、Excel、PPT 等格式</text>\n          <text class=\"upload-limit\">文件大小不超过 50MB</text>\n        </view>\n        \n        <view class=\"file-preview\" v-else>\n          <view class=\"file-info\">\n            <view class=\"file-icon\" :style=\"{ background: getFileColor(selectedFile.type) }\">\n              <text class=\"iconfont\" :class=\"getFileIcon(selectedFile.type)\"></text>\n            </view>\n            <view class=\"file-details\">\n              <text class=\"file-name\">{{ selectedFile.name }}</text>\n              <text class=\"file-size\">{{ formatFileSize(selectedFile.size) }}</text>\n              <text class=\"file-status\" :class=\"{ success: uploadSuccess, error: uploadError }\">\n                {{ getUploadStatus() }}\n              </text>\n            </view>\n          </view>\n          \n          <view class=\"file-actions\">\n            <view class=\"action-btn remove\" @click.stop=\"removeFile\">\n              <u-icon name=\"close\" size=\"16\" color=\"#EF4444\"></u-icon>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 转换选项 -->\n    <view class=\"options-section\">\n      <view class=\"section-title\">转换选项</view>\n      \n      <view class=\"option-list\">\n        <view class=\"option-item\">\n          <view class=\"option-info\">\n            <text class=\"option-name\">保持原有格式</text>\n            <text class=\"option-desc\">尽可能保持原文档的排版和样式</text>\n          </view>\n          <switch \n            :checked=\"options.keepFormat\" \n            @change=\"toggleOption('keepFormat')\"\n            color=\"#667eea\"\n          />\n        </view>\n        \n        <view class=\"option-item\">\n          <view class=\"option-info\">\n            <text class=\"option-name\">高质量转换</text>\n            <text class=\"option-desc\">使用AI增强转换质量，处理时间较长</text>\n          </view>\n          <switch \n            :checked=\"options.highQuality\" \n            @change=\"toggleOption('highQuality')\"\n            color=\"#667eea\"\n          />\n        </view>\n        \n        <view class=\"option-item\">\n          <view class=\"option-info\">\n            <text class=\"option-name\">批量处理</text>\n            <text class=\"option-desc\">同时转换多个文件</text>\n          </view>\n          <switch \n            :checked=\"options.batchMode\" \n            @change=\"toggleOption('batchMode')\"\n            color=\"#667eea\"\n          />\n        </view>\n      </view>\n    </view>\n    \n    <!-- 转换按钮 -->\n    <view class=\"action-section\">\n      <view \n        class=\"convert-btn\"\n        :class=\"{ disabled: !canConvert, loading: isConverting }\"\n        @click=\"startConvert\"\n      >\n        <view class=\"btn-content\" v-if=\"!isConverting\">\n          <u-icon name=\"refresh\" size=\"20\" color=\"#FFFFFF\"></u-icon>\n          <text class=\"btn-text\">开始转换</text>\n        </view>\n        <view class=\"btn-content\" v-else>\n          <view class=\"loading-icon\">\n            <u-icon name=\"loading\" size=\"20\" color=\"#FFFFFF\"></u-icon>\n          </view>\n          <text class=\"btn-text\">转换中... {{ convertProgress }}%</text>\n        </view>\n      </view>\n      \n      <text class=\"convert-tip\">转换完成后将自动下载到您的设备</text>\n    </view>\n    \n    <!-- 转换历史 -->\n    <view class=\"history-section\" v-if=\"convertHistory.length > 0\">\n      <view class=\"section-title\">转换历史</view>\n      \n      <view class=\"history-list\">\n        <view \n          class=\"history-item\"\n          v-for=\"(item, index) in convertHistory\"\n          :key=\"index\"\n          @click=\"downloadFile(item)\"\n        >\n          <view class=\"history-icon\">\n            <text class=\"iconfont\" :class=\"getFileIcon(item.targetType)\"></text>\n          </view>\n          \n          <view class=\"history-content\">\n            <text class=\"history-name\">{{ item.fileName }}</text>\n            <text class=\"history-desc\">\n              {{ item.sourceType.toUpperCase() }} → {{ item.targetType.toUpperCase() }}\n            </text>\n            <text class=\"history-time\">{{ formatTime(item.convertTime) }}</text>\n          </view>\n          \n          <view class=\"history-action\">\n            <u-icon name=\"download\" size=\"16\" color=\"#667eea\"></u-icon>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部安全区域 -->\n    <view class=\"bottom-safe-area\"></view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\n\n// 响应式数据\nconst selectedFile = ref<any>(null)\nconst uploadSuccess = ref<boolean>(false)\nconst uploadError = ref<boolean>(false)\nconst isConverting = ref<boolean>(false)\nconst convertProgress = ref<number>(0)\n\nconst sourceFormat = ref({\n  name: 'PDF',\n  icon: 'icon-pdf',\n  color: '#EF4444',\n  type: 'pdf'\n})\n\nconst targetFormat = ref({\n  name: 'Word',\n  icon: 'icon-document',\n  color: '#2563EB',\n  type: 'docx'\n})\n\nconst options = ref({\n  keepFormat: true,\n  highQuality: false,\n  batchMode: false\n})\n\nconst supportedFormats = ref([\n  { name: 'PDF', icon: 'icon-pdf', color: '#EF4444', type: 'pdf' },\n  { name: 'Word', icon: 'icon-document', color: '#2563EB', type: 'docx' },\n  { name: 'Excel', icon: 'icon-table', color: '#10B981', type: 'xlsx' },\n  { name: 'PowerPoint', icon: 'icon-presentation', color: '#F59E0B', type: 'pptx' },\n  { name: 'Text', icon: 'icon-text', color: '#8E8E93', type: 'txt' },\n  { name: 'HTML', icon: 'icon-code', color: '#EC4899', type: 'html' }\n])\n\nconst convertHistory = ref([\n  {\n    fileName: '商业计划书.docx',\n    sourceType: 'pdf',\n    targetType: 'docx',\n    convertTime: new Date(Date.now() - 2 * 60 * 60 * 1000),\n    downloadUrl: '/downloads/business-plan.docx'\n  },\n  {\n    fileName: '财务报表.xlsx',\n    sourceType: 'pdf',\n    targetType: 'xlsx',\n    convertTime: new Date(Date.now() - 5 * 60 * 60 * 1000),\n    downloadUrl: '/downloads/financial-report.xlsx'\n  }\n])\n\n// 计算属性\nconst canConvert = computed(() => {\n  return selectedFile.value && !isConverting.value && sourceFormat.value.type !== targetFormat.value.type\n})\n\n// 方法\nconst showSourcePicker = () => {\n  const itemList = supportedFormats.value.map(format => format.name)\n  uni.showActionSheet({\n    itemList,\n    success: (res) => {\n      sourceFormat.value = supportedFormats.value[res.tapIndex]\n    }\n  })\n}\n\nconst showTargetPicker = () => {\n  const itemList = supportedFormats.value.map(format => format.name)\n  uni.showActionSheet({\n    itemList,\n    success: (res) => {\n      targetFormat.value = supportedFormats.value[res.tapIndex]\n    }\n  })\n}\n\nconst swapFormats = () => {\n  const temp = sourceFormat.value\n  sourceFormat.value = targetFormat.value\n  targetFormat.value = temp\n}\n\nconst chooseFile = () => {\n  uni.chooseFile({\n    count: 1,\n    type: 'file',\n    success: (res) => {\n      const file = res.tempFiles[0]\n      selectedFile.value = {\n        name: file.name,\n        size: file.size,\n        type: getFileTypeFromName(file.name),\n        path: file.path\n      }\n      uploadSuccess.value = true\n      uploadError.value = false\n    },\n    fail: (err) => {\n      uni.__f__('error','at pages/utilities/document/converter/index.vue:287','选择文件失败:', err)\n      uploadError.value = true\n    }\n  })\n}\n\nconst removeFile = () => {\n  selectedFile.value = null\n  uploadSuccess.value = false\n  uploadError.value = false\n}\n\nconst toggleOption = (key: string) => {\n  options.value[key] = !options.value[key]\n}\n\nconst startConvert = () => {\n  if (!canConvert.value) return\n  \n  isConverting.value = true\n  convertProgress.value = 0\n  \n  // 模拟转换进度\n  const progressInterval = setInterval(() => {\n    convertProgress.value += Math.random() * 15\n    if (convertProgress.value >= 100) {\n      convertProgress.value = 100\n      clearInterval(progressInterval)\n      \n      setTimeout(() => {\n        isConverting.value = false\n        convertProgress.value = 0\n        \n        // 添加到转换历史\n        convertHistory.value.unshift({\n          fileName: selectedFile.value.name.replace(/\\.[^/.]+$/, '') + '.' + targetFormat.value.type,\n          sourceType: sourceFormat.value.type,\n          targetType: targetFormat.value.type,\n          convertTime: new Date(),\n          downloadUrl: '/downloads/converted-file.' + targetFormat.value.type\n        })\n        \n        uni.showToast({\n          title: '转换完成',\n          icon: 'success'\n        })\n        \n        // 重置文件选择\n        removeFile()\n      }, 1000)\n    }\n  }, 200)\n}\n\nconst downloadFile = (item: any) => {\n  uni.showToast({\n    title: '开始下载',\n    icon: 'success'\n  })\n  uni.__f__('log','at pages/utilities/document/converter/index.vue:346','下载文件:', item.fileName)\n}\n\nconst getFileTypeFromName = (fileName: string) => {\n  const ext = fileName.split('.').pop()?.toLowerCase()\n  return ext || 'unknown'\n}\n\nconst getFileIcon = (type: string) => {\n  const iconMap: Record<string, string> = {\n    pdf: 'icon-pdf',\n    doc: 'icon-document',\n    docx: 'icon-document',\n    xls: 'icon-table',\n    xlsx: 'icon-table',\n    ppt: 'icon-presentation',\n    pptx: 'icon-presentation',\n    txt: 'icon-text',\n    html: 'icon-code'\n  }\n  return iconMap[type] || 'icon-file'\n}\n\nconst getFileColor = (type: string) => {\n  const colorMap: Record<string, string> = {\n    pdf: '#EF4444',\n    doc: '#2563EB',\n    docx: '#2563EB',\n    xls: '#10B981',\n    xlsx: '#10B981',\n    ppt: '#F59E0B',\n    pptx: '#F59E0B',\n    txt: '#8E8E93',\n    html: '#EC4899'\n  }\n  return colorMap[type] || '#6B7280'\n}\n\nconst formatFileSize = (bytes: number) => {\n  if (bytes === 0) return '0 B'\n  const k = 1024\n  const sizes = ['B', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\nconst formatTime = (time: Date) => {\n  const now = new Date()\n  const diff = now.getTime() - time.getTime()\n  const hours = Math.floor(diff / (1000 * 60 * 60))\n  \n  if (hours < 1) return '刚刚'\n  if (hours < 24) return `${hours}小时前`\n  return `${Math.floor(hours / 24)}天前`\n}\n\nconst getUploadStatus = () => {\n  if (uploadError.value) return '上传失败'\n  if (uploadSuccess.value) return '上传成功'\n  return '准备上传'\n}\n\n// 生命周期\nonMounted(() => {\n  uni.__f__('log','at pages/utilities/document/converter/index.vue:410','文档转换页面加载完成')\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.document-converter {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);\n  padding: 32rpx;\n}\n\n// 头部区域\n.header-section {\n  margin-bottom: 48rpx;\n  \n  .header-content {\n    text-align: center;\n    \n    .page-title {\n      display: block;\n      font-size: 48rpx;\n      font-weight: 700;\n      color: #1D1D1F;\n      margin-bottom: 16rpx;\n    }\n    \n    .page-subtitle {\n      font-size: 28rpx;\n      color: #8E8E93;\n      line-height: 1.4;\n    }\n  }\n}\n\n// 转换配置区域\n.converter-section {\n  background: #FFFFFF;\n  border-radius: 24rpx;\n  padding: 40rpx;\n  margin-bottom: 32rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  \n  .format-selector {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    \n    .format-item {\n      flex: 1;\n      \n      .format-label {\n        display: block;\n        font-size: 24rpx;\n        color: #8E8E93;\n        margin-bottom: 16rpx;\n      }\n      \n      .format-picker {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        background: #F2F2F7;\n        border-radius: 16rpx;\n        padding: 24rpx;\n        \n        .format-display {\n          display: flex;\n          align-items: center;\n          \n          .format-icon {\n            width: 48rpx;\n            height: 48rpx;\n            border-radius: 12rpx;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            margin-right: 16rpx;\n            \n            .iconfont {\n              font-size: 24rpx;\n              color: #FFFFFF;\n            }\n          }\n          \n          .format-name {\n            font-size: 28rpx;\n            font-weight: 500;\n            color: #1D1D1F;\n          }\n        }\n      }\n    }\n    \n    .convert-arrow {\n      margin: 0 32rpx;\n      \n      .arrow-icon {\n        width: 64rpx;\n        height: 64rpx;\n        background: rgba(102, 126, 234, 0.1);\n        border-radius: 32rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: all 0.3s ease;\n        \n        &:active {\n          transform: scale(0.95);\n        }\n      }\n    }\n  }\n}\n\n// 文件上传区域\n.upload-section {\n  margin-bottom: 32rpx;\n  \n  .upload-area {\n    background: #FFFFFF;\n    border: 2rpx dashed #D1D5DB;\n    border-radius: 24rpx;\n    padding: 80rpx 40rpx;\n    text-align: center;\n    transition: all 0.3s ease;\n    \n    &:active {\n      border-color: #667eea;\n      background: rgba(102, 126, 234, 0.02);\n    }\n    \n    .upload-content {\n      .upload-icon {\n        margin-bottom: 24rpx;\n      }\n      \n      .upload-title {\n        display: block;\n        font-size: 32rpx;\n        font-weight: 600;\n        color: #1D1D1F;\n        margin-bottom: 12rpx;\n      }\n      \n      .upload-desc {\n        display: block;\n        font-size: 26rpx;\n        color: #8E8E93;\n        margin-bottom: 8rpx;\n      }\n      \n      .upload-limit {\n        font-size: 22rpx;\n        color: #C7C7CC;\n      }\n    }\n    \n    .file-preview {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      text-align: left;\n      \n      .file-info {\n        display: flex;\n        align-items: center;\n        flex: 1;\n        \n        .file-icon {\n          width: 80rpx;\n          height: 80rpx;\n          border-radius: 20rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin-right: 24rpx;\n          \n          .iconfont {\n            font-size: 32rpx;\n            color: #FFFFFF;\n          }\n        }\n        \n        .file-details {\n          .file-name {\n            display: block;\n            font-size: 28rpx;\n            font-weight: 600;\n            color: #1D1D1F;\n            margin-bottom: 8rpx;\n          }\n          \n          .file-size {\n            display: block;\n            font-size: 24rpx;\n            color: #8E8E93;\n            margin-bottom: 8rpx;\n          }\n          \n          .file-status {\n            font-size: 22rpx;\n            color: #8E8E93;\n            \n            &.success {\n              color: #10B981;\n            }\n            \n            &.error {\n              color: #EF4444;\n            }\n          }\n        }\n      }\n      \n      .file-actions {\n        .action-btn {\n          width: 56rpx;\n          height: 56rpx;\n          background: rgba(239, 68, 68, 0.1);\n          border-radius: 28rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          \n          &.remove {\n            background: rgba(239, 68, 68, 0.1);\n          }\n        }\n      }\n    }\n  }\n}\n\n// 转换选项\n.options-section {\n  background: #FFFFFF;\n  border-radius: 24rpx;\n  padding: 40rpx;\n  margin-bottom: 32rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  \n  .section-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: #1D1D1F;\n    margin-bottom: 32rpx;\n  }\n  \n  .option-list {\n    .option-item {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 24rpx 0;\n      border-bottom: 1rpx solid #F2F2F7;\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .option-info {\n        flex: 1;\n        margin-right: 32rpx;\n        \n        .option-name {\n          display: block;\n          font-size: 28rpx;\n          font-weight: 500;\n          color: #1D1D1F;\n          margin-bottom: 8rpx;\n        }\n        \n        .option-desc {\n          font-size: 24rpx;\n          color: #8E8E93;\n          line-height: 1.4;\n        }\n      }\n    }\n  }\n}\n\n// 转换按钮\n.action-section {\n  text-align: center;\n  margin-bottom: 48rpx;\n  \n  .convert-btn {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-radius: 28rpx;\n    padding: 32rpx 64rpx;\n    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);\n    transition: all 0.3s ease;\n    \n    &:active:not(.disabled):not(.loading) {\n      transform: scale(0.98);\n    }\n    \n    &.disabled {\n      opacity: 0.5;\n      box-shadow: none;\n    }\n    \n    &.loading {\n      pointer-events: none;\n    }\n    \n    .btn-content {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      \n      .btn-text {\n        font-size: 32rpx;\n        font-weight: 600;\n        color: #FFFFFF;\n        margin-left: 16rpx;\n      }\n      \n      .loading-icon {\n        animation: spin 1s linear infinite;\n      }\n    }\n  }\n  \n  .convert-tip {\n    display: block;\n    font-size: 24rpx;\n    color: #8E8E93;\n    margin-top: 24rpx;\n  }\n}\n\n// 转换历史\n.history-section {\n  .section-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: #1D1D1F;\n    margin-bottom: 32rpx;\n  }\n  \n  .history-list {\n    .history-item {\n      display: flex;\n      align-items: center;\n      background: #FFFFFF;\n      border-radius: 20rpx;\n      padding: 32rpx;\n      margin-bottom: 16rpx;\n      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);\n      transition: all 0.3s ease;\n      \n      &:active {\n        transform: scale(0.98);\n      }\n      \n      .history-icon {\n        width: 64rpx;\n        height: 64rpx;\n        background: #F2F2F7;\n        border-radius: 16rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 24rpx;\n        \n        .iconfont {\n          font-size: 24rpx;\n          color: #8E8E93;\n        }\n      }\n      \n      .history-content {\n        flex: 1;\n        \n        .history-name {\n          display: block;\n          font-size: 28rpx;\n          font-weight: 500;\n          color: #1D1D1F;\n          margin-bottom: 8rpx;\n        }\n        \n        .history-desc {\n          display: block;\n          font-size: 24rpx;\n          color: #8E8E93;\n          margin-bottom: 8rpx;\n        }\n        \n        .history-time {\n          font-size: 22rpx;\n          color: #C7C7CC;\n        }\n      }\n      \n      .history-action {\n        padding: 16rpx;\n      }\n    }\n  }\n}\n\n// 底部安全区域\n.bottom-safe-area {\n  height: calc(120rpx + env(safe-area-inset-bottom));\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n// 暗色模式适配\n@media (prefers-color-scheme: dark) {\n  .document-converter {\n    background: linear-gradient(180deg, #1c1c1e 0%, #000000 100%);\n  }\n  \n  .converter-section,\n  .upload-area,\n  .options-section,\n  .history-item {\n    background: #2c2c2e;\n    border-color: #38383a;\n  }\n  \n  .page-title,\n  .section-title,\n  .format-name,\n  .option-name,\n  .history-name {\n    color: #ffffff !important;\n  }\n  \n  .format-picker {\n    background: #38383a !important;\n  }\n  \n  .history-icon {\n    background: #38383a;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/AI_system/mobile-uniapp/src/pages/utilities/document/converter/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "onMounted"], "mappings": ";;;;;;;;;AA0LM,UAAA,eAAeA,kBAAS,IAAI;AAC5B,UAAA,gBAAgBA,kBAAa,KAAK;AAClC,UAAA,cAAcA,kBAAa,KAAK;AAChC,UAAA,eAAeA,kBAAa,KAAK;AACjC,UAAA,kBAAkBA,kBAAY,CAAC;AAErC,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACvB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,IAAA,CACP;AAED,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACvB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,IAAA,CACP;AAED,UAAM,UAAUA,cAAAA,IAAI;AAAA,MAClB,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,WAAW;AAAA,IAAA,CACZ;AAED,UAAM,mBAAmBA,cAAAA,IAAI;AAAA,MAC3B,EAAE,MAAM,OAAO,MAAM,YAAY,OAAO,WAAW,MAAM,MAAM;AAAA,MAC/D,EAAE,MAAM,QAAQ,MAAM,iBAAiB,OAAO,WAAW,MAAM,OAAO;AAAA,MACtE,EAAE,MAAM,SAAS,MAAM,cAAc,OAAO,WAAW,MAAM,OAAO;AAAA,MACpE,EAAE,MAAM,cAAc,MAAM,qBAAqB,OAAO,WAAW,MAAM,OAAO;AAAA,MAChF,EAAE,MAAM,QAAQ,MAAM,aAAa,OAAO,WAAW,MAAM,MAAM;AAAA,MACjE,EAAE,MAAM,QAAQ,MAAM,aAAa,OAAO,WAAW,MAAM,OAAO;AAAA,IAAA,CACnE;AAED,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB;AAAA,QACE,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa,IAAI,KAAK,KAAK,IAAQ,IAAA,IAAI,KAAK,KAAK,GAAI;AAAA,QACrD,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa,IAAI,KAAK,KAAK,IAAQ,IAAA,IAAI,KAAK,KAAK,GAAI;AAAA,QACrD,aAAa;AAAA,MACf;AAAA,IAAA,CACD;AAGK,UAAA,aAAaC,cAAAA,SAAS,MAAM;AACzB,aAAA,aAAa,SAAS,CAAC,aAAa,SAAS,aAAa,MAAM,SAAS,aAAa,MAAM;AAAA,IAAA,CACpG;AAGD,UAAM,mBAAmB,MAAM;AAC7B,YAAM,WAAW,iBAAiB,MAAM,IAAI,CAAA,WAAU,OAAO,IAAI;AACjEC,oBAAAA,MAAI,gBAAgB;AAAA,QAClB;AAAA,QACA,SAAS,CAAC,QAAQ;AAChB,uBAAa,QAAQ,iBAAiB,MAAM,IAAI,QAAQ;AAAA,QAC1D;AAAA,MAAA,CACD;AAAA,IAAA;AAGH,UAAM,mBAAmB,MAAM;AAC7B,YAAM,WAAW,iBAAiB,MAAM,IAAI,CAAA,WAAU,OAAO,IAAI;AACjEA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB;AAAA,QACA,SAAS,CAAC,QAAQ;AAChB,uBAAa,QAAQ,iBAAiB,MAAM,IAAI,QAAQ;AAAA,QAC1D;AAAA,MAAA,CACD;AAAA,IAAA;AAGH,UAAM,cAAc,MAAM;AACxB,YAAM,OAAO,aAAa;AAC1B,mBAAa,QAAQ,aAAa;AAClC,mBAAa,QAAQ;AAAA,IAAA;AAGvB,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,SAAS,CAAC,QAAQ;AACV,gBAAA,OAAO,IAAI,UAAU,CAAC;AAC5B,uBAAa,QAAQ;AAAA,YACnB,MAAM,KAAK;AAAA,YACX,MAAM,KAAK;AAAA,YACX,MAAM,oBAAoB,KAAK,IAAI;AAAA,YACnC,MAAM,KAAK;AAAA,UAAA;AAEb,wBAAc,QAAQ;AACtB,sBAAY,QAAQ;AAAA,QACtB;AAAA,QACA,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAI,MAAM,SAAQ,uDAAsD,WAAW,GAAG;AACtF,sBAAY,QAAQ;AAAA,QACtB;AAAA,MAAA,CACD;AAAA,IAAA;AAGH,UAAM,aAAa,MAAM;AACvB,mBAAa,QAAQ;AACrB,oBAAc,QAAQ;AACtB,kBAAY,QAAQ;AAAA,IAAA;AAGhB,UAAA,eAAe,CAAC,QAAgB;AACpC,cAAQ,MAAM,GAAG,IAAI,CAAC,QAAQ,MAAM,GAAG;AAAA,IAAA;AAGzC,UAAM,eAAe,MAAM;AACzB,UAAI,CAAC,WAAW;AAAO;AAEvB,mBAAa,QAAQ;AACrB,sBAAgB,QAAQ;AAGlB,YAAA,mBAAmB,YAAY,MAAM;AACzB,wBAAA,SAAS,KAAK,OAAA,IAAW;AACrC,YAAA,gBAAgB,SAAS,KAAK;AAChC,0BAAgB,QAAQ;AACxB,wBAAc,gBAAgB;AAE9B,qBAAW,MAAM;AACf,yBAAa,QAAQ;AACrB,4BAAgB,QAAQ;AAGxB,2BAAe,MAAM,QAAQ;AAAA,cAC3B,UAAU,aAAa,MAAM,KAAK,QAAQ,aAAa,EAAE,IAAI,MAAM,aAAa,MAAM;AAAA,cACtF,YAAY,aAAa,MAAM;AAAA,cAC/B,YAAY,aAAa,MAAM;AAAA,cAC/B,iCAAiB,KAAK;AAAA,cACtB,aAAa,+BAA+B,aAAa,MAAM;AAAA,YAAA,CAChE;AAEDA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAAA,CACP;AAGU;aACV,GAAI;AAAA,QACT;AAAA,SACC,GAAG;AAAA,IAAA;AAGF,UAAA,eAAe,CAAC,SAAc;AAClCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AACDA,oBAAA,MAAI,MAAM,OAAM,uDAAsD,SAAS,KAAK,QAAQ;AAAA,IAAA;AAGxF,UAAA,sBAAsB,CAAC,aAAqB;;AAChD,YAAM,OAAM,cAAS,MAAM,GAAG,EAAE,IAAA,MAApB,mBAA2B;AACvC,aAAO,OAAO;AAAA,IAAA;AAGV,UAAA,cAAc,CAAC,SAAiB;AACpC,YAAM,UAAkC;AAAA,QACtC,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MAAA;AAED,aAAA,QAAQ,IAAI,KAAK;AAAA,IAAA;AAGpB,UAAA,eAAe,CAAC,SAAiB;AACrC,YAAM,WAAmC;AAAA,QACvC,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MAAA;AAED,aAAA,SAAS,IAAI,KAAK;AAAA,IAAA;AAGrB,UAAA,iBAAiB,CAAC,UAAkB;AACxC,UAAI,UAAU;AAAU,eAAA;AACxB,YAAM,IAAI;AACV,YAAM,QAAQ,CAAC,KAAK,MAAM,MAAM,IAAI;AAC9B,YAAA,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,CAAC;AAClD,aAAO,YAAY,QAAQ,KAAK,IAAI,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC;AAAA,IAAA;AAGlE,UAAA,aAAa,CAAC,SAAe;AAC3B,YAAA,0BAAU;AAChB,YAAM,OAAO,IAAI,QAAQ,IAAI,KAAK,QAAQ;AAC1C,YAAM,QAAQ,KAAK,MAAM,QAAQ,MAAO,KAAK,GAAG;AAEhD,UAAI,QAAQ;AAAU,eAAA;AACtB,UAAI,QAAQ;AAAI,eAAO,GAAG,KAAK;AAC/B,aAAO,GAAG,KAAK,MAAM,QAAQ,EAAE,CAAC;AAAA,IAAA;AAGlC,UAAM,kBAAkB,MAAM;AAC5B,UAAI,YAAY;AAAc,eAAA;AAC9B,UAAI,cAAc;AAAc,eAAA;AACzB,aAAA;AAAA,IAAA;AAITC,kBAAAA,UAAU,MAAM;AACVD,oBAAAA,MAAA,MAAM,OAAM,uDAAsD,YAAY;AAAA,IAAA,CACnF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzZD,GAAG,WAAW,eAAe;"}