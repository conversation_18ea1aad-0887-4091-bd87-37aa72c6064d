{"version": 3, "file": "user.js", "sources": ["api/user.ts"], "sourcesContent": ["/**\n * 用户相关API接口\n */\nimport request from '@/utils/request'\n\n// 用户信息接口类型定义\nexport interface UserInfo {\n  id: string\n  username: string\n  nickname: string\n  avatar: string\n  email?: string\n  phone?: string\n  gender?: 'male' | 'female' | 'unknown'\n  birthday?: string\n  bio?: string\n  level: 'free' | 'pro' | 'enterprise'\n  vipExpireTime?: string\n  createdAt: string\n  updatedAt: string\n}\n\n// 登录请求参数\nexport interface LoginParams {\n  username: string\n  password: string\n  captcha?: string\n  rememberMe?: boolean\n}\n\n// 登录响应数据\nexport interface LoginResponse {\n  token: string\n  refreshToken: string\n  userInfo: UserInfo\n  expiresIn: number\n}\n\n// 注册请求参数\nexport interface RegisterParams {\n  username: string\n  password: string\n  confirmPassword: string\n  email?: string\n  phone?: string\n  captcha: string\n  inviteCode?: string\n}\n\n// 用户统计信息\nexport interface UserStats {\n  translationCount: number\n  chatCount: number\n  documentCount: number\n  totalUsage: number\n  monthlyUsage: number\n  remainingQuota: number\n}\n\n// 用户设置\nexport interface UserSettings {\n  theme: 'light' | 'dark' | 'auto'\n  language: string\n  notifications: {\n    email: boolean\n    push: boolean\n    sms: boolean\n  }\n  privacy: {\n    profileVisible: boolean\n    activityVisible: boolean\n  }\n  preferences: {\n    autoTranslate: boolean\n    voiceInput: boolean\n    smartRecommend: boolean\n  }\n}\n\n/**\n * 用户登录\n */\nexport const login = (params: LoginParams): Promise<LoginResponse> => {\n  return request.post('/auth/login', params)\n}\n\n/**\n * 用户注册\n */\nexport const register = (params: RegisterParams): Promise<{ message: string }> => {\n  return request.post('/auth/register', params)\n}\n\n/**\n * 用户登出\n */\nexport const logout = (): Promise<{ message: string }> => {\n  return request.post('/auth/logout')\n}\n\n/**\n * 刷新Token\n */\nexport const refreshToken = (refreshToken: string): Promise<{ token: string; expiresIn: number }> => {\n  return request.post('/auth/refresh', { refreshToken })\n}\n\n/**\n * 获取用户信息\n */\nexport const getUserInfo = (): Promise<UserInfo> => {\n  return request.get('/user/profile')\n}\n\n/**\n * 更新用户信息\n */\nexport const updateUserInfo = (params: Partial<UserInfo>): Promise<UserInfo> => {\n  return request.put('/user/profile', params)\n}\n\n/**\n * 上传用户头像\n */\nexport const uploadAvatar = (file: File): Promise<{ avatar: string }> => {\n  const formData = new FormData()\n  formData.append('avatar', file)\n  return request.post('/user/avatar', formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  })\n}\n\n/**\n * 修改密码\n */\nexport const changePassword = (params: {\n  oldPassword: string\n  newPassword: string\n  confirmPassword: string\n}): Promise<{ message: string }> => {\n  return request.put('/user/password', params)\n}\n\n/**\n * 获取用户统计信息\n */\nexport const getUserStats = (): Promise<UserStats> => {\n  return request.get('/user/stats')\n}\n\n/**\n * 获取用户设置\n */\nexport const getUserSettings = (): Promise<UserSettings> => {\n  return request.get('/user/settings')\n}\n\n/**\n * 更新用户设置\n */\nexport const updateUserSettings = (settings: Partial<UserSettings>): Promise<UserSettings> => {\n  return request.put('/user/settings', settings)\n}\n\n/**\n * 发送验证码\n */\nexport const sendVerificationCode = (params: {\n  type: 'email' | 'phone'\n  target: string\n  purpose: 'register' | 'login' | 'reset' | 'bind'\n}): Promise<{ message: string }> => {\n  return request.post('/auth/verification-code', params)\n}\n\n/**\n * 验证验证码\n */\nexport const verifyCode = (params: {\n  type: 'email' | 'phone'\n  target: string\n  code: string\n}): Promise<{ valid: boolean }> => {\n  return request.post('/auth/verify-code', params)\n}\n\n/**\n * 重置密码\n */\nexport const resetPassword = (params: {\n  type: 'email' | 'phone'\n  target: string\n  code: string\n  newPassword: string\n  confirmPassword: string\n}): Promise<{ message: string }> => {\n  return request.post('/auth/reset-password', params)\n}\n\n/**\n * 绑定邮箱/手机\n */\nexport const bindContact = (params: {\n  type: 'email' | 'phone'\n  target: string\n  code: string\n}): Promise<{ message: string }> => {\n  return request.post('/user/bind-contact', params)\n}\n\n/**\n * 解绑邮箱/手机\n */\nexport const unbindContact = (params: {\n  type: 'email' | 'phone'\n  password: string\n}): Promise<{ message: string }> => {\n  return request.post('/user/unbind-contact', params)\n}\n\n/**\n * 获取用户使用历史\n */\nexport const getUserHistory = (params: {\n  type?: 'translation' | 'chat' | 'document' | 'all'\n  page?: number\n  limit?: number\n  startDate?: string\n  endDate?: string\n}): Promise<{\n  list: Array<{\n    id: string\n    type: string\n    title: string\n    content: string\n    result?: string\n    status: 'success' | 'failed' | 'processing'\n    createdAt: string\n  }>\n  total: number\n  page: number\n  limit: number\n}> => {\n  return request.get('/user/history', { params })\n}\n\n/**\n * 删除使用历史\n */\nexport const deleteHistory = (ids: string[]): Promise<{ message: string }> => {\n  return request.delete('/user/history', { data: { ids } })\n}\n\n/**\n * 清空使用历史\n */\nexport const clearHistory = (type?: string): Promise<{ message: string }> => {\n  return request.delete('/user/history/clear', { data: { type } })\n}\n\n/**\n * 获取用户收藏\n */\nexport const getUserFavorites = (params: {\n  type?: 'agent' | 'translation' | 'document'\n  page?: number\n  limit?: number\n}): Promise<{\n  list: Array<{\n    id: string\n    type: string\n    title: string\n    description: string\n    thumbnail?: string\n    createdAt: string\n  }>\n  total: number\n  page: number\n  limit: number\n}> => {\n  return request.get('/user/favorites', { params })\n}\n\n/**\n * 添加收藏\n */\nexport const addFavorite = (params: {\n  type: string\n  targetId: string\n  title: string\n  description?: string\n  thumbnail?: string\n}): Promise<{ message: string }> => {\n  return request.post('/user/favorites', params)\n}\n\n/**\n * 取消收藏\n */\nexport const removeFavorite = (id: string): Promise<{ message: string }> => {\n  return request.delete(`/user/favorites/${id}`)\n}\n\n/**\n * 检查是否已收藏\n */\nexport const checkFavorite = (targetId: string, type: string): Promise<{ isFavorite: boolean }> => {\n  return request.get('/user/favorites/check', { params: { targetId, type } })\n}\n\n// 默认导出所有API\nexport default {\n  login,\n  register,\n  logout,\n  refreshToken,\n  getUserInfo,\n  updateUserInfo,\n  uploadAvatar,\n  changePassword,\n  getUserStats,\n  getUserSettings,\n  updateUserSettings,\n  sendVerificationCode,\n  verifyCode,\n  resetPassword,\n  bindContact,\n  unbindContact,\n  getUserHistory,\n  deleteHistory,\n  clearHistory,\n  getUserFavorites,\n  addFavorite,\n  removeFavorite,\n  checkFavorite\n}\n"], "names": ["request", "refreshToken"], "mappings": ";;AAkFa,MAAA,QAAQ,CAAC,WAAgD;AAC7D,SAAAA,sBAAQ,KAAK,eAAe,MAAM;AAC3C;AAKa,MAAA,WAAW,CAAC,WAAyD;AACzE,SAAAA,sBAAQ,KAAK,kBAAkB,MAAM;AAC9C;AAKO,MAAM,SAAS,MAAoC;AACjD,SAAAA,cAAA,QAAQ,KAAK,cAAc;AACpC;AAKa,MAAA,eAAe,CAACC,kBAAwE;AACnG,SAAOD,cAAAA,QAAQ,KAAK,iBAAiB,EAAE,cAAAC,eAAc;AACvD;AAKO,MAAM,cAAc,MAAyB;AAC3C,SAAAD,cAAA,QAAQ,IAAI,eAAe;AACpC;AAKa,MAAA,iBAAiB,CAAC,WAAiD;AACvE,SAAAA,sBAAQ,IAAI,iBAAiB,MAAM;AAC5C;AAKa,MAAA,eAAe,CAAC,SAA4C;AACjE,QAAA,WAAW,IAAI;AACZ,WAAA,OAAO,UAAU,IAAI;AACvB,SAAAA,sBAAQ,KAAK,gBAAgB,UAAU;AAAA,IAC5C,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,EAAA,CACD;AACH;AAKa,MAAA,iBAAiB,CAAC,WAIK;AAC3B,SAAAA,sBAAQ,IAAI,kBAAkB,MAAM;AAC7C;AAKO,MAAM,eAAe,MAA0B;AAC7C,SAAAA,cAAA,QAAQ,IAAI,aAAa;AAClC;AAKO,MAAM,kBAAkB,MAA6B;AACnD,SAAAA,cAAA,QAAQ,IAAI,gBAAgB;AACrC;AAKa,MAAA,qBAAqB,CAAC,aAA2D;AACrF,SAAAA,sBAAQ,IAAI,kBAAkB,QAAQ;AAC/C;AAKa,MAAA,uBAAuB,CAAC,WAID;AAC3B,SAAAA,sBAAQ,KAAK,2BAA2B,MAAM;AACvD;AAKa,MAAA,aAAa,CAAC,WAIQ;AAC1B,SAAAA,sBAAQ,KAAK,qBAAqB,MAAM;AACjD;AAKa,MAAA,gBAAgB,CAAC,WAMM;AAC3B,SAAAA,sBAAQ,KAAK,wBAAwB,MAAM;AACpD;AAKa,MAAA,cAAc,CAAC,WAIQ;AAC3B,SAAAA,sBAAQ,KAAK,sBAAsB,MAAM;AAClD;AAKa,MAAA,gBAAgB,CAAC,WAGM;AAC3B,SAAAA,sBAAQ,KAAK,wBAAwB,MAAM;AACpD;AAKa,MAAA,iBAAiB,CAAC,WAmBzB;AACJ,SAAOA,cAAQ,QAAA,IAAI,iBAAiB,EAAE,OAAQ,CAAA;AAChD;AAKa,MAAA,gBAAgB,CAAC,QAAgD;AACrE,SAAAA,cAAAA,QAAQ,OAAO,iBAAiB,EAAE,MAAM,EAAE,OAAO;AAC1D;AAKa,MAAA,eAAe,CAAC,SAAgD;AACpE,SAAAA,cAAAA,QAAQ,OAAO,uBAAuB,EAAE,MAAM,EAAE,QAAQ;AACjE;AAKa,MAAA,mBAAmB,CAAC,WAgB3B;AACJ,SAAOA,cAAQ,QAAA,IAAI,mBAAmB,EAAE,OAAQ,CAAA;AAClD;AAKa,MAAA,cAAc,CAAC,WAMQ;AAC3B,SAAAA,sBAAQ,KAAK,mBAAmB,MAAM;AAC/C;AAKa,MAAA,iBAAiB,CAAC,OAA6C;AAC1E,SAAOA,cAAAA,QAAQ,OAAO,mBAAmB,EAAE,EAAE;AAC/C;AAKa,MAAA,gBAAgB,CAAC,UAAkB,SAAmD;AAC1F,SAAAA,cAAA,QAAQ,IAAI,yBAAyB,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAA,CAAG;AAC5E;AAGA,MAAe,UAAA;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;"}