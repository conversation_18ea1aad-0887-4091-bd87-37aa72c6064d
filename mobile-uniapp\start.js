#!/usr/bin/env node

const { spawn } = require('child_process')
const path = require('path')

// 设置环境变量
process.env.UNI_PLATFORM = process.argv[2] || 'h5'
process.env.NODE_ENV = 'development'

console.log(`🚀 启动 Uni-app 项目 - 平台: ${process.env.UNI_PLATFORM}`)

// 启动 Vite 开发服务器
const vite = spawn('npx', ['vite'], {
  stdio: 'inherit',
  shell: true,
  cwd: __dirname,
  env: process.env
})

vite.on('close', (code) => {
  console.log(`进程退出，退出码: ${code}`)
})

vite.on('error', (err) => {
  console.error('启动失败:', err)
})
