{"version": 3, "file": "translation.js", "sources": ["store/modules/translation.ts"], "sourcesContent": ["import { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\nimport { translationApi, type LanguageInfo, type TranslationHistory } from '@/api/translation'\n\nexport const useTranslationStore = defineStore('translation', () => {\n  // 状态\n  const languages = ref<LanguageInfo[]>([])\n  const currentSourceLang = ref<string>('auto')\n  const currentTargetLang = ref<string>('zh-CN')\n  const translationHistory = ref<TranslationHistory[]>([])\n  const favorites = ref<TranslationHistory[]>([])\n  const isTranslating = ref<boolean>(false)\n  const lastTranslation = ref<{\n    source: string\n    target: string\n    sourceLang: string\n    targetLang: string\n  } | null>(null)\n\n  // 计算属性\n  const sourceLanguage = computed(() => {\n    return languages.value.find(lang => lang.code === currentSourceLang.value)\n  })\n\n  const targetLanguage = computed(() => {\n    return languages.value.find(lang => lang.code === currentTargetLang.value)\n  })\n\n  const recentLanguagePairs = computed(() => {\n    const pairs = new Set<string>()\n    const recentPairs: Array<{ source: string; target: string }> = []\n    \n    translationHistory.value.slice(0, 10).forEach(item => {\n      const pairKey = `${item.source_lang}-${item.target_lang}`\n      if (!pairs.has(pairKey)) {\n        pairs.add(pairKey)\n        recentPairs.push({\n          source: item.source_lang,\n          target: item.target_lang\n        })\n      }\n    })\n    \n    return recentPairs.slice(0, 5)\n  })\n\n  // 加载支持的语言\n  const loadLanguages = async () => {\n    try {\n      const response = await translationApi.getLanguages()\n      if (response.success) {\n        languages.value = response.data\n      }\n    } catch (error) {\n      console.error('加载语言列表失败:', error)\n    }\n  }\n\n  // 文本翻译\n  const translateText = async (text: string, options?: {\n    sourceLang?: string\n    targetLang?: string\n    domain?: string\n    style?: string\n  }) => {\n    if (!text.trim()) {\n      throw new Error('请输入要翻译的文本')\n    }\n\n    try {\n      isTranslating.value = true\n      \n      const sourceLang = options?.sourceLang || currentSourceLang.value\n      const targetLang = options?.targetLang || currentTargetLang.value\n      \n      const response = await translationApi.translateText({\n        text: text.trim(),\n        source_lang: sourceLang,\n        target_lang: targetLang,\n        domain: options?.domain,\n        style: options?.style\n      })\n\n      if (response.success) {\n        // 保存最后一次翻译\n        lastTranslation.value = {\n          source: text,\n          target: response.data.translated_text,\n          sourceLang: response.data.source_lang,\n          targetLang: response.data.target_lang\n        }\n\n        // 更新当前语言设置\n        if (sourceLang === 'auto') {\n          currentSourceLang.value = response.data.source_lang\n        }\n\n        // 添加到历史记录\n        await loadHistory()\n\n        return response.data\n      } else {\n        throw new Error(response.message || '翻译失败')\n      }\n    } catch (error: any) {\n      console.error('翻译失败:', error)\n      throw error\n    } finally {\n      isTranslating.value = false\n    }\n  }\n\n  // 语言检测\n  const detectLanguage = async (text: string) => {\n    if (!text.trim()) return null\n\n    try {\n      const response = await translationApi.detectLanguage(text.trim())\n      if (response.success) {\n        return response.data\n      }\n    } catch (error) {\n      console.error('语言检测失败:', error)\n    }\n    return null\n  }\n\n  // 加载翻译历史\n  const loadHistory = async (page = 1, limit = 20) => {\n    try {\n      const response = await translationApi.getHistory({ page, limit })\n      if (response.success) {\n        if (page === 1) {\n          translationHistory.value = response.data.items\n        } else {\n          translationHistory.value.push(...response.data.items)\n        }\n        return response.data\n      }\n    } catch (error) {\n      console.error('加载翻译历史失败:', error)\n    }\n    return null\n  }\n\n  // 删除历史记录\n  const deleteHistoryItem = async (id: string) => {\n    try {\n      const response = await translationApi.deleteHistory(id)\n      if (response.success) {\n        translationHistory.value = translationHistory.value.filter(item => item.id !== id)\n        uni.showToast({\n          title: '删除成功',\n          icon: 'success'\n        })\n      }\n    } catch (error) {\n      console.error('删除历史记录失败:', error)\n      uni.showToast({\n        title: '删除失败',\n        icon: 'error'\n      })\n    }\n  }\n\n  // 清空历史记录\n  const clearHistory = async () => {\n    try {\n      const response = await translationApi.clearHistory()\n      if (response.success) {\n        translationHistory.value = []\n        uni.showToast({\n          title: '清空成功',\n          icon: 'success'\n        })\n      }\n    } catch (error) {\n      console.error('清空历史记录失败:', error)\n      uni.showToast({\n        title: '清空失败',\n        icon: 'error'\n      })\n    }\n  }\n\n  // 收藏翻译\n  const toggleFavorite = async (id: string, isFavorite: boolean) => {\n    try {\n      const response = isFavorite \n        ? await translationApi.unfavoriteTranslation(id)\n        : await translationApi.favoriteTranslation(id)\n      \n      if (response.success) {\n        // 更新历史记录中的收藏状态\n        const historyItem = translationHistory.value.find(item => item.id === id)\n        if (historyItem) {\n          (historyItem as any).is_favorite = !isFavorite\n        }\n\n        // 更新收藏列表\n        if (isFavorite) {\n          favorites.value = favorites.value.filter(item => item.id !== id)\n        } else {\n          await loadFavorites()\n        }\n\n        uni.showToast({\n          title: isFavorite ? '取消收藏' : '收藏成功',\n          icon: 'success'\n        })\n      }\n    } catch (error) {\n      console.error('操作收藏失败:', error)\n      uni.showToast({\n        title: '操作失败',\n        icon: 'error'\n      })\n    }\n  }\n\n  // 加载收藏列表\n  const loadFavorites = async (page = 1, limit = 20) => {\n    try {\n      const response = await translationApi.getFavorites({ page, limit })\n      if (response.success) {\n        if (page === 1) {\n          favorites.value = response.data.items\n        } else {\n          favorites.value.push(...response.data.items)\n        }\n        return response.data\n      }\n    } catch (error) {\n      console.error('加载收藏列表失败:', error)\n    }\n    return null\n  }\n\n  // 交换源语言和目标语言\n  const swapLanguages = () => {\n    if (currentSourceLang.value === 'auto') {\n      uni.showToast({\n        title: '自动检测语言无法交换',\n        icon: 'none'\n      })\n      return\n    }\n\n    const temp = currentSourceLang.value\n    currentSourceLang.value = currentTargetLang.value\n    currentTargetLang.value = temp\n  }\n\n  // 设置语言\n  const setSourceLanguage = (langCode: string) => {\n    currentSourceLang.value = langCode\n  }\n\n  const setTargetLanguage = (langCode: string) => {\n    currentTargetLang.value = langCode\n  }\n\n  // 使用最近的语言对\n  const useLanguagePair = (sourceLang: string, targetLang: string) => {\n    currentSourceLang.value = sourceLang\n    currentTargetLang.value = targetLang\n  }\n\n  return {\n    // 状态\n    languages,\n    currentSourceLang,\n    currentTargetLang,\n    translationHistory,\n    favorites,\n    isTranslating,\n    lastTranslation,\n\n    // 计算属性\n    sourceLanguage,\n    targetLanguage,\n    recentLanguagePairs,\n\n    // 方法\n    loadLanguages,\n    translateText,\n    detectLanguage,\n    loadHistory,\n    deleteHistoryItem,\n    clearHistory,\n    toggleFavorite,\n    loadFavorites,\n    swapLanguages,\n    setSourceLanguage,\n    setTargetLanguage,\n    useLanguagePair\n  }\n}, {\n  persist: {\n    key: 'translation-store',\n    paths: ['currentSourceLang', 'currentTargetLang', 'lastTranslation']\n  }\n})\n"], "names": ["defineStore", "ref", "computed", "translationApi", "uni"], "mappings": ";;;AAIa,MAAA,sBAAsBA,cAAAA,YAAY,eAAe,MAAM;AAE5D,QAAA,YAAYC,kBAAoB,CAAA,CAAE;AAClC,QAAA,oBAAoBA,kBAAY,MAAM;AACtC,QAAA,oBAAoBA,kBAAY,OAAO;AACvC,QAAA,qBAAqBA,kBAA0B,CAAA,CAAE;AACjD,QAAA,YAAYA,kBAA0B,CAAA,CAAE;AACxC,QAAA,gBAAgBA,kBAAa,KAAK;AAClC,QAAA,kBAAkBA,kBAKd,IAAI;AAGR,QAAA,iBAAiBC,cAAAA,SAAS,MAAM;AACpC,WAAO,UAAU,MAAM,KAAK,UAAQ,KAAK,SAAS,kBAAkB,KAAK;AAAA,EAAA,CAC1E;AAEK,QAAA,iBAAiBA,cAAAA,SAAS,MAAM;AACpC,WAAO,UAAU,MAAM,KAAK,UAAQ,KAAK,SAAS,kBAAkB,KAAK;AAAA,EAAA,CAC1E;AAEK,QAAA,sBAAsBA,cAAAA,SAAS,MAAM;AACnC,UAAA,4BAAY;AAClB,UAAM,cAAyD,CAAA;AAE/D,uBAAmB,MAAM,MAAM,GAAG,EAAE,EAAE,QAAQ,CAAQ,SAAA;AACpD,YAAM,UAAU,GAAG,KAAK,WAAW,IAAI,KAAK,WAAW;AACvD,UAAI,CAAC,MAAM,IAAI,OAAO,GAAG;AACvB,cAAM,IAAI,OAAO;AACjB,oBAAY,KAAK;AAAA,UACf,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,QAAA,CACd;AAAA,MACH;AAAA,IAAA,CACD;AAEM,WAAA,YAAY,MAAM,GAAG,CAAC;AAAA,EAAA,CAC9B;AAGD,QAAM,gBAAgB,YAAY;AAC5B,QAAA;AACI,YAAA,WAAW,MAAMC,+BAAe;AACtC,UAAI,SAAS,SAAS;AACpB,kBAAU,QAAQ,SAAS;AAAA,MAC7B;AAAA,aACO,OAAO;AACdC,oBAAA,2DAAc,aAAa,KAAK;AAAA,IAClC;AAAA,EAAA;AAII,QAAA,gBAAgB,OAAO,MAAc,YAKrC;AACA,QAAA,CAAC,KAAK,QAAQ;AACV,YAAA,IAAI,MAAM,WAAW;AAAA,IAC7B;AAEI,QAAA;AACF,oBAAc,QAAQ;AAEhB,YAAA,cAAa,mCAAS,eAAc,kBAAkB;AACtD,YAAA,cAAa,mCAAS,eAAc,kBAAkB;AAEtD,YAAA,WAAW,MAAMD,gBAAA,eAAe,cAAc;AAAA,QAClD,MAAM,KAAK,KAAK;AAAA,QAChB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,QAAQ,mCAAS;AAAA,QACjB,OAAO,mCAAS;AAAA,MAAA,CACjB;AAED,UAAI,SAAS,SAAS;AAEpB,wBAAgB,QAAQ;AAAA,UACtB,QAAQ;AAAA,UACR,QAAQ,SAAS,KAAK;AAAA,UACtB,YAAY,SAAS,KAAK;AAAA,UAC1B,YAAY,SAAS,KAAK;AAAA,QAAA;AAI5B,YAAI,eAAe,QAAQ;AACP,4BAAA,QAAQ,SAAS,KAAK;AAAA,QAC1C;AAGA,cAAM,YAAY;AAElB,eAAO,SAAS;AAAA,MAAA,OACX;AACL,cAAM,IAAI,MAAM,SAAS,WAAW,MAAM;AAAA,MAC5C;AAAA,aACO,OAAY;AACnBC,oBAAA,MAAc,MAAA,SAAA,uCAAA,SAAS,KAAK;AACtB,YAAA;AAAA,IAAA,UACN;AACA,oBAAc,QAAQ;AAAA,IACxB;AAAA,EAAA;AAII,QAAA,iBAAiB,OAAO,SAAiB;AACzC,QAAA,CAAC,KAAK,KAAK;AAAU,aAAA;AAErB,QAAA;AACF,YAAM,WAAW,MAAMD,gBAAA,eAAe,eAAe,KAAK,MAAM;AAChE,UAAI,SAAS,SAAS;AACpB,eAAO,SAAS;AAAA,MAClB;AAAA,aACO,OAAO;AACdC,oBAAA,MAAc,MAAA,SAAA,uCAAA,WAAW,KAAK;AAAA,IAChC;AACO,WAAA;AAAA,EAAA;AAIT,QAAM,cAAc,OAAO,OAAO,GAAG,QAAQ,OAAO;AAC9C,QAAA;AACF,YAAM,WAAW,MAAMD,+BAAe,WAAW,EAAE,MAAM,OAAO;AAChE,UAAI,SAAS,SAAS;AACpB,YAAI,SAAS,GAAG;AACK,6BAAA,QAAQ,SAAS,KAAK;AAAA,QAAA,OACpC;AACL,6BAAmB,MAAM,KAAK,GAAG,SAAS,KAAK,KAAK;AAAA,QACtD;AACA,eAAO,SAAS;AAAA,MAClB;AAAA,aACO,OAAO;AACdC,oBAAA,4DAAc,aAAa,KAAK;AAAA,IAClC;AACO,WAAA;AAAA,EAAA;AAIH,QAAA,oBAAoB,OAAO,OAAe;AAC1C,QAAA;AACF,YAAM,WAAW,MAAMD,gBAAAA,eAAe,cAAc,EAAE;AACtD,UAAI,SAAS,SAAS;AACpB,2BAAmB,QAAQ,mBAAmB,MAAM,OAAO,CAAQ,SAAA,KAAK,OAAO,EAAE;AACjFC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,aACO,OAAO;AACdA,oBAAA,4DAAc,aAAa,KAAK;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IACH;AAAA,EAAA;AAIF,QAAM,eAAe,YAAY;AAC3B,QAAA;AACI,YAAA,WAAW,MAAMD,+BAAe;AACtC,UAAI,SAAS,SAAS;AACpB,2BAAmB,QAAQ;AAC3BC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,aACO,OAAO;AACdA,oBAAA,4DAAc,aAAa,KAAK;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IACH;AAAA,EAAA;AAII,QAAA,iBAAiB,OAAO,IAAY,eAAwB;AAC5D,QAAA;AACI,YAAA,WAAW,aACb,MAAMD,gBAAAA,eAAe,sBAAsB,EAAE,IAC7C,MAAMA,gBAAe,eAAA,oBAAoB,EAAE;AAE/C,UAAI,SAAS,SAAS;AAEpB,cAAM,cAAc,mBAAmB,MAAM,KAAK,CAAQ,SAAA,KAAK,OAAO,EAAE;AACxE,YAAI,aAAa;AACd,sBAAoB,cAAc,CAAC;AAAA,QACtC;AAGA,YAAI,YAAY;AACd,oBAAU,QAAQ,UAAU,MAAM,OAAO,CAAQ,SAAA,KAAK,OAAO,EAAE;AAAA,QAAA,OAC1D;AACL,gBAAM,cAAc;AAAA,QACtB;AAEAC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,aAAa,SAAS;AAAA,UAC7B,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,aACO,OAAO;AACdA,oBAAA,MAAc,MAAA,SAAA,uCAAA,WAAW,KAAK;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IACH;AAAA,EAAA;AAIF,QAAM,gBAAgB,OAAO,OAAO,GAAG,QAAQ,OAAO;AAChD,QAAA;AACF,YAAM,WAAW,MAAMD,+BAAe,aAAa,EAAE,MAAM,OAAO;AAClE,UAAI,SAAS,SAAS;AACpB,YAAI,SAAS,GAAG;AACJ,oBAAA,QAAQ,SAAS,KAAK;AAAA,QAAA,OAC3B;AACL,oBAAU,MAAM,KAAK,GAAG,SAAS,KAAK,KAAK;AAAA,QAC7C;AACA,eAAO,SAAS;AAAA,MAClB;AAAA,aACO,OAAO;AACdC,oBAAA,4DAAc,aAAa,KAAK;AAAA,IAClC;AACO,WAAA;AAAA,EAAA;AAIT,QAAM,gBAAgB,MAAM;AACtB,QAAA,kBAAkB,UAAU,QAAQ;AACtCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AACD;AAAA,IACF;AAEA,UAAM,OAAO,kBAAkB;AAC/B,sBAAkB,QAAQ,kBAAkB;AAC5C,sBAAkB,QAAQ;AAAA,EAAA;AAItB,QAAA,oBAAoB,CAAC,aAAqB;AAC9C,sBAAkB,QAAQ;AAAA,EAAA;AAGtB,QAAA,oBAAoB,CAAC,aAAqB;AAC9C,sBAAkB,QAAQ;AAAA,EAAA;AAItB,QAAA,kBAAkB,CAAC,YAAoB,eAAuB;AAClE,sBAAkB,QAAQ;AAC1B,sBAAkB,QAAQ;AAAA,EAAA;AAGrB,SAAA;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ,GAAG;AAAA,EACD,SAAS;AAAA,IACP,KAAK;AAAA,IACL,OAAO,CAAC,qBAAqB,qBAAqB,iBAAiB;AAAA,EACrE;AACF,CAAC;;"}