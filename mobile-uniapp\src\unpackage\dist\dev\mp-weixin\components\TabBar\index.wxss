/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 导入自定义样式变量 */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
.custom-tabbar.data-v-5b533c2d {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, white 100%);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  padding: 0 20rpx 20rpx;
  z-index: 1000;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}
.custom-tabbar .tab-item.data-v-5b533c2d {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 8rpx 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.custom-tabbar .tab-item.data-v-5b533c2d:active {
  transform: scale(0.95);
}
.custom-tabbar .tab-item .tab-icon.data-v-5b533c2d {
  position: relative;
  margin-bottom: 4rpx;
}
.custom-tabbar .tab-item .tab-icon .icon-wrapper.data-v-5b533c2d {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}
.custom-tabbar .tab-item .tab-icon .icon-wrapper.active.data-v-5b533c2d {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
}
.custom-tabbar .tab-item .tab-icon .icon-wrapper .iconfont.data-v-5b533c2d {
  font-size: 24rpx;
  color: #8E8E93;
  transition: all 0.3s ease;
}
.custom-tabbar .tab-item .tab-icon .icon-wrapper.active .iconfont.data-v-5b533c2d {
  color: #FFFFFF;
  font-size: 26rpx;
}
.custom-tabbar .tab-item .tab-icon .icon-wrapper .icon-badge.data-v-5b533c2d {
  position: absolute;
  top: -6rpx;
  right: -8rpx;
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  color: white;
  font-size: 18rpx;
  padding: 2rpx 6rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  height: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);
  animation: pulse-5b533c2d 2s infinite;
}
.custom-tabbar .tab-item .tab-text.data-v-5b533c2d {
  font-size: 20rpx;
  color: #8E8E93;
  font-weight: 500;
  transition: all 0.3s ease;
}
.custom-tabbar .tab-item .tab-text.active.data-v-5b533c2d {
  color: #667eea;
  font-weight: 600;
  transform: scale(1.05);
}
.custom-tabbar .tab-item .tab-indicator.data-v-5b533c2d {
  position: absolute;
  bottom: -2rpx;
  width: 20rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2rpx;
  animation: slideIn-5b533c2d 0.3s ease;
}
@keyframes pulse-5b533c2d {
0%, 100% {
    transform: scale(1);
}
50% {
    transform: scale(1.1);
}
}
@keyframes slideIn-5b533c2d {
from {
    width: 0;
    opacity: 0;
}
to {
    width: 20rpx;
    opacity: 1;
}
}
@media (prefers-color-scheme: dark) {
.custom-tabbar.data-v-5b533c2d {
    background: linear-gradient(180deg, rgba(28, 28, 30, 0.95) 0%, #1c1c1e 100%);
    border-top-color: rgba(255, 255, 255, 0.1);
}
.custom-tabbar .tab-item .tab-icon .icon-wrapper .iconfont.data-v-5b533c2d {
    color: #8E8E93;
}
.custom-tabbar .tab-item .tab-icon .icon-wrapper.active .iconfont.data-v-5b533c2d {
    color: #FFFFFF;
}
.custom-tabbar .tab-item .tab-text.data-v-5b533c2d {
    color: #8E8E93;
}
.custom-tabbar .tab-item .tab-text.active.data-v-5b533c2d {
    color: #667eea;
}
}