{"name": "ai-system-mobile", "version": "1.0.0", "description": "AI系统移动端应用", "main": "index.html", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "serve": "http-server . -p 3000 -o", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.4.21", "pinia": "^2.1.7", "dayjs": "^1.11.10", "lodash-es": "^4.17.21"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "cross-env": "^7.0.3", "http-server": "^14.1.1", "sass": "^1.70.0", "typescript": "^5.3.3", "vite": "^5.1.4", "vue-tsc": "^1.8.27"}, "keywords": ["ai", "mobile", "vue", "typescript"], "author": "AI System Team", "license": "MIT"}