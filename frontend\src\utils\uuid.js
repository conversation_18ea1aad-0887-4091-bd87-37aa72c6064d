/**
 * UUID 生成工具
 * 提供各种格式的唯一标识符生成功能
 */

/**
 * 生成标准的 UUID v4
 * @returns {string} UUID 字符串
 */
export function generateUUID() {
  // 使用 crypto.randomUUID() 如果可用（现代浏览器）
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  
  // 回退到传统方法
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 生成短 UUID（不包含连字符）
 * @returns {string} 短 UUID 字符串
 */
export function generateShortUUID() {
  return generateUUID().replace(/-/g, '');
}

/**
 * 生成指定长度的随机 ID
 * @param {number} length - ID 长度
 * @param {string} chars - 可用字符集
 * @returns {string} 随机 ID
 */
export function generateRandomId(length = 8, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成数字 ID
 * @param {number} length - ID 长度
 * @returns {string} 数字 ID
 */
export function generateNumericId(length = 6) {
  return generateRandomId(length, '0123456789');
}

/**
 * 生成带前缀的 ID
 * @param {string} prefix - 前缀
 * @param {number} length - 随机部分长度
 * @returns {string} 带前缀的 ID
 */
export function generatePrefixedId(prefix, length = 8) {
  return `${prefix}_${generateRandomId(length)}`;
}

/**
 * 生成时间戳 ID
 * @param {string} prefix - 可选前缀
 * @returns {string} 时间戳 ID
 */
export function generateTimestampId(prefix = '') {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return prefix ? `${prefix}_${timestamp}_${random}` : `${timestamp}_${random}`;
}

/**
 * 验证 UUID 格式
 * @param {string} uuid - 要验证的 UUID
 * @returns {boolean} 是否为有效的 UUID
 */
export function isValidUUID(uuid) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * 生成会话 ID
 * @returns {string} 会话 ID
 */
export function generateSessionId() {
  return generatePrefixedId('session', 12);
}

/**
 * 生成消息 ID
 * @returns {string} 消息 ID
 */
export function generateMessageId() {
  return generatePrefixedId('msg', 10);
}

/**
 * 生成任务 ID
 * @returns {string} 任务 ID
 */
export function generateTaskId() {
  return generatePrefixedId('task', 10);
}

/**
 * 生成文件 ID
 * @returns {string} 文件 ID
 */
export function generateFileId() {
  return generatePrefixedId('file', 12);
}
