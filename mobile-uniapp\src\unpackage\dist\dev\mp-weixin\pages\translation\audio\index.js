"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_modules_translation = require("../../../store/modules/translation.js");
if (!Array) {
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  const _component_u_button = common_vendor.resolveComponent("u-button");
  const _component_u_loading_icon = common_vendor.resolveComponent("u-loading-icon");
  const _component_u_radio = common_vendor.resolveComponent("u-radio");
  const _component_u_radio_group = common_vendor.resolveComponent("u-radio-group");
  const _component_u_picker = common_vendor.resolveComponent("u-picker");
  (_component_u_icon + _component_u_button + _component_u_loading_icon + _component_u_radio + _component_u_radio_group + _component_u_picker)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props, { expose: __expose }) {
    const translationStore = store_modules_translation.useTranslationStore();
    const isRecording = common_vendor.ref(false);
    const isProcessing = common_vendor.ref(false);
    const isTranslating = common_vendor.ref(false);
    const isPlayingResult = common_vendor.ref(false);
    const recordingTime = common_vendor.ref(0);
    const waveData = common_vendor.ref(Array(20).fill(20));
    const selectedAudioFile = common_vendor.ref(null);
    const translationResult = common_vendor.ref(null);
    const audioHistory = common_vendor.ref([]);
    const showSourceLanguagePicker = common_vendor.ref(false);
    const showTargetLanguagePicker = common_vendor.ref(false);
    const translationMode = common_vendor.ref("full");
    const outputFormat = common_vendor.ref("text");
    let recordingTimer = null;
    let waveTimer = null;
    const {
      languages,
      currentSourceLang,
      currentTargetLang,
      sourceLanguage,
      targetLanguage
    } = translationStore;
    const canTranslate = common_vendor.computed(() => {
      return selectedAudioFile.value && currentSourceLang.value && currentTargetLang.value;
    });
    const sourceLanguageColumns = common_vendor.computed(() => {
      const autoDetect = [{ text: "自动检测", value: "auto" }];
      const languageOptions = languages.map((lang) => ({
        text: lang.name,
        value: lang.code
      }));
      return [autoDetect.concat(languageOptions)];
    });
    const targetLanguageColumns = common_vendor.computed(() => {
      const languageOptions = languages.filter((lang) => lang.code !== "auto").map((lang) => ({
        text: lang.name,
        value: lang.code
      }));
      return [languageOptions];
    });
    common_vendor.onMounted(async () => {
      await translationStore.loadLanguages();
      loadAudioHistory();
    });
    common_vendor.onUnmounted(() => {
      stopRecording();
      clearTimers();
    });
    const toggleRecording = async () => {
      if (isProcessing.value)
        return;
      if (isRecording.value) {
        await stopRecording();
      } else {
        await startRecording();
      }
    };
    const startRecording = async () => {
      try {
        isRecording.value = true;
        recordingTime.value = 0;
        recordingTimer = setInterval(() => {
          recordingTime.value++;
        }, 1e3);
        startWaveAnimation();
        common_vendor.index.showToast({
          title: "开始录音",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/translation/audio/index.vue:390", "开始录音失败:", error);
        common_vendor.index.showToast({
          title: "录音权限被拒绝",
          icon: "error"
        });
      }
    };
    const stopRecording = async () => {
      isRecording.value = false;
      isProcessing.value = true;
      clearTimers();
      common_vendor.index.showToast({
        title: "录音结束",
        icon: "success"
      });
    };
    const startWaveAnimation = () => {
      waveTimer = setInterval(() => {
        waveData.value = waveData.value.map(
          () => Math.floor(Math.random() * 60) + 20
        );
      }, 100);
    };
    const clearTimers = () => {
      if (recordingTimer) {
        clearInterval(recordingTimer);
        recordingTimer = null;
      }
      if (waveTimer) {
        clearInterval(waveTimer);
        waveTimer = null;
      }
    };
    const chooseAudioFile = () => {
      common_vendor.index.chooseFile({
        count: 1,
        type: "file",
        extension: [".mp3", ".wav", ".m4a", ".aac"],
        success: (res) => {
          const file = res.tempFiles[0];
          handleSelectedFile(file);
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/translation/audio/index.vue:522", "选择文件失败:", error);
        }
      });
    };
    const handleSelectedFile = (file) => {
      if (file.size > 100 * 1024 * 1024) {
        common_vendor.index.showToast({
          title: "文件大小不能超过100MB",
          icon: "error"
        });
        return;
      }
      selectedAudioFile.value = {
        name: file.name,
        size: file.size,
        path: file.path || URL.createObjectURL(file),
        duration: null
        // 这里可以获取音频时长
      };
      getAudioDuration(selectedAudioFile.value.path);
    };
    const getAudioDuration = (audioPath) => {
    };
    const removeAudioFile = () => {
      selectedAudioFile.value = null;
      translationResult.value = null;
    };
    const playAudio = () => {
      if (!selectedAudioFile.value)
        return;
    };
    const translateAudioFile = async () => {
      if (!canTranslate.value)
        return;
      try {
        isTranslating.value = true;
        await new Promise((resolve) => setTimeout(resolve, 3e3));
        const mockResult = {
          type: outputFormat.value === "segment" ? "segments" : outputFormat.value,
          originalText: "这是音频文件中识别的原文内容",
          translatedText: "This is the original text content recognized from the audio file",
          segments: outputFormat.value === "segment" ? [
            {
              startTime: 0,
              endTime: 5,
              originalText: "第一段原文",
              translatedText: "First segment translation"
            },
            {
              startTime: 5,
              endTime: 10,
              originalText: "第二段原文",
              translatedText: "Second segment translation"
            }
          ] : null,
          duration: selectedAudioFile.value.duration,
          audioUrl: outputFormat.value === "audio" ? "mock-audio-url" : null
        };
        translationResult.value = mockResult;
        const historyItem = {
          id: Date.now().toString(),
          type: "file",
          filename: selectedAudioFile.value.name,
          originalText: mockResult.originalText,
          translatedText: mockResult.translatedText,
          sourceLang: currentSourceLang.value,
          targetLang: currentTargetLang.value,
          duration: selectedAudioFile.value.duration,
          createdAt: (/* @__PURE__ */ new Date()).toISOString()
        };
        audioHistory.value.unshift(historyItem);
        saveAudioHistory();
        common_vendor.index.showToast({
          title: "翻译完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/translation/audio/index.vue:641", "翻译失败:", error);
        common_vendor.index.showToast({
          title: "翻译失败",
          icon: "error"
        });
      } finally {
        isTranslating.value = false;
      }
    };
    const swapLanguages = () => {
      translationStore.swapLanguages();
    };
    const onSourceLanguageConfirm = (value) => {
      translationStore.setSourceLanguage(value[0].value);
      showSourceLanguagePicker.value = false;
    };
    const onTargetLanguageConfirm = (value) => {
      translationStore.setTargetLanguage(value[0].value);
      showTargetLanguagePicker.value = false;
    };
    const copyResult = () => {
      if (!translationResult.value)
        return;
      let textToCopy = "";
      if (translationResult.value.type === "text") {
        textToCopy = `原文: ${translationResult.value.originalText}
译文: ${translationResult.value.translatedText}`;
      } else if (translationResult.value.type === "segments") {
        textToCopy = translationResult.value.segments.map(
          (seg) => `${formatTime(seg.startTime)}-${formatTime(seg.endTime)}
${seg.originalText}
${seg.translatedText}`
        ).join("\n\n");
      }
      common_vendor.index.setClipboardData({
        data: textToCopy,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        }
      });
    };
    const shareResult = () => {
      if (!translationResult.value)
        return;
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 0,
        summary: `音频翻译结果: ${translationResult.value.translatedText}`,
        success: () => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        }
      });
    };
    const downloadResult = () => {
      if (!translationResult.value)
        return;
      common_vendor.index.showToast({
        title: "下载功能开发中",
        icon: "none"
      });
    };
    const toggleResultAudio = () => {
      var _a;
      if (!((_a = translationResult.value) == null ? void 0 : _a.audioUrl))
        return;
      if (isPlayingResult.value) {
        common_vendor.index.stopBackgroundAudio();
        isPlayingResult.value = false;
      } else {
        common_vendor.index.playBackgroundAudio({
          dataUrl: translationResult.value.audioUrl,
          title: "翻译语音",
          success: () => {
            isPlayingResult.value = true;
          }
        });
      }
    };
    const formatTime = (seconds) => {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    };
    const formatDuration = (seconds) => {
      if (!seconds)
        return "00:00";
      return formatTime(seconds);
    };
    const formatFileSize = (bytes) => {
      if (bytes === 0)
        return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };
    const saveAudioHistory = () => {
      common_vendor.index.setStorageSync("audio_translation_history", audioHistory.value);
    };
    const loadAudioHistory = () => {
      const history = common_vendor.index.getStorageSync("audio_translation_history") || [];
      audioHistory.value = history;
    };
    const viewAllHistory = () => {
      common_vendor.index.navigateTo({
        url: "/pages/translation/audio/history/index"
      });
    };
    const viewHistoryItem = (item) => {
      translationResult.value = {
        type: "text",
        originalText: item.originalText,
        translatedText: item.translatedText
      };
    };
    const onPullDownRefresh = () => {
      loadAudioHistory();
      common_vendor.index.stopPullDownRefresh();
    };
    __expose({
      onPullDownRefresh
    });
    return (_ctx, _cache) => {
      var _a, _b;
      return common_vendor.e({
        a: common_vendor.p({
          name: "arrow-down",
          size: "12",
          color: "#2563EB",
          ["margin-left"]: "4"
        }),
        b: common_vendor.o(($event) => showSourceLanguagePicker.value = true),
        c: common_vendor.p({
          type: "text",
          text: ((_a = common_vendor.unref(sourceLanguage)) == null ? void 0 : _a.name) || "自动检测",
          ["custom-style"]: {
            color: "#2563EB"
          }
        }),
        d: common_vendor.p({
          name: "arrow-left-right",
          size: "20",
          color: "#6B7280"
        }),
        e: common_vendor.o(swapLanguages),
        f: common_vendor.p({
          name: "arrow-down",
          size: "12",
          color: "#2563EB",
          ["margin-left"]: "4"
        }),
        g: common_vendor.o(($event) => showTargetLanguagePicker.value = true),
        h: common_vendor.p({
          type: "text",
          text: ((_b = common_vendor.unref(targetLanguage)) == null ? void 0 : _b.name) || "中文",
          ["custom-style"]: {
            color: "#2563EB"
          }
        }),
        i: isRecording.value
      }, isRecording.value ? {
        j: common_vendor.f(waveData.value, (height, index, i0) => {
          return {
            a: index,
            b: height + "rpx"
          };
        })
      } : {}, {
        k: common_vendor.p({
          name: isRecording.value ? "stop" : "mic",
          size: "32",
          color: "#fff"
        }),
        l: isRecording.value ? 1 : "",
        m: isProcessing.value ? 1 : "",
        n: common_vendor.o(toggleRecording),
        o: isRecording.value
      }, isRecording.value ? {
        p: common_vendor.t(formatTime(recordingTime.value))
      } : {}, {
        q: isProcessing.value
      }, isProcessing.value ? {
        r: common_vendor.p({
          mode: "flower"
        })
      } : {}, {
        s: !selectedAudioFile.value
      }, !selectedAudioFile.value ? {
        t: common_vendor.p({
          name: "cloud-upload",
          size: "48",
          color: "#2563EB"
        })
      } : common_vendor.e({
        v: common_vendor.p({
          name: "music",
          size: "32",
          color: "#2563EB"
        }),
        w: common_vendor.t(selectedAudioFile.value.name),
        x: common_vendor.t(formatFileSize(selectedAudioFile.value.size)),
        y: selectedAudioFile.value.duration
      }, selectedAudioFile.value.duration ? {
        z: common_vendor.t(formatDuration(selectedAudioFile.value.duration))
      } : {}, {
        A: common_vendor.o(playAudio),
        B: common_vendor.p({
          name: "play-circle",
          size: "24",
          color: "#2563EB"
        }),
        C: common_vendor.o(removeAudioFile),
        D: common_vendor.p({
          name: "close-circle",
          size: "24",
          color: "#EF4444"
        })
      }), {
        E: common_vendor.o(chooseAudioFile),
        F: selectedAudioFile.value
      }, selectedAudioFile.value ? {
        G: common_vendor.p({
          name: "full",
          label: "完整翻译"
        }),
        H: common_vendor.p({
          name: "segment",
          label: "分段翻译"
        }),
        I: common_vendor.o(($event) => translationMode.value = $event),
        J: common_vendor.p({
          modelValue: translationMode.value
        }),
        K: common_vendor.p({
          name: "text",
          label: "纯文本"
        }),
        L: common_vendor.p({
          name: "srt",
          label: "字幕文件"
        }),
        M: common_vendor.p({
          name: "audio",
          label: "语音合成"
        }),
        N: common_vendor.o(($event) => outputFormat.value = $event),
        O: common_vendor.p({
          modelValue: outputFormat.value
        })
      } : {}, {
        P: selectedAudioFile.value
      }, selectedAudioFile.value ? {
        Q: common_vendor.t(isTranslating.value ? "翻译中..." : "开始翻译"),
        R: common_vendor.o(translateAudioFile),
        S: common_vendor.p({
          type: "primary",
          size: "large",
          loading: isTranslating.value,
          disabled: !canTranslate.value,
          ["custom-style"]: {
            width: "100%"
          }
        })
      } : {}, {
        T: translationResult.value
      }, translationResult.value ? common_vendor.e({
        U: common_vendor.o(copyResult),
        V: common_vendor.p({
          name: "copy",
          size: "18",
          color: "#2563EB"
        }),
        W: common_vendor.o(shareResult),
        X: common_vendor.p({
          name: "share",
          size: "18",
          color: "#2563EB",
          ["margin-left"]: "12"
        }),
        Y: common_vendor.o(downloadResult),
        Z: common_vendor.p({
          name: "download",
          size: "18",
          color: "#2563EB",
          ["margin-left"]: "12"
        }),
        aa: translationResult.value.type === "text"
      }, translationResult.value.type === "text" ? {
        ab: common_vendor.t(translationResult.value.originalText),
        ac: common_vendor.t(translationResult.value.translatedText)
      } : {}, {
        ad: translationResult.value.type === "segments"
      }, translationResult.value.type === "segments" ? {
        ae: common_vendor.f(translationResult.value.segments, (segment, index, i0) => {
          return {
            a: common_vendor.t(formatTime(segment.startTime)),
            b: common_vendor.t(formatTime(segment.endTime)),
            c: common_vendor.t(segment.originalText),
            d: common_vendor.t(segment.translatedText),
            e: index
          };
        })
      } : {}, {
        af: translationResult.value.type === "audio"
      }, translationResult.value.type === "audio" ? {
        ag: common_vendor.o(toggleResultAudio),
        ah: common_vendor.p({
          name: isPlayingResult.value ? "pause-circle-fill" : "play-circle-fill",
          size: "48",
          color: "#2563EB"
        }),
        ai: common_vendor.t(formatDuration(translationResult.value.duration))
      } : {}) : {}, {
        aj: audioHistory.value.length > 0
      }, audioHistory.value.length > 0 ? {
        ak: common_vendor.o(viewAllHistory),
        al: common_vendor.p({
          type: "text",
          text: "查看全部",
          size: "small",
          ["custom-style"]: {
            color: "#2563EB",
            fontSize: "24rpx"
          }
        }),
        am: common_vendor.f(audioHistory.value.slice(0, 3), (item, k0, i0) => {
          return {
            a: "5c0b728c-24-" + i0,
            b: common_vendor.t(item.filename || "录音翻译"),
            c: common_vendor.t(item.originalText.substring(0, 50)),
            d: common_vendor.t(formatTime(item.createdAt)),
            e: "5c0b728c-25-" + i0,
            f: item.id,
            g: common_vendor.o(($event) => viewHistoryItem(item), item.id)
          };
        }),
        an: common_vendor.p({
          name: "music",
          size: "20",
          color: "#2563EB"
        }),
        ao: common_vendor.p({
          name: "play-circle",
          size: "16",
          color: "#6B7280"
        })
      } : {}, {
        ap: common_vendor.o(onSourceLanguageConfirm),
        aq: common_vendor.o(($event) => showSourceLanguagePicker.value = false),
        ar: common_vendor.o(($event) => showSourceLanguagePicker.value = $event),
        as: common_vendor.p({
          columns: sourceLanguageColumns.value,
          modelValue: showSourceLanguagePicker.value
        }),
        at: common_vendor.o(onTargetLanguageConfirm),
        av: common_vendor.o(($event) => showTargetLanguagePicker.value = false),
        aw: common_vendor.o(($event) => showTargetLanguagePicker.value = $event),
        ax: common_vendor.p({
          columns: targetLanguageColumns.value,
          modelValue: showTargetLanguagePicker.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5c0b728c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/translation/audio/index.js.map
