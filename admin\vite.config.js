import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import path from 'path'
import { networkInterfaces } from 'os'

// 获取本地 IP 地址的函数
function getLocalIP() {
  const nets = networkInterfaces();
  const results = {};

  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      // 跳过非 IPv4 和内部 IP
      if (net.family === 'IPv4' && !net.internal) {
        if (!results[name]) {
          results[name] = [];
        }
        results[name].push(net.address);
      }
    }
  }

  // 尝试找到第一个有效的 IP
  for (const name in results) {
    if (results[name].length > 0) {
      return results[name][0];
    }
  }

  // 如果找不到，使用 localhost
  return 'localhost';
}

// 获取本地 IP
const localIP = getLocalIP();
console.log(`检测到本地 IP: ${localIP}`);

export default defineConfig({
  base: './',
  plugins: [
    vue(),
    vueJsx() // 简化JSX配置
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        additionalData: '@import "./src/assets/styles/variables.less";'
      }
    }
  },
  optimizeDeps: {
    include: ['echarts', 'ant-design-vue', '@ant-design/icons-vue']
  },
  server: {
    port: 3002,
    open: false,
    host: '0.0.0.0', // 允许从所有网络接口访问
    cors: true,
    hmr: true, // 简化HMR配置，让Vite自动管理连接
    proxy: {
      // 统一处理所有API请求
      '/api': {
        target: 'http://*************:9000',
        changeOrigin: true,
        secure: false,
        ws: true
      }
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    chunkSizeWarningLimit: 1500
  }
}) 