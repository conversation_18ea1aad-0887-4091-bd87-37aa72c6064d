<template>
  <view class="video-translation-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">视频翻译</text>
      <text class="page-desc">支持视频字幕提取、翻译和生成</text>
    </view>

    <!-- 语言选择 -->
    <view class="language-selector">
      <view class="language-item">
        <text class="language-label">源语言</text>
        <u-button 
          type="text" 
          :text="sourceLanguage?.name || '自动检测'" 
          @click="showSourceLanguagePicker = true"
          :custom-style="{ color: '#2563EB' }"
        >
          <u-icon name="arrow-down" size="12" color="#2563EB" margin-left="4"></u-icon>
        </u-button>
      </view>
      
      <view class="swap-button" @click="swapLanguages">
        <u-icon name="arrow-left-right" size="20" color="#6B7280"></u-icon>
      </view>
      
      <view class="language-item">
        <text class="language-label">目标语言</text>
        <u-button 
          type="text" 
          :text="targetLanguage?.name || '中文'" 
          @click="showTargetLanguagePicker = true"
          :custom-style="{ color: '#2563EB' }"
        >
          <u-icon name="arrow-down" size="12" color="#2563EB" margin-left="4"></u-icon>
        </u-button>
      </view>
    </view>

    <!-- 视频上传区域 -->
    <view class="upload-section">
      <view class="section-title">
        <text class="title-text">上传视频文件</text>
        <text class="title-desc">支持MP4、AVI、MOV等格式，最大500MB</text>
      </view>
      
      <view class="upload-area" @click="chooseVideoFile">
        <view class="upload-content" v-if="!selectedVideoFile">
          <u-icon name="video" size="48" color="#2563EB"></u-icon>
          <text class="upload-text">选择视频文件</text>
          <text class="upload-desc">支持 MP4、AVI、MOV、MKV 格式</text>
          <text class="upload-limit">文件大小限制：500MB</text>
        </view>
        
        <view class="file-info" v-else>
          <view class="video-preview">
            <video 
              class="preview-video"
              :src="selectedVideoFile.path"
              :poster="selectedVideoFile.thumbnail"
              controls
              @loadedmetadata="onVideoLoaded"
            ></video>
          </view>
          <view class="file-details">
            <text class="file-name">{{ selectedVideoFile.name }}</text>
            <text class="file-size">{{ formatFileSize(selectedVideoFile.size) }}</text>
            <text class="file-duration" v-if="selectedVideoFile.duration">
              时长: {{ formatDuration(selectedVideoFile.duration) }}
            </text>
            <text class="file-resolution" v-if="selectedVideoFile.resolution">
              分辨率: {{ selectedVideoFile.resolution }}
            </text>
          </view>
          <view class="file-actions">
            <u-icon name="close-circle" size="24" color="#EF4444" @click.stop="removeVideoFile"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 翻译选项 -->
    <view class="translation-options" v-if="selectedVideoFile">
      <view class="section-title">
        <text class="title-text">翻译选项</text>
      </view>
      
      <view class="options-list">
        <!-- 字幕提取方式 -->
        <view class="option-item">
          <text class="option-label">字幕提取方式</text>
          <u-radio-group v-model="extractionMode">
            <u-radio name="auto" label="自动识别"></u-radio>
            <u-radio name="existing" label="现有字幕"></u-radio>
          </u-radio-group>
        </view>
        
        <!-- 输出格式 -->
        <view class="option-item">
          <text class="option-label">输出格式</text>
          <u-radio-group v-model="outputFormat">
            <u-radio name="srt" label="SRT字幕"></u-radio>
            <u-radio name="vtt" label="VTT字幕"></u-radio>
            <u-radio name="ass" label="ASS字幕"></u-radio>
            <u-radio name="video" label="嵌入视频"></u-radio>
          </u-radio-group>
        </view>
        
        <!-- 字幕样式 -->
        <view class="option-item" v-if="outputFormat === 'video'">
          <text class="option-label">字幕样式</text>
          <view class="subtitle-style-options">
            <view class="style-preview">
              <text class="preview-text" :style="subtitleStyle">示例字幕文本</text>
            </view>
            <view class="style-controls">
              <view class="control-item">
                <text class="control-label">字体大小</text>
                <u-slider v-model="subtitleConfig.fontSize" :min="12" :max="32" @change="updateSubtitleStyle"></u-slider>
              </view>
              <view class="control-item">
                <text class="control-label">字体颜色</text>
                <view class="color-picker">
                  <view 
                    class="color-option"
                    v-for="color in colorOptions"
                    :key="color"
                    :style="{ backgroundColor: color }"
                    :class="{ active: subtitleConfig.color === color }"
                    @click="selectColor(color)"
                  ></view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="translate-button">
        <u-button 
          type="primary" 
          size="large"
          :loading="isTranslating"
          :disabled="!canTranslate"
          @click="startTranslation"
          :custom-style="{ width: '100%' }"
        >
          {{ isTranslating ? '处理中...' : '开始翻译' }}
        </u-button>
      </view>
    </view>

    <!-- 翻译进度 -->
    <view class="translation-progress" v-if="isTranslating">
      <view class="progress-header">
        <text class="progress-title">正在处理视频</text>
        <text class="progress-desc">{{ progressStatus }}</text>
      </view>
      
      <view class="progress-bar">
        <u-line-progress 
          :percent="translationProgress" 
          :show-percent="true"
          active-color="#2563EB"
        ></u-line-progress>
      </view>
      
      <view class="progress-steps">
        <view 
          class="step-item"
          v-for="(step, index) in progressSteps"
          :key="index"
          :class="{ 
            active: currentStep === index,
            completed: currentStep > index 
          }"
        >
          <view class="step-icon">
            <u-icon 
              :name="currentStep > index ? 'checkmark' : step.icon" 
              size="16" 
              :color="currentStep >= index ? '#2563EB' : '#9CA3AF'"
            ></u-icon>
          </view>
          <text class="step-text">{{ step.text }}</text>
        </view>
      </view>
    </view>

    <!-- 翻译结果 -->
    <view class="translation-result" v-if="translationResult">
      <view class="section-title">
        <text class="title-text">翻译结果</text>
        <view class="result-actions">
          <u-icon name="download" size="18" color="#2563EB" @click="downloadResult"></u-icon>
          <u-icon name="share" size="18" color="#2563EB" margin-left="12" @click="shareResult"></u-icon>
        </view>
      </view>
      
      <view class="result-content">
        <!-- 视频预览 -->
        <view class="video-result" v-if="translationResult.type === 'video'">
          <video 
            class="result-video"
            :src="translationResult.videoUrl"
            controls
            :poster="translationResult.thumbnail"
          ></video>
          <view class="video-info">
            <text class="video-title">{{ translationResult.filename }}</text>
            <text class="video-size">{{ formatFileSize(translationResult.size) }}</text>
          </view>
        </view>
        
        <!-- 字幕文件 -->
        <view class="subtitle-result" v-if="translationResult.type === 'subtitle'">
          <view class="subtitle-preview">
            <view 
              class="subtitle-item"
              v-for="(subtitle, index) in translationResult.subtitles.slice(0, 5)"
              :key="index"
            >
              <view class="subtitle-time">
                {{ formatTime(subtitle.startTime) }} --> {{ formatTime(subtitle.endTime) }}
              </view>
              <view class="subtitle-original">{{ subtitle.originalText }}</view>
              <view class="subtitle-translated">{{ subtitle.translatedText }}</view>
            </view>
            
            <view class="subtitle-more" v-if="translationResult.subtitles.length > 5">
              <text class="more-text">还有 {{ translationResult.subtitles.length - 5 }} 条字幕...</text>
            </view>
          </view>
          
          <view class="subtitle-download">
            <u-button 
              type="primary" 
              size="small"
              @click="downloadSubtitle"
            >
              下载字幕文件
            </u-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 翻译历史 -->
    <view class="translation-history" v-if="videoHistory.length > 0">
      <view class="section-title">
        <text class="title-text">翻译历史</text>
        <u-button 
          type="text" 
          text="查看全部" 
          size="small"
          @click="viewAllHistory"
          :custom-style="{ color: '#2563EB', fontSize: '24rpx' }"
        ></u-button>
      </view>
      
      <view class="history-list">
        <view 
          class="history-item"
          v-for="item in videoHistory.slice(0, 3)"
          :key="item.id"
          @click="viewHistoryItem(item)"
        >
          <view class="history-thumbnail">
            <image class="thumbnail-image" :src="item.thumbnail" mode="aspectFill"></image>
            <view class="play-overlay">
              <u-icon name="play" size="16" color="#fff"></u-icon>
            </view>
          </view>
          <view class="history-content">
            <text class="history-title">{{ item.filename }}</text>
            <text class="history-desc">{{ item.sourceLang }} → {{ item.targetLang }}</text>
            <text class="history-time">{{ formatTime(item.createdAt) }}</text>
          </view>
          <view class="history-actions">
            <u-icon name="download" size="16" color="#6B7280"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 语言选择器 -->
    <u-picker
      v-model="showSourceLanguagePicker"
      :columns="sourceLanguageColumns"
      @confirm="onSourceLanguageConfirm"
      @cancel="showSourceLanguagePicker = false"
    ></u-picker>

    <u-picker
      v-model="showTargetLanguagePicker"
      :columns="targetLanguageColumns"
      @confirm="onTargetLanguageConfirm"
      @cancel="showTargetLanguagePicker = false"
    ></u-picker>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useTranslationStore } from '@/store/modules/translation'
import dayjs from 'dayjs'

// Store
const translationStore = useTranslationStore()

// 响应式数据
const selectedVideoFile = ref<any>(null)
const isTranslating = ref<boolean>(false)
const translationProgress = ref<number>(0)
const progressStatus = ref<string>('')
const currentStep = ref<number>(0)
const translationResult = ref<any>(null)
const videoHistory = ref<any[]>([])
const showSourceLanguagePicker = ref<boolean>(false)
const showTargetLanguagePicker = ref<boolean>(false)
const extractionMode = ref<string>('auto')
const outputFormat = ref<string>('srt')
const subtitleConfig = ref({
  fontSize: 18,
  color: '#FFFFFF',
  backgroundColor: 'rgba(0,0,0,0.7)',
  position: 'bottom'
})

// 进度步骤
const progressSteps = ref([
  { icon: 'video', text: '视频解析' },
  { icon: 'mic', text: '语音识别' },
  { icon: 'translate', text: '文本翻译' },
  { icon: 'subtitles', text: '字幕生成' },
  { icon: 'checkmark', text: '完成处理' }
])

// 颜色选项
const colorOptions = ref([
  '#FFFFFF', '#000000', '#FF0000', '#00FF00',
  '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'
])

// 计算属性
const {
  languages,
  currentSourceLang,
  currentTargetLang,
  sourceLanguage,
  targetLanguage
} = translationStore

const canTranslate = computed(() => {
  return selectedVideoFile.value && currentSourceLang.value && currentTargetLang.value
})

const sourceLanguageColumns = computed(() => {
  const autoDetect = [{ text: '自动检测', value: 'auto' }]
  const languageOptions = languages.map(lang => ({
    text: lang.name,
    value: lang.code
  }))
  return [autoDetect.concat(languageOptions)]
})

const targetLanguageColumns = computed(() => {
  const languageOptions = languages.filter(lang => lang.code !== 'auto').map(lang => ({
    text: lang.name,
    value: lang.code
  }))
  return [languageOptions]
})

const subtitleStyle = computed(() => {
  return {
    fontSize: subtitleConfig.value.fontSize + 'px',
    color: subtitleConfig.value.color,
    backgroundColor: subtitleConfig.value.backgroundColor,
    padding: '8px 12px',
    borderRadius: '4px',
    display: 'inline-block'
  }
})

// 页面加载
onMounted(async () => {
  await translationStore.loadLanguages()
  loadVideoHistory()
})

// 选择视频文件
const chooseVideoFile = () => {
  // #ifdef H5
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'video/*'
  input.onchange = (e: any) => {
    const file = e.target.files[0]
    if (file) {
      handleSelectedFile(file)
    }
  }
  input.click()
  // #endif

  // #ifdef APP-PLUS || MP
  uni.chooseVideo({
    sourceType: ['album', 'camera'],
    maxDuration: 300, // 5分钟
    success: (res) => {
      handleSelectedFile({
        name: res.tempFilePath.split('/').pop(),
        size: res.size,
        path: res.tempFilePath,
        duration: res.duration,
        width: res.width,
        height: res.height
      })
    },
    fail: (error) => {
      console.error('选择视频失败:', error)
    }
  })
  // #endif
}

// 处理选中的文件
const handleSelectedFile = (file: any) => {
  // 检查文件大小
  if (file.size > 500 * 1024 * 1024) {
    uni.showToast({
      title: '文件大小不能超过500MB',
      icon: 'error'
    })
    return
  }

  selectedVideoFile.value = {
    name: file.name,
    size: file.size,
    path: file.path || URL.createObjectURL(file),
    duration: file.duration,
    resolution: file.width && file.height ? `${file.width}x${file.height}` : null,
    thumbnail: null
  }

  // 生成缩略图
  generateThumbnail(selectedVideoFile.value.path)
}

// 生成视频缩略图
const generateThumbnail = (videoPath: string) => {
  // #ifdef H5
  const video = document.createElement('video')
  video.src = videoPath
  video.currentTime = 1 // 获取第1秒的帧

  video.onloadeddata = () => {
    const canvas = document.createElement('canvas')
    canvas.width = video.videoWidth
    canvas.height = video.videoHeight

    const ctx = canvas.getContext('2d')
    ctx?.drawImage(video, 0, 0)

    selectedVideoFile.value.thumbnail = canvas.toDataURL('image/jpeg', 0.8)
  }
  // #endif
}

// 视频加载完成
const onVideoLoaded = (e: any) => {
  if (selectedVideoFile.value && !selectedVideoFile.value.duration) {
    selectedVideoFile.value.duration = e.target.duration
  }
}

// 移除视频文件
const removeVideoFile = () => {
  selectedVideoFile.value = null
  translationResult.value = null
}

// 开始翻译
const startTranslation = async () => {
  if (!canTranslate.value) return

  try {
    isTranslating.value = true
    translationProgress.value = 0
    currentStep.value = 0

    // 模拟翻译进度
    const steps = [
      { status: '正在解析视频文件...', duration: 2000 },
      { status: '正在识别语音内容...', duration: 3000 },
      { status: '正在翻译文本...', duration: 2000 },
      { status: '正在生成字幕...', duration: 2000 },
      { status: '正在合成视频...', duration: 3000 }
    ]

    for (let i = 0; i < steps.length; i++) {
      currentStep.value = i
      progressStatus.value = steps[i].status

      // 模拟进度更新
      const stepProgress = 100 / steps.length
      const startProgress = i * stepProgress

      await new Promise(resolve => {
        const interval = setInterval(() => {
          translationProgress.value += 2
          if (translationProgress.value >= (i + 1) * stepProgress) {
            clearInterval(interval)
            resolve(true)
          }
        }, steps[i].duration / 50)
      })
    }

    // 生成模拟结果
    const mockResult = outputFormat.value === 'video' ? {
      type: 'video',
      filename: `translated_${selectedVideoFile.value.name}`,
      videoUrl: selectedVideoFile.value.path,
      thumbnail: selectedVideoFile.value.thumbnail,
      size: selectedVideoFile.value.size * 1.2,
      duration: selectedVideoFile.value.duration
    } : {
      type: 'subtitle',
      filename: `${selectedVideoFile.value.name.split('.')[0]}.${outputFormat.value}`,
      subtitles: [
        {
          startTime: 0,
          endTime: 3,
          originalText: '这是第一句原文',
          translatedText: 'This is the first original sentence'
        },
        {
          startTime: 3,
          endTime: 6,
          originalText: '这是第二句原文',
          translatedText: 'This is the second original sentence'
        },
        {
          startTime: 6,
          endTime: 9,
          originalText: '这是第三句原文',
          translatedText: 'This is the third original sentence'
        }
      ]
    }

    translationResult.value = mockResult

    // 添加到历史记录
    const historyItem = {
      id: Date.now().toString(),
      filename: selectedVideoFile.value.name,
      thumbnail: selectedVideoFile.value.thumbnail,
      sourceLang: currentSourceLang.value,
      targetLang: currentTargetLang.value,
      outputFormat: outputFormat.value,
      size: selectedVideoFile.value.size,
      duration: selectedVideoFile.value.duration,
      createdAt: new Date().toISOString()
    }

    videoHistory.value.unshift(historyItem)
    saveVideoHistory()

    uni.showToast({
      title: '翻译完成',
      icon: 'success'
    })

  } catch (error) {
    console.error('翻译失败:', error)
    uni.showToast({
      title: '翻译失败',
      icon: 'error'
    })
  } finally {
    isTranslating.value = false
    currentStep.value = progressSteps.value.length
  }
}

// 交换语言
const swapLanguages = () => {
  translationStore.swapLanguages()
}

// 语言选择确认
const onSourceLanguageConfirm = (value: any) => {
  translationStore.setSourceLanguage(value[0].value)
  showSourceLanguagePicker.value = false
}

const onTargetLanguageConfirm = (value: any) => {
  translationStore.setTargetLanguage(value[0].value)
  showTargetLanguagePicker.value = false
}

// 更新字幕样式
const updateSubtitleStyle = () => {
  // 字幕样式更新逻辑
}

// 选择颜色
const selectColor = (color: string) => {
  subtitleConfig.value.color = color
  updateSubtitleStyle()
}

// 下载结果
const downloadResult = () => {
  if (!translationResult.value) return

  uni.showToast({
    title: '开始下载',
    icon: 'success'
  })
}

// 分享结果
const shareResult = () => {
  if (!translationResult.value) return

  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 2,
    imageUrl: translationResult.value.thumbnail,
    title: '视频翻译完成',
    summary: `已完成视频翻译: ${translationResult.value.filename}`,
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    }
  })
}

// 下载字幕
const downloadSubtitle = () => {
  if (!translationResult.value?.subtitles) return

  // 生成字幕文件内容
  let subtitleContent = ''

  if (outputFormat.value === 'srt') {
    subtitleContent = translationResult.value.subtitles.map((sub: any, index: number) => {
      return `${index + 1}\n${formatSRTTime(sub.startTime)} --> ${formatSRTTime(sub.endTime)}\n${sub.translatedText}\n`
    }).join('\n')
  }

  // 下载文件
  uni.showToast({
    title: '字幕文件已生成',
    icon: 'success'
  })
}

// 工具函数
const formatTime = (seconds: number) => {
  if (typeof seconds === 'string') {
    return dayjs(seconds).format('MM-DD HH:mm')
  }

  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const formatDuration = (seconds: number) => {
  if (!seconds) return '00:00'
  return formatTime(seconds)
}

const formatSRTTime = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const mins = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)

  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 历史记录相关
const saveVideoHistory = () => {
  uni.setStorageSync('video_translation_history', videoHistory.value)
}

const loadVideoHistory = () => {
  const history = uni.getStorageSync('video_translation_history') || []
  videoHistory.value = history
}

const viewAllHistory = () => {
  uni.navigateTo({
    url: '/pages/translation/video/history/index'
  })
}

const viewHistoryItem = (item: any) => {
  uni.navigateTo({
    url: `/pages/translation/video/detail/index?id=${item.id}`
  })
}

// 页面下拉刷新
const onPullDownRefresh = () => {
  loadVideoHistory()
  uni.stopPullDownRefresh()
}

// 导出方法供页面使用
defineExpose({
  onPullDownRefresh
})
</script>

<style lang="scss" scoped>
.video-translation-page {
  min-height: 100vh;
  background-color: $bg-secondary;
}

// 页面头部
.page-header {
  background-color: $bg-primary;
  padding: $spacing-6 $spacing-4;
  text-align: center;
  border-bottom: 1px solid $border-primary;

  .page-title {
    display: block;
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: $spacing-2;
  }

  .page-desc {
    font-size: $font-size-base;
    color: $text-secondary;
  }
}

// 语言选择器
.language-selector {
  background-color: $bg-primary;
  padding: $spacing-4;
  border-bottom: 1px solid $border-primary;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .language-item {
    flex: 1;
    text-align: center;

    .language-label {
      display: block;
      font-size: $font-size-sm;
      color: $text-tertiary;
      margin-bottom: $spacing-2;
    }
  }

  .swap-button {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $bg-tertiary;
    border-radius: $radius-full;
    margin: 0 $spacing-4;

    &:active {
      background-color: $gray-200;
    }
  }
}

// 通用区块标题
.section-title {
  padding: $spacing-4 $spacing-4 $spacing-3;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title-text {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
  }

  .title-desc {
    font-size: $font-size-sm;
    color: $text-tertiary;
  }

  .result-actions {
    display: flex;
    align-items: center;
    gap: $spacing-2;
  }
}

// 文件上传区域
.upload-section {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .upload-area {
    margin: 0 $spacing-4 $spacing-4;
    border: 2px dashed $border-primary;
    border-radius: $radius-lg;
    padding: $spacing-6;

    .upload-content {
      text-align: center;

      .upload-text {
        display: block;
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin: $spacing-4 0 $spacing-2;
      }

      .upload-desc {
        display: block;
        font-size: $font-size-base;
        color: $text-secondary;
        margin-bottom: $spacing-2;
      }

      .upload-limit {
        font-size: $font-size-sm;
        color: $text-tertiary;
      }
    }

    .file-info {
      display: flex;
      flex-direction: column;
      gap: $spacing-3;

      .video-preview {
        .preview-video {
          width: 100%;
          height: 400rpx;
          border-radius: $radius-lg;
          background-color: $bg-tertiary;
        }
      }

      .file-details {
        flex: 1;

        .file-name {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-size,
        .file-duration,
        .file-resolution {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: 4rpx;
        }
      }

      .file-actions {
        display: flex;
        justify-content: flex-end;
        gap: $spacing-2;
      }
    }
  }
}

// 翻译选项
.translation-options {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .options-list {
    padding: 0 $spacing-4;

    .option-item {
      padding: $spacing-4 0;
      border-bottom: 1px solid $border-primary;

      &:last-child {
        border-bottom: none;
      }

      .option-label {
        display: block;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-3;
      }

      .subtitle-style-options {
        .style-preview {
          background-color: $bg-tertiary;
          border-radius: $radius-md;
          padding: $spacing-4;
          margin-bottom: $spacing-3;
          text-align: center;

          .preview-text {
            font-weight: $font-weight-medium;
          }
        }

        .style-controls {
          .control-item {
            margin-bottom: $spacing-3;

            .control-label {
              display: block;
              font-size: $font-size-sm;
              color: $text-secondary;
              margin-bottom: $spacing-2;
            }

            .color-picker {
              display: flex;
              gap: $spacing-2;

              .color-option {
                width: 60rpx;
                height: 60rpx;
                border-radius: $radius-md;
                border: 2px solid transparent;

                &.active {
                  border-color: $primary;
                }
              }
            }
          }
        }
      }
    }
  }

  .translate-button {
    padding: 0 $spacing-4 $spacing-4;
  }
}

// 翻译进度
.translation-progress {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;
  padding: $spacing-4;

  .progress-header {
    text-align: center;
    margin-bottom: $spacing-4;

    .progress-title {
      display: block;
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-primary;
      margin-bottom: $spacing-1;
    }

    .progress-desc {
      font-size: $font-size-sm;
      color: $text-secondary;
    }
  }

  .progress-bar {
    margin-bottom: $spacing-6;
  }

  .progress-steps {
    display: flex;
    justify-content: space-between;

    .step-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;

      .step-icon {
        width: 64rpx;
        height: 64rpx;
        border-radius: $radius-full;
        background-color: $bg-tertiary;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: $spacing-2;

        &.active,
        &.completed {
          background-color: $primary-50;
        }
      }

      .step-text {
        font-size: $font-size-xs;
        color: $text-tertiary;
        text-align: center;

        &.active {
          color: $text-primary;
          font-weight: $font-weight-medium;
        }
      }
    }
  }
}

// 翻译结果
.translation-result {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .result-content {
    padding: 0 $spacing-4 $spacing-4;

    .video-result {
      .result-video {
        width: 100%;
        height: 400rpx;
        border-radius: $radius-lg;
        background-color: $bg-tertiary;
        margin-bottom: $spacing-3;
      }

      .video-info {
        text-align: center;

        .video-title {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-1;
        }

        .video-size {
          font-size: $font-size-sm;
          color: $text-secondary;
        }
      }
    }

    .subtitle-result {
      .subtitle-preview {
        background-color: $bg-tertiary;
        border-radius: $radius-lg;
        padding: $spacing-4;
        margin-bottom: $spacing-4;
        max-height: 600rpx;
        overflow-y: auto;

        .subtitle-item {
          margin-bottom: $spacing-4;

          &:last-child {
            margin-bottom: 0;
          }

          .subtitle-time {
            font-size: $font-size-xs;
            color: $text-tertiary;
            margin-bottom: $spacing-1;
          }

          .subtitle-original {
            font-size: $font-size-sm;
            color: $text-secondary;
            margin-bottom: $spacing-1;
          }

          .subtitle-translated {
            font-size: $font-size-base;
            color: $text-primary;
            font-weight: $font-weight-medium;
          }
        }

        .subtitle-more {
          text-align: center;
          padding: $spacing-3;

          .more-text {
            font-size: $font-size-sm;
            color: $text-tertiary;
          }
        }
      }

      .subtitle-download {
        text-align: center;
      }
    }
  }
}

// 翻译历史
.translation-history {
  background-color: $bg-primary;

  .history-list {
    padding: 0 $spacing-4 $spacing-4;

    .history-item {
      display: flex;
      align-items: center;
      gap: $spacing-3;
      padding: $spacing-3;
      background-color: $bg-tertiary;
      border-radius: $radius-lg;
      margin-bottom: $spacing-2;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        background-color: $gray-200;
      }

      .history-thumbnail {
        position: relative;
        width: 120rpx;
        height: 80rpx;
        border-radius: $radius-md;
        overflow: hidden;

        .thumbnail-image {
          width: 100%;
          height: 100%;
          background-color: $bg-quaternary;
        }

        .play-overlay {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 48rpx;
          height: 48rpx;
          background-color: rgba(0, 0, 0, 0.6);
          border-radius: $radius-full;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .history-content {
        flex: 1;

        .history-title {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .history-desc {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-1;
        }

        .history-time {
          font-size: $font-size-xs;
          color: $text-tertiary;
        }
      }

      .history-actions {
        padding: $spacing-2;
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 480px) {
  .language-selector {
    flex-direction: column;
    gap: $spacing-3;

    .swap-button {
      transform: rotate(90deg);
    }
  }

  .progress-steps {
    flex-wrap: wrap;
    gap: $spacing-3;

    .step-item {
      flex: 0 0 calc(50% - #{$spacing-3});
    }
  }

  .upload-area .file-info {
    .video-preview .preview-video {
      height: 300rpx;
    }
  }

  .result-content .video-result .result-video {
    height: 300rpx;
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .video-translation-page {
    background-color: #1a1a1a;
  }

  .page-header,
  .language-selector,
  .upload-section,
  .translation-options,
  .translation-progress,
  .translation-result,
  .translation-history {
    background-color: #2d2d2d;
    border-color: #404040;
  }

  .upload-area,
  .style-preview,
  .subtitle-preview,
  .history-item {
    background-color: #404040;
    border-color: #555555;
  }

  .swap-button,
  .step-icon {
    background-color: #404040;
  }

  .preview-video,
  .result-video {
    background-color: #555555;
  }

  .thumbnail-image {
    background-color: #666666;
  }
}
</style>
