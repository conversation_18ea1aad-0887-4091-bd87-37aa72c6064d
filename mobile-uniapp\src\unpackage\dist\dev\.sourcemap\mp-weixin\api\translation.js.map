{"version": 3, "file": "translation.js", "sources": ["api/translation.ts"], "sourcesContent": ["import { http } from '@/utils/request'\n\n// 翻译相关接口\nexport interface TranslationRequest {\n  text: string\n  source_lang: string\n  target_lang: string\n  domain?: string\n  style?: string\n}\n\nexport interface TranslationResponse {\n  translated_text: string\n  source_lang: string\n  target_lang: string\n  confidence: number\n  word_count: number\n  char_count: number\n}\n\nexport interface LanguageInfo {\n  code: string\n  name: string\n  native_name: string\n  flag?: string\n}\n\nexport interface TranslationHistory {\n  id: string\n  source_text: string\n  translated_text: string\n  source_lang: string\n  target_lang: string\n  created_at: string\n  word_count: number\n}\n\nexport const translationApi = {\n  // 获取支持的语言列表\n  getLanguages: () => {\n    return http.get<LanguageInfo[]>('/translation/languages')\n  },\n\n  // 文本翻译\n  translateText: (data: TranslationRequest) => {\n    return http.post<TranslationResponse>('/translation/text', data)\n  },\n\n  // 语言检测\n  detectLanguage: (text: string) => {\n    return http.post<{ language: string; confidence: number }>('/translation/detect', { text })\n  },\n\n  // 获取翻译历史\n  getHistory: (params?: { page?: number; limit?: number }) => {\n    return http.get<{\n      items: TranslationHistory[]\n      total: number\n      page: number\n      limit: number\n    }>('/translation/history', params)\n  },\n\n  // 删除翻译历史\n  deleteHistory: (id: string) => {\n    return http.delete(`/translation/history/${id}`)\n  },\n\n  // 清空翻译历史\n  clearHistory: () => {\n    return http.delete('/translation/history/clear')\n  },\n\n  // 收藏翻译\n  favoriteTranslation: (id: string) => {\n    return http.post(`/translation/favorite/${id}`)\n  },\n\n  // 取消收藏\n  unfavoriteTranslation: (id: string) => {\n    return http.delete(`/translation/favorite/${id}`)\n  },\n\n  // 获取收藏的翻译\n  getFavorites: (params?: { page?: number; limit?: number }) => {\n    return http.get<{\n      items: TranslationHistory[]\n      total: number\n      page: number\n      limit: number\n    }>('/translation/favorites', params)\n  },\n\n  // 文档翻译\n  translateDocument: (filePath: string, sourceLang: string, targetLang: string) => {\n    return new Promise((resolve, reject) => {\n      uni.uploadFile({\n        url: '/api/translation/document',\n        filePath,\n        name: 'file',\n        formData: {\n          source_lang: sourceLang,\n          target_lang: targetLang\n        },\n        success: (res) => {\n          try {\n            const data = JSON.parse(res.data)\n            if (data.success) {\n              resolve(data)\n            } else {\n              reject(data)\n            }\n          } catch (error) {\n            reject(error)\n          }\n        },\n        fail: reject\n      })\n    })\n  },\n\n  // 音频翻译\n  translateAudio: (filePath: string, sourceLang: string, targetLang: string) => {\n    return new Promise((resolve, reject) => {\n      uni.uploadFile({\n        url: '/api/translation/audio',\n        filePath,\n        name: 'audio',\n        formData: {\n          source_lang: sourceLang,\n          target_lang: targetLang\n        },\n        success: (res) => {\n          try {\n            const data = JSON.parse(res.data)\n            if (data.success) {\n              resolve(data)\n            } else {\n              reject(data)\n            }\n          } catch (error) {\n            reject(error)\n          }\n        },\n        fail: reject\n      })\n    })\n  },\n\n  // 视频翻译\n  translateVideo: (filePath: string, sourceLang: string, targetLang: string) => {\n    return new Promise((resolve, reject) => {\n      uni.uploadFile({\n        url: '/api/translation/video',\n        filePath,\n        name: 'video',\n        formData: {\n          source_lang: sourceLang,\n          target_lang: targetLang\n        },\n        success: (res) => {\n          try {\n            const data = JSON.parse(res.data)\n            if (data.success) {\n              resolve(data)\n            } else {\n              reject(data)\n            }\n          } catch (error) {\n            reject(error)\n          }\n        },\n        fail: reject\n      })\n    })\n  },\n\n  // 获取翻译任务状态\n  getTaskStatus: (taskId: string) => {\n    return http.get(`/translation/task/${taskId}`)\n  },\n\n  // 下载翻译结果\n  downloadResult: (taskId: string) => {\n    return http.get(`/translation/download/${taskId}`, {}, { responseType: 'blob' })\n  }\n}\n"], "names": ["http", "uni"], "mappings": ";;;AAqCO,MAAM,iBAAiB;AAAA;AAAA,EAE5B,cAAc,MAAM;AACX,WAAAA,cAAA,KAAK,IAAoB,wBAAwB;AAAA,EAC1D;AAAA;AAAA,EAGA,eAAe,CAAC,SAA6B;AACpC,WAAAA,mBAAK,KAA0B,qBAAqB,IAAI;AAAA,EACjE;AAAA;AAAA,EAGA,gBAAgB,CAAC,SAAiB;AAChC,WAAOA,cAAK,KAAA,KAA+C,uBAAuB,EAAE,KAAM,CAAA;AAAA,EAC5F;AAAA;AAAA,EAGA,YAAY,CAAC,WAA+C;AACnD,WAAAA,mBAAK,IAKT,wBAAwB,MAAM;AAAA,EACnC;AAAA;AAAA,EAGA,eAAe,CAAC,OAAe;AAC7B,WAAOA,cAAAA,KAAK,OAAO,wBAAwB,EAAE,EAAE;AAAA,EACjD;AAAA;AAAA,EAGA,cAAc,MAAM;AACX,WAAAA,cAAA,KAAK,OAAO,4BAA4B;AAAA,EACjD;AAAA;AAAA,EAGA,qBAAqB,CAAC,OAAe;AACnC,WAAOA,cAAAA,KAAK,KAAK,yBAAyB,EAAE,EAAE;AAAA,EAChD;AAAA;AAAA,EAGA,uBAAuB,CAAC,OAAe;AACrC,WAAOA,cAAAA,KAAK,OAAO,yBAAyB,EAAE,EAAE;AAAA,EAClD;AAAA;AAAA,EAGA,cAAc,CAAC,WAA+C;AACrD,WAAAA,mBAAK,IAKT,0BAA0B,MAAM;AAAA,EACrC;AAAA;AAAA,EAGA,mBAAmB,CAAC,UAAkB,YAAoB,eAAuB;AAC/E,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCC,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL;AAAA,QACA,MAAM;AAAA,QACN,UAAU;AAAA,UACR,aAAa;AAAA,UACb,aAAa;AAAA,QACf;AAAA,QACA,SAAS,CAAC,QAAQ;AACZ,cAAA;AACF,kBAAM,OAAO,KAAK,MAAM,IAAI,IAAI;AAChC,gBAAI,KAAK,SAAS;AAChB,sBAAQ,IAAI;AAAA,YAAA,OACP;AACL,qBAAO,IAAI;AAAA,YACb;AAAA,mBACO,OAAO;AACd,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QACA,MAAM;AAAA,MAAA,CACP;AAAA,IAAA,CACF;AAAA,EACH;AAAA;AAAA,EAGA,gBAAgB,CAAC,UAAkB,YAAoB,eAAuB;AAC5E,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL;AAAA,QACA,MAAM;AAAA,QACN,UAAU;AAAA,UACR,aAAa;AAAA,UACb,aAAa;AAAA,QACf;AAAA,QACA,SAAS,CAAC,QAAQ;AACZ,cAAA;AACF,kBAAM,OAAO,KAAK,MAAM,IAAI,IAAI;AAChC,gBAAI,KAAK,SAAS;AAChB,sBAAQ,IAAI;AAAA,YAAA,OACP;AACL,qBAAO,IAAI;AAAA,YACb;AAAA,mBACO,OAAO;AACd,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QACA,MAAM;AAAA,MAAA,CACP;AAAA,IAAA,CACF;AAAA,EACH;AAAA;AAAA,EAGA,gBAAgB,CAAC,UAAkB,YAAoB,eAAuB;AAC5E,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL;AAAA,QACA,MAAM;AAAA,QACN,UAAU;AAAA,UACR,aAAa;AAAA,UACb,aAAa;AAAA,QACf;AAAA,QACA,SAAS,CAAC,QAAQ;AACZ,cAAA;AACF,kBAAM,OAAO,KAAK,MAAM,IAAI,IAAI;AAChC,gBAAI,KAAK,SAAS;AAChB,sBAAQ,IAAI;AAAA,YAAA,OACP;AACL,qBAAO,IAAI;AAAA,YACb;AAAA,mBACO,OAAO;AACd,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QACA,MAAM;AAAA,MAAA,CACP;AAAA,IAAA,CACF;AAAA,EACH;AAAA;AAAA,EAGA,eAAe,CAAC,WAAmB;AACjC,WAAOD,cAAAA,KAAK,IAAI,qBAAqB,MAAM,EAAE;AAAA,EAC/C;AAAA;AAAA,EAGA,gBAAgB,CAAC,WAAmB;AAC3B,WAAAA,mBAAK,IAAI,yBAAyB,MAAM,IAAI,CAAA,GAAI,EAAE,cAAc,OAAA,CAAQ;AAAA,EACjF;AACF;;"}