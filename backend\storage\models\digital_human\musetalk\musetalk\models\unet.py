import torch
import torch.nn as nn
import math
import json

from diffusers import UNet2DConditionModel
import sys
import time
import numpy as np
import os

class PositionalEncoding(nn.Module):
    def __init__(self, d_model=384, max_len=5000):
        super(PositionalEncoding, self).__init__()
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

    def forward(self, x):
        b, seq_len, d_model = x.size()
        pe = self.pe[:, :seq_len, :]
        x = x + pe.to(x.device)
        return x
    
class UNet():
    def __init__(self, 
                 unet_config,
                 model_path,
                 use_float16=False,
                 device=None
        ):
        with open(unet_config, 'r') as f:
            unet_config = json.load(f)

        # 使用与预训练模型匹配的参数
        supported_params = {
            'sample_size': 64,
            'in_channels': 8,  # 匹配预训练模型的conv_in.weight [320, 8, 3, 3]
            'out_channels': 4,  # 匹配预训练模型的conv_out.weight [4, 320, 3, 3]
            'layers_per_block': 2,
            'block_out_channels': [320, 640, 1280, 1280],
            'down_block_types': [
                "CrossAttnDownBlock2D",
                "CrossAttnDownBlock2D",
                "CrossAttnDownBlock2D",
                "DownBlock2D"
            ],
            'up_block_types': [
                "UpBlock2D",
                "CrossAttnUpBlock2D",
                "CrossAttnUpBlock2D",
                "CrossAttnUpBlock2D"
            ],
            'cross_attention_dim': 384,  # 匹配预训练模型的attention维度
            'attention_head_dim': 8
        }

        self.model = UNet2DConditionModel(**supported_params)
        self.pe = PositionalEncoding(d_model=384)
        if device != None:
            self.device = device
        else:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # 加载权重到CPU先，节省GPU内存
        print("Loading UNet weights to CPU first...")
        weights = torch.load(model_path, map_location="cpu")
        self.model.load_state_dict(weights)

        # 清理权重变量
        del weights
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # 使用半精度以节省内存
        if use_float16:
            self.model = self.model.half()
            print("Using half precision (float16)")

        # 移动到GPU
        print(f"Moving UNet model to {self.device}...")
        self.model.to(self.device)

        # 启用内存优化
        if torch.cuda.is_available():
            try:
                # 启用内存高效的注意力机制
                if hasattr(self.model, 'enable_xformers_memory_efficient_attention'):
                    self.model.enable_xformers_memory_efficient_attention()
                    print("Enabled xformers memory efficient attention")
            except Exception as e:
                print(f"Could not enable xformers: {e}")

            # 最终清理
            torch.cuda.empty_cache()
            print(f"GPU memory after loading: {torch.cuda.memory_allocated()/1024**3:.2f}GB / {torch.cuda.max_memory_allocated()/1024**3:.2f}GB")
    
if __name__ == "__main__":
    unet = UNet()
