/**
 * SVG转PNG脚本
 * 使用Node.js和sharp库将SVG图标转换为PNG格式
 */

const fs = require('fs');
const path = require('path');

// 如果您有sharp库，可以使用以下代码转换
// npm install sharp

/*
const sharp = require('sharp');

const svgFiles = [
  'home.svg',
  'home-active.svg',
  'translate.svg',
  'translate-active.svg',
  'chat.svg',
  'chat-active.svg',
  'market.svg',
  'market-active.svg',
  'user.svg',
  'user-active.svg'
];

async function convertSvgToPng() {
  for (const svgFile of svgFiles) {
    const svgPath = path.join(__dirname, svgFile);
    const pngPath = path.join(__dirname, svgFile.replace('.svg', '.png'));
    
    try {
      await sharp(svgPath)
        .resize(40, 40)
        .png()
        .toFile(pngPath);
      
      console.log(`✅ 转换成功: ${svgFile} -> ${svgFile.replace('.svg', '.png')}`);
    } catch (error) {
      console.error(`❌ 转换失败: ${svgFile}`, error);
    }
  }
}

convertSvgToPng();
*/

console.log('SVG转PNG转换脚本');
console.log('请安装sharp库: npm install sharp');
console.log('然后取消注释上面的代码并运行: node convert-to-png.js');
