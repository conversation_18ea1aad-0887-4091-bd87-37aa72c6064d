"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  const _component_u_radio = common_vendor.resolveComponent("u-radio");
  const _component_u_radio_group = common_vendor.resolveComponent("u-radio-group");
  const _component_u_slider = common_vendor.resolveComponent("u-slider");
  const _component_u_switch = common_vendor.resolveComponent("u-switch");
  const _component_u_button = common_vendor.resolveComponent("u-button");
  (_component_u_icon + _component_u_radio + _component_u_radio_group + _component_u_slider + _component_u_switch + _component_u_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props, { expose: __expose }) {
    const selectedType = common_vendor.ref("text");
    const isGenerating = common_vendor.ref(false);
    const generatedQRCode = common_vendor.ref("");
    const qrcodeHistory = common_vendor.ref([]);
    const qrcodeContent = common_vendor.ref({
      text: "",
      url: "",
      wifi: {
        ssid: "",
        password: "",
        security: "WPA"
      },
      contact: {
        name: "",
        phone: "",
        email: "",
        organization: ""
      },
      sms: {
        phone: "",
        message: ""
      },
      email: {
        to: "",
        subject: "",
        body: ""
      }
    });
    const qrcodeStyle = common_vendor.ref({
      size: 400,
      foreground: "#000000",
      background: "#FFFFFF",
      errorLevel: "M",
      withLogo: false,
      logoUrl: ""
    });
    const qrcodeTypes = common_vendor.ref([
      {
        id: "text",
        name: "文本",
        icon: "file-text"
      },
      {
        id: "url",
        name: "网址",
        icon: "link"
      },
      {
        id: "wifi",
        name: "WiFi",
        icon: "wifi"
      },
      {
        id: "contact",
        name: "联系人",
        icon: "account"
      },
      {
        id: "sms",
        name: "短信",
        icon: "message"
      },
      {
        id: "email",
        name: "邮件",
        icon: "email"
      }
    ]);
    const colorOptions = common_vendor.ref([
      "#000000",
      "#2563EB",
      "#DC2626",
      "#059669",
      "#D97706",
      "#7C3AED",
      "#DB2777",
      "#0891B2"
    ]);
    const backgroundColors = common_vendor.ref([
      "#FFFFFF",
      "#F3F4F6",
      "#FEF3C7",
      "#DBEAFE",
      "#DCFCE7",
      "#FDE2E7",
      "#F3E8FF",
      "#E0F2FE"
    ]);
    common_vendor.onMounted(() => {
      loadQRCodeHistory();
    });
    const selectType = (typeId) => {
      selectedType.value = typeId;
      generatedQRCode.value = "";
    };
    const getInputTitle = () => {
      const typeMap = {
        text: "输入文本内容",
        url: "输入网址",
        wifi: "输入WiFi信息",
        contact: "输入联系人信息",
        sms: "输入短信信息",
        email: "输入邮件信息"
      };
      return typeMap[selectedType.value] || "输入内容";
    };
    const getInputDescription = () => {
      const descMap = {
        text: "支持中英文、数字、符号等",
        url: "请输入完整的网址，包含http://或https://",
        wifi: "生成WiFi连接二维码，扫码即可连接",
        contact: "生成联系人信息，扫码即可保存到通讯录",
        sms: "生成短信二维码，扫码即可发送短信",
        email: "生成邮件二维码，扫码即可发送邮件"
      };
      return descMap[selectedType.value] || "";
    };
    const hasContent = () => {
      switch (selectedType.value) {
        case "text":
          return qrcodeContent.value.text.trim() !== "";
        case "url":
          return qrcodeContent.value.url.trim() !== "";
        case "wifi":
          return qrcodeContent.value.wifi.ssid.trim() !== "";
        case "contact":
          return qrcodeContent.value.contact.name.trim() !== "" || qrcodeContent.value.contact.phone.trim() !== "";
        case "sms":
          return qrcodeContent.value.sms.phone.trim() !== "";
        case "email":
          return qrcodeContent.value.email.to.trim() !== "";
        default:
          return false;
      }
    };
    const selectColor = (type, color) => {
      qrcodeStyle.value[type] = color;
      if (generatedQRCode.value) {
        generateQRCode();
      }
    };
    const chooseLogo = () => {
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: ["album"],
        success: (res) => {
          qrcodeStyle.value.logoUrl = res.tempFilePaths[0];
          if (generatedQRCode.value) {
            generateQRCode();
          }
        }
      });
    };
    const generateContentString = () => {
      switch (selectedType.value) {
        case "text":
          return qrcodeContent.value.text;
        case "url":
          return qrcodeContent.value.url;
        case "wifi":
          const wifi = qrcodeContent.value.wifi;
          return `WIFI:T:${wifi.security};S:${wifi.ssid};P:${wifi.password};;`;
        case "contact":
          const contact = qrcodeContent.value.contact;
          return `BEGIN:VCARD
VERSION:3.0
FN:${contact.name}
TEL:${contact.phone}
EMAIL:${contact.email}
ORG:${contact.organization}
END:VCARD`;
        case "sms":
          const sms = qrcodeContent.value.sms;
          return `SMSTO:${sms.phone}:${sms.message}`;
        case "email":
          const email = qrcodeContent.value.email;
          return `mailto:${email.to}?subject=${encodeURIComponent(email.subject)}&body=${encodeURIComponent(email.body)}`;
        default:
          return "";
      }
    };
    const generateQRCode = async () => {
      if (!hasContent())
        return;
      try {
        isGenerating.value = true;
        const contentString = generateContentString();
        await new Promise((resolve) => setTimeout(resolve, 1e3));
        const mockQRCodeUrl = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
        generatedQRCode.value = mockQRCodeUrl;
        const historyItem = {
          id: Date.now().toString(),
          type: selectedType.value,
          content: contentString,
          qrcodeUrl: mockQRCodeUrl,
          style: { ...qrcodeStyle.value },
          createdAt: (/* @__PURE__ */ new Date()).toISOString()
        };
        qrcodeHistory.value.unshift(historyItem);
        saveQRCodeHistory();
        common_vendor.index.showToast({
          title: "生成成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/utilities/qrcode/generator/index.vue:531", "生成二维码失败:", error);
        common_vendor.index.showToast({
          title: "生成失败",
          icon: "error"
        });
      } finally {
        isGenerating.value = false;
      }
    };
    const downloadQRCode = () => {
      if (!generatedQRCode.value)
        return;
      common_vendor.index.showToast({
        title: "开始下载",
        icon: "success"
      });
    };
    const shareQRCode = () => {
      if (!generatedQRCode.value)
        return;
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 1,
        imageUrl: generatedQRCode.value,
        success: () => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        }
      });
    };
    const getTypeName = (typeId) => {
      const type = qrcodeTypes.value.find((t) => t.id === typeId);
      return (type == null ? void 0 : type.name) || "未知类型";
    };
    const getHistoryDescription = (item) => {
      var _a, _b, _c, _d;
      switch (item.type) {
        case "text":
          return item.content.substring(0, 20) + (item.content.length > 20 ? "..." : "");
        case "url":
          return item.content;
        case "wifi":
          return `WiFi: ${((_a = item.content.match(/S:([^;]+)/)) == null ? void 0 : _a[1]) || ""}`;
        case "contact":
          return `联系人: ${((_b = item.content.match(/FN:([^\n]+)/)) == null ? void 0 : _b[1]) || ""}`;
        case "sms":
          return `短信: ${((_c = item.content.match(/SMSTO:([^:]+)/)) == null ? void 0 : _c[1]) || ""}`;
        case "email":
          return `邮件: ${((_d = item.content.match(/mailto:([^?]+)/)) == null ? void 0 : _d[1]) || ""}`;
        default:
          return item.content.substring(0, 20);
      }
    };
    const loadHistoryItem = (item) => {
      selectedType.value = item.type;
      switch (item.type) {
        case "text":
          qrcodeContent.value.text = item.content;
          break;
        case "url":
          qrcodeContent.value.url = item.content;
          break;
      }
      qrcodeStyle.value = { ...item.style };
      generatedQRCode.value = item.qrcodeUrl;
    };
    const formatTime = (time) => {
      return common_vendor.dayjs(time).format("MM-DD HH:mm");
    };
    const saveQRCodeHistory = () => {
      common_vendor.index.setStorageSync("qrcode_generation_history", qrcodeHistory.value);
    };
    const loadQRCodeHistory = () => {
      const history = common_vendor.index.getStorageSync("qrcode_generation_history") || [];
      qrcodeHistory.value = history;
    };
    const clearHistory = () => {
      common_vendor.index.showModal({
        title: "确认清空",
        content: "确定要清空所有生成历史吗？",
        success: (res) => {
          if (res.confirm) {
            qrcodeHistory.value = [];
            saveQRCodeHistory();
            common_vendor.index.showToast({
              title: "清空成功",
              icon: "success"
            });
          }
        }
      });
    };
    const onPullDownRefresh = () => {
      loadQRCodeHistory();
      common_vendor.index.stopPullDownRefresh();
    };
    __expose({
      onPullDownRefresh
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(qrcodeTypes.value, (type, k0, i0) => {
          return {
            a: "af3ddd8d-0-" + i0,
            b: common_vendor.p({
              name: type.icon,
              size: "20",
              color: selectedType.value === type.id ? "#2563EB" : "#6B7280"
            }),
            c: common_vendor.t(type.name),
            d: type.id,
            e: selectedType.value === type.id ? 1 : "",
            f: common_vendor.o(($event) => selectType(type.id), type.id)
          };
        }),
        b: common_vendor.t(getInputTitle()),
        c: common_vendor.t(getInputDescription()),
        d: selectedType.value === "text"
      }, selectedType.value === "text" ? {
        e: qrcodeContent.value.text,
        f: common_vendor.o(($event) => qrcodeContent.value.text = $event.detail.value)
      } : {}, {
        g: selectedType.value === "url"
      }, selectedType.value === "url" ? {
        h: qrcodeContent.value.url,
        i: common_vendor.o(($event) => qrcodeContent.value.url = $event.detail.value)
      } : {}, {
        j: selectedType.value === "wifi"
      }, selectedType.value === "wifi" ? {
        k: qrcodeContent.value.wifi.ssid,
        l: common_vendor.o(($event) => qrcodeContent.value.wifi.ssid = $event.detail.value),
        m: qrcodeContent.value.wifi.password,
        n: common_vendor.o(($event) => qrcodeContent.value.wifi.password = $event.detail.value),
        o: common_vendor.p({
          name: "WPA",
          label: "WPA/WPA2"
        }),
        p: common_vendor.p({
          name: "WEP",
          label: "WEP"
        }),
        q: common_vendor.p({
          name: "nopass",
          label: "无密码"
        }),
        r: common_vendor.o(($event) => qrcodeContent.value.wifi.security = $event),
        s: common_vendor.p({
          modelValue: qrcodeContent.value.wifi.security
        })
      } : {}, {
        t: selectedType.value === "contact"
      }, selectedType.value === "contact" ? {
        v: qrcodeContent.value.contact.name,
        w: common_vendor.o(($event) => qrcodeContent.value.contact.name = $event.detail.value),
        x: qrcodeContent.value.contact.phone,
        y: common_vendor.o(($event) => qrcodeContent.value.contact.phone = $event.detail.value),
        z: qrcodeContent.value.contact.email,
        A: common_vendor.o(($event) => qrcodeContent.value.contact.email = $event.detail.value),
        B: qrcodeContent.value.contact.organization,
        C: common_vendor.o(($event) => qrcodeContent.value.contact.organization = $event.detail.value)
      } : {}, {
        D: selectedType.value === "sms"
      }, selectedType.value === "sms" ? {
        E: qrcodeContent.value.sms.phone,
        F: common_vendor.o(($event) => qrcodeContent.value.sms.phone = $event.detail.value),
        G: qrcodeContent.value.sms.message,
        H: common_vendor.o(($event) => qrcodeContent.value.sms.message = $event.detail.value)
      } : {}, {
        I: selectedType.value === "email"
      }, selectedType.value === "email" ? {
        J: qrcodeContent.value.email.to,
        K: common_vendor.o(($event) => qrcodeContent.value.email.to = $event.detail.value),
        L: qrcodeContent.value.email.subject,
        M: common_vendor.o(($event) => qrcodeContent.value.email.subject = $event.detail.value),
        N: qrcodeContent.value.email.body,
        O: common_vendor.o(($event) => qrcodeContent.value.email.body = $event.detail.value)
      } : {}, {
        P: hasContent()
      }, hasContent() ? common_vendor.e({
        Q: common_vendor.t(qrcodeStyle.value.size),
        R: common_vendor.o(generateQRCode),
        S: common_vendor.o(($event) => qrcodeStyle.value.size = $event),
        T: common_vendor.p({
          min: 200,
          max: 800,
          step: 50,
          ["active-color"]: "#2563EB",
          modelValue: qrcodeStyle.value.size
        }),
        U: common_vendor.f(colorOptions.value, (color, k0, i0) => {
          return {
            a: color,
            b: color,
            c: qrcodeStyle.value.foreground === color ? 1 : "",
            d: common_vendor.o(($event) => selectColor("foreground", color), color)
          };
        }),
        V: common_vendor.f(backgroundColors.value, (color, k0, i0) => {
          return {
            a: color,
            b: color,
            c: qrcodeStyle.value.background === color ? 1 : "",
            d: common_vendor.o(($event) => selectColor("background", color), color)
          };
        }),
        W: common_vendor.p({
          name: "L",
          label: "低 (7%)"
        }),
        X: common_vendor.p({
          name: "M",
          label: "中 (15%)"
        }),
        Y: common_vendor.p({
          name: "Q",
          label: "较高 (25%)"
        }),
        Z: common_vendor.p({
          name: "H",
          label: "高 (30%)"
        }),
        aa: common_vendor.o(generateQRCode),
        ab: common_vendor.o(($event) => qrcodeStyle.value.errorLevel = $event),
        ac: common_vendor.p({
          modelValue: qrcodeStyle.value.errorLevel
        }),
        ad: common_vendor.o(generateQRCode),
        ae: common_vendor.o(($event) => qrcodeStyle.value.withLogo = $event),
        af: common_vendor.p({
          modelValue: qrcodeStyle.value.withLogo
        }),
        ag: qrcodeStyle.value.withLogo
      }, qrcodeStyle.value.withLogo ? common_vendor.e({
        ah: common_vendor.p({
          name: "image",
          size: "20",
          color: "#2563EB"
        }),
        ai: common_vendor.o(chooseLogo),
        aj: qrcodeStyle.value.logoUrl
      }, qrcodeStyle.value.logoUrl ? {
        ak: qrcodeStyle.value.logoUrl
      } : {}) : {}) : {}, {
        al: generatedQRCode.value
      }, generatedQRCode.value ? {
        am: common_vendor.o(downloadQRCode),
        an: common_vendor.p({
          name: "download",
          size: "18",
          color: "#2563EB"
        }),
        ao: common_vendor.o(shareQRCode),
        ap: common_vendor.p({
          name: "share",
          size: "18",
          color: "#2563EB",
          ["margin-left"]: "12"
        }),
        aq: qrcodeStyle.value.size + "rpx",
        ar: qrcodeStyle.value.size + "rpx",
        as: common_vendor.t(getTypeName(selectedType.value)),
        at: common_vendor.t(qrcodeStyle.value.size),
        av: common_vendor.t(qrcodeStyle.value.size),
        aw: common_vendor.t(qrcodeStyle.value.errorLevel)
      } : {}, {
        ax: qrcodeHistory.value.length > 0
      }, qrcodeHistory.value.length > 0 ? {
        ay: common_vendor.o(clearHistory),
        az: common_vendor.p({
          type: "text",
          text: "清空",
          size: "small",
          ["custom-style"]: {
            color: "#EF4444",
            fontSize: "24rpx"
          }
        }),
        aA: common_vendor.f(qrcodeHistory.value.slice(0, 5), (item, k0, i0) => {
          return {
            a: item.qrcodeUrl,
            b: common_vendor.t(getTypeName(item.type)),
            c: common_vendor.t(getHistoryDescription(item)),
            d: common_vendor.t(formatTime(item.createdAt)),
            e: "af3ddd8d-16-" + i0,
            f: item.id,
            g: common_vendor.o(($event) => loadHistoryItem(item), item.id)
          };
        }),
        aB: common_vendor.p({
          name: "download",
          size: "16",
          color: "#6B7280"
        })
      } : {}, {
        aC: hasContent() && !generatedQRCode.value
      }, hasContent() && !generatedQRCode.value ? {
        aD: common_vendor.t(isGenerating.value ? "生成中..." : "生成二维码"),
        aE: common_vendor.o(generateQRCode),
        aF: common_vendor.p({
          type: "primary",
          size: "large",
          loading: isGenerating.value,
          ["custom-style"]: {
            width: "100%"
          }
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-af3ddd8d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pages/utilities/qrcode/generator/index.js.map
