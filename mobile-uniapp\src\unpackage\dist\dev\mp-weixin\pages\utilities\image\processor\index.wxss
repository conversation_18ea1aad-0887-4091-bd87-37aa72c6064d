/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 导入自定义样式变量 */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
.image-processor-page.data-v-fb23d7ac {
  min-height: 100vh;
  background-color: #F9FAFB;
}
.page-header.data-v-fb23d7ac {
  background-color: #FFFFFF;
  padding: 48rpx 32rpx;
  text-align: center;
  border-bottom: 1px solid #E5E7EB;
}
.page-header .page-title.data-v-fb23d7ac {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #111827;
  margin-bottom: 16rpx;
}
.page-header .page-desc.data-v-fb23d7ac {
  font-size: 28rpx;
  color: #374151;
}
.section-title.data-v-fb23d7ac {
  padding: 32rpx 32rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.section-title .title-text.data-v-fb23d7ac {
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
}
.section-title .title-desc.data-v-fb23d7ac {
  font-size: 24rpx;
  color: #6B7280;
}
.section-title .result-actions.data-v-fb23d7ac {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.upload-section.data-v-fb23d7ac {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.upload-section .upload-area.data-v-fb23d7ac {
  margin: 0 32rpx 32rpx;
  border: 2px dashed #E5E7EB;
  border-radius: 16rpx;
  padding: 48rpx;
}
.upload-section .upload-area .upload-content.data-v-fb23d7ac {
  text-align: center;
}
.upload-section .upload-area .upload-content .upload-text.data-v-fb23d7ac {
  display: block;
  font-size: 36rpx;
  font-weight: 500;
  color: #111827;
  margin: 32rpx 0 16rpx;
}
.upload-section .upload-area .upload-content .upload-desc.data-v-fb23d7ac {
  font-size: 28rpx;
  color: #374151;
}
.upload-section .upload-area .image-preview.data-v-fb23d7ac {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}
.upload-section .upload-area .image-preview .preview-image.data-v-fb23d7ac {
  width: 100%;
  max-height: 400rpx;
  border-radius: 16rpx;
}
.upload-section .upload-area .image-preview .image-info.data-v-fb23d7ac {
  text-align: center;
}
.upload-section .upload-area .image-preview .image-info .image-name.data-v-fb23d7ac {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
}
.upload-section .upload-area .image-preview .image-info .image-size.data-v-fb23d7ac,
.upload-section .upload-area .image-preview .image-info .image-dimensions.data-v-fb23d7ac {
  display: block;
  font-size: 24rpx;
  color: #374151;
}
.upload-section .upload-area .image-preview .image-actions.data-v-fb23d7ac {
  display: flex;
  justify-content: center;
}
.tools-section.data-v-fb23d7ac {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.tools-section .tools-grid.data-v-fb23d7ac {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  padding: 0 32rpx 32rpx;
}
.tools-section .tools-grid .tool-item.data-v-fb23d7ac {
  background-color: #F3F4F6;
  border-radius: 16rpx;
  padding: 32rpx;
  text-align: center;
  border: 2px solid transparent;
}
.tools-section .tools-grid .tool-item.active.data-v-fb23d7ac {
  border-color: #667eea;
  background-color: #EFF6FF;
}
.tools-section .tools-grid .tool-item .tool-icon.data-v-fb23d7ac {
  width: 80rpx;
  height: 80rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
}
.tools-section .tools-grid .tool-item .tool-name.data-v-fb23d7ac {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
}
.tools-section .tools-grid .tool-item .tool-desc.data-v-fb23d7ac {
  font-size: 24rpx;
  color: #374151;
}
.tool-options.data-v-fb23d7ac {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.tool-options .options-content.data-v-fb23d7ac {
  padding: 0 32rpx;
}
.tool-options .options-content .option-group.data-v-fb23d7ac {
  margin-bottom: 48rpx;
}
.tool-options .options-content .option-group .option-label.data-v-fb23d7ac {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 24rpx;
}
.tool-options .options-content .option-group .format-options.data-v-fb23d7ac {
  display: flex;
  gap: 16rpx;
}
.tool-options .options-content .option-group .format-options .format-item.data-v-fb23d7ac {
  flex: 1;
  background-color: #F3F4F6;
  border: 1px solid #E5E7EB;
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
}
.tool-options .options-content .option-group .format-options .format-item.active.data-v-fb23d7ac {
  border-color: #667eea;
  background-color: #EFF6FF;
}
.tool-options .options-content .option-group .format-options .format-item .format-text.data-v-fb23d7ac {
  font-size: 24rpx;
  color: #111827;
}
.tool-options .options-content .option-group .custom-input.data-v-fb23d7ac {
  width: 100%;
  margin-top: 16rpx;
  padding: 16rpx 24rpx;
  border: 1px solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #111827;
  background-color: #F3F4F6;
}
.tool-options .options-content .option-group .size-inputs.data-v-fb23d7ac {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.tool-options .options-content .option-group .size-inputs .size-input.data-v-fb23d7ac {
  flex: 1;
  padding: 16rpx 24rpx;
  border: 1px solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #111827;
  background-color: #F3F4F6;
}
.tool-options .options-content .option-group .size-inputs .size-separator.data-v-fb23d7ac {
  font-size: 28rpx;
  color: #6B7280;
}
.tool-options .options-content .option-group .filter-options.data-v-fb23d7ac {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}
.tool-options .options-content .option-group .filter-options .filter-item.data-v-fb23d7ac {
  text-align: center;
}
.tool-options .options-content .option-group .filter-options .filter-item.active .filter-preview.data-v-fb23d7ac {
  border-color: #667eea;
}
.tool-options .options-content .option-group .filter-options .filter-item .filter-preview.data-v-fb23d7ac {
  width: 100%;
  height: 120rpx;
  border-radius: 12rpx;
  border: 2px solid transparent;
  overflow: hidden;
  margin-bottom: 16rpx;
}
.tool-options .options-content .option-group .filter-options .filter-item .filter-preview .filter-image.data-v-fb23d7ac {
  width: 100%;
  height: 100%;
}
.tool-options .options-content .option-group .filter-options .filter-item .filter-name.data-v-fb23d7ac {
  font-size: 20rpx;
  color: #374151;
}
.tool-options .options-content .option-group .adjust-control.data-v-fb23d7ac {
  margin-bottom: 32rpx;
}
.tool-options .options-content .option-group .adjust-control .control-label.data-v-fb23d7ac {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 16rpx;
}
.tool-options .process-button.data-v-fb23d7ac {
  padding: 0 32rpx 32rpx;
}
.processing-progress.data-v-fb23d7ac {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
  padding: 32rpx;
}
.processing-progress .progress-header.data-v-fb23d7ac {
  text-align: center;
  margin-bottom: 32rpx;
}
.processing-progress .progress-header .progress-title.data-v-fb23d7ac {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8rpx;
}
.processing-progress .progress-header .progress-desc.data-v-fb23d7ac {
  font-size: 24rpx;
  color: #374151;
}
.processing-result.data-v-fb23d7ac {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.processing-result .result-content.data-v-fb23d7ac {
  padding: 0 32rpx 32rpx;
}
.processing-result .result-content .result-comparison.data-v-fb23d7ac {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 32rpx;
}
.processing-result .result-content .result-comparison .comparison-item.data-v-fb23d7ac {
  flex: 1;
  text-align: center;
}
.processing-result .result-content .result-comparison .comparison-item .comparison-label.data-v-fb23d7ac {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 16rpx;
}
.processing-result .result-content .result-comparison .comparison-item .comparison-image.data-v-fb23d7ac {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  background-color: #F3F4F6;
  margin-bottom: 16rpx;
}
.processing-result .result-content .result-comparison .comparison-item .comparison-info.data-v-fb23d7ac {
  font-size: 20rpx;
  color: #6B7280;
}
.processing-result .result-content .result-comparison .comparison-arrow.data-v-fb23d7ac {
  display: flex;
  align-items: center;
  justify-content: center;
}
.processing-result .result-content .result-stats.data-v-fb23d7ac {
  background-color: #F3F4F6;
  border-radius: 16rpx;
  padding: 32rpx;
}
.processing-result .result-content .result-stats .stat-item.data-v-fb23d7ac {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 8rpx;
}
.processing-result .result-content .result-stats .stat-item.data-v-fb23d7ac:last-child {
  margin-bottom: 0;
}
.processing-history.data-v-fb23d7ac {
  background-color: #FFFFFF;
}
.processing-history .history-list.data-v-fb23d7ac {
  padding: 0 32rpx 32rpx;
}
.processing-history .history-list .history-item.data-v-fb23d7ac {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  background-color: #F3F4F6;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}
.processing-history .history-list .history-item.data-v-fb23d7ac:last-child {
  margin-bottom: 0;
}
.processing-history .history-list .history-item.data-v-fb23d7ac:active {
  background-color: #E5E7EB;
}
.processing-history .history-list .history-item .history-thumbnail.data-v-fb23d7ac {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  background-color: #E5E7EB;
}
.processing-history .history-list .history-item .history-content.data-v-fb23d7ac {
  flex: 1;
}
.processing-history .history-list .history-item .history-content .history-title.data-v-fb23d7ac {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.processing-history .history-list .history-item .history-content .history-desc.data-v-fb23d7ac {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 8rpx;
}
.processing-history .history-list .history-item .history-content .history-time.data-v-fb23d7ac {
  font-size: 20rpx;
  color: #6B7280;
}
.processing-history .history-list .history-item .history-actions.data-v-fb23d7ac {
  padding: 16rpx;
}
@media screen and (max-width: 480px) {
.tools-grid.data-v-fb23d7ac {
    grid-template-columns: 1fr;
}
.format-options.data-v-fb23d7ac {
    flex-direction: column;
}
.filter-options.data-v-fb23d7ac {
    grid-template-columns: repeat(2, 1fr);
}
.result-comparison.data-v-fb23d7ac {
    flex-direction: column;
}
.result-comparison .comparison-arrow.data-v-fb23d7ac {
    transform: rotate(90deg);
}
}
@media (prefers-color-scheme: dark) {
.image-processor-page.data-v-fb23d7ac {
    background-color: #1a1a1a;
}
.page-header.data-v-fb23d7ac,
.upload-section.data-v-fb23d7ac,
.tools-section.data-v-fb23d7ac,
.tool-options.data-v-fb23d7ac,
.processing-progress.data-v-fb23d7ac,
.processing-result.data-v-fb23d7ac,
.processing-history.data-v-fb23d7ac {
    background-color: #2d2d2d;
    border-color: #404040;
}
.upload-area.data-v-fb23d7ac,
.tool-item.data-v-fb23d7ac,
.format-item.data-v-fb23d7ac,
.result-stats.data-v-fb23d7ac,
.history-item.data-v-fb23d7ac {
    background-color: #404040;
    border-color: #555555;
}
.tool-item.active.data-v-fb23d7ac,
.format-item.active.data-v-fb23d7ac {
    background-color: #1e3a8a;
    border-color: #3b82f6;
}
.custom-input.data-v-fb23d7ac,
.size-input.data-v-fb23d7ac {
    background-color: #555555;
    border-color: #666666;
    color: #ffffff;
}
.comparison-image.data-v-fb23d7ac,
.history-thumbnail.data-v-fb23d7ac {
    background-color: #555555;
}
.filter-image.data-v-fb23d7ac {
    opacity: 0.8;
}
}