/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 导入自定义样式变量 */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
.home-page.data-v-1cf27b2a {
  min-height: 100vh;
  background-color: #F9FAFB;
}
.banner-section.data-v-1cf27b2a {
  height: 400rpx;
}
.banner-section .banner-swiper.data-v-1cf27b2a {
  height: 100%;
}
.banner-section .banner-swiper .banner-item.data-v-1cf27b2a {
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 48rpx;
}
.banner-section .banner-swiper .banner-item.data-v-1cf27b2a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
}
.banner-section .banner-swiper .banner-item .banner-content.data-v-1cf27b2a {
  position: relative;
  z-index: 1;
  color: white;
}
.banner-section .banner-swiper .banner-item .banner-content .banner-title.data-v-1cf27b2a {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
}
.banner-section .banner-swiper .banner-item .banner-content .banner-desc.data-v-1cf27b2a {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 32rpx;
}
.section-title.data-v-1cf27b2a {
  padding: 48rpx 32rpx 32rpx;
}
.section-title .title-text.data-v-1cf27b2a {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8rpx;
}
.section-title .title-desc.data-v-1cf27b2a {
  font-size: 24rpx;
  color: #6B7280;
}
.quick-actions.data-v-1cf27b2a {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.quick-actions .actions-grid.data-v-1cf27b2a {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
  padding: 0 32rpx 48rpx;
}
.quick-actions .actions-grid .action-item.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.quick-actions .actions-grid .action-item .action-icon.data-v-1cf27b2a {
  width: 96rpx;
  height: 96rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.quick-actions .actions-grid .action-item .action-title.data-v-1cf27b2a {
  font-size: 24rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
}
.quick-actions .actions-grid .action-item .action-desc.data-v-1cf27b2a {
  font-size: 20rpx;
  color: #6B7280;
}
.feature-modules.data-v-1cf27b2a {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.feature-modules .modules-list.data-v-1cf27b2a {
  padding: 0 32rpx 48rpx;
}
.feature-modules .modules-list .module-card.data-v-1cf27b2a {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1px solid #E5E7EB;
  display: flex;
  align-items: flex-start;
}
.feature-modules .modules-list .module-card .module-header.data-v-1cf27b2a {
  position: relative;
  margin-right: 24rpx;
}
.feature-modules .modules-list .module-card .module-header .module-icon.data-v-1cf27b2a {
  width: 80rpx;
  height: 80rpx;
  background-color: #F3F4F6;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.feature-modules .modules-list .module-card .module-header .module-badge.data-v-1cf27b2a {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #EF4444;
  border-radius: 9999rpx;
  padding: 4rpx 12rpx;
}
.feature-modules .modules-list .module-card .module-header .module-badge .badge-text.data-v-1cf27b2a {
  font-size: 20rpx;
  color: white;
}
.feature-modules .modules-list .module-card .module-content.data-v-1cf27b2a {
  flex: 1;
}
.feature-modules .modules-list .module-card .module-content .module-title.data-v-1cf27b2a {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8rpx;
}
.feature-modules .modules-list .module-card .module-content .module-desc.data-v-1cf27b2a {
  display: block;
  font-size: 24rpx;
  color: #374151;
  line-height: 1.625;
  margin-bottom: 16rpx;
}
.feature-modules .modules-list .module-card .module-content .module-features.data-v-1cf27b2a {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}
.feature-modules .modules-list .module-card .module-content .module-features .feature-tag.data-v-1cf27b2a {
  background-color: #F3F4F6;
  color: #6B7280;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 9999rpx;
}
.feature-modules .modules-list .module-card .module-arrow.data-v-1cf27b2a {
  margin-left: 16rpx;
  display: flex;
  align-items: center;
}
.recent-section.data-v-1cf27b2a {
  background-color: #FFFFFF;
}
.recent-section .recent-scroll.data-v-1cf27b2a {
  white-space: nowrap;
}
.recent-section .recent-scroll .recent-list.data-v-1cf27b2a {
  display: flex;
  padding: 0 32rpx 48rpx;
  gap: 24rpx;
}
.recent-section .recent-scroll .recent-list .recent-item.data-v-1cf27b2a {
  flex-shrink: 0;
  width: 200rpx;
  background-color: #F3F4F6;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
}
.recent-section .recent-scroll .recent-list .recent-item .recent-icon.data-v-1cf27b2a {
  width: 64rpx;
  height: 64rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16rpx;
}
.recent-section .recent-scroll .recent-list .recent-item .recent-title.data-v-1cf27b2a {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.recent-section .recent-scroll .recent-list .recent-item .recent-time.data-v-1cf27b2a {
  font-size: 20rpx;
  color: #6B7280;
}