/**
 * 数字人模块路由配置
 * 模块化的路由管理
 */

import StandaloneLayout from '../../components/layout/StandaloneLayout.vue';

// 懒加载组件
const DigitalHumanApp = () => import('./views/DigitalHumanApp.vue');
const DigitalHumanChat = () => import('../../views/digital-human/chat.vue');
const TestChat = () => import('./views/TestChat.vue');
const DigitalHumanCreate = () => import('./views/create/DigitalHumanCreate.vue');
const DigitalHumanEdit = () => import('./views/DigitalHumanEdit.vue');
const ModelManager = () => import('./views/ModelManager.vue');

// 数字人模块路由
const digitalHumanRoutes = [
  // 独立布局路由
  {
    path: '/digital-human',
    component: StandaloneLayout,
    children: [
      {
        path: '',
        redirect: 'app'
      },
      {
        path: 'app',
        name: 'DigitalHumanApp',
        component: DigitalHumanApp,
        meta: {
          title: '数字人应用中心',
          icon: 'appstore',
          requiresAuth: false,
          module: 'digital-human'
        }
      },
      {
        path: 'chat/:id',
        name: 'DigitalHumanChat',
        component: DigitalHumanChat,
        meta: {
          title: '数字人聊天',
          icon: 'message',
          requiresAuth: false,
          module: 'digital-human'
        }
      },
      {
        path: 'test-chat/:id',
        name: 'TestChat',
        component: TestChat,
        meta: {
          title: '测试聊天',
          icon: 'message',
          requiresAuth: false,
          module: 'digital-human'
        }
      },
      {
        path: 'create',
        name: 'DigitalHumanCreate',
        component: DigitalHumanCreate,
        meta: {
          title: '创建数字人',
          icon: 'plus',
          requiresAuth: true,
          module: 'digital-human'
        }
      },
      {
        path: 'edit/:id',
        name: 'DigitalHumanEdit',
        component: DigitalHumanEdit,
        meta: { 
          title: '编辑数字人', 
          icon: 'edit', 
          requiresAuth: true,
          module: 'digital-human'
        }
      },
      {
        path: 'models',
        name: 'ModelManager',
        component: ModelManager,
        meta: { 
          title: '模型管理', 
          icon: 'setting', 
          requiresAuth: true,
          module: 'digital-human'
        }
      }
    ]
  },
  
  // 兼容性路由 (重定向到新路径)
  {
    path: '/standalone/digital-human/app',
    redirect: '/digital-human/app'
  },
  {
    path: '/standalone/digital-human/chat/:id',
    redirect: to => `/digital-human/chat/${to.params.id}`
  },
  {
    path: '/standalone/digital-human/create',
    redirect: '/digital-human/create'
  }
];

export default digitalHumanRoutes;
