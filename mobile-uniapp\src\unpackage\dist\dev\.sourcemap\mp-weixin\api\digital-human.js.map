{"version": 3, "file": "digital-human.js", "sources": ["api/digital-human.ts"], "sourcesContent": ["import { http, uploadFile } from '@/utils/request'\n\n// 数字人相关接口\nexport interface DigitalHuman {\n  id: string\n  name: string\n  avatar: string\n  description: string\n  voice_id: string\n  language: string\n  personality: string\n  created_at: string\n  is_active: boolean\n}\n\nexport interface ChatMessage {\n  id: string\n  role: 'user' | 'assistant'\n  content: string\n  timestamp: string\n  message_type: 'text' | 'audio' | 'video'\n  audio_url?: string\n  video_url?: string\n  duration?: number\n}\n\nexport interface ChatSession {\n  id: string\n  digital_human_id: string\n  digital_human_name: string\n  title: string\n  created_at: string\n  updated_at: string\n  message_count: number\n  last_message?: string\n}\n\nexport interface VoiceConfig {\n  voice_id: string\n  speed: number\n  pitch: number\n  volume: number\n  emotion: string\n}\n\nexport const digitalHumanApi = {\n  // 获取数字人列表\n  getDigitalHumans: (params?: { page?: number; limit?: number }) => {\n    return http.get<{\n      items: DigitalHuman[]\n      total: number\n      page: number\n      limit: number\n    }>('/digital-human/list', params)\n  },\n\n  // 获取数字人详情\n  getDigitalHuman: (id: string) => {\n    return http.get<DigitalHuman>(`/digital-human/${id}`)\n  },\n\n  // 创建数字人\n  createDigitalHuman: (data: {\n    name: string\n    description: string\n    voice_id: string\n    language: string\n    personality: string\n    avatar?: string\n  }) => {\n    return http.post<DigitalHuman>('/digital-human/create', data)\n  },\n\n  // 更新数字人\n  updateDigitalHuman: (id: string, data: Partial<DigitalHuman>) => {\n    return http.put<DigitalHuman>(`/digital-human/${id}`, data)\n  },\n\n  // 删除数字人\n  deleteDigitalHuman: (id: string) => {\n    return http.delete(`/digital-human/${id}`)\n  },\n\n  // 上传数字人头像\n  uploadAvatar: (filePath: string) => {\n    return uploadFile({\n      url: '/digital-human/upload-avatar',\n      filePath,\n      name: 'avatar'\n    })\n  },\n\n  // 获取对话会话列表\n  getChatSessions: (params?: { page?: number; limit?: number }) => {\n    return http.get<{\n      items: ChatSession[]\n      total: number\n      page: number\n      limit: number\n    }>('/digital-human/sessions', params)\n  },\n\n  // 创建对话会话\n  createChatSession: (digitalHumanId: string, title?: string) => {\n    return http.post<ChatSession>('/digital-human/sessions', {\n      digital_human_id: digitalHumanId,\n      title: title || '新对话'\n    })\n  },\n\n  // 获取会话消息\n  getChatMessages: (sessionId: string, params?: { page?: number; limit?: number }) => {\n    return http.get<{\n      items: ChatMessage[]\n      total: number\n      page: number\n      limit: number\n    }>(`/digital-human/sessions/${sessionId}/messages`, params)\n  },\n\n  // 发送文本消息\n  sendTextMessage: (sessionId: string, content: string) => {\n    return http.post<ChatMessage>(`/digital-human/sessions/${sessionId}/messages`, {\n      content,\n      message_type: 'text'\n    })\n  },\n\n  // 发送语音消息\n  sendAudioMessage: (sessionId: string, filePath: string) => {\n    return uploadFile({\n      url: `/digital-human/sessions/${sessionId}/audio`,\n      filePath,\n      name: 'audio',\n      formData: {\n        message_type: 'audio'\n      }\n    })\n  },\n\n  // 获取数字人回复\n  getDigitalHumanReply: (sessionId: string, messageId: string, options?: {\n    voice_config?: VoiceConfig\n    response_type?: 'text' | 'audio' | 'video'\n  }) => {\n    return http.post<ChatMessage>(`/digital-human/sessions/${sessionId}/reply`, {\n      message_id: messageId,\n      ...options\n    })\n  },\n\n  // 生成数字人视频回复\n  generateVideoReply: (sessionId: string, messageId: string, voiceConfig?: VoiceConfig) => {\n    return http.post<{\n      task_id: string\n      status: 'pending' | 'processing' | 'completed' | 'failed'\n      video_url?: string\n    }>(`/digital-human/sessions/${sessionId}/video`, {\n      message_id: messageId,\n      voice_config: voiceConfig\n    })\n  },\n\n  // 获取视频生成任务状态\n  getVideoTaskStatus: (taskId: string) => {\n    return http.get<{\n      task_id: string\n      status: 'pending' | 'processing' | 'completed' | 'failed'\n      progress: number\n      video_url?: string\n      error_message?: string\n    }>(`/digital-human/video-task/${taskId}`)\n  },\n\n  // 删除对话会话\n  deleteChatSession: (sessionId: string) => {\n    return http.delete(`/digital-human/sessions/${sessionId}`)\n  },\n\n  // 清空会话消息\n  clearChatMessages: (sessionId: string) => {\n    return http.delete(`/digital-human/sessions/${sessionId}/messages`)\n  },\n\n  // 获取可用语音列表\n  getAvailableVoices: (language?: string) => {\n    return http.get<Array<{\n      voice_id: string\n      name: string\n      language: string\n      gender: 'male' | 'female'\n      age: string\n      style: string\n      preview_url?: string\n    }>>('/digital-human/voices', { language })\n  },\n\n  // 语音合成预览\n  previewVoice: (text: string, voiceConfig: VoiceConfig) => {\n    return http.post<{\n      audio_url: string\n      duration: number\n    }>('/digital-human/voice-preview', {\n      text,\n      voice_config: voiceConfig\n    })\n  },\n\n  // 获取数字人统计信息\n  getDigitalHumanStats: (id: string) => {\n    return http.get<{\n      total_sessions: number\n      total_messages: number\n      total_duration: number\n      last_active: string\n    }>(`/digital-human/${id}/stats`)\n  },\n\n  // 导出对话记录\n  exportChatHistory: (sessionId: string, format: 'json' | 'txt' | 'pdf') => {\n    return http.get(`/digital-human/sessions/${sessionId}/export`, \n      { format }, \n      { responseType: 'blob' }\n    )\n  },\n\n  // 搜索对话记录\n  searchMessages: (query: string, params?: {\n    session_id?: string\n    digital_human_id?: string\n    start_date?: string\n    end_date?: string\n    page?: number\n    limit?: number\n  }) => {\n    return http.get<{\n      items: ChatMessage[]\n      total: number\n      page: number\n      limit: number\n    }>('/digital-human/search', { query, ...params })\n  }\n}\n"], "names": ["http", "uploadFile"], "mappings": ";;AA6CO,MAAM,kBAAkB;AAAA;AAAA,EAE7B,kBAAkB,CAAC,WAA+C;AACzD,WAAAA,mBAAK,IAKT,uBAAuB,MAAM;AAAA,EAClC;AAAA;AAAA,EAGA,iBAAiB,CAAC,OAAe;AAC/B,WAAOA,cAAAA,KAAK,IAAkB,kBAAkB,EAAE,EAAE;AAAA,EACtD;AAAA;AAAA,EAGA,oBAAoB,CAAC,SAOf;AACG,WAAAA,mBAAK,KAAmB,yBAAyB,IAAI;AAAA,EAC9D;AAAA;AAAA,EAGA,oBAAoB,CAAC,IAAY,SAAgC;AAC/D,WAAOA,cAAAA,KAAK,IAAkB,kBAAkB,EAAE,IAAI,IAAI;AAAA,EAC5D;AAAA;AAAA,EAGA,oBAAoB,CAAC,OAAe;AAClC,WAAOA,cAAAA,KAAK,OAAO,kBAAkB,EAAE,EAAE;AAAA,EAC3C;AAAA;AAAA,EAGA,cAAc,CAAC,aAAqB;AAClC,WAAOC,yBAAW;AAAA,MAChB,KAAK;AAAA,MACL;AAAA,MACA,MAAM;AAAA,IAAA,CACP;AAAA,EACH;AAAA;AAAA,EAGA,iBAAiB,CAAC,WAA+C;AACxD,WAAAD,mBAAK,IAKT,2BAA2B,MAAM;AAAA,EACtC;AAAA;AAAA,EAGA,mBAAmB,CAAC,gBAAwB,UAAmB;AACtD,WAAAA,cAAA,KAAK,KAAkB,2BAA2B;AAAA,MACvD,kBAAkB;AAAA,MAClB,OAAO,SAAS;AAAA,IAAA,CACjB;AAAA,EACH;AAAA;AAAA,EAGA,iBAAiB,CAAC,WAAmB,WAA+C;AAClF,WAAOA,cAAAA,KAAK,IAKT,2BAA2B,SAAS,aAAa,MAAM;AAAA,EAC5D;AAAA;AAAA,EAGA,iBAAiB,CAAC,WAAmB,YAAoB;AACvD,WAAOA,cAAAA,KAAK,KAAkB,2BAA2B,SAAS,aAAa;AAAA,MAC7E;AAAA,MACA,cAAc;AAAA,IAAA,CACf;AAAA,EACH;AAAA;AAAA,EAGA,kBAAkB,CAAC,WAAmB,aAAqB;AACzD,WAAOC,yBAAW;AAAA,MAChB,KAAK,2BAA2B,SAAS;AAAA,MACzC;AAAA,MACA,MAAM;AAAA,MACN,UAAU;AAAA,QACR,cAAc;AAAA,MAChB;AAAA,IAAA,CACD;AAAA,EACH;AAAA;AAAA,EAGA,sBAAsB,CAAC,WAAmB,WAAmB,YAGvD;AACJ,WAAOD,cAAAA,KAAK,KAAkB,2BAA2B,SAAS,UAAU;AAAA,MAC1E,YAAY;AAAA,MACZ,GAAG;AAAA,IAAA,CACJ;AAAA,EACH;AAAA;AAAA,EAGA,oBAAoB,CAAC,WAAmB,WAAmB,gBAA8B;AACvF,WAAOA,cAAAA,KAAK,KAIT,2BAA2B,SAAS,UAAU;AAAA,MAC/C,YAAY;AAAA,MACZ,cAAc;AAAA,IAAA,CACf;AAAA,EACH;AAAA;AAAA,EAGA,oBAAoB,CAAC,WAAmB;AACtC,WAAOA,cAAAA,KAAK,IAMT,6BAA6B,MAAM,EAAE;AAAA,EAC1C;AAAA;AAAA,EAGA,mBAAmB,CAAC,cAAsB;AACxC,WAAOA,cAAAA,KAAK,OAAO,2BAA2B,SAAS,EAAE;AAAA,EAC3D;AAAA;AAAA,EAGA,mBAAmB,CAAC,cAAsB;AACxC,WAAOA,cAAAA,KAAK,OAAO,2BAA2B,SAAS,WAAW;AAAA,EACpE;AAAA;AAAA,EAGA,oBAAoB,CAAC,aAAsB;AACzC,WAAOA,cAAK,KAAA,IAQR,yBAAyB,EAAE,SAAU,CAAA;AAAA,EAC3C;AAAA;AAAA,EAGA,cAAc,CAAC,MAAc,gBAA6B;AACjD,WAAAA,cAAA,KAAK,KAGT,gCAAgC;AAAA,MACjC;AAAA,MACA,cAAc;AAAA,IAAA,CACf;AAAA,EACH;AAAA;AAAA,EAGA,sBAAsB,CAAC,OAAe;AACpC,WAAOA,cAAAA,KAAK,IAKT,kBAAkB,EAAE,QAAQ;AAAA,EACjC;AAAA;AAAA,EAGA,mBAAmB,CAAC,WAAmB,WAAmC;AACxE,WAAOA,cAAK,KAAA;AAAA,MAAI,2BAA2B,SAAS;AAAA,MAClD,EAAE,OAAO;AAAA,MACT,EAAE,cAAc,OAAO;AAAA,IAAA;AAAA,EAE3B;AAAA;AAAA,EAGA,gBAAgB,CAAC,OAAe,WAO1B;AACJ,WAAOA,cAAAA,KAAK,IAKT,yBAAyB,EAAE,OAAO,GAAG,QAAQ;AAAA,EAClD;AACF;;"}