#!/usr/bin/env python3
"""
GPU加速的MuseTalk推理脚本
使用现有的MuseTalk模型和GPU加速
"""

import os
import sys
import argparse
import logging
import cv2
import numpy as np
import torch
from pathlib import Path
import subprocess
import json
import time

# 添加MuseTalk目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='GPU加速的MuseTalk推理')
    parser.add_argument('--source_image', required=True, help='源图像路径')
    parser.add_argument('--driving_audio', required=True, help='驱动音频路径')
    parser.add_argument('--output', required=True, help='输出视频路径')
    parser.add_argument('--device', default='cuda', choices=['cuda', 'cpu'], help='设备类型')
    parser.add_argument('--fps', type=int, default=25, help='输出视频帧率')
    parser.add_argument('--quality', default='high', choices=['low', 'medium', 'high'], help='输出质量')
    parser.add_argument('--bbox_shift', type=int, default=0, help='边界框偏移')
    return parser.parse_args()

def check_gpu():
    """检查GPU可用性"""
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"✅ GPU可用: {gpu_name} ({gpu_memory:.1f}GB)")
        return True
    else:
        logger.warning("⚠️ GPU不可用，将使用CPU")
        return False

def get_audio_duration(audio_path):
    """获取音频时长"""
    try:
        cmd = ['ffprobe', '-v', 'quiet', '-show_entries', 'format=duration', 
               '-of', 'csv=p=0', str(audio_path)]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            duration = float(result.stdout.strip())
            logger.info(f"🎵 音频时长: {duration:.2f}秒")
            return duration
        else:
            logger.warning("无法获取音频时长，使用默认值")
            return 5.0
    except Exception as e:
        logger.warning(f"获取音频时长失败: {e}")
        return 5.0

def run_official_inference(args):
    """运行官方MuseTalk推理"""
    start_time = time.time()
    
    # 检查输入文件
    if not os.path.exists(args.source_image):
        raise FileNotFoundError(f"源图像不存在: {args.source_image}")
    if not os.path.exists(args.driving_audio):
        raise FileNotFoundError(f"音频文件不存在: {args.driving_audio}")
    
    # 获取音频时长
    audio_duration = get_audio_duration(args.driving_audio)
    total_frames = int(audio_duration * args.fps)
    
    logger.info(f"🎬 预计生成帧数: {total_frames}")
    
    # 设置设备
    device = args.device if args.device == 'cpu' or torch.cuda.is_available() else 'cpu'
    logger.info(f"🖥️ 使用设备: {device}")
    
    # 检查模型文件
    model_dir = current_dir / "models"
    if not model_dir.exists():
        raise FileNotFoundError(f"模型目录不存在: {model_dir}")
    
    # 创建临时配置文件
    config = {
        "video_path": str(args.source_image),
        "audio_path": str(args.driving_audio),
        "bbox_shift": args.bbox_shift,
        "fps": args.fps,
        "device": device
    }
    
    config_path = Path(args.output).parent / "temp_musetalk_config.json"
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    # 使用真正的MuseTalk推理脚本
    inference_script = current_dir / "scripts" / "inference.py"

    if not inference_script.exists():
        raise FileNotFoundError(f"MuseTalk推理脚本不存在: {inference_script}")

    # 检查可用的模型文件（优先使用V1.5，回退到V1.0）
    unet_model_path = current_dir / "models" / "musetalkV15" / "unet.pth"
    unet_config = current_dir / "models" / "musetalkV15" / "musetalk.json"
    version = "v15"

    if not unet_model_path.exists() or not unet_config.exists():
        logger.warning("⚠️ MuseTalk V1.5模型不完整，回退到V1.0")
        unet_model_path = current_dir / "models" / "musetalk" / "pytorch_model.bin"
        unet_config = current_dir / "models" / "musetalk" / "config.json"
        version = "v1"

        if not unet_model_path.exists():
            raise FileNotFoundError(f"MuseTalk模型不存在: {unet_model_path}")
        if not unet_config.exists():
            raise FileNotFoundError(f"MuseTalk配置不存在: {unet_config}")

    logger.info(f"🎭 使用MuseTalk {version.upper()}模型")
    logger.info(f"📦 模型文件: {unet_model_path.name}")
    logger.info(f"⚙️ 配置文件: {unet_config.name}")

    # 创建临时配置文件
    temp_config = {
        "task_0": {
            "video_path": str(args.source_image),
            "audio_path": str(args.driving_audio),
            "bbox_shift": args.bbox_shift
        }
    }

    import yaml
    config_path = Path(args.output).parent / "temp_inference_config.yaml"
    with open(config_path, 'w') as f:
        yaml.dump(temp_config, f)

    # 构建真正的MuseTalk命令
    cmd = [
        sys.executable, "-m", "scripts.inference",
        "--inference_config", str(config_path),
        "--result_dir", str(Path(args.output).parent),
        "--unet_model_path", str(unet_model_path),
        "--unet_config", str(unet_config),
        "--version", version
    ]

    # 添加GPU参数
    if device == 'cuda':
        cmd.extend(["--gpu_id", "0"])

    logger.info(f"📝 创建临时配置: {config_path}")
    logger.info(f"📝 配置内容: {temp_config}")
    
    logger.info(f"🚀 开始GPU加速MuseTalk推理...")
    logger.info(f"📝 命令: {' '.join(cmd)}")
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        # 添加MuseTalk目录到Python路径
        pythonpath = str(current_dir)
        if 'PYTHONPATH' in env:
            pythonpath = f"{pythonpath}{os.pathsep}{env['PYTHONPATH']}"
        env['PYTHONPATH'] = pythonpath

        if device == 'cuda':
            env['CUDA_VISIBLE_DEVICES'] = '0'

        logger.info(f"🐍 PYTHONPATH: {env['PYTHONPATH']}")
        
        # 执行推理
        result = subprocess.run(
            cmd,
            cwd=str(current_dir),
            capture_output=True,
            text=True,
            timeout=300,  # 5分钟超时
            env=env
        )
        
        elapsed_time = time.time() - start_time
        
        if result.returncode == 0:
            logger.info(f"✅ MuseTalk推理成功完成!")
            logger.info(f"⏱️ 总耗时: {elapsed_time:.2f}秒")
            logger.info(f"🎬 平均速度: {total_frames/elapsed_time:.1f} fps")
            
            # 检查输出文件
            if os.path.exists(args.output):
                file_size = os.path.getsize(args.output)
                logger.info(f"📁 输出文件: {args.output}")
                logger.info(f"📊 文件大小: {file_size/1024:.1f} KB")
            else:
                # 查找可能的输出文件
                output_dir = Path(args.output).parent
                possible_outputs = list(output_dir.glob("*.mp4"))
                if possible_outputs:
                    actual_output = possible_outputs[0]
                    logger.info(f"📁 找到输出文件: {actual_output}")
                    # 移动到期望的位置
                    actual_output.rename(args.output)
                    logger.info(f"📁 已移动到: {args.output}")
                else:
                    logger.warning("⚠️ 未找到输出文件")
            
            return True
        else:
            logger.error(f"❌ MuseTalk推理失败")
            logger.error(f"标准输出: {result.stdout}")
            logger.error(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ 推理超时")
        return False
    except Exception as e:
        logger.error(f"❌ 推理过程中出错: {e}")
        return False
    finally:
        # 清理临时文件
        try:
            if config_path.exists():
                config_path.unlink()
                logger.info("🧹 清理临时配置文件")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {e}")

def main():
    args = parse_args()
    
    logger.info("🎭 GPU加速MuseTalk推理开始...")
    logger.info(f"源图像: {args.source_image}")
    logger.info(f"驱动音频: {args.driving_audio}")
    logger.info(f"输出视频: {args.output}")
    logger.info(f"设备: {args.device}")
    logger.info(f"质量: {args.quality}")
    
    # 检查GPU
    if args.device == 'cuda':
        check_gpu()
    
    try:
        # 运行推理
        success = run_official_inference(args)
        
        if success:
            logger.info("🎉 GPU加速MuseTalk推理完成！")
            sys.exit(0)
        else:
            logger.error("💥 MuseTalk推理失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"💥 程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
