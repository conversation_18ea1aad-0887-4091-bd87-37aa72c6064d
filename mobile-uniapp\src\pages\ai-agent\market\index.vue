<template>
  <view class="market-page">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input-container">
        <u-icon name="search" size="18" color="#9CA3AF"></u-icon>
        <input 
          class="search-input"
          v-model="searchQuery"
          placeholder="搜索AI智能体..."
          @confirm="handleSearch"
          confirm-type="search"
        />
        <u-icon 
          v-if="searchQuery"
          name="close-circle-fill" 
          size="18" 
          color="#9CA3AF"
          @click="clearSearch"
        ></u-icon>
      </view>
      
      <u-button 
        type="text" 
        text="筛选" 
        size="small"
        @click="showFilterModal = true"
        :custom-style="{ color: '#2563EB', fontSize: '28rpx' }"
      >
        <u-icon name="options" size="16" color="#2563EB" margin-right="4"></u-icon>
      </u-button>
    </view>

    <!-- 分类标签 -->
    <view class="category-tabs">
      <scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false">
        <view class="tabs-list">
          <view 
            class="tab-item"
            :class="{ active: selectedCategory === '' }"
            @click="selectCategory('')"
          >
            <text class="tab-text">全部</text>
          </view>
          <view 
            class="tab-item"
            v-for="category in categories"
            :key="category.id"
            :class="{ active: selectedCategory === category.id }"
            @click="selectCategory(category.id)"
          >
            <u-icon :name="category.icon" size="16" margin-right="4"></u-icon>
            <text class="tab-text">{{ category.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 推荐智能体 -->
    <view class="featured-section" v-if="featuredAgents.length > 0 && !searchQuery">
      <view class="section-header">
        <text class="section-title">推荐智能体</text>
        <u-button 
          type="text" 
          text="查看全部" 
          size="small"
          @click="viewAllFeatured"
          :custom-style="{ color: '#2563EB', fontSize: '24rpx' }"
        ></u-button>
      </view>
      
      <scroll-view class="featured-scroll" scroll-x="true" show-scrollbar="false">
        <view class="featured-list">
          <view 
            class="featured-item"
            v-for="agent in featuredAgents.slice(0, 5)"
            :key="agent.id"
            @click="viewAgentDetail(agent)"
          >
            <image class="featured-avatar" :src="agent.avatar" mode="aspectFill"></image>
            <view class="featured-content">
              <text class="featured-name">{{ agent.name }}</text>
              <text class="featured-desc">{{ agent.description }}</text>
              <view class="featured-meta">
                <view class="rating">
                  <u-icon name="star-fill" size="12" color="#F59E0B"></u-icon>
                  <text class="rating-text">{{ agent.rating.toFixed(1) }}</text>
                </view>
                <text class="usage-count">{{ formatUsageCount(agent.usage_count) }}次使用</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 智能体列表 -->
    <view class="agents-section">
      <view class="section-header" v-if="!searchQuery">
        <text class="section-title">
          {{ selectedCategory ? getCategoryName(selectedCategory) : '全部智能体' }}
        </text>
        <view class="sort-options">
          <u-button 
            type="text" 
            :text="getSortText(sortBy)" 
            size="small"
            @click="showSortModal = true"
            :custom-style="{ color: '#6B7280', fontSize: '24rpx' }"
          >
            <u-icon name="arrow-down" size="12" color="#6B7280" margin-left="4"></u-icon>
          </u-button>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-container" v-if="isLoading && agents.length === 0">
        <u-loading-icon mode="flower"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 智能体网格 -->
      <view class="agents-grid" v-else>
        <view 
          class="agent-card"
          v-for="agent in filteredAgents"
          :key="agent.id"
          @click="viewAgentDetail(agent)"
        >
          <view class="card-header">
            <image class="agent-avatar" :src="agent.avatar" mode="aspectFill"></image>
            <view class="agent-badge" v-if="agent.is_featured">
              <text class="badge-text">推荐</text>
            </view>
            <view class="favorite-btn" @click.stop="toggleFavorite(agent)">
              <u-icon 
                :name="agent.is_favorite ? 'heart-fill' : 'heart'" 
                size="16" 
                :color="agent.is_favorite ? '#EF4444' : '#9CA3AF'"
              ></u-icon>
            </view>
          </view>
          
          <view class="card-content">
            <text class="agent-name">{{ agent.name }}</text>
            <text class="agent-desc">{{ agent.description }}</text>
            
            <view class="agent-tags">
              <text 
                class="tag-item" 
                v-for="tag in agent.tags.slice(0, 3)" 
                :key="tag"
              >
                {{ tag }}
              </text>
            </view>
            
            <view class="agent-meta">
              <view class="rating">
                <u-icon name="star-fill" size="12" color="#F59E0B"></u-icon>
                <text class="rating-text">{{ agent.rating.toFixed(1) }}</text>
              </view>
              <text class="usage-count">{{ formatUsageCount(agent.usage_count) }}</text>
              <view class="price" v-if="!agent.is_free">
                <text class="price-text">¥{{ agent.price }}</text>
              </view>
              <view class="free-badge" v-else>
                <text class="free-text">免费</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="!isLoading && filteredAgents.length === 0">
        <u-icon name="robot" size="48" color="#D1D5DB"></u-icon>
        <text class="empty-text">暂无智能体</text>
        <text class="empty-desc">{{ searchQuery ? '试试其他关键词' : '敬请期待更多智能体' }}</text>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore && !isLoading">
        <u-button 
          type="text" 
          text="加载更多" 
          @click="loadMore"
          :custom-style="{ color: '#2563EB' }"
        ></u-button>
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <u-modal 
      v-model="showFilterModal" 
      title="筛选条件"
      :show-cancel-button="true"
      cancel-text="重置"
      confirm-text="确定"
      @cancel="resetFilter"
      @confirm="applyFilter"
    >
      <view class="filter-content">
        <view class="filter-group">
          <text class="filter-label">价格</text>
          <view class="filter-options">
            <u-checkbox-group v-model="filterOptions.priceType">
              <u-checkbox name="free" label="免费"></u-checkbox>
              <u-checkbox name="paid" label="付费"></u-checkbox>
            </u-checkbox-group>
          </view>
        </view>
        
        <view class="filter-group">
          <text class="filter-label">评分</text>
          <view class="filter-options">
            <u-rate v-model="filterOptions.minRating" :count="5" size="16"></u-rate>
          </view>
        </view>
      </view>
    </u-modal>

    <!-- 排序弹窗 -->
    <u-action-sheet 
      v-model="showSortModal"
      :actions="sortActions"
      @click="handleSort"
    ></u-action-sheet>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAIAgentStore } from '@/store/modules/aiAgent'
import { type AIAgent } from '@/api/ai-agent'

// Store
const aiAgentStore = useAIAgentStore()

// 响应式数据
const searchQuery = ref<string>('')
const showFilterModal = ref<boolean>(false)
const showSortModal = ref<boolean>(false)
const hasMore = ref<boolean>(true)
const currentPage = ref<number>(1)
const filterOptions = ref({
  priceType: [],
  minRating: 0
})

// 计算属性
const {
  categories,
  agents,
  featuredAgents,
  filteredAgents,
  isLoading,
  selectedCategory,
  sortBy
} = aiAgentStore

// 排序选项
const sortActions = ref([
  { name: '最受欢迎', value: 'popular' },
  { name: '评分最高', value: 'rating' },
  { name: '最新发布', value: 'newest' },
  { name: '价格最低', value: 'price' }
])

// 页面加载
onMounted(async () => {
  // 加载分类
  await aiAgentStore.loadCategories()

  // 加载推荐智能体
  await aiAgentStore.loadFeaturedAgents()

  // 加载智能体列表
  await loadAgents()
})

// 监听搜索查询变化
watch(searchQuery, (newQuery) => {
  if (newQuery.trim()) {
    handleSearch()
  } else {
    loadAgents()
  }
}, { debounce: 500 })

// 监听分类变化
watch(() => selectedCategory.value, () => {
  currentPage.value = 1
  loadAgents()
})

// 监听排序变化
watch(() => sortBy.value, () => {
  currentPage.value = 1
  loadAgents()
})

// 加载智能体
const loadAgents = async (page = 1) => {
  try {
    const params = {
      category: selectedCategory.value || undefined,
      search: searchQuery.value || undefined,
      sort: sortBy.value,
      page,
      limit: 20
    }

    const result = await aiAgentStore.loadAgents(params)

    if (result) {
      hasMore.value = result.page * result.limit < result.total
      currentPage.value = result.page
    }
  } catch (error) {
    console.error('加载智能体失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  }
}

// 搜索处理
const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    await loadAgents()
    return
  }

  try {
    await aiAgentStore.searchAgents(
      searchQuery.value.trim(),
      selectedCategory.value || undefined
    )
  } catch (error) {
    console.error('搜索失败:', error)
    uni.showToast({
      title: '搜索失败',
      icon: 'error'
    })
  }
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  aiAgentStore.setSearchQuery('')
  loadAgents()
}

// 选择分类
const selectCategory = (categoryId: string) => {
  aiAgentStore.setCategory(categoryId)
}

// 查看智能体详情
const viewAgentDetail = (agent: AIAgent) => {
  uni.navigateTo({
    url: `/pages/ai-agent/detail/index?id=${agent.id}`
  })
}

// 查看全部推荐
const viewAllFeatured = () => {
  // 设置筛选条件为推荐
  uni.navigateTo({
    url: '/pages/ai-agent/featured/index'
  })
}

// 切换收藏
const toggleFavorite = async (agent: AIAgent) => {
  const isFavorite = (agent as any).is_favorite || false
  await aiAgentStore.toggleFavorite(agent.id, isFavorite)
}

// 加载更多
const loadMore = async () => {
  if (!hasMore.value || isLoading.value) return

  const nextPage = currentPage.value + 1
  await loadAgents(nextPage)
}

// 处理排序
const handleSort = (action: any) => {
  aiAgentStore.setSortBy(action.value)
  showSortModal.value = false
}

// 应用筛选
const applyFilter = () => {
  showFilterModal.value = false
  // 这里可以根据筛选条件重新加载数据
  loadAgents()
}

// 重置筛选
const resetFilter = () => {
  filterOptions.value = {
    priceType: [],
    minRating: 0
  }
  showFilterModal.value = false
  loadAgents()
}

// 获取分类名称
const getCategoryName = (categoryId: string) => {
  const category = categories.value.find(c => c.id === categoryId)
  return category?.name || '未知分类'
}

// 获取排序文本
const getSortText = (sort: string) => {
  const sortOption = sortActions.value.find(s => s.value === sort)
  return sortOption?.name || '排序'
}

// 格式化使用次数
const formatUsageCount = (count: number) => {
  if (count >= 10000) {
    return `${(count / 10000).toFixed(1)}万`
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}k`
  }
  return count.toString()
}

// 页面下拉刷新
const onPullDownRefresh = async () => {
  try {
    currentPage.value = 1
    await Promise.all([
      aiAgentStore.loadFeaturedAgents(),
      loadAgents(1)
    ])
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '刷新失败',
      icon: 'error'
    })
  } finally {
    uni.stopPullDownRefresh()
  }
}

// 页面触底加载更多
const onReachBottom = () => {
  loadMore()
}

// 导出方法供页面使用
defineExpose({
  onPullDownRefresh,
  onReachBottom
})
</script>

<style lang="scss" scoped>
.market-page {
  min-height: 100vh;
  background-color: $bg-secondary;
}

// 搜索栏
.search-bar {
  background-color: $bg-primary;
  padding: $spacing-4;
  border-bottom: 1px solid $border-primary;
  display: flex;
  align-items: center;
  gap: $spacing-3;

  .search-input-container {
    flex: 1;
    display: flex;
    align-items: center;
    background-color: $bg-tertiary;
    border-radius: $radius-full;
    padding: $spacing-2 $spacing-4;
    gap: $spacing-2;

    .search-input {
      flex: 1;
      font-size: $font-size-base;
      color: $text-primary;
      background: transparent;
      border: none;
      outline: none;

      &::placeholder {
        color: $text-quaternary;
      }
    }
  }
}

// 分类标签
.category-tabs {
  background-color: $bg-primary;
  border-bottom: 1px solid $border-primary;

  .tabs-scroll {
    white-space: nowrap;

    .tabs-list {
      display: flex;
      padding: $spacing-3 $spacing-4;
      gap: $spacing-3;

      .tab-item {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        padding: $spacing-2 $spacing-4;
        border-radius: $radius-full;
        background-color: $bg-tertiary;
        border: 1px solid $border-primary;

        &.active {
          background-color: $primary;
          border-color: $primary;

          .tab-text {
            color: white;
          }
        }

        .tab-text {
          font-size: $font-size-sm;
          color: $text-secondary;
          white-space: nowrap;
        }
      }
    }
  }
}

// 推荐区域
.featured-section {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-4 $spacing-4 $spacing-2;

    .section-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-primary;
    }
  }

  .featured-scroll {
    white-space: nowrap;

    .featured-list {
      display: flex;
      padding: 0 $spacing-4 $spacing-4;
      gap: $spacing-3;

      .featured-item {
        flex-shrink: 0;
        width: 280rpx;
        background-color: $bg-tertiary;
        border-radius: $radius-lg;
        padding: $spacing-3;

        .featured-avatar {
          width: 100%;
          height: 160rpx;
          border-radius: $radius-md;
          margin-bottom: $spacing-3;
        }

        .featured-content {
          .featured-name {
            display: block;
            font-size: $font-size-base;
            font-weight: $font-weight-medium;
            color: $text-primary;
            margin-bottom: $spacing-1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .featured-desc {
            display: block;
            font-size: $font-size-sm;
            color: $text-secondary;
            line-height: $line-height-snug;
            margin-bottom: $spacing-2;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            white-space: normal;
          }

          .featured-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .rating {
              display: flex;
              align-items: center;
              gap: 4rpx;

              .rating-text {
                font-size: $font-size-xs;
                color: $text-tertiary;
              }
            }

            .usage-count {
              font-size: $font-size-xs;
              color: $text-quaternary;
            }
          }
        }
      }
    }
  }
}

// 智能体区域
.agents-section {
  background-color: $bg-primary;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-4 $spacing-4 $spacing-2;

    .section-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-primary;
    }

    .sort-options {
      display: flex;
      align-items: center;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: $spacing-12;

    .loading-text {
      font-size: $font-size-sm;
      color: $text-tertiary;
      margin-top: $spacing-3;
    }
  }

  // 智能体网格
  .agents-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-3;
    padding: 0 $spacing-4 $spacing-4;

    .agent-card {
      background-color: $bg-primary;
      border-radius: $radius-lg;
      overflow: hidden;
      box-shadow: $shadow-sm;
      border: 1px solid $border-primary;

      .card-header {
        position: relative;

        .agent-avatar {
          width: 100%;
          height: 200rpx;
          background-color: $bg-tertiary;
        }

        .agent-badge {
          position: absolute;
          top: $spacing-2;
          left: $spacing-2;
          background-color: $primary;
          border-radius: $radius-sm;
          padding: 4rpx 12rpx;

          .badge-text {
            font-size: $font-size-xs;
            color: white;
          }
        }

        .favorite-btn {
          position: absolute;
          top: $spacing-2;
          right: $spacing-2;
          width: 56rpx;
          height: 56rpx;
          background-color: rgba(255, 255, 255, 0.9);
          border-radius: $radius-full;
          display: flex;
          align-items: center;
          justify-content: center;
          backdrop-filter: blur(4px);
        }
      }

      .card-content {
        padding: $spacing-3;

        .agent-name {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .agent-desc {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          line-height: $line-height-snug;
          margin-bottom: $spacing-2;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          white-space: normal;
        }

        .agent-tags {
          display: flex;
          flex-wrap: wrap;
          gap: $spacing-1;
          margin-bottom: $spacing-2;

          .tag-item {
            background-color: $bg-tertiary;
            color: $text-tertiary;
            font-size: $font-size-xs;
            padding: 4rpx 12rpx;
            border-radius: $radius-full;
          }
        }

        .agent-meta {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .rating {
            display: flex;
            align-items: center;
            gap: 4rpx;

            .rating-text {
              font-size: $font-size-xs;
              color: $text-tertiary;
            }
          }

          .usage-count {
            font-size: $font-size-xs;
            color: $text-quaternary;
          }

          .price {
            .price-text {
              font-size: $font-size-sm;
              font-weight: $font-weight-medium;
              color: $primary;
            }
          }

          .free-badge {
            background-color: $success;
            border-radius: $radius-sm;
            padding: 2rpx 8rpx;

            .free-text {
              font-size: $font-size-xs;
              color: white;
            }
          }
        }
      }
    }
  }

  // 空状态
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: $spacing-12;

    .empty-text {
      font-size: $font-size-base;
      color: $text-secondary;
      margin: $spacing-4 0 $spacing-2;
    }

    .empty-desc {
      font-size: $font-size-sm;
      color: $text-tertiary;
    }
  }

  // 加载更多
  .load-more {
    text-align: center;
    padding: $spacing-4;
  }
}

// 筛选弹窗内容
.filter-content {
  padding: $spacing-4 0;

  .filter-group {
    margin-bottom: $spacing-6;

    .filter-label {
      display: block;
      font-size: $font-size-base;
      font-weight: $font-weight-medium;
      color: $text-primary;
      margin-bottom: $spacing-3;
    }

    .filter-options {
      padding-left: $spacing-2;
    }
  }
}

// 响应式适配
@media screen and (max-width: 480px) {
  .agents-grid {
    grid-template-columns: 1fr;

    .agent-card .card-header .agent-avatar {
      height: 240rpx;
    }
  }

  .featured-list .featured-item {
    width: 240rpx;
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .market-page {
    background-color: #1a1a1a;
  }

  .search-bar,
  .category-tabs,
  .featured-section,
  .agents-section {
    background-color: #2d2d2d;
    border-color: #404040;
  }

  .search-input-container,
  .tab-item,
  .featured-item,
  .agent-card {
    background-color: #404040;
    border-color: #555555;
  }

  .search-input {
    color: #ffffff;

    &::placeholder {
      color: #888888;
    }
  }

  .favorite-btn {
    background-color: rgba(45, 45, 45, 0.9);
  }
}
</style>
