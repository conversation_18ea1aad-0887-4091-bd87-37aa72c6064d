/**
 * API模块统一导出
 */

// 导入所有API模块
import userApi from './user'
import translationApi from './translation'
import chatApi from './chat'
import documentApi from './document'
import agentApi from './agent'
import commonApi from './common'

// 统一导出
export {
  userApi,
  translationApi,
  chatApi,
  documentApi,
  agentApi,
  commonApi
}

// 默认导出
export default {
  user: userApi,
  translation: translationApi,
  chat: chatApi,
  document: documentApi,
  agent: agentApi,
  common: commonApi
}
