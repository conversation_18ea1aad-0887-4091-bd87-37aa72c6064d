"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_modules_user = require("../../../store/modules/user.js");
if (!Array) {
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  const _component_u_button = common_vendor.resolveComponent("u-button");
  const _component_u_action_sheet = common_vendor.resolveComponent("u-action-sheet");
  const _component_u_picker = common_vendor.resolveComponent("u-picker");
  (_component_u_icon + _component_u_button + _component_u_action_sheet + _component_u_picker)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props, { expose: __expose }) {
    const userStore = store_modules_user.useUserStore();
    const userStats = common_vendor.ref(null);
    const showAvatarSheet = common_vendor.ref(false);
    const showLanguagePicker = common_vendor.ref(false);
    const showThemeSheet = common_vendor.ref(false);
    const appVersion = common_vendor.ref("1.0.0");
    const { userInfo, userDisplayName } = userStore;
    const quickActions = common_vendor.ref([
      {
        id: "favorites",
        title: "我的收藏",
        icon: "heart-fill",
        color: "#EF4444",
        badge: "12",
        path: "/pages/user/favorites/index"
      },
      {
        id: "history",
        title: "使用历史",
        icon: "clock-fill",
        color: "#2563EB",
        path: "/pages/user/history/index"
      },
      {
        id: "downloads",
        title: "我的下载",
        icon: "download",
        color: "#10B981",
        path: "/pages/user/downloads/index"
      },
      {
        id: "wallet",
        title: "我的钱包",
        icon: "wallet",
        color: "#F59E0B",
        path: "/pages/user/wallet/index"
      }
    ]);
    const myContentMenus = common_vendor.ref([
      {
        id: "translations",
        title: "翻译记录",
        icon: "globe",
        color: "#2563EB",
        count: 156,
        path: "/pages/user/translations/index"
      },
      {
        id: "digital-humans",
        title: "数字人对话",
        icon: "account-circle",
        color: "#10B981",
        count: 23,
        path: "/pages/user/digital-humans/index"
      },
      {
        id: "ai-agents",
        title: "AI智能体",
        icon: "robot",
        color: "#F59E0B",
        count: 8,
        path: "/pages/user/ai-agents/index"
      },
      {
        id: "documents",
        title: "文档转换",
        icon: "file-document",
        color: "#8B5CF6",
        count: 45,
        path: "/pages/user/documents/index"
      }
    ]);
    const settingsMenus = common_vendor.ref([
      {
        id: "account",
        title: "账号设置",
        icon: "account-settings",
        color: "#6B7280",
        path: "/pages/user/account/index"
      },
      {
        id: "privacy",
        title: "隐私设置",
        icon: "shield-check",
        color: "#059669",
        path: "/pages/user/privacy/index"
      },
      {
        id: "notifications",
        title: "消息通知",
        icon: "bell",
        color: "#DC2626",
        extra: "开启"
      },
      {
        id: "language",
        title: "语言设置",
        icon: "translate",
        color: "#2563EB",
        extra: "中文"
      },
      {
        id: "theme",
        title: "主题设置",
        icon: "palette",
        color: "#7C3AED",
        extra: "跟随系统"
      },
      {
        id: "cache",
        title: "清理缓存",
        icon: "delete-sweep",
        color: "#EF4444",
        extra: "125MB"
      }
    ]);
    const helpMenus = common_vendor.ref([
      {
        id: "help",
        title: "使用帮助",
        icon: "help-circle",
        color: "#2563EB",
        path: "/pages/help/index"
      },
      {
        id: "feedback",
        title: "意见反馈",
        icon: "message-text",
        color: "#10B981",
        path: "/pages/feedback/index"
      },
      {
        id: "about",
        title: "关于我们",
        icon: "information",
        color: "#6B7280",
        path: "/pages/about/index"
      },
      {
        id: "update",
        title: "检查更新",
        icon: "update",
        color: "#F59E0B",
        badge: true
      }
    ]);
    const avatarActions = common_vendor.ref([
      { name: "拍照", value: "camera" },
      { name: "从相册选择", value: "album" },
      { name: "查看头像", value: "view" }
    ]);
    const languageColumns = common_vendor.ref([[
      { text: "中文", value: "zh-CN" },
      { text: "English", value: "en-US" },
      { text: "日本語", value: "ja-JP" },
      { text: "한국어", value: "ko-KR" }
    ]]);
    const themeActions = common_vendor.ref([
      { name: "跟随系统", value: "auto" },
      { name: "浅色模式", value: "light" },
      { name: "深色模式", value: "dark" }
    ]);
    common_vendor.onMounted(async () => {
      await loadUserStats();
    });
    const loadUserStats = async () => {
      try {
        const stats = await userStore.getUserStats();
        if (stats) {
          userStats.value = stats;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/user/profile/index.vue:379", "加载用户统计失败:", error);
      }
    };
    const handleQuickAction = (action) => {
      if (action.path) {
        navigateTo(action.path);
      }
    };
    const handleSettingsMenu = (item) => {
      switch (item.id) {
        case "notifications":
          toggleNotifications();
          break;
        case "language":
          showLanguagePicker.value = true;
          break;
        case "theme":
          showThemeSheet.value = true;
          break;
        case "cache":
          clearCache();
          break;
        default:
          if (item.path) {
            navigateTo(item.path);
          }
      }
    };
    const handleHelpMenu = (item) => {
      switch (item.id) {
        case "update":
          checkForUpdate();
          break;
        default:
          if (item.path) {
            navigateTo(item.path);
          }
      }
    };
    const changeAvatar = () => {
      showAvatarSheet.value = true;
    };
    const handleAvatarAction = (action) => {
      showAvatarSheet.value = false;
      switch (action.value) {
        case "camera":
          chooseAvatar("camera");
          break;
        case "album":
          chooseAvatar("album");
          break;
        case "view":
          previewAvatar();
          break;
      }
    };
    const chooseAvatar = (sourceType) => {
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: [sourceType],
        success: async (res) => {
          const filePath = res.tempFilePaths[0];
          try {
            common_vendor.index.showLoading({ title: "上传中..." });
            const result = await userStore.uploadAvatar(filePath);
            common_vendor.index.hideLoading();
            if (result.success) {
              common_vendor.index.showToast({
                title: "头像更新成功",
                icon: "success"
              });
            }
          } catch (error) {
            common_vendor.index.hideLoading();
            common_vendor.index.__f__("error", "at pages/user/profile/index.vue:470", "上传头像失败:", error);
          }
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/user/profile/index.vue:474", "选择图片失败:", error);
        }
      });
    };
    const previewAvatar = () => {
      var _a;
      if ((_a = userInfo.value) == null ? void 0 : _a.avatar) {
        common_vendor.index.previewImage({
          urls: [userInfo.value.avatar],
          current: 0
        });
      }
    };
    const toggleNotifications = () => {
      common_vendor.index.showModal({
        title: "消息通知",
        content: "是否开启消息通知？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "设置成功",
              icon: "success"
            });
          }
        }
      });
    };
    const onLanguageConfirm = (value) => {
      const language = value[0].value;
      common_vendor.index.setStorageSync("app_language", language);
      const languageMenu = settingsMenus.value.find((m) => m.id === "language");
      if (languageMenu) {
        languageMenu.extra = value[0].text;
      }
      showLanguagePicker.value = false;
      common_vendor.index.showToast({
        title: "语言设置成功",
        icon: "success"
      });
    };
    const handleThemeAction = (action) => {
      showThemeSheet.value = false;
      common_vendor.index.setStorageSync("app_theme", action.value);
      const themeMenu = settingsMenus.value.find((m) => m.id === "theme");
      if (themeMenu) {
        themeMenu.extra = action.name;
      }
      common_vendor.index.showToast({
        title: "主题设置成功",
        icon: "success"
      });
    };
    const clearCache = () => {
      common_vendor.index.showModal({
        title: "清理缓存",
        content: "确定要清理应用缓存吗？这将删除临时文件和图片缓存。",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({ title: "清理中..." });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              const cacheMenu = settingsMenus.value.find((m) => m.id === "cache");
              if (cacheMenu) {
                cacheMenu.extra = "0MB";
              }
              common_vendor.index.showToast({
                title: "清理完成",
                icon: "success"
              });
            }, 2e3);
          }
        }
      });
    };
    const checkForUpdate = () => {
      common_vendor.index.showLoading({ title: "检查中..." });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "检查更新",
          content: "当前已是最新版本",
          showCancel: false,
          confirmText: "确定"
        });
      }, 1500);
    };
    const handleLogout = () => {
      common_vendor.index.showModal({
        title: "退出登录",
        content: "确定要退出当前账号吗？",
        success: async (res) => {
          if (res.confirm) {
            try {
              await userStore.logout();
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/user/profile/index.vue:602", "退出登录失败:", error);
            }
          }
        }
      });
    };
    const navigateTo = (path) => {
      common_vendor.index.navigateTo({
        url: path,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/user/profile/index.vue:614", "页面跳转失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "error"
          });
        }
      });
    };
    const onPullDownRefresh = async () => {
      try {
        await loadUserStats();
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "刷新失败",
          icon: "error"
        });
      } finally {
        common_vendor.index.stopPullDownRefresh();
      }
    };
    __expose({
      onPullDownRefresh
    });
    return (_ctx, _cache) => {
      var _a, _b, _c, _d, _e, _f;
      return common_vendor.e({
        a: ((_a = common_vendor.unref(userInfo)) == null ? void 0 : _a.avatar) || "/static/images/default-avatar.png",
        b: common_vendor.p({
          name: "camera",
          size: "16",
          color: "#fff"
        }),
        c: common_vendor.o(changeAvatar),
        d: common_vendor.t(common_vendor.unref(userDisplayName)),
        e: (_b = common_vendor.unref(userInfo)) == null ? void 0 : _b.email
      }, ((_c = common_vendor.unref(userInfo)) == null ? void 0 : _c.email) ? {
        f: common_vendor.t(common_vendor.unref(userInfo).email)
      } : {}, {
        g: common_vendor.t(((_d = userStats.value) == null ? void 0 : _d.total_translations) || 0),
        h: common_vendor.t(((_e = userStats.value) == null ? void 0 : _e.total_conversations) || 0),
        i: common_vendor.t(((_f = userStats.value) == null ? void 0 : _f.total_agents) || 0),
        j: common_vendor.f(quickActions.value, (action, k0, i0) => {
          return common_vendor.e({
            a: "db22c714-1-" + i0,
            b: common_vendor.p({
              name: action.icon,
              color: "#fff",
              size: "20"
            }),
            c: action.color,
            d: common_vendor.t(action.title),
            e: action.badge
          }, action.badge ? {
            f: common_vendor.t(action.badge)
          } : {}, {
            g: action.id,
            h: common_vendor.o(($event) => handleQuickAction(action), action.id)
          });
        }),
        k: common_vendor.f(myContentMenus.value, (item, k0, i0) => {
          return common_vendor.e({
            a: "db22c714-2-" + i0,
            b: common_vendor.p({
              name: item.icon,
              color: item.color,
              size: "20"
            }),
            c: common_vendor.t(item.title),
            d: item.count !== void 0
          }, item.count !== void 0 ? {
            e: common_vendor.t(item.count)
          } : {}, {
            f: "db22c714-3-" + i0,
            g: item.id,
            h: common_vendor.o(($event) => navigateTo(item.path), item.id)
          });
        }),
        l: common_vendor.p({
          name: "arrow-right",
          color: "#C4C4C4",
          size: "16"
        }),
        m: common_vendor.f(settingsMenus.value, (item, k0, i0) => {
          return common_vendor.e({
            a: "db22c714-4-" + i0,
            b: common_vendor.p({
              name: item.icon,
              color: item.color,
              size: "20"
            }),
            c: common_vendor.t(item.title),
            d: item.extra
          }, item.extra ? {
            e: common_vendor.t(item.extra)
          } : {}, {
            f: "db22c714-5-" + i0,
            g: item.id,
            h: common_vendor.o(($event) => handleSettingsMenu(item), item.id)
          });
        }),
        n: common_vendor.p({
          name: "arrow-right",
          color: "#C4C4C4",
          size: "16"
        }),
        o: common_vendor.f(helpMenus.value, (item, k0, i0) => {
          return common_vendor.e({
            a: "db22c714-6-" + i0,
            b: common_vendor.p({
              name: item.icon,
              color: item.color,
              size: "20"
            }),
            c: common_vendor.t(item.title),
            d: item.badge
          }, item.badge ? {} : {}, {
            e: "db22c714-7-" + i0,
            f: item.id,
            g: common_vendor.o(($event) => handleHelpMenu(item), item.id)
          });
        }),
        p: common_vendor.p({
          name: "arrow-right",
          color: "#C4C4C4",
          size: "16"
        }),
        q: common_vendor.o(handleLogout),
        r: common_vendor.p({
          type: "error",
          size: "large",
          ["custom-style"]: {
            width: "100%",
            marginTop: "40rpx"
          }
        }),
        s: common_vendor.t(appVersion.value),
        t: common_vendor.o(handleAvatarAction),
        v: common_vendor.o(($event) => showAvatarSheet.value = $event),
        w: common_vendor.p({
          actions: avatarActions.value,
          modelValue: showAvatarSheet.value
        }),
        x: common_vendor.o(onLanguageConfirm),
        y: common_vendor.o(($event) => showLanguagePicker.value = false),
        z: common_vendor.o(($event) => showLanguagePicker.value = $event),
        A: common_vendor.p({
          columns: languageColumns.value,
          modelValue: showLanguagePicker.value
        }),
        B: common_vendor.o(handleThemeAction),
        C: common_vendor.o(($event) => showThemeSheet.value = $event),
        D: common_vendor.p({
          actions: themeActions.value,
          modelValue: showThemeSheet.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-db22c714"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/user/profile/index.js.map
