/**
 * 唇形同步工具函数
 * 用于处理数字人的唇形同步动画
 */

/**
 * 创建唇形同步渲染器
 * @param {HTMLCanvasElement} canvas - 画布元素
 * @param {Object} options - 配置选项
 * @returns {Object} 渲染器对象
 */
export function createLipSyncRenderer(canvas, options = {}) {
  const ctx = canvas.getContext('2d');
  const defaultOptions = {
    width: 400,
    height: 300,
    frameRate: 30,
    smoothing: true,
    ...options
  };

  let animationId = null;
  let currentFrame = 0;
  let frames = [];
  let isPlaying = false;

  const renderer = {
    /**
     * 设置帧数据
     * @param {Array} frameData - 帧数据数组
     */
    setFrames(frameData) {
      frames = frameData || [];
      currentFrame = 0;
    },

    /**
     * 开始播放动画
     */
    play() {
      if (isPlaying || frames.length === 0) return;
      
      isPlaying = true;
      currentFrame = 0;
      
      const animate = () => {
        if (!isPlaying) return;
        
        // 清除画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 绘制当前帧
        if (frames[currentFrame]) {
          drawFrame(ctx, frames[currentFrame], defaultOptions);
        }
        
        // 更新帧索引
        currentFrame = (currentFrame + 1) % frames.length;
        
        // 继续动画
        animationId = requestAnimationFrame(animate);
      };
      
      animate();
    },

    /**
     * 停止播放动画
     */
    stop() {
      isPlaying = false;
      if (animationId) {
        cancelAnimationFrame(animationId);
        animationId = null;
      }
    },

    /**
     * 暂停播放
     */
    pause() {
      isPlaying = false;
      if (animationId) {
        cancelAnimationFrame(animationId);
        animationId = null;
      }
    },

    /**
     * 恢复播放
     */
    resume() {
      if (!isPlaying && frames.length > 0) {
        this.play();
      }
    },

    /**
     * 获取当前状态
     */
    getState() {
      return {
        isPlaying,
        currentFrame,
        totalFrames: frames.length
      };
    },

    /**
     * 销毁渲染器
     */
    destroy() {
      this.stop();
      frames = [];
      currentFrame = 0;
    }
  };

  return renderer;
}

/**
 * 绘制单帧
 * @param {CanvasRenderingContext2D} ctx - 画布上下文
 * @param {Object} frameData - 帧数据
 * @param {Object} options - 配置选项
 */
function drawFrame(ctx, frameData, options) {
  const { width, height } = options;
  
  // 设置基本样式
  ctx.fillStyle = '#f0f0f0';
  ctx.fillRect(0, 0, width, height);
  
  // 绘制嘴巴轮廓（简化版本）
  if (frameData.mouth) {
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    const mouth = frameData.mouth;
    const centerX = width / 2;
    const centerY = height * 0.7;
    
    // 绘制嘴巴椭圆
    ctx.ellipse(
      centerX, 
      centerY, 
      mouth.width || 30, 
      mouth.height || 15, 
      0, 
      0, 
      2 * Math.PI
    );
    
    ctx.stroke();
    
    // 如果嘴巴张开，填充内部
    if (mouth.openness > 0.3) {
      ctx.fillStyle = '#222';
      ctx.fill();
    }
  }
}

/**
 * 从音频数据创建唇形同步帧
 * @param {ArrayBuffer} audioData - 音频数据
 * @param {Object} options - 配置选项
 * @returns {Promise<Array>} 帧数据数组
 */
export async function createLipSyncFrames(audioData, options = {}) {
  const defaultOptions = {
    frameRate: 30,
    duration: 3.0, // 默认3秒
    sensitivity: 0.5,
    ...options
  };

  try {
    // 创建音频上下文
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const audioBuffer = await audioContext.decodeAudioData(audioData.slice());
    
    // 分析音频数据
    const channelData = audioBuffer.getChannelData(0);
    const sampleRate = audioBuffer.sampleRate;
    const totalFrames = Math.floor(defaultOptions.frameRate * defaultOptions.duration);
    const samplesPerFrame = Math.floor(channelData.length / totalFrames);
    
    const frames = [];
    
    for (let i = 0; i < totalFrames; i++) {
      const startSample = i * samplesPerFrame;
      const endSample = Math.min(startSample + samplesPerFrame, channelData.length);
      
      // 计算当前帧的音频强度
      let sum = 0;
      for (let j = startSample; j < endSample; j++) {
        sum += Math.abs(channelData[j]);
      }
      const amplitude = sum / (endSample - startSample);
      
      // 根据音频强度计算嘴巴开合程度
      const openness = Math.min(amplitude * defaultOptions.sensitivity * 10, 1.0);
      
      frames.push({
        timestamp: i / defaultOptions.frameRate,
        mouth: {
          width: 30 + openness * 20,
          height: 15 + openness * 25,
          openness: openness
        },
        amplitude: amplitude
      });
    }
    
    return frames;
    
  } catch (error) {
    console.warn('音频分析失败，使用默认动画:', error);
    
    // 返回默认的简单动画帧
    const totalFrames = Math.floor(defaultOptions.frameRate * defaultOptions.duration);
    const frames = [];
    
    for (let i = 0; i < totalFrames; i++) {
      const t = i / totalFrames;
      const openness = Math.sin(t * Math.PI * 4) * 0.5 + 0.5; // 简单的正弦波动画
      
      frames.push({
        timestamp: i / defaultOptions.frameRate,
        mouth: {
          width: 30 + openness * 15,
          height: 15 + openness * 20,
          openness: openness
        },
        amplitude: openness
      });
    }
    
    return frames;
  }
}

/**
 * 从文本创建简单的唇形同步帧
 * @param {string} text - 文本内容
 * @param {Object} options - 配置选项
 * @returns {Array} 帧数据数组
 */
export function createLipSyncFramesFromText(text, options = {}) {
  const defaultOptions = {
    frameRate: 30,
    wordsPerSecond: 3,
    ...options
  };

  const words = text.split(/\s+/).filter(word => word.length > 0);
  const totalDuration = words.length / defaultOptions.wordsPerSecond;
  const totalFrames = Math.floor(defaultOptions.frameRate * totalDuration);
  
  const frames = [];
  
  for (let i = 0; i < totalFrames; i++) {
    const t = i / totalFrames;
    const wordIndex = Math.floor(t * words.length);
    const word = words[wordIndex] || '';
    
    // 根据字符类型调整嘴型
    let openness = 0.3; // 默认开合程度
    
    if (word.length > 0) {
      const char = word[0].toLowerCase();
      // 元音字母嘴巴张得更大
      if ('aeiou'.includes(char)) {
        openness = 0.8;
      } else if ('bpmf'.includes(char)) {
        // 双唇音嘴巴闭合
        openness = 0.1;
      } else {
        openness = 0.5;
      }
    }
    
    // 添加一些随机变化使动画更自然
    openness += (Math.random() - 0.5) * 0.2;
    openness = Math.max(0, Math.min(1, openness));
    
    frames.push({
      timestamp: i / defaultOptions.frameRate,
      mouth: {
        width: 30 + openness * 20,
        height: 15 + openness * 25,
        openness: openness
      },
      word: word,
      amplitude: openness
    });
  }
  
  return frames;
}

/**
 * 平滑处理帧数据
 * @param {Array} frames - 原始帧数据
 * @param {number} smoothingFactor - 平滑因子 (0-1)
 * @returns {Array} 平滑后的帧数据
 */
export function smoothFrames(frames, smoothingFactor = 0.3) {
  if (frames.length < 2) return frames;
  
  const smoothedFrames = [...frames];
  
  for (let i = 1; i < smoothedFrames.length; i++) {
    const current = smoothedFrames[i];
    const previous = smoothedFrames[i - 1];
    
    // 平滑嘴巴参数
    if (current.mouth && previous.mouth) {
      current.mouth.width = lerp(previous.mouth.width, current.mouth.width, smoothingFactor);
      current.mouth.height = lerp(previous.mouth.height, current.mouth.height, smoothingFactor);
      current.mouth.openness = lerp(previous.mouth.openness, current.mouth.openness, smoothingFactor);
    }
  }
  
  return smoothedFrames;
}

/**
 * 线性插值函数
 * @param {number} a - 起始值
 * @param {number} b - 结束值
 * @param {number} t - 插值因子 (0-1)
 * @returns {number} 插值结果
 */
function lerp(a, b, t) {
  return a + (b - a) * t;
}
