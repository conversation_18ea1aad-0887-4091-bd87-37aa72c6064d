<template>
  <view class="qrcode-generator-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">二维码生成器</text>
      <text class="page-desc">快速生成各种类型的二维码</text>
    </view>

    <!-- 二维码类型选择 -->
    <view class="type-selector">
      <view class="section-title">
        <text class="title-text">选择二维码类型</text>
      </view>
      
      <view class="type-tabs">
        <view 
          class="tab-item"
          v-for="type in qrcodeTypes"
          :key="type.id"
          :class="{ active: selectedType === type.id }"
          @click="selectType(type.id)"
        >
          <u-icon :name="type.icon" size="20" :color="selectedType === type.id ? '#2563EB' : '#6B7280'"></u-icon>
          <text class="tab-text">{{ type.name }}</text>
        </view>
      </view>
    </view>

    <!-- 内容输入区域 -->
    <view class="content-input">
      <view class="section-title">
        <text class="title-text">{{ getInputTitle() }}</text>
        <text class="title-desc">{{ getInputDescription() }}</text>
      </view>
      
      <view class="input-area">
        <!-- 文本输入 -->
        <textarea 
          v-if="selectedType === 'text'"
          class="text-input"
          v-model="qrcodeContent.text"
          placeholder="请输入要生成二维码的文本内容"
          :maxlength="500"
        ></textarea>
        
        <!-- URL输入 -->
        <input 
          v-if="selectedType === 'url'"
          class="url-input"
          v-model="qrcodeContent.url"
          placeholder="https://example.com"
          type="url"
        />
        
        <!-- WiFi信息输入 -->
        <view v-if="selectedType === 'wifi'" class="wifi-inputs">
          <input 
            class="wifi-input"
            v-model="qrcodeContent.wifi.ssid"
            placeholder="WiFi名称(SSID)"
          />
          <input 
            class="wifi-input"
            v-model="qrcodeContent.wifi.password"
            placeholder="WiFi密码"
            type="password"
          />
          <view class="wifi-security">
            <text class="security-label">加密方式:</text>
            <u-radio-group v-model="qrcodeContent.wifi.security">
              <u-radio name="WPA" label="WPA/WPA2"></u-radio>
              <u-radio name="WEP" label="WEP"></u-radio>
              <u-radio name="nopass" label="无密码"></u-radio>
            </u-radio-group>
          </view>
        </view>
        
        <!-- 联系人信息输入 -->
        <view v-if="selectedType === 'contact'" class="contact-inputs">
          <input 
            class="contact-input"
            v-model="qrcodeContent.contact.name"
            placeholder="姓名"
          />
          <input 
            class="contact-input"
            v-model="qrcodeContent.contact.phone"
            placeholder="电话号码"
            type="tel"
          />
          <input 
            class="contact-input"
            v-model="qrcodeContent.contact.email"
            placeholder="邮箱地址"
            type="email"
          />
          <input 
            class="contact-input"
            v-model="qrcodeContent.contact.organization"
            placeholder="公司/组织"
          />
        </view>
        
        <!-- 短信输入 -->
        <view v-if="selectedType === 'sms'" class="sms-inputs">
          <input 
            class="sms-input"
            v-model="qrcodeContent.sms.phone"
            placeholder="手机号码"
            type="tel"
          />
          <textarea 
            class="sms-input"
            v-model="qrcodeContent.sms.message"
            placeholder="短信内容"
            :maxlength="160"
          ></textarea>
        </view>
        
        <!-- 邮件输入 -->
        <view v-if="selectedType === 'email'" class="email-inputs">
          <input 
            class="email-input"
            v-model="qrcodeContent.email.to"
            placeholder="收件人邮箱"
            type="email"
          />
          <input 
            class="email-input"
            v-model="qrcodeContent.email.subject"
            placeholder="邮件主题"
          />
          <textarea 
            class="email-input"
            v-model="qrcodeContent.email.body"
            placeholder="邮件内容"
            :maxlength="300"
          ></textarea>
        </view>
      </view>
    </view>

    <!-- 二维码样式设置 -->
    <view class="style-settings" v-if="hasContent()">
      <view class="section-title">
        <text class="title-text">样式设置</text>
      </view>
      
      <view class="settings-content">
        <!-- 尺寸设置 -->
        <view class="setting-item">
          <text class="setting-label">二维码尺寸: {{ qrcodeStyle.size }}px</text>
          <u-slider 
            v-model="qrcodeStyle.size" 
            :min="200" 
            :max="800" 
            :step="50"
            active-color="#2563EB"
            @change="generateQRCode"
          ></u-slider>
        </view>
        
        <!-- 颜色设置 -->
        <view class="setting-item">
          <text class="setting-label">前景色</text>
          <view class="color-picker">
            <view 
              class="color-option"
              v-for="color in colorOptions"
              :key="color"
              :style="{ backgroundColor: color }"
              :class="{ active: qrcodeStyle.foreground === color }"
              @click="selectColor('foreground', color)"
            ></view>
          </view>
        </view>
        
        <view class="setting-item">
          <text class="setting-label">背景色</text>
          <view class="color-picker">
            <view 
              class="color-option"
              v-for="color in backgroundColors"
              :key="color"
              :style="{ backgroundColor: color }"
              :class="{ active: qrcodeStyle.background === color }"
              @click="selectColor('background', color)"
            ></view>
          </view>
        </view>
        
        <!-- 容错级别 -->
        <view class="setting-item">
          <text class="setting-label">容错级别</text>
          <u-radio-group v-model="qrcodeStyle.errorLevel" @change="generateQRCode">
            <u-radio name="L" label="低 (7%)"></u-radio>
            <u-radio name="M" label="中 (15%)"></u-radio>
            <u-radio name="Q" label="较高 (25%)"></u-radio>
            <u-radio name="H" label="高 (30%)"></u-radio>
          </u-radio-group>
        </view>
        
        <!-- Logo设置 -->
        <view class="setting-item">
          <text class="setting-label">添加Logo</text>
          <u-switch v-model="qrcodeStyle.withLogo" @change="generateQRCode"></u-switch>
          
          <view v-if="qrcodeStyle.withLogo" class="logo-upload">
            <view class="upload-btn" @click="chooseLogo">
              <u-icon name="image" size="20" color="#2563EB"></u-icon>
              <text class="upload-text">选择Logo</text>
            </view>
            <image 
              v-if="qrcodeStyle.logoUrl" 
              class="logo-preview" 
              :src="qrcodeStyle.logoUrl" 
              mode="aspectFit"
            ></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 二维码预览 -->
    <view class="qrcode-preview" v-if="generatedQRCode">
      <view class="section-title">
        <text class="title-text">二维码预览</text>
        <view class="preview-actions">
          <u-icon name="download" size="18" color="#2563EB" @click="downloadQRCode"></u-icon>
          <u-icon name="share" size="18" color="#2563EB" margin-left="12" @click="shareQRCode"></u-icon>
        </view>
      </view>
      
      <view class="preview-content">
        <view class="qrcode-container">
          <canvas 
            class="qrcode-canvas"
            canvas-id="qrcode"
            :style="{ width: qrcodeStyle.size + 'rpx', height: qrcodeStyle.size + 'rpx' }"
          ></canvas>
        </view>
        
        <view class="qrcode-info">
          <text class="info-item">类型: {{ getTypeName(selectedType) }}</text>
          <text class="info-item">尺寸: {{ qrcodeStyle.size }}×{{ qrcodeStyle.size }}px</text>
          <text class="info-item">容错级别: {{ qrcodeStyle.errorLevel }}</text>
        </view>
      </view>
    </view>

    <!-- 生成历史 -->
    <view class="generation-history" v-if="qrcodeHistory.length > 0">
      <view class="section-title">
        <text class="title-text">生成历史</text>
        <u-button 
          type="text" 
          text="清空" 
          size="small"
          @click="clearHistory"
          :custom-style="{ color: '#EF4444', fontSize: '24rpx' }"
        ></u-button>
      </view>
      
      <view class="history-list">
        <view 
          class="history-item"
          v-for="item in qrcodeHistory.slice(0, 5)"
          :key="item.id"
          @click="loadHistoryItem(item)"
        >
          <image class="history-qrcode" :src="item.qrcodeUrl" mode="aspectFit"></image>
          <view class="history-content">
            <text class="history-type">{{ getTypeName(item.type) }}</text>
            <text class="history-desc">{{ getHistoryDescription(item) }}</text>
            <text class="history-time">{{ formatTime(item.createdAt) }}</text>
          </view>
          <view class="history-actions">
            <u-icon name="download" size="16" color="#6B7280"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 生成按钮 -->
    <view class="generate-button" v-if="hasContent() && !generatedQRCode">
      <u-button 
        type="primary" 
        size="large"
        :loading="isGenerating"
        @click="generateQRCode"
        :custom-style="{ width: '100%' }"
      >
        {{ isGenerating ? '生成中...' : '生成二维码' }}
      </u-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import dayjs from 'dayjs'

// 响应式数据
const selectedType = ref<string>('text')
const isGenerating = ref<boolean>(false)
const generatedQRCode = ref<string>('')
const qrcodeHistory = ref<any[]>([])

// 二维码内容
const qrcodeContent = ref({
  text: '',
  url: '',
  wifi: {
    ssid: '',
    password: '',
    security: 'WPA'
  },
  contact: {
    name: '',
    phone: '',
    email: '',
    organization: ''
  },
  sms: {
    phone: '',
    message: ''
  },
  email: {
    to: '',
    subject: '',
    body: ''
  }
})

// 二维码样式
const qrcodeStyle = ref({
  size: 400,
  foreground: '#000000',
  background: '#FFFFFF',
  errorLevel: 'M',
  withLogo: false,
  logoUrl: ''
})

// 二维码类型
const qrcodeTypes = ref([
  {
    id: 'text',
    name: '文本',
    icon: 'file-text'
  },
  {
    id: 'url',
    name: '网址',
    icon: 'link'
  },
  {
    id: 'wifi',
    name: 'WiFi',
    icon: 'wifi'
  },
  {
    id: 'contact',
    name: '联系人',
    icon: 'account'
  },
  {
    id: 'sms',
    name: '短信',
    icon: 'message'
  },
  {
    id: 'email',
    name: '邮件',
    icon: 'email'
  }
])

// 颜色选项
const colorOptions = ref([
  '#000000', '#2563EB', '#DC2626', '#059669',
  '#D97706', '#7C3AED', '#DB2777', '#0891B2'
])

const backgroundColors = ref([
  '#FFFFFF', '#F3F4F6', '#FEF3C7', '#DBEAFE',
  '#DCFCE7', '#FDE2E7', '#F3E8FF', '#E0F2FE'
])

// 页面加载
onMounted(() => {
  loadQRCodeHistory()
})

// 选择类型
const selectType = (typeId: string) => {
  selectedType.value = typeId
  generatedQRCode.value = ''
}

// 获取输入标题
const getInputTitle = () => {
  const typeMap: Record<string, string> = {
    text: '输入文本内容',
    url: '输入网址',
    wifi: '输入WiFi信息',
    contact: '输入联系人信息',
    sms: '输入短信信息',
    email: '输入邮件信息'
  }
  return typeMap[selectedType.value] || '输入内容'
}

// 获取输入描述
const getInputDescription = () => {
  const descMap: Record<string, string> = {
    text: '支持中英文、数字、符号等',
    url: '请输入完整的网址，包含http://或https://',
    wifi: '生成WiFi连接二维码，扫码即可连接',
    contact: '生成联系人信息，扫码即可保存到通讯录',
    sms: '生成短信二维码，扫码即可发送短信',
    email: '生成邮件二维码，扫码即可发送邮件'
  }
  return descMap[selectedType.value] || ''
}

// 检查是否有内容
const hasContent = () => {
  switch (selectedType.value) {
    case 'text':
      return qrcodeContent.value.text.trim() !== ''
    case 'url':
      return qrcodeContent.value.url.trim() !== ''
    case 'wifi':
      return qrcodeContent.value.wifi.ssid.trim() !== ''
    case 'contact':
      return qrcodeContent.value.contact.name.trim() !== '' ||
             qrcodeContent.value.contact.phone.trim() !== ''
    case 'sms':
      return qrcodeContent.value.sms.phone.trim() !== ''
    case 'email':
      return qrcodeContent.value.email.to.trim() !== ''
    default:
      return false
  }
}

// 选择颜色
const selectColor = (type: 'foreground' | 'background', color: string) => {
  qrcodeStyle.value[type] = color
  if (generatedQRCode.value) {
    generateQRCode()
  }
}

// 选择Logo
const chooseLogo = () => {
  uni.chooseImage({
    count: 1,
    sourceType: ['album'],
    success: (res) => {
      qrcodeStyle.value.logoUrl = res.tempFilePaths[0]
      if (generatedQRCode.value) {
        generateQRCode()
      }
    }
  })
}

// 生成二维码内容字符串
const generateContentString = () => {
  switch (selectedType.value) {
    case 'text':
      return qrcodeContent.value.text
    case 'url':
      return qrcodeContent.value.url
    case 'wifi':
      const wifi = qrcodeContent.value.wifi
      return `WIFI:T:${wifi.security};S:${wifi.ssid};P:${wifi.password};;`
    case 'contact':
      const contact = qrcodeContent.value.contact
      return `BEGIN:VCARD\nVERSION:3.0\nFN:${contact.name}\nTEL:${contact.phone}\nEMAIL:${contact.email}\nORG:${contact.organization}\nEND:VCARD`
    case 'sms':
      const sms = qrcodeContent.value.sms
      return `SMSTO:${sms.phone}:${sms.message}`
    case 'email':
      const email = qrcodeContent.value.email
      return `mailto:${email.to}?subject=${encodeURIComponent(email.subject)}&body=${encodeURIComponent(email.body)}`
    default:
      return ''
  }
}

// 生成二维码
const generateQRCode = async () => {
  if (!hasContent()) return

  try {
    isGenerating.value = true

    const contentString = generateContentString()

    // 模拟二维码生成
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 这里应该使用真实的二维码生成库，比如 qrcode.js
    // 现在使用模拟的二维码图片
    const mockQRCodeUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='

    generatedQRCode.value = mockQRCodeUrl

    // 添加到历史记录
    const historyItem = {
      id: Date.now().toString(),
      type: selectedType.value,
      content: contentString,
      qrcodeUrl: mockQRCodeUrl,
      style: { ...qrcodeStyle.value },
      createdAt: new Date().toISOString()
    }

    qrcodeHistory.value.unshift(historyItem)
    saveQRCodeHistory()

    uni.showToast({
      title: '生成成功',
      icon: 'success'
    })

  } catch (error) {
    console.error('生成二维码失败:', error)
    uni.showToast({
      title: '生成失败',
      icon: 'error'
    })
  } finally {
    isGenerating.value = false
  }
}

// 下载二维码
const downloadQRCode = () => {
  if (!generatedQRCode.value) return

  uni.showToast({
    title: '开始下载',
    icon: 'success'
  })
}

// 分享二维码
const shareQRCode = () => {
  if (!generatedQRCode.value) return

  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 1,
    imageUrl: generatedQRCode.value,
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    }
  })
}

// 获取类型名称
const getTypeName = (typeId: string) => {
  const type = qrcodeTypes.value.find(t => t.id === typeId)
  return type?.name || '未知类型'
}

// 获取历史描述
const getHistoryDescription = (item: any) => {
  switch (item.type) {
    case 'text':
      return item.content.substring(0, 20) + (item.content.length > 20 ? '...' : '')
    case 'url':
      return item.content
    case 'wifi':
      return `WiFi: ${item.content.match(/S:([^;]+)/)?.[1] || ''}`
    case 'contact':
      return `联系人: ${item.content.match(/FN:([^\n]+)/)?.[1] || ''}`
    case 'sms':
      return `短信: ${item.content.match(/SMSTO:([^:]+)/)?.[1] || ''}`
    case 'email':
      return `邮件: ${item.content.match(/mailto:([^?]+)/)?.[1] || ''}`
    default:
      return item.content.substring(0, 20)
  }
}

// 加载历史项目
const loadHistoryItem = (item: any) => {
  selectedType.value = item.type

  // 根据类型恢复内容
  switch (item.type) {
    case 'text':
      qrcodeContent.value.text = item.content
      break
    case 'url':
      qrcodeContent.value.url = item.content
      break
    // 其他类型的恢复逻辑...
  }

  // 恢复样式
  qrcodeStyle.value = { ...item.style }
  generatedQRCode.value = item.qrcodeUrl
}

// 工具函数
const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

// 历史记录相关
const saveQRCodeHistory = () => {
  uni.setStorageSync('qrcode_generation_history', qrcodeHistory.value)
}

const loadQRCodeHistory = () => {
  const history = uni.getStorageSync('qrcode_generation_history') || []
  qrcodeHistory.value = history
}

const clearHistory = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有生成历史吗？',
    success: (res) => {
      if (res.confirm) {
        qrcodeHistory.value = []
        saveQRCodeHistory()
        uni.showToast({
          title: '清空成功',
          icon: 'success'
        })
      }
    }
  })
}

// 页面下拉刷新
const onPullDownRefresh = () => {
  loadQRCodeHistory()
  uni.stopPullDownRefresh()
}

// 导出方法供页面使用
defineExpose({
  onPullDownRefresh
})
</script>

<style lang="scss" scoped>
.qrcode-generator-page {
  min-height: 100vh;
  background-color: $bg-secondary;
}

// 页面头部
.page-header {
  background-color: $bg-primary;
  padding: $spacing-6 $spacing-4;
  text-align: center;
  border-bottom: 1px solid $border-primary;

  .page-title {
    display: block;
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: $spacing-2;
  }

  .page-desc {
    font-size: $font-size-base;
    color: $text-secondary;
  }
}

// 通用区块标题
.section-title {
  padding: $spacing-4 $spacing-4 $spacing-3;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title-text {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
  }

  .title-desc {
    font-size: $font-size-sm;
    color: $text-tertiary;
  }

  .preview-actions {
    display: flex;
    align-items: center;
    gap: $spacing-2;
  }
}

// 类型选择
.type-selector {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .type-tabs {
    display: flex;
    padding: 0 $spacing-4 $spacing-4;
    gap: $spacing-2;
    overflow-x: auto;

    .tab-item {
      flex: 0 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: $spacing-3;
      background-color: $bg-tertiary;
      border-radius: $radius-lg;
      border: 2px solid transparent;
      min-width: 120rpx;

      &.active {
        border-color: $primary;
        background-color: $primary-50;
      }

      .tab-text {
        font-size: $font-size-sm;
        color: $text-primary;
        margin-top: $spacing-1;
      }
    }
  }
}

// 内容输入
.content-input {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .input-area {
    padding: 0 $spacing-4 $spacing-4;

    .text-input,
    .url-input {
      width: 100%;
      min-height: 200rpx;
      padding: $spacing-3;
      border: 1px solid $border-primary;
      border-radius: $radius-md;
      font-size: $font-size-base;
      color: $text-primary;
      background-color: $bg-tertiary;

      &::placeholder {
        color: $text-quaternary;
      }
    }

    .url-input {
      min-height: 80rpx;
    }

    .wifi-inputs,
    .contact-inputs,
    .sms-inputs,
    .email-inputs {
      display: flex;
      flex-direction: column;
      gap: $spacing-3;

      .wifi-input,
      .contact-input,
      .sms-input,
      .email-input {
        width: 100%;
        padding: $spacing-3;
        border: 1px solid $border-primary;
        border-radius: $radius-md;
        font-size: $font-size-base;
        color: $text-primary;
        background-color: $bg-tertiary;

        &::placeholder {
          color: $text-quaternary;
        }
      }

      .wifi-security {
        .security-label {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-2;
        }
      }
    }
  }
}

// 样式设置
.style-settings {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .settings-content {
    padding: 0 $spacing-4 $spacing-4;

    .setting-item {
      margin-bottom: $spacing-6;

      .setting-label {
        display: block;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-3;
      }

      .color-picker {
        display: flex;
        gap: $spacing-2;
        flex-wrap: wrap;

        .color-option {
          width: 60rpx;
          height: 60rpx;
          border-radius: $radius-md;
          border: 2px solid transparent;

          &.active {
            border-color: $primary;
          }
        }
      }

      .logo-upload {
        margin-top: $spacing-3;

        .upload-btn {
          display: flex;
          align-items: center;
          gap: $spacing-2;
          padding: $spacing-3;
          background-color: $bg-tertiary;
          border: 1px dashed $border-primary;
          border-radius: $radius-md;

          .upload-text {
            font-size: $font-size-sm;
            color: $text-secondary;
          }
        }

        .logo-preview {
          width: 120rpx;
          height: 120rpx;
          margin-top: $spacing-2;
          border-radius: $radius-md;
          background-color: $bg-tertiary;
        }
      }
    }
  }
}

// 二维码预览
.qrcode-preview {
  background-color: $bg-primary;
  margin-bottom: $spacing-3;

  .preview-content {
    padding: 0 $spacing-4 $spacing-4;

    .qrcode-container {
      display: flex;
      justify-content: center;
      margin-bottom: $spacing-4;

      .qrcode-canvas {
        border: 1px solid $border-primary;
        border-radius: $radius-md;
        background-color: $bg-tertiary;
      }
    }

    .qrcode-info {
      background-color: $bg-tertiary;
      border-radius: $radius-lg;
      padding: $spacing-4;

      .info-item {
        display: block;
        font-size: $font-size-sm;
        color: $text-secondary;
        margin-bottom: $spacing-1;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 生成历史
.generation-history {
  background-color: $bg-primary;

  .history-list {
    padding: 0 $spacing-4 $spacing-4;

    .history-item {
      display: flex;
      align-items: center;
      gap: $spacing-3;
      padding: $spacing-3;
      background-color: $bg-tertiary;
      border-radius: $radius-lg;
      margin-bottom: $spacing-2;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        background-color: $gray-200;
      }

      .history-qrcode {
        width: 80rpx;
        height: 80rpx;
        border-radius: $radius-md;
        background-color: $bg-quaternary;
      }

      .history-content {
        flex: 1;

        .history-type {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-1;
        }

        .history-desc {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .history-time {
          font-size: $font-size-xs;
          color: $text-tertiary;
        }
      }

      .history-actions {
        padding: $spacing-2;
      }
    }
  }
}

// 生成按钮
.generate-button {
  padding: $spacing-4;
}

// 响应式适配
@media screen and (max-width: 480px) {
  .type-tabs {
    .tab-item {
      min-width: 100rpx;
      padding: $spacing-2;
    }
  }

  .color-picker {
    .color-option {
      width: 48rpx;
      height: 48rpx;
    }
  }

  .qrcode-container {
    .qrcode-canvas {
      max-width: 100%;
      height: auto;
    }
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .qrcode-generator-page {
    background-color: #1a1a1a;
  }

  .page-header,
  .type-selector,
  .content-input,
  .style-settings,
  .qrcode-preview,
  .generation-history {
    background-color: #2d2d2d;
    border-color: #404040;
  }

  .tab-item,
  .text-input,
  .url-input,
  .wifi-input,
  .contact-input,
  .sms-input,
  .email-input,
  .upload-btn,
  .qrcode-info,
  .history-item {
    background-color: #404040;
    border-color: #555555;
  }

  .tab-item.active {
    background-color: #1e3a8a;
    border-color: #3b82f6;
  }

  .text-input,
  .url-input,
  .wifi-input,
  .contact-input,
  .sms-input,
  .email-input {
    color: #ffffff;

    &::placeholder {
      color: #888888;
    }
  }

  .qrcode-canvas,
  .history-qrcode {
    background-color: #555555;
  }
}
</style>
