"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  _component_u_icon();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const searchKeyword = common_vendor.ref("");
    const currentCategory = common_vendor.ref(0);
    const categories = common_vendor.ref([
      { name: "全部", icon: "apps" },
      { name: "写作助手", icon: "edit" },
      { name: "编程助手", icon: "code" },
      { name: "翻译助手", icon: "translate" },
      { name: "设计助手", icon: "palette" },
      { name: "商务助手", icon: "briefcase" },
      { name: "学习助手", icon: "book" },
      { name: "生活助手", icon: "home" }
    ]);
    const featuredAgents = common_vendor.ref([
      {
        id: 1,
        name: "GPT-4 写作大师",
        description: "专业的写作助手，帮您创作高质量文章",
        avatar: "/static/images/agent-writer.png",
        rating: 4.9,
        users: "10万+",
        likes: "8.5万",
        tags: ["写作", "创意", "文案"],
        price: 29,
        isPro: true,
        category: "writing"
      },
      {
        id: 2,
        name: "代码助手Pro",
        description: "智能编程助手，支持多种编程语言",
        avatar: "/static/images/agent-coder.png",
        rating: 4.8,
        users: "8万+",
        likes: "6.2万",
        tags: ["编程", "AI", "调试"],
        price: 0,
        isPro: false,
        category: "coding"
      },
      {
        id: 3,
        name: "多语言翻译专家",
        description: "支持100+语言的专业翻译助手",
        avatar: "/static/images/agent-translator.png",
        rating: 4.7,
        users: "15万+",
        likes: "12万",
        tags: ["翻译", "多语言", "商务"],
        price: 19,
        isPro: true,
        category: "translation"
      },
      {
        id: 4,
        name: "UI设计师",
        description: "专业的UI/UX设计建议和灵感助手",
        avatar: "/static/images/agent-designer.png",
        rating: 4.6,
        users: "5万+",
        likes: "4.1万",
        tags: ["设计", "UI", "UX"],
        price: 39,
        isPro: true,
        category: "design"
      }
    ]);
    const allAgents = common_vendor.ref([
      ...featuredAgents.value,
      {
        id: 5,
        name: "商务邮件助手",
        description: "帮您撰写专业的商务邮件和文档",
        avatar: "/static/images/agent-business.png",
        rating: 4.5,
        users: "3万+",
        likes: "2.8万",
        tags: ["商务", "邮件", "文档"],
        price: 15,
        isPro: false,
        category: "business"
      },
      {
        id: 6,
        name: "学习规划师",
        description: "个性化学习计划制定和进度跟踪",
        avatar: "/static/images/agent-study.png",
        rating: 4.4,
        users: "6万+",
        likes: "5.2万",
        tags: ["学习", "规划", "教育"],
        price: 0,
        isPro: false,
        category: "study"
      }
    ]);
    const filteredAgents = common_vendor.computed(() => {
      let agents = allAgents.value;
      if (currentCategory.value > 0) {
        const categoryName = categories.value[currentCategory.value].name;
        agents = agents.filter((agent) => {
          return agent.tags.some((tag) => tag.includes(categoryName.replace("助手", "")));
        });
      }
      if (searchKeyword.value) {
        agents = agents.filter(
          (agent) => agent.name.includes(searchKeyword.value) || agent.description.includes(searchKeyword.value) || agent.tags.some((tag) => tag.includes(searchKeyword.value))
        );
      }
      return agents;
    });
    const handleSearch = () => {
      common_vendor.index.__f__("log", "at pages/ai-agents/market/index.vue:292", "搜索:", searchKeyword.value);
    };
    const showFilter = () => {
      common_vendor.index.showActionSheet({
        itemList: ["按评分排序", "按使用量排序", "按价格排序", "只看免费"],
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/ai-agents/market/index.vue:299", "筛选选项:", res.tapIndex);
        }
      });
    };
    const switchCategory = (index) => {
      currentCategory.value = index;
    };
    const viewAgent = (agent) => {
      common_vendor.index.navigateTo({
        url: `/pages/ai-agents/detail/index?id=${agent.id}`
      });
    };
    const useAgent = (agent) => {
      if (agent.price > 0) {
        common_vendor.index.showModal({
          title: "付费智能体",
          content: `${agent.name} 需要付费使用，价格：¥${agent.price}/月`,
          confirmText: "立即购买",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.__f__("log", "at pages/ai-agents/market/index.vue:323", "购买智能体:", agent.name);
            }
          }
        });
      } else {
        common_vendor.index.navigateTo({
          url: `/pages/digital-human/chat/index?agentId=${agent.id}`
        });
      }
    };
    const viewMore = (type) => {
      common_vendor.index.__f__("log", "at pages/ai-agents/market/index.vue:336", "查看更多:", type);
    };
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at pages/ai-agents/market/index.vue:341", "AI智能体市场页面加载完成");
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          name: "search",
          size: "18",
          color: "#8E8E93"
        }),
        b: common_vendor.o([($event) => searchKeyword.value = $event.detail.value, handleSearch]),
        c: searchKeyword.value,
        d: common_vendor.p({
          name: "filter",
          size: "16",
          color: "#667eea"
        }),
        e: common_vendor.o(showFilter),
        f: common_vendor.f(categories.value, (category, index, i0) => {
          return {
            a: common_vendor.t(category.name),
            b: index,
            c: currentCategory.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchCategory(index), index)
          };
        }),
        g: currentCategory.value === 0
      }, currentCategory.value === 0 ? {
        h: common_vendor.o(($event) => viewMore("hot")),
        i: common_vendor.f(featuredAgents.value, (agent, index, i0) => {
          return common_vendor.e({
            a: agent.avatar,
            b: agent.isPro
          }, agent.isPro ? {} : {}, {
            c: "4fe86039-2-" + i0,
            d: common_vendor.t(agent.rating),
            e: common_vendor.t(agent.name),
            f: common_vendor.t(agent.description),
            g: common_vendor.f(agent.tags.slice(0, 2), (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            }),
            h: "4fe86039-3-" + i0,
            i: common_vendor.t(agent.users),
            j: "4fe86039-4-" + i0,
            k: common_vendor.t(agent.likes),
            l: agent.price > 0
          }, agent.price > 0 ? {
            m: common_vendor.t(agent.price)
          } : {}, {
            n: common_vendor.o(($event) => useAgent(agent), index),
            o: index,
            p: common_vendor.o(($event) => viewAgent(agent), index)
          });
        }),
        j: common_vendor.p({
          name: "star-fill",
          size: "12",
          color: "#FFD700"
        }),
        k: common_vendor.p({
          name: "user",
          size: "12",
          color: "#8E8E93"
        }),
        l: common_vendor.p({
          name: "heart",
          size: "12",
          color: "#8E8E93"
        })
      } : {}, {
        m: common_vendor.t(categories.value[currentCategory.value].name),
        n: common_vendor.t(filteredAgents.value.length),
        o: common_vendor.f(filteredAgents.value, (agent, index, i0) => {
          return common_vendor.e({
            a: agent.avatar,
            b: common_vendor.t(agent.name),
            c: "4fe86039-5-" + i0,
            d: common_vendor.t(agent.rating),
            e: common_vendor.t(agent.description),
            f: common_vendor.f(agent.tags.slice(0, 3), (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            }),
            g: agent.price > 0
          }, agent.price > 0 ? {
            h: common_vendor.t(agent.price)
          } : {}, {
            i: "4fe86039-6-" + i0,
            j: common_vendor.o(($event) => useAgent(agent), index),
            k: index,
            l: common_vendor.o(($event) => viewAgent(agent), index)
          });
        }),
        p: common_vendor.p({
          name: "star-fill",
          size: "10",
          color: "#FFD700"
        }),
        q: common_vendor.p({
          name: "plus",
          size: "14",
          color: "#667eea"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-4fe86039"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/ai-agents/market/index.js.map
