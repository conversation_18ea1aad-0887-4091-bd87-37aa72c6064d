#!/usr/bin/env python3
"""
优化的MuseTalk 1.5推理脚本
支持GPU加速、bbox_shift参数调整、高质量输出
"""

import argparse
import os
import sys
import logging
import torch
import numpy as np
from pathlib import Path
import cv2
import subprocess
import json
import time

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description='优化的MuseTalk 1.5推理')
    parser.add_argument('--source_image', required=True, help='源图像路径')
    parser.add_argument('--driving_audio', required=True, help='驱动音频路径')
    parser.add_argument('--output', required=True, help='输出视频路径')
    parser.add_argument('--device', default='cuda', choices=['cuda', 'cpu'], help='设备类型')
    parser.add_argument('--fps', type=int, default=25, help='输出视频帧率')
    parser.add_argument('--quality', default='high', choices=['low', 'medium', 'high'], help='输出质量')
    parser.add_argument('--bbox_shift', type=int, default=0, help='边界框偏移 (影响嘴巴开合程度)')
    parser.add_argument('--batch_size', type=int, default=4, help='批处理大小')
    parser.add_argument('--use_float16', action='store_true', help='使用半精度浮点数')
    return parser.parse_args()

def check_dependencies():
    """检查依赖项"""
    try:
        import torch
        import cv2
        import numpy as np
        logger.info("✅ 所有依赖项检查通过")
        return True
    except ImportError as e:
        logger.error(f"❌ 缺少依赖项: {e}")
        return False

def check_gpu():
    """检查GPU可用性"""
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"✅ GPU可用: {gpu_name} ({gpu_memory:.1f}GB)")
        return True
    else:
        logger.warning("⚠️ GPU不可用，将使用CPU")
        return False

def get_audio_duration(audio_path):
    """获取音频时长"""
    try:
        cmd = ['ffprobe', '-v', 'quiet', '-show_entries', 'format=duration', 
               '-of', 'csv=p=0', str(audio_path)]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            duration = float(result.stdout.strip())
            logger.info(f"🎵 音频时长: {duration:.2f}秒")
            return duration
        else:
            logger.warning("无法获取音频时长，使用默认值")
            return 5.0
    except Exception as e:
        logger.warning(f"获取音频时长失败: {e}")
        return 5.0

def optimize_image(image_path, target_size=512):
    """优化输入图像"""
    try:
        img = cv2.imread(str(image_path))
        if img is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        h, w = img.shape[:2]
        logger.info(f"📸 原始图像尺寸: {w}x{h}")
        
        # 如果图像太大，进行缩放
        if max(w, h) > target_size:
            scale = target_size / max(w, h)
            new_w, new_h = int(w * scale), int(h * scale)
            img = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_LANCZOS4)
            logger.info(f"📸 图像已缩放至: {new_w}x{new_h}")
        
        return img
    except Exception as e:
        logger.error(f"图像优化失败: {e}")
        return None

def run_musetalk_inference(args):
    """运行MuseTalk 1.5推理"""
    start_time = time.time()
    
    # 检查输入文件
    if not os.path.exists(args.source_image):
        raise FileNotFoundError(f"源图像不存在: {args.source_image}")
    if not os.path.exists(args.driving_audio):
        raise FileNotFoundError(f"音频文件不存在: {args.driving_audio}")
    
    # 获取音频时长
    audio_duration = get_audio_duration(args.driving_audio)
    total_frames = int(audio_duration * args.fps)
    
    logger.info(f"🎬 预计生成帧数: {total_frames}")
    
    # 优化图像
    optimized_img = optimize_image(args.source_image)
    if optimized_img is None:
        raise ValueError("图像优化失败")
    
    # 设置设备
    device = args.device if args.device == 'cpu' or torch.cuda.is_available() else 'cpu'
    logger.info(f"🖥️ 使用设备: {device}")
    
    # 构建MuseTalk命令
    script_path = Path(__file__).parent / "scripts" / "inference.py"
    if not script_path.exists():
        # 回退到官方推理脚本
        script_path = Path(__file__).parent / "inference.py"
    
    cmd = [
        sys.executable, "-m", "scripts.inference",
        "--inference_config", "configs/inference/test.yaml",
        "--result_dir", str(Path(args.output).parent),
        "--unet_model_path", "models/musetalkV15/unet.pth",
        "--unet_config", "models/musetalkV15/musetalk.json",
        "--version", "v15"
    ]
    
    # 添加bbox_shift参数
    if args.bbox_shift != 0:
        cmd.extend(["--bbox_shift", str(args.bbox_shift)])
        logger.info(f"🎯 使用bbox_shift: {args.bbox_shift}")
    
    # 添加批处理大小
    if args.batch_size != 4:
        cmd.extend(["--batch_size", str(args.batch_size)])
    
    # 添加半精度支持
    if args.use_float16:
        cmd.append("--use_float16")
        logger.info("🚀 启用半精度加速")
    
    # 创建临时配置文件
    config = {
        "video_path": str(args.source_image),
        "audio_path": str(args.driving_audio),
        "bbox_shift": args.bbox_shift,
        "fps": args.fps,
        "batch_size": args.batch_size
    }
    
    config_path = Path(args.output).parent / "temp_config.yaml"
    with open(config_path, 'w') as f:
        import yaml
        yaml.dump(config, f)
    
    # 更新命令使用临时配置
    cmd[cmd.index("configs/inference/test.yaml")] = str(config_path)
    
    logger.info(f"🚀 开始MuseTalk 1.5推理...")
    logger.info(f"📝 命令: {' '.join(cmd)}")
    
    try:
        # 执行推理
        result = subprocess.run(
            cmd,
            cwd=str(Path(__file__).parent),
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        elapsed_time = time.time() - start_time
        
        if result.returncode == 0:
            logger.info(f"✅ MuseTalk推理成功完成!")
            logger.info(f"⏱️ 总耗时: {elapsed_time:.2f}秒")
            logger.info(f"🎬 平均速度: {total_frames/elapsed_time:.1f} fps")
            
            # 检查输出文件
            if os.path.exists(args.output):
                file_size = os.path.getsize(args.output)
                logger.info(f"📁 输出文件: {args.output}")
                logger.info(f"📊 文件大小: {file_size/1024:.1f} KB")
            else:
                logger.warning("⚠️ 输出文件未找到，可能在其他位置")
            
            return True
        else:
            logger.error(f"❌ MuseTalk推理失败")
            logger.error(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ 推理超时")
        return False
    except Exception as e:
        logger.error(f"❌ 推理过程中出错: {e}")
        return False
    finally:
        # 清理临时文件
        if config_path.exists():
            config_path.unlink()

def main():
    args = parse_args()
    
    logger.info("🎭 优化的MuseTalk 1.5推理开始...")
    logger.info(f"源图像: {args.source_image}")
    logger.info(f"驱动音频: {args.driving_audio}")
    logger.info(f"输出视频: {args.output}")
    logger.info(f"设备: {args.device}")
    logger.info(f"质量: {args.quality}")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查GPU
    if args.device == 'cuda':
        check_gpu()
    
    try:
        # 运行推理
        success = run_musetalk_inference(args)
        
        if success:
            logger.info("🎉 MuseTalk 1.5推理完成！")
            sys.exit(0)
        else:
            logger.error("💥 MuseTalk推理失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"💥 程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
