/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 导入自定义样式变量 */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
.text-processor-page.data-v-7e6b32d9 {
  min-height: 100vh;
  background-color: #F9FAFB;
}
.page-header.data-v-7e6b32d9 {
  background-color: #FFFFFF;
  padding: 48rpx 32rpx;
  text-align: center;
  border-bottom: 1px solid #E5E7EB;
}
.page-header .page-title.data-v-7e6b32d9 {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #111827;
  margin-bottom: 16rpx;
}
.page-header .page-desc.data-v-7e6b32d9 {
  font-size: 28rpx;
  color: #374151;
}
.section-title.data-v-7e6b32d9 {
  padding: 32rpx 32rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.section-title .title-text.data-v-7e6b32d9 {
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
}
.section-title .input-actions.data-v-7e6b32d9,
.section-title .result-actions.data-v-7e6b32d9 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.tools-selector.data-v-7e6b32d9 {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.tools-selector .tools-tabs.data-v-7e6b32d9 {
  display: flex;
  padding: 0 32rpx 32rpx;
  gap: 16rpx;
  overflow-x: auto;
}
.tools-selector .tools-tabs .tab-item.data-v-7e6b32d9 {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background-color: #F3F4F6;
  border-radius: 16rpx;
  border: 2px solid transparent;
  min-width: 120rpx;
}
.tools-selector .tools-tabs .tab-item.active.data-v-7e6b32d9 {
  border-color: #667eea;
  background-color: #EFF6FF;
}
.tools-selector .tools-tabs .tab-item .tab-text.data-v-7e6b32d9 {
  font-size: 24rpx;
  color: #111827;
  margin-top: 8rpx;
}
.text-input-section.data-v-7e6b32d9 {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.text-input-section .input-area.data-v-7e6b32d9 {
  padding: 0 32rpx 32rpx;
}
.text-input-section .input-area .text-input.data-v-7e6b32d9 {
  width: 100%;
  min-height: 300rpx;
  padding: 24rpx;
  border: 1px solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #111827;
  background-color: #F3F4F6;
  line-height: 1.625;
}
.text-input-section .input-area .text-input.data-v-7e6b32d9::-webkit-input-placeholder {
  color: #9CA3AF;
}
.text-input-section .input-area .text-input.data-v-7e6b32d9::placeholder {
  color: #9CA3AF;
}
.text-input-section .input-area .input-stats.data-v-7e6b32d9 {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
  padding: 16rpx 24rpx;
  background-color: #F3F4F6;
  border-radius: 12rpx;
}
.text-input-section .input-area .input-stats .stat-item.data-v-7e6b32d9 {
  font-size: 24rpx;
  color: #374151;
}
.tool-options.data-v-7e6b32d9 {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.tool-options .options-content.data-v-7e6b32d9 {
  padding: 0 32rpx;
}
.tool-options .options-content .option-group .option-item.data-v-7e6b32d9 {
  margin-bottom: 32rpx;
}
.tool-options .options-content .option-group .option-item .option-label.data-v-7e6b32d9 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 24rpx;
}
.tool-options .options-content .option-group .option-item .option-input.data-v-7e6b32d9 {
  width: 100%;
  padding: 16rpx 24rpx;
  border: 1px solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #111827;
  background-color: #F3F4F6;
}
.tool-options .options-content .option-group .option-item .option-input.data-v-7e6b32d9::-webkit-input-placeholder {
  color: #9CA3AF;
}
.tool-options .options-content .option-group .option-item .option-input.data-v-7e6b32d9::placeholder {
  color: #9CA3AF;
}
.tool-options .options-content .option-group .option-item .checkbox-group.data-v-7e6b32d9 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.tool-options .options-content .option-group .stats-display.data-v-7e6b32d9 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}
.tool-options .options-content .option-group .stats-display .stat-card.data-v-7e6b32d9 {
  background-color: #F3F4F6;
  border-radius: 16rpx;
  padding: 32rpx;
  text-align: center;
}
.tool-options .options-content .option-group .stats-display .stat-card .stat-number.data-v-7e6b32d9 {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 8rpx;
}
.tool-options .options-content .option-group .stats-display .stat-card .stat-label.data-v-7e6b32d9 {
  font-size: 24rpx;
  color: #374151;
}
.tool-options .options-content .option-group .replace-preview.data-v-7e6b32d9 {
  background-color: #F3F4F6;
  border-radius: 12rpx;
  padding: 24rpx;
}
.tool-options .options-content .option-group .replace-preview .preview-label.data-v-7e6b32d9 {
  font-size: 24rpx;
  color: #374151;
}
.tool-options .process-button.data-v-7e6b32d9 {
  padding: 0 32rpx 32rpx;
}
.processing-result.data-v-7e6b32d9 {
  background-color: #FFFFFF;
  margin-bottom: 24rpx;
}
.processing-result .result-content.data-v-7e6b32d9 {
  padding: 0 32rpx 32rpx;
}
.processing-result .result-content .result-text.data-v-7e6b32d9 {
  width: 100%;
  padding: 24rpx;
  border: 1px solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #111827;
  background-color: #F3F4F6;
  line-height: 1.625;
  resize: none;
  margin-bottom: 24rpx;
}
.processing-result .result-content .result-stats.data-v-7e6b32d9 {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 24rpx;
  background-color: #F3F4F6;
  border-radius: 12rpx;
}
.processing-result .result-content .result-stats .stat-item.data-v-7e6b32d9 {
  font-size: 24rpx;
  color: #374151;
}
.processing-history.data-v-7e6b32d9 {
  background-color: #FFFFFF;
}
.processing-history .history-list.data-v-7e6b32d9 {
  padding: 0 32rpx 32rpx;
}
.processing-history .history-list .history-item.data-v-7e6b32d9 {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  background-color: #F3F4F6;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}
.processing-history .history-list .history-item.data-v-7e6b32d9:last-child {
  margin-bottom: 0;
}
.processing-history .history-list .history-item.data-v-7e6b32d9:active {
  background-color: #E5E7EB;
}
.processing-history .history-list .history-item .history-icon.data-v-7e6b32d9 {
  width: 64rpx;
  height: 64rpx;
  background-color: #EFF6FF;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.processing-history .history-list .history-item .history-content.data-v-7e6b32d9 {
  flex: 1;
}
.processing-history .history-list .history-item .history-content .history-title.data-v-7e6b32d9 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
}
.processing-history .history-list .history-item .history-content .history-desc.data-v-7e6b32d9 {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.processing-history .history-list .history-item .history-content .history-time.data-v-7e6b32d9 {
  font-size: 20rpx;
  color: #6B7280;
}
.processing-history .history-list .history-item .history-actions.data-v-7e6b32d9 {
  padding: 16rpx;
}
@media screen and (max-width: 480px) {
.tools-tabs .tab-item.data-v-7e6b32d9 {
    min-width: 100rpx;
    padding: 16rpx;
}
.stats-display.data-v-7e6b32d9 {
    grid-template-columns: 1fr;
}
.input-stats.data-v-7e6b32d9 {
    flex-direction: column;
    gap: 8rpx;
}
.input-stats .stat-item.data-v-7e6b32d9 {
    text-align: center;
}
.result-stats.data-v-7e6b32d9 {
    flex-direction: column;
    gap: 8rpx;
}
.result-stats .stat-item.data-v-7e6b32d9 {
    text-align: center;
}
}
@media (prefers-color-scheme: dark) {
.text-processor-page.data-v-7e6b32d9 {
    background-color: #1a1a1a;
}
.page-header.data-v-7e6b32d9,
.tools-selector.data-v-7e6b32d9,
.text-input-section.data-v-7e6b32d9,
.tool-options.data-v-7e6b32d9,
.processing-result.data-v-7e6b32d9,
.processing-history.data-v-7e6b32d9 {
    background-color: #2d2d2d;
    border-color: #404040;
}
.tab-item.data-v-7e6b32d9,
.text-input.data-v-7e6b32d9,
.option-input.data-v-7e6b32d9,
.stat-card.data-v-7e6b32d9,
.replace-preview.data-v-7e6b32d9,
.result-text.data-v-7e6b32d9,
.input-stats.data-v-7e6b32d9,
.result-stats.data-v-7e6b32d9,
.history-item.data-v-7e6b32d9 {
    background-color: #404040;
    border-color: #555555;
}
.tab-item.active.data-v-7e6b32d9 {
    background-color: #1e3a8a;
    border-color: #3b82f6;
}
.text-input.data-v-7e6b32d9,
.option-input.data-v-7e6b32d9,
.result-text.data-v-7e6b32d9 {
    color: #ffffff;
}
.text-input.data-v-7e6b32d9::-webkit-input-placeholder, .option-input.data-v-7e6b32d9::-webkit-input-placeholder, .result-text.data-v-7e6b32d9::-webkit-input-placeholder {
    color: #888888;
}
.text-input.data-v-7e6b32d9::placeholder,
.option-input.data-v-7e6b32d9::placeholder,
.result-text.data-v-7e6b32d9::placeholder {
    color: #888888;
}
.history-icon.data-v-7e6b32d9 {
    background-color: #1e3a8a;
}
}