/**
 * 聊天对话相关API接口
 */
import request from '@/utils/request'

// 消息类型
export interface ChatMessage {
  id: string
  conversationId: string
  role: 'user' | 'assistant' | 'system'
  content: string
  type: 'text' | 'image' | 'audio' | 'file'
  metadata?: {
    tokens?: number
    model?: string
    temperature?: number
    attachments?: Array<{
      type: string
      url: string
      name: string
      size: number
    }>
  }
  createdAt: string
  updatedAt: string
}

// 对话会话
export interface Conversation {
  id: string
  title: string
  agentId?: string
  agentName?: string
  agentAvatar?: string
  messageCount: number
  lastMessage?: string
  lastMessageTime: string
  isStarred: boolean
  tags: string[]
  createdAt: string
  updatedAt: string
}

// 发送消息参数
export interface SendMessageParams {
  conversationId?: string
  content: string
  type?: 'text' | 'image' | 'audio' | 'file'
  agentId?: string
  attachments?: Array<{
    type: string
    url: string
    name: string
    size: number
  }>
  options?: {
    temperature?: number
    maxTokens?: number
    stream?: boolean
  }
}

// AI智能体信息
export interface AIAgent {
  id: string
  name: string
  description: string
  avatar: string
  category: string
  tags: string[]
  capabilities: string[]
  model: string
  temperature: number
  maxTokens: number
  systemPrompt: string
  isPublic: boolean
  isPremium: boolean
  price: number
  rating: number
  usageCount: number
  createdBy: string
  createdAt: string
  updatedAt: string
}

/**
 * 发送消息
 */
export const sendMessage = (params: SendMessageParams): Promise<{
  message: ChatMessage
  conversation: Conversation
}> => {
  return request.post('/chat/message', params)
}

/**
 * 流式发送消息
 */
export const sendMessageStream = (params: SendMessageParams): Promise<ReadableStream> => {
  return request.post('/chat/message/stream', params, {
    responseType: 'stream'
  })
}

/**
 * 获取对话列表
 */
export const getConversations = (params: {
  page?: number
  limit?: number
  keyword?: string
  agentId?: string
  starred?: boolean
  tags?: string[]
}): Promise<{
  list: Conversation[]
  total: number
  page: number
  limit: number
}> => {
  return request.get('/chat/conversations', { params })
}

/**
 * 获取对话详情
 */
export const getConversation = (conversationId: string): Promise<Conversation> => {
  return request.get(`/chat/conversations/${conversationId}`)
}

/**
 * 创建新对话
 */
export const createConversation = (params: {
  title?: string
  agentId?: string
  tags?: string[]
}): Promise<Conversation> => {
  return request.post('/chat/conversations', params)
}

/**
 * 更新对话信息
 */
export const updateConversation = (conversationId: string, params: {
  title?: string
  tags?: string[]
  isStarred?: boolean
}): Promise<Conversation> => {
  return request.put(`/chat/conversations/${conversationId}`, params)
}

/**
 * 删除对话
 */
export const deleteConversation = (conversationId: string): Promise<{ message: string }> => {
  return request.delete(`/chat/conversations/${conversationId}`)
}

/**
 * 获取对话消息列表
 */
export const getMessages = (conversationId: string, params: {
  page?: number
  limit?: number
  before?: string
  after?: string
}): Promise<{
  list: ChatMessage[]
  total: number
  page: number
  limit: number
  hasMore: boolean
}> => {
  return request.get(`/chat/conversations/${conversationId}/messages`, { params })
}

/**
 * 删除消息
 */
export const deleteMessage = (messageId: string): Promise<{ message: string }> => {
  return request.delete(`/chat/messages/${messageId}`)
}

/**
 * 重新生成消息
 */
export const regenerateMessage = (messageId: string, params?: {
  temperature?: number
  maxTokens?: number
}): Promise<ChatMessage> => {
  return request.post(`/chat/messages/${messageId}/regenerate`, params)
}

/**
 * 获取AI智能体列表
 */
export const getAIAgents = (params: {
  page?: number
  limit?: number
  category?: string
  keyword?: string
  tags?: string[]
  isPremium?: boolean
  sortBy?: 'rating' | 'usage' | 'created' | 'updated'
  sortOrder?: 'asc' | 'desc'
}): Promise<{
  list: AIAgent[]
  total: number
  page: number
  limit: number
  categories: string[]
  tags: string[]
}> => {
  return request.get('/chat/agents', { params })
}

/**
 * 获取AI智能体详情
 */
export const getAIAgent = (agentId: string): Promise<AIAgent> => {
  return request.get(`/chat/agents/${agentId}`)
}

/**
 * 创建自定义智能体
 */
export const createAIAgent = (params: {
  name: string
  description: string
  avatar?: string
  category: string
  tags: string[]
  capabilities: string[]
  model: string
  temperature: number
  maxTokens: number
  systemPrompt: string
  isPublic: boolean
}): Promise<AIAgent> => {
  return request.post('/chat/agents', params)
}

/**
 * 更新智能体
 */
export const updateAIAgent = (agentId: string, params: Partial<AIAgent>): Promise<AIAgent> => {
  return request.put(`/chat/agents/${agentId}`, params)
}

/**
 * 删除智能体
 */
export const deleteAIAgent = (agentId: string): Promise<{ message: string }> => {
  return request.delete(`/chat/agents/${agentId}`)
}

/**
 * 收藏智能体
 */
export const favoriteAgent = (agentId: string): Promise<{ message: string }> => {
  return request.post(`/chat/agents/${agentId}/favorite`)
}

/**
 * 取消收藏智能体
 */
export const unfavoriteAgent = (agentId: string): Promise<{ message: string }> => {
  return request.delete(`/chat/agents/${agentId}/favorite`)
}

/**
 * 获取收藏的智能体
 */
export const getFavoriteAgents = (params: {
  page?: number
  limit?: number
}): Promise<{
  list: AIAgent[]
  total: number
  page: number
  limit: number
}> => {
  return request.get('/chat/agents/favorites', { params })
}

/**
 * 评价智能体
 */
export const rateAgent = (agentId: string, params: {
  rating: number
  comment?: string
}): Promise<{ message: string }> => {
  return request.post(`/chat/agents/${agentId}/rate`, params)
}

/**
 * 获取智能体评价
 */
export const getAgentRatings = (agentId: string, params: {
  page?: number
  limit?: number
}): Promise<{
  list: Array<{
    id: string
    userId: string
    username: string
    avatar: string
    rating: number
    comment: string
    createdAt: string
  }>
  total: number
  page: number
  limit: number
  averageRating: number
  ratingDistribution: Record<string, number>
}> => {
  return request.get(`/chat/agents/${agentId}/ratings`, { params })
}

/**
 * 上传文件到对话
 */
export const uploadChatFile = (file: File, conversationId?: string): Promise<{
  url: string
  name: string
  size: number
  type: string
  id: string
}> => {
  const formData = new FormData()
  formData.append('file', file)
  if (conversationId) {
    formData.append('conversationId', conversationId)
  }
  
  return request.post('/chat/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 语音转文字
 */
export const speechToText = (audioFile: File): Promise<{
  text: string
  confidence: number
  duration: number
  language: string
}> => {
  const formData = new FormData()
  formData.append('audio', audioFile)
  
  return request.post('/chat/speech-to-text', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 文字转语音
 */
export const textToSpeech = (params: {
  text: string
  voice?: string
  speed?: number
  pitch?: number
  volume?: number
}): Promise<{
  audioUrl: string
  duration: number
  size: number
}> => {
  return request.post('/chat/text-to-speech', params)
}

// 默认导出
export default {
  sendMessage,
  sendMessageStream,
  getConversations,
  getConversation,
  createConversation,
  updateConversation,
  deleteConversation,
  getMessages,
  deleteMessage,
  regenerateMessage,
  getAIAgents,
  getAIAgent,
  createAIAgent,
  updateAIAgent,
  deleteAIAgent,
  favoriteAgent,
  unfavoriteAgent,
  getFavoriteAgents,
  rateAgent,
  getAgentRatings,
  uploadChatFile,
  speechToText,
  textToSpeech
}
