"""
数字人生成任务
包含数字人创建、训练、渲染等异步任务
"""

import os
import time
import logging
from datetime import datetime
from typing import Dict, Any
from pathlib import Path

from app.core.celery_unified import celery_app
from app.tasks.base import BaseTask, TaskProgressTracker, task_with_progress
from app.models.digital_human import DigitalHuman, DigitalHumanGeneration

logger = logging.getLogger(__name__)

# 输出目录配置
OUTPUT_DIR = Path("storage/digital_humans")
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

# 模型目录配置
MODELS_BASE_DIR = Path("storage/models")
DIGITAL_HUMAN_MODELS_DIR = MODELS_BASE_DIR / "digital_human"
AI_MODELS_DIR = MODELS_BASE_DIR / "ai_generation"  # 用于存放下载的AI模型
AI_MODELS_DIR.mkdir(parents=True, exist_ok=True)

# 数字人模型路径
MUSETALK_MODEL_DIR = DIGITAL_HUMAN_MODELS_DIR / "musetalk"
MUSETALK_OFFICIAL_DIR = DIGITAL_HUMAN_MODELS_DIR / "musetalk_official"
SADTALKER_MODEL_DIR = DIGITAL_HUMAN_MODELS_DIR / "sadtalker"
WAV2LIP_MODEL_DIR = DIGITAL_HUMAN_MODELS_DIR / "wav2lip"
LIVEPORTRAIT_MODEL_DIR = DIGITAL_HUMAN_MODELS_DIR / "liveportrait"

# Stable Diffusion模型缓存目录
STABLE_DIFFUSION_CACHE = AI_MODELS_DIR / "stable_diffusion"
STABLE_DIFFUSION_CACHE.mkdir(parents=True, exist_ok=True)

@celery_app.task(bind=True, base=BaseTask, name="generate_digital_human")
@task_with_progress("数字人生成")
def generate_digital_human(self, task_id: str, tracker: TaskProgressTracker, 
                          digital_human_id: int, input_data: Dict[str, Any]):
    """
    生成数字人的主任务
    
    Args:
        task_id: 任务ID
        tracker: 进度跟踪器
        digital_human_id: 数字人ID
        input_data: 输入数据
    """
    
    try:
        # 1. 初始化和验证
        tracker.update(5, "初始化数字人生成任务...")
        
        # 获取数字人记录
        digital_human = self.db_session.query(DigitalHuman).filter(
            DigitalHuman.id == digital_human_id
        ).first()
        
        if not digital_human:
            raise ValueError(f"数字人记录不存在: {digital_human_id}")
        
        logger.info(f"开始生成数字人: {digital_human.name} (ID: {digital_human_id})")
        
        # 2. 处理输入数据
        tracker.update(10, "处理输入数据...")
        processed_data = _process_input_data(input_data)
        
        # 3. 生成头像
        tracker.update(20, "生成数字人头像...")
        avatar_result = _generate_avatar(digital_human_id, processed_data, tracker)
        
        # 4. 处理语音
        tracker.update(50, "处理语音设置...")
        voice_result = _process_voice(digital_human_id, processed_data, tracker)
        
        # 5. 生成数字人模型
        tracker.update(70, "生成数字人模型...")
        model_result = _generate_model(digital_human_id, processed_data, tracker)
        
        # 6. 生成预览视频
        tracker.update(85, "生成预览视频...")
        video_result = _generate_preview_video(digital_human_id, processed_data, tracker)
        
        # 7. 完成处理
        tracker.update(95, "完成最终处理...")
        
        # 构建结果URLs
        result_urls = {
            "avatar_url": avatar_result.get("avatar_url"),
            "model_url": model_result.get("model_url"),
            "preview_video_url": video_result.get("video_url"),
            "voice_config_url": voice_result.get("voice_config_url")
        }

        # 更新数字人记录
        digital_human.status = "ready"
        digital_human.avatar_url = avatar_result.get("avatar_url")
        digital_human.model_url = model_result.get("model_url")
        digital_human.preview_video_url = video_result.get("video_url")
        digital_human.updated_at = datetime.utcnow()

        # 更新生成任务记录
        try:
            generation_task = self.db_session.query(DigitalHumanGeneration).filter(
                DigitalHumanGeneration.task_id == task_id
            ).first()

            if generation_task:
                generation_task.status = "completed"
                generation_task.progress = 100
                generation_task.message = "数字人生成完成！"
                generation_task.result_urls = result_urls
                generation_task.end_time = datetime.utcnow()
                generation_task.updated_at = datetime.utcnow()
                self.db_session.commit()
                logger.info(f"生成任务记录已更新: {task_id}, result_urls: {result_urls}")
            else:
                logger.error(f"未找到生成任务记录: {task_id}")
        except Exception as e:
            logger.error(f"更新生成任务记录失败: {e}")
            self.db_session.rollback()

        # 构建结果
        result = {
            "success": True,
            "digital_human_id": digital_human_id,
            "task_id": task_id,
            "avatar_url": avatar_result.get("avatar_url"),
            "model_url": model_result.get("model_url"),
            "preview_video_url": video_result.get("video_url"),
            "voice_settings": voice_result,
            "result_urls": result_urls,
            "generated_at": datetime.utcnow().isoformat()
        }
        
        tracker.complete("数字人生成完成！")
        
        logger.info(f"数字人生成成功: {digital_human_id}")
        return result
        
    except Exception as e:
        logger.error(f"数字人生成失败: {e}")
        tracker.fail(f"生成失败: {str(e)}")
        
        # 更新数字人状态为失败
        if 'digital_human' in locals():
            digital_human.status = "failed"
            digital_human.error_message = str(e)
            digital_human.updated_at = datetime.utcnow()
        
        raise

def _process_input_data(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """处理输入数据"""
    processed = {
        "name": input_data.get("name", "未命名数字人"),
        "type": input_data.get("type", "assistant"),
        "gender": input_data.get("gender", "female"),
        "description": input_data.get("description", ""),
        "welcome_text": input_data.get("welcome_text", "你好！"),
        
        # 外观设置
        "appearance_type": input_data.get("appearance_type", "template"),
        "selected_template": input_data.get("selected_template"),
        "uploaded_image_url": input_data.get("uploaded_image_url"),
        "facial_expression": input_data.get("facial_expression", "friendly"),
        "clothing_style": input_data.get("clothing_style", "business"),
        "background": input_data.get("background", "office"),
        
        # 声音设置
        "voice_type": input_data.get("voice_type", "standard"),
        "voice_speed": input_data.get("voice_speed", 1.0),
        "voice_pitch": input_data.get("voice_pitch", 1.0),
        "selected_voice": input_data.get("selected_voice"),
    }
    
    logger.info(f"处理后的输入数据: {processed}")
    return processed

def _generate_avatar(digital_human_id: int, data: Dict[str, Any], tracker: TaskProgressTracker) -> Dict[str, Any]:
    """生成数字人头像"""
    logger.info(f"开始生成头像: {digital_human_id}")

    try:
        # 生成头像文件路径
        avatar_filename = f"avatar_{digital_human_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
        avatar_path = OUTPUT_DIR / avatar_filename

        # 检查是否有上传的图片
        uploaded_image_url = data.get("uploaded_image_url")
        if uploaded_image_url and uploaded_image_url.strip():
            tracker.update(25, "使用上传的头像图片...")
            success = _use_uploaded_avatar(uploaded_image_url, avatar_path, tracker)
            if success:
                return _build_avatar_result(avatar_filename, avatar_path, data, "uploaded")

        # 检查是否有生成的图片URL
        generated_image_url = data.get("generated_image_url")
        if generated_image_url and generated_image_url.strip():
            tracker.update(25, "使用已生成的头像图片...")
            success = _use_uploaded_avatar(generated_image_url, avatar_path, tracker)
            if success:
                return _build_avatar_result(avatar_filename, avatar_path, data, "generated")

        # 检查是否使用模板（优先于AI生成）
        appearance_type = data.get("appearance_type", "template")
        selected_template = data.get("selected_template")

        if appearance_type == "template" and selected_template:
            tracker.update(25, f"使用预设模板: {selected_template}")
            success = _use_template_avatar(selected_template, data, avatar_path, tracker)
            if success:
                return _build_avatar_result(avatar_filename, avatar_path, data, "template")

        # 如果没有模板或模板失败，使用AI生成
        tracker.update(22, "构建数字人图像生成提示词...")
        prompt = _build_avatar_prompt(data)
        logger.info(f"为{data.get('name', '数字人')}生成头像，提示词: {prompt}")

        # 尝试使用真实的图像生成
        tracker.update(25, "调用AI图像生成模型...")
        success = _generate_real_avatar(prompt, avatar_path, tracker)

        if success:
            tracker.update(50, "AI头像生成完成")
            return _build_avatar_result(avatar_filename, avatar_path, data, "ai_generated")

        # 最后回退到占位图像
        tracker.update(40, "使用默认头像模板...")
        _generate_placeholder_avatar(data, avatar_path)
        tracker.update(50, "头像生成完成")
        return _build_avatar_result(avatar_filename, avatar_path, data, "placeholder")

    except Exception as e:
        logger.error(f"头像生成失败: {e}")
        # 生成基础占位图像
        avatar_filename = f"avatar_{digital_human_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
        avatar_path = OUTPUT_DIR / avatar_filename
        _generate_placeholder_avatar(data, avatar_path)

        return _build_avatar_result(avatar_filename, avatar_path, data, "fallback")

def _build_avatar_result(avatar_filename: str, avatar_path: Path, data: Dict[str, Any], method: str) -> Dict[str, Any]:
    """构建头像生成结果"""
    return {
        "avatar_url": f"/storage/digital_humans/{avatar_filename}",
        "avatar_path": str(avatar_path),
        "facial_expression": data.get("facial_expression"),
        "clothing_style": data.get("clothing_style"),
        "background": data.get("background"),
        "generation_method": method
    }

def _use_uploaded_avatar(uploaded_url: str, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """使用上传的头像（支持URL和base64数据）"""
    try:
        import requests
        import base64
        import io
        from PIL import Image

        logger.info(f"处理头像数据，类型: {'base64' if uploaded_url.startswith('data:') else 'URL'}")

        # 处理base64数据
        if uploaded_url.startswith('data:image/'):
            try:
                # 解析base64数据
                header, data = uploaded_url.split(',', 1)
                image_data = base64.b64decode(data)

                # 直接从内存中打开图片
                img = Image.open(io.BytesIO(image_data))
                img = img.convert('RGB')
                img = img.resize((512, 512), Image.Resampling.LANCZOS)
                img.save(output_path, 'JPEG', quality=95)

                logger.info(f"Base64头像处理成功: {output_path}")
                return True

            except Exception as e:
                logger.error(f"处理base64头像失败: {e}")
                return False

        # 处理URL数据
        else:
            try:
                # 下载上传的图片
                response = requests.get(uploaded_url, timeout=30)
                if response.status_code == 200:
                    # 保存并处理图片
                    with open(output_path, 'wb') as f:
                        f.write(response.content)

                    # 验证图片格式并调整大小
                    img = Image.open(output_path)
                    img = img.convert('RGB')
                    img = img.resize((512, 512), Image.Resampling.LANCZOS)
                    img.save(output_path, 'JPEG', quality=95)

                    logger.info(f"URL头像处理成功: {output_path}")
                    return True
                else:
                    logger.error(f"下载头像失败: {response.status_code}")
                    return False

            except Exception as e:
                logger.error(f"处理URL头像失败: {e}")
                return False

    except Exception as e:
        logger.error(f"处理头像失败: {e}")
        return False

def _use_template_avatar(template_name: str, data: Dict[str, Any], output_path: Path, tracker: TaskProgressTracker) -> bool:
    """使用预设模板头像"""
    try:
        # 模板映射 - 包含真实的图片URL
        template_configs = {
            "female-teacher": {
                "gender": "female",
                "profession": "teacher",
                "style": "professional",
                "age": "young adult",
                "image_url": "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=512&h=512&fit=crop&crop=face"
            },
            "male-teacher": {
                "gender": "male",
                "profession": "teacher",
                "style": "professional",
                "age": "young adult",
                "image_url": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=512&h=512&fit=crop&crop=face"
            },
            "female-assistant": {
                "gender": "female",
                "profession": "assistant",
                "style": "business",
                "age": "young adult",
                "image_url": "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=512&h=512&fit=crop&crop=face"
            },
            "male-assistant": {
                "gender": "male",
                "profession": "assistant",
                "style": "business",
                "age": "young adult",
                "image_url": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=512&h=512&fit=crop&crop=face"
            }
        }

        template_config = template_configs.get(template_name)
        if not template_config:
            logger.warning(f"未知的模板: {template_name}")
            return False

        # 尝试下载真实的模板图片
        image_url = template_config.get("image_url")
        if image_url:
            logger.info(f"下载模板图片: {image_url}")
            success = _download_template_image(image_url, output_path, tracker)
            if success:
                logger.info(f"模板图片下载成功: {template_name}")
                return True
            else:
                logger.warning(f"模板图片下载失败，使用生成的图片: {template_name}")

        # 如果下载失败，回退到生成图片
        _generate_template_avatar(template_config, data, output_path)
        logger.info(f"模板头像生成成功: {template_name}")
        return True

    except Exception as e:
        logger.error(f"模板头像生成失败: {e}")
        return False

def _download_template_image(image_url: str, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """下载模板图片"""
    try:
        import requests
        from PIL import Image

        # 下载图片
        response = requests.get(image_url, timeout=30)
        if response.status_code == 200:
            # 保存并处理图片
            with open(output_path, 'wb') as f:
                f.write(response.content)

            # 验证图片格式并调整大小
            img = Image.open(output_path)
            img = img.convert('RGB')
            img = img.resize((512, 512), Image.Resampling.LANCZOS)
            img.save(output_path, 'JPEG', quality=95)

            logger.info(f"模板图片下载成功: {output_path}")
            return True
        else:
            logger.error(f"下载模板图片失败: HTTP {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"下载模板图片失败: {e}")
        return False

def _process_voice(digital_human_id: int, data: Dict[str, Any], tracker: TaskProgressTracker) -> Dict[str, Any]:
    """处理语音设置"""
    logger.info(f"开始处理语音: {digital_human_id}")
    
    # 模拟语音处理
    time.sleep(1)
    tracker.update(60, "配置语音参数...")
    
    result = {
        "voice_type": data.get("voice_type"),
        "voice_speed": data.get("voice_speed"),
        "voice_pitch": data.get("voice_pitch"),
        "selected_voice": data.get("selected_voice"),
        "voice_config_url": f"/storage/digital_humans/voice_config_{digital_human_id}.json"
    }
    
    logger.info(f"语音处理完成: {result}")
    return result

def _generate_model(digital_human_id: int, data: Dict[str, Any], tracker: TaskProgressTracker) -> Dict[str, Any]:
    """生成数字人模型"""
    logger.info(f"开始生成模型: {digital_human_id}")
    
    # 模拟模型生成
    for i in range(3):
        time.sleep(1)
        progress = 70 + (i + 1) * 5  # 70-85%
        tracker.update(progress, f"生成3D模型... ({i+1}/3)")
    
    model_filename = f"model_{digital_human_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.glb"
    model_path = OUTPUT_DIR / model_filename
    
    # 模拟生成模型文件
    with open(model_path, "wb") as f:
        f.write(b"mock 3d model data")
    
    result = {
        "model_url": f"/storage/digital_humans/{model_filename}",
        "model_path": str(model_path),
        "model_type": "glb"
    }
    
    logger.info(f"模型生成完成: {result}")
    return result

def _generate_preview_video(digital_human_id: int, data: Dict[str, Any], tracker: TaskProgressTracker) -> Dict[str, Any]:
    """生成预览视频"""
    logger.info(f"开始生成预览视频: {digital_human_id}")

    try:
        video_filename = f"preview_{digital_human_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"
        video_path = OUTPUT_DIR / video_filename

        # 尝试生成真实的视频
        tracker.update(87, "准备视频生成...")
        success = _generate_real_video(digital_human_id, data, video_path, tracker)

        if not success:
            # 如果真实生成失败，创建占位视频
            tracker.update(92, "生成占位视频...")
            _generate_placeholder_video(data, video_path)

        tracker.update(95, "视频生成完成")

        result = {
            "video_url": f"/storage/digital_humans/{video_filename}",
            "video_path": str(video_path),
            "duration": 10,  # 秒
            "resolution": "1280x720",
            "generation_method": "ai_generated" if success else "placeholder"
        }

        logger.info(f"预览视频生成完成: {result}")
        return result

    except Exception as e:
        logger.error(f"预览视频生成失败: {e}")
        # 生成基础占位视频
        video_filename = f"preview_{digital_human_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"
        video_path = OUTPUT_DIR / video_filename
        _generate_placeholder_video(data, video_path)

        return {
            "video_url": f"/storage/digital_humans/{video_filename}",
            "video_path": str(video_path),
            "duration": 10,
            "resolution": "1280x720",
            "generation_method": "fallback"
        }

# ==================== 真实生成函数 ====================

def _build_avatar_prompt(data: Dict[str, Any]) -> str:
    """构建头像生成提示词"""
    gender = data.get("gender", "female")
    facial_expression = data.get("facial_expression", "friendly")
    clothing_style = data.get("clothing_style", "business")
    background = data.get("background", "office")

    # 性别描述
    gender_desc = "beautiful young woman" if gender == "female" else "handsome young man"

    # 表情描述
    expression_map = {
        "friendly": "warm friendly smile",
        "professional": "confident professional expression",
        "cheerful": "bright cheerful smile",
        "calm": "calm peaceful expression"
    }
    expression_desc = expression_map.get(facial_expression, "friendly smile")

    # 服装描述
    clothing_map = {
        "business": "professional business attire",
        "casual": "smart casual clothing",
        "formal": "elegant formal wear"
    }
    clothing_desc = clothing_map.get(clothing_style, "professional attire")

    # 背景描述
    background_map = {
        "office": "modern office background",
        "studio": "clean studio background",
        "outdoor": "natural outdoor setting"
    }
    background_desc = background_map.get(background, "neutral background")

    prompt = f"High quality portrait photo of a {gender_desc} with {expression_desc}, wearing {clothing_desc}, {background_desc}, professional lighting, 4K resolution, photorealistic"

    logger.info(f"生成的提示词: {prompt}")
    return prompt

def _generate_real_avatar(prompt: str, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """使用真实的AI模型生成头像"""
    try:
        # 尝试使用Stable Diffusion或其他图像生成模型
        tracker.update(30, "初始化图像生成模型...")

        # 检查是否有可用的图像生成服务
        if _check_image_generation_available():
            tracker.update(35, "生成图像中...")
            return _call_image_generation_api(prompt, output_path, tracker)
        else:
            logger.warning("图像生成服务不可用，将使用占位图像")
            return False

    except Exception as e:
        logger.error(f"真实头像生成失败: {e}")
        return False

def _check_image_generation_available() -> bool:
    """检查图像生成服务是否可用"""
    try:
        # 检查本地Stable Diffusion
        import torch
        if torch.cuda.is_available():
            logger.info("检测到CUDA，可以使用本地图像生成")
            return True

        # 检查在线API服务
        # TODO: 添加对OpenAI DALL-E、Midjourney等API的检查

        return False
    except ImportError:
        logger.warning("PyTorch未安装，无法使用本地图像生成")
        return False

def _call_image_generation_api(prompt: str, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """调用图像生成API"""
    try:
        # 这里可以集成多种图像生成服务
        # 1. 本地Stable Diffusion
        # 2. OpenAI DALL-E
        # 3. Midjourney API
        # 4. 其他图像生成服务

        # 示例：使用本地Stable Diffusion
        success = _generate_with_stable_diffusion(prompt, output_path, tracker)
        if success:
            return True

        # 如果本地生成失败，尝试在线服务
        # success = _generate_with_online_api(prompt, output_path, tracker)
        # if success:
        #     return True

        return False

    except Exception as e:
        logger.error(f"图像生成API调用失败: {e}")
        return False

def _generate_with_stable_diffusion(prompt: str, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """使用Stable Diffusion生成图像"""
    try:
        # 尝试导入和使用Stable Diffusion
        from diffusers import StableDiffusionPipeline
        import torch

        tracker.update(38, "加载Stable Diffusion模型...")

        # 设置模型缓存目录
        os.environ['HF_HOME'] = str(STABLE_DIFFUSION_CACHE)
        os.environ['TRANSFORMERS_CACHE'] = str(STABLE_DIFFUSION_CACHE)

        # 加载模型（使用本地缓存目录）
        model_id = "runwayml/stable-diffusion-v1-5"
        pipe = StableDiffusionPipeline.from_pretrained(
            model_id,
            cache_dir=str(STABLE_DIFFUSION_CACHE),
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            safety_checker=None,
            requires_safety_checker=False
        )

        if torch.cuda.is_available():
            pipe = pipe.to("cuda")

        tracker.update(42, "生成图像...")

        # 生成图像
        image = pipe(
            prompt,
            num_inference_steps=20,  # 减少步数以加快生成
            guidance_scale=7.5,
            width=512,
            height=512
        ).images[0]

        tracker.update(48, "保存图像...")

        # 保存图像
        image.save(output_path, "JPEG", quality=95)

        logger.info(f"Stable Diffusion生成成功: {output_path}")
        return True

    except ImportError:
        logger.warning("diffusers库未安装，无法使用Stable Diffusion")
        return False
    except Exception as e:
        logger.error(f"Stable Diffusion生成失败: {e}")
        return False

def _generate_placeholder_avatar(data: Dict[str, Any], output_path: Path):
    """生成高质量的占位头像"""
    try:
        from PIL import Image, ImageDraw, ImageFont

        # 创建高质量的占位图像
        width, height = 512, 512

        # 根据性别选择颜色
        gender = data.get("gender", "female")
        if gender == "female":
            bg_color = (255, 240, 245)  # 淡粉色
            text_color = (139, 69, 19)   # 棕色
        else:
            bg_color = (240, 248, 255)  # 淡蓝色
            text_color = (25, 25, 112)   # 深蓝色

        # 创建图像
        image = Image.new('RGB', (width, height), bg_color)
        draw = ImageDraw.Draw(image)

        # 绘制圆形头像框
        margin = 50
        circle_bbox = [margin, margin, width-margin, height-margin]
        draw.ellipse(circle_bbox, fill=(255, 255, 255), outline=text_color, width=3)

        # 添加文字
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", 36)
        except:
            font = ImageFont.load_default()

        name = data.get("name", "数字人")
        text = f"{name}\n{gender}\n头像"

        # 计算文字位置
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        text_x = (width - text_width) // 2
        text_y = (height - text_height) // 2

        draw.text((text_x, text_y), text, fill=text_color, font=font, align="center")

        # 保存图像
        image.save(output_path, "JPEG", quality=95)

        logger.info(f"占位头像生成成功: {output_path}")

    except Exception as e:
        logger.error(f"占位头像生成失败: {e}")
        # 如果PIL也失败，创建最基本的文件
        with open(output_path, "wb") as f:
            f.write(b"Digital Human Avatar Placeholder")

def _generate_template_avatar(template_config: Dict[str, Any], data: Dict[str, Any], output_path: Path):
    """生成基于模板的高质量头像"""
    try:
        from PIL import Image, ImageDraw, ImageFont

        # 创建高质量的模板图像
        width, height = 512, 512

        # 根据性别和职业选择颜色方案
        gender = template_config.get("gender", "female")
        profession = template_config.get("profession", "assistant")

        if gender == "female":
            if profession == "teacher":
                bg_color = (255, 248, 240)  # 温暖的米色
                accent_color = (139, 69, 19)  # 棕色
            else:
                bg_color = (240, 248, 255)  # 淡蓝色
                accent_color = (70, 130, 180)  # 钢蓝色
        else:
            if profession == "teacher":
                bg_color = (248, 248, 255)  # 淡紫色
                accent_color = (72, 61, 139)  # 深蓝紫色
            else:
                bg_color = (245, 245, 245)  # 浅灰色
                accent_color = (105, 105, 105)  # 暗灰色

        # 创建图像
        image = Image.new('RGB', (width, height), bg_color)
        draw = ImageDraw.Draw(image)

        # 绘制专业的头像框架
        margin = 40

        # 外圆环
        outer_circle = [margin-10, margin-10, width-margin+10, height-margin+10]
        draw.ellipse(outer_circle, fill=accent_color)

        # 内圆
        inner_circle = [margin, margin, width-margin, height-margin]
        draw.ellipse(inner_circle, fill=(255, 255, 255))

        # 添加专业信息
        try:
            font_large = ImageFont.truetype("arial.ttf", 48)
            font_medium = ImageFont.truetype("arial.ttf", 32)
            font_small = ImageFont.truetype("arial.ttf", 24)
        except:
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
            font_small = ImageFont.load_default()

        # 获取用户信息
        name = data.get("name", "数字人")
        profession_cn = "老师" if profession == "teacher" else "助手"
        gender_cn = "女" if gender == "female" else "男"

        # 绘制文字
        text_lines = [
            (name, font_large),
            (f"{gender_cn}{profession_cn}", font_medium),
            (template_config.get("style", "专业"), font_small)
        ]

        y_offset = height // 2 - 60
        for text, font in text_lines:
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_x = (width - text_width) // 2
            draw.text((text_x, y_offset), text, fill=accent_color, font=font)
            y_offset += 50

        # 添加装饰元素
        # 绘制小圆点装饰
        for i in range(8):
            angle = i * 45
            import math
            x = width//2 + int(180 * math.cos(math.radians(angle)))
            y = height//2 + int(180 * math.sin(math.radians(angle)))
            draw.ellipse([x-5, y-5, x+5, y+5], fill=accent_color)

        # 保存图像
        image.save(output_path, "JPEG", quality=95)

        logger.info(f"模板头像生成成功: {output_path}")

    except Exception as e:
        logger.error(f"模板头像生成失败: {e}")
        # 回退到基础占位图像
        _generate_placeholder_avatar(data, output_path)

def _generate_real_video(digital_human_id: int, data: Dict[str, Any], output_path: Path, tracker: TaskProgressTracker) -> bool:
    """生成真实的数字人视频"""
    try:
        tracker.update(89, "检查可用的数字人模型...")

        # 获取可用的本地数字人模型
        available_models = _get_available_digital_human_models()
        logger.info(f"可用的数字人模型: {list(available_models.keys())}")

        # 获取头像路径（如果存在）
        avatar_path = None
        avatar_files = list(OUTPUT_DIR.glob(f"avatar_{digital_human_id}_*.jpg"))
        if avatar_files:
            avatar_path = avatar_files[0]

        # 生成欢迎文本的语音
        welcome_text = data.get("welcome_text", "你好，我是你的数字助手！")

        # 优先使用本地数字人模型
        for model_name, model_info in available_models.items():
            if model_info["available"]:
                tracker.update(91, f"尝试使用{model_info['name']}生成视频...")
                success = _generate_with_local_model(model_name, model_info, avatar_path, welcome_text, output_path, tracker)
                if success:
                    logger.info(f"使用{model_info['name']}生成视频成功")
                    return True
                else:
                    logger.warning(f"使用{model_info['name']}生成视频失败，尝试下一个模型")

        # 如果本地模型都不可用，使用增强的视频生成方法
        tracker.update(92, "使用增强视频生成方法...")

        # 优先尝试OpenCV说话效果
        success = _generate_talking_video_with_opencv(avatar_path, welcome_text, output_path, tracker)
        if success:
            return True

        # 如果OpenCV失败，使用ffmpeg增强方法
        if _check_video_generation_available():
            success = _generate_with_video_models(avatar_path, welcome_text, output_path, tracker)
            if success:
                return True

        return False

    except Exception as e:
        logger.error(f"真实视频生成失败: {e}")
        return False

def _check_video_generation_available() -> bool:
    """检查视频生成服务是否可用"""
    try:
        # 检查是否有ffmpeg
        import subprocess
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("检测到ffmpeg，可以生成基础视频")
            return True
        return False
    except FileNotFoundError:
        logger.warning("ffmpeg未安装，无法生成视频")
        return False

def _generate_with_video_models(avatar_path: Path, text: str, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """使用视频生成模型"""
    try:
        # 方法1: 使用ffmpeg创建简单的视频
        success = _generate_with_ffmpeg(avatar_path, text, output_path, tracker)
        if success:
            return True

        # 方法2: 如果有Wav2Lip等模型，可以在这里调用
        # success = _generate_with_wav2lip(avatar_path, text, output_path, tracker)
        # if success:
        #     return True

        return False

    except Exception as e:
        logger.error(f"视频模型生成失败: {e}")
        return False

def _generate_with_ffmpeg(avatar_path: Path, text: str, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """使用ffmpeg生成带说话效果的视频"""
    try:
        import subprocess

        tracker.update(91, "使用ffmpeg生成数字人说话视频...")

        if not avatar_path or not avatar_path.exists():
            logger.error("ffmpeg需要头像图片")
            return False

        # 首先生成音频
        audio_path = output_path.parent / f"temp_audio_{output_path.stem}.wav"
        if not _generate_audio_from_text(text, audio_path):
            logger.warning("无法生成音频，使用静默音频")
            _generate_silent_audio(audio_path, 10)

        # 创建带说话效果的视频
        # 使用ffmpeg的滤镜来模拟说话效果
        cmd = [
            'ffmpeg', '-y',
            '-loop', '1', '-i', str(avatar_path),  # 输入图片
            '-i', str(audio_path),  # 输入音频
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-pix_fmt', 'yuv420p',
            '-vf', 'scale=1280:720,zoompan=z=\'if(lte(zoom,1.0),1.5,max(1.001,zoom-0.0015))\':d=25*10:s=1280x720',  # 添加轻微的缩放效果模拟说话
            '-shortest',  # 以最短的流为准
            '-t', '10',  # 10秒
            str(output_path)
        ]

        logger.info(f"执行ffmpeg命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

        # 清理临时音频文件
        if audio_path.exists():
            audio_path.unlink()

        if result.returncode == 0 and output_path.exists():
            logger.info(f"ffmpeg数字人视频生成成功: {output_path}")
            return True
        else:
            logger.error(f"ffmpeg生成失败: {result.stderr}")
            # 如果复杂命令失败，尝试简单的静态视频
            return _generate_simple_video_with_audio(avatar_path, audio_path, output_path, tracker)

    except subprocess.TimeoutExpired:
        logger.error("ffmpeg生成超时")
        return False
    except Exception as e:
        logger.error(f"ffmpeg生成异常: {e}")
        return False

def _generate_simple_video_with_audio(avatar_path: Path, audio_path: Path, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """生成简单的带音频的视频"""
    try:
        import subprocess

        # 重新生成音频（如果需要）
        if not audio_path.exists():
            _generate_silent_audio(audio_path, 10)

        # 简单的图片+音频合成
        cmd = [
            'ffmpeg', '-y',
            '-loop', '1', '-i', str(avatar_path),
            '-i', str(audio_path),
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-pix_fmt', 'yuv420p',
            '-vf', 'scale=1280:720',
            '-shortest',
            '-t', '10',
            str(output_path)
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

        # 清理临时文件
        if audio_path.exists():
            audio_path.unlink()

        if result.returncode == 0 and output_path.exists():
            logger.info(f"简单数字人视频生成成功: {output_path}")
            return True
        else:
            logger.error(f"简单视频生成失败: {result.stderr}")
            return False

    except Exception as e:
        logger.error(f"简单视频生成异常: {e}")
        return False

def _generate_placeholder_video(data: Dict[str, Any], output_path: Path):
    """生成占位视频"""
    try:
        import subprocess

        # 创建简单的文字视频
        name = data.get("name", "数字人")
        text = f"数字人: {name}"

        cmd = [
            'ffmpeg', '-y',
            '-f', 'lavfi',
            '-i', f'color=c=lightgray:size=1280x720:duration=10',
            '-vf', f'drawtext=text="{text}":fontcolor=black:fontsize=60:x=(w-text_w)/2:y=(h-text_h)/2',
            '-c:v', 'libx264',
            '-pix_fmt', 'yuv420p',
            str(output_path)
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            logger.info(f"占位视频生成成功: {output_path}")
        else:
            logger.error(f"占位视频生成失败，创建基础文件: {result.stderr}")
            # 如果ffmpeg失败，创建基础文件
            with open(output_path, "wb") as f:
                f.write(b"Digital Human Video Placeholder")

    except Exception as e:
        logger.error(f"占位视频生成异常: {e}")
        # 创建基础文件
        with open(output_path, "wb") as f:
            f.write(b"Digital Human Video Placeholder")

# ==================== 本地数字人模型集成 ====================

def _get_available_digital_human_models() -> Dict[str, Dict[str, Any]]:
    """获取可用的本地数字人模型"""
    models = {}

    logger.info("开始检查可用的数字人模型...")

    # 检查MuseTalk
    if MUSETALK_MODEL_DIR.exists():
        is_available = _check_musetalk_model(MUSETALK_MODEL_DIR)
        models["musetalk"] = {
            "name": "MuseTalk",
            "path": MUSETALK_MODEL_DIR,
            "type": "audio_driven",
            "available": is_available
        }
        logger.info(f"MuseTalk模型状态: {'可用' if is_available else '不可用'}")

    # 检查MuseTalk Official
    if MUSETALK_OFFICIAL_DIR.exists():
        models["musetalk_official"] = {
            "name": "MuseTalk Official",
            "path": MUSETALK_OFFICIAL_DIR,
            "type": "audio_driven",
            "available": _check_musetalk_model(MUSETALK_OFFICIAL_DIR)
        }

    # 检查SadTalker
    if SADTALKER_MODEL_DIR.exists():
        models["sadtalker"] = {
            "name": "SadTalker",
            "path": SADTALKER_MODEL_DIR,
            "type": "image_to_video",
            "available": _check_sadtalker_model(SADTALKER_MODEL_DIR)
        }

    # 检查Wav2Lip
    if WAV2LIP_MODEL_DIR.exists():
        models["wav2lip"] = {
            "name": "Wav2Lip",
            "path": WAV2LIP_MODEL_DIR,
            "type": "lip_sync",
            "available": _check_wav2lip_model(WAV2LIP_MODEL_DIR)
        }

    # 检查LivePortrait
    if LIVEPORTRAIT_MODEL_DIR.exists():
        models["liveportrait"] = {
            "name": "LivePortrait",
            "path": LIVEPORTRAIT_MODEL_DIR,
            "type": "portrait_animation",
            "available": _check_liveportrait_model(LIVEPORTRAIT_MODEL_DIR)
        }

    return models

def _check_musetalk_model(model_path: Path) -> bool:
    """检查MuseTalk模型是否可用"""
    try:
        # 检查关键文件（相对于MuseTalk根目录）
        required_files = [
            "models/musetalk/pytorch_model.bin",
            "models/musetalk/config.json",
            "working_musetalk.py"  # 使用实际存在的推理脚本
        ]

        missing_files = []
        for file_path in required_files:
            full_path = model_path / file_path
            if not full_path.exists():
                missing_files.append(file_path)
                logger.warning(f"MuseTalk({model_path.name})缺少文件: {file_path}")

        if missing_files:
            logger.warning(f"MuseTalk({model_path.name})模型不完整，缺少文件: {missing_files}")
            # 即使缺少某些文件，如果核心模型文件存在，仍然可以使用
            core_model = model_path / "models/musetalk/pytorch_model.bin"
            if core_model.exists():
                logger.info(f"MuseTalk({model_path.name})核心模型文件存在，可以继续使用")
                return True
            return False

        logger.info(f"MuseTalk({model_path.name})模型检查通过")
        return True
    except Exception as e:
        logger.error(f"检查MuseTalk({model_path.name})模型失败: {e}")
        return False

def _check_sadtalker_model(model_path: Path) -> bool:
    """检查SadTalker模型是否可用"""
    try:
        # 检查关键文件
        required_files = [
            "checkpoints/SadTalker_V0.0.2_256.safetensors",
            "inference.py"
        ]

        for file_path in required_files:
            if not (model_path / file_path).exists():
                logger.warning(f"SadTalker缺少文件: {file_path}")
                return False

        return True
    except Exception as e:
        logger.error(f"检查SadTalker模型失败: {e}")
        return False

def _check_wav2lip_model(model_path: Path) -> bool:
    """检查Wav2Lip模型是否可用"""
    try:
        # 检查关键文件
        required_files = [
            "checkpoints/wav2lip_gan.pth",
            "inference.py"
        ]

        for file_path in required_files:
            if not (model_path / file_path).exists():
                logger.warning(f"Wav2Lip缺少文件: {file_path}")
                return False

        return True
    except Exception as e:
        logger.error(f"检查Wav2Lip模型失败: {e}")
        return False

def _check_liveportrait_model(model_path: Path) -> bool:
    """检查LivePortrait模型是否可用"""
    try:
        # 检查关键文件
        required_files = [
            "src",
            "inference.py"
        ]

        for file_path in required_files:
            if not (model_path / file_path).exists():
                logger.warning(f"LivePortrait缺少文件: {file_path}")
                return False

        return True
    except Exception as e:
        logger.error(f"检查LivePortrait模型失败: {e}")
        return False

def _generate_with_local_model(model_name: str, model_info: Dict[str, Any], avatar_path: Path, text: str, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """使用本地数字人模型生成视频"""
    try:
        model_path = model_info["path"]
        model_type = model_info["type"]

        if model_name.startswith("musetalk"):
            return _generate_with_musetalk(model_path, avatar_path, text, output_path, tracker)
        elif model_name == "sadtalker":
            return _generate_with_sadtalker(model_path, avatar_path, text, output_path, tracker)
        elif model_name == "wav2lip":
            return _generate_with_wav2lip(model_path, avatar_path, text, output_path, tracker)
        elif model_name == "liveportrait":
            return _generate_with_liveportrait(model_path, avatar_path, text, output_path, tracker)
        else:
            logger.warning(f"未知的模型类型: {model_name}")
            return False

    except Exception as e:
        logger.error(f"使用本地模型{model_name}生成失败: {e}")
        return False

def _generate_with_musetalk(model_path: Path, avatar_path: Path, text: str, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """使用MuseTalk生成视频"""
    try:
        import subprocess
        import sys

        tracker.update(93, "使用MuseTalk生成数字人视频...")

        if not avatar_path or not avatar_path.exists():
            logger.warning("MuseTalk需要头像图片，但未找到")
            return False

        # 检查MuseTalk的推理脚本 - 根据设备选择最优版本
        # MuseTalk脚本优先级（从高到低）
        gpu_script = model_path / "gpu_musetalk_inference.py"
        optimized_v15_script = model_path / "optimized_musetalk_v15.py"
        cpu_optimized_script = model_path / "cpu_optimized_musetalk.py"
        official_dl_script = model_path / "official_musetalk_inference.py"
        enhanced_script = model_path / "enhanced_musetalk_inference.py"
        official_script = model_path / "inference.py"
        real_script = model_path / "real_musetalk_inference.py"
        working_script = model_path / "working_musetalk.py"

        # 根据CUDA可用性选择最优脚本
        if _check_cuda_available():
            # GPU环境：优先使用真正的深度学习MuseTalk
            if gpu_script.exists():
                logger.info("🚀 使用GPU加速MuseTalk推理脚本 (RTX 3060 Ti + 真正的深度学习)")
                script_to_use = gpu_script
            elif official_dl_script.exists():
                logger.info("使用官方MuseTalk深度学习推理脚本 (GPU最高质量)")
                script_to_use = official_dl_script
            elif optimized_v15_script.exists():
                logger.info("使用优化的MuseTalk 1.5推理脚本 (GPU + 最新优化)")
                script_to_use = optimized_v15_script
            elif enhanced_script.exists():
                logger.warning("⚠️ 回退到增强版本MuseTalk (非深度学习，效果较差)")
                script_to_use = enhanced_script
            else:
                logger.info("GPU环境但深度学习脚本不存在，使用CPU优化版本")
                script_to_use = cpu_optimized_script
        else:
            # CPU环境：优先使用增强版本获得更好的唇形同步效果
            if enhanced_script.exists():
                logger.info("使用增强的MuseTalk推理脚本 (CPU + MediaPipe，更好的唇形同步)")
                script_to_use = enhanced_script
            elif official_script.exists():
                logger.info("使用官方MuseTalk推理脚本 (CPU)")
                script_to_use = official_script
            elif real_script.exists():
                logger.info("使用真正的MuseTalk推理脚本 (CPU)")
                script_to_use = real_script
            elif working_script.exists():
                logger.info("使用简化的MuseTalk推理脚本 (CPU)")
                script_to_use = working_script
            elif cpu_optimized_script.exists():
                logger.info("使用CPU优化的MuseTalk推理脚本 (CPU最快，但效果较差)")
                script_to_use = cpu_optimized_script
            else:
                logger.error(f"MuseTalk推理脚本不存在")
                return False

        # 首先需要生成音频文件
        audio_path = output_path.parent / f"temp_audio_{output_path.stem}.wav"
        logger.info(f"为文本生成音频: {text}")

        if not _generate_audio_from_text(text, audio_path):
            logger.warning("无法生成音频文件，使用静默音频")
            _generate_silent_audio(audio_path, 10)  # 10秒静默音频

        # 构建MuseTalk命令 - 根据脚本类型使用不同参数
        if script_to_use.name == "gpu_musetalk_inference.py":
            # GPU加速MuseTalk参数 (最优性能)
            cmd = [
                sys.executable,
                str(script_to_use.absolute()),
                "--source_image", str(avatar_path.absolute()),
                "--driving_audio", str(audio_path.absolute()),
                "--output", str(output_path.absolute()),
                "--device", "cuda",
                "--fps", "25",
                "--quality", "high",
                "--bbox_shift", "0"  # 可调整嘴巴开合程度
            ]
        elif script_to_use.name == "optimized_musetalk_v15.py":
            # 优化的MuseTalk 1.5参数 (最新最优)
            cmd = [
                sys.executable,
                str(script_to_use.absolute()),
                "--source_image", str(avatar_path.absolute()),
                "--driving_audio", str(audio_path.absolute()),
                "--output", str(output_path.absolute()),
                "--device", "cuda" if _check_cuda_available() else "cpu",
                "--fps", "25",
                "--quality", "high",
                "--bbox_shift", "0",  # 可调整嘴巴开合程度
                "--batch_size", "4",
                "--use_float16"  # 启用半精度加速
            ]
        elif script_to_use.name == "cpu_optimized_musetalk.py":
            # CPU优化MuseTalk参数 (最快)
            cmd = [
                sys.executable,
                str(script_to_use.absolute()),
                "--source_image", str(avatar_path.absolute()),
                "--driving_audio", str(audio_path.absolute()),
                "--output", str(output_path.absolute()),
                "--device", "cpu",
                "--fps", "25",
                "--quality", "medium"  # CPU使用中等质量
            ]
        elif script_to_use.name == "official_musetalk_inference.py":
            # 官方深度学习MuseTalk参数 (最高质量)
            cmd = [
                sys.executable,
                str(script_to_use.absolute()),
                "--source_image", str(avatar_path.absolute()),
                "--driving_audio", str(audio_path.absolute()),
                "--output", str(output_path.absolute()),
                "--device", "cuda" if _check_cuda_available() else "cpu",
                "--batch_size", "4",
                "--fps", "25",
                "--quality", "high"
            ]
        elif script_to_use.name == "enhanced_musetalk_inference.py":
            # 增强MuseTalk参数 (MediaPipe + 深度学习)
            cmd = [
                sys.executable,
                str(script_to_use.absolute()),
                "--source_image", str(avatar_path.absolute()),
                "--driving_audio", str(audio_path.absolute()),
                "--output", str(output_path.absolute()),
                "--device", "cuda" if _check_cuda_available() else "cpu",
                "--fps", "25",
                "--quality", "high"
            ]
        elif script_to_use.name == "inference.py":
            # 官方MuseTalk参数
            cmd = [
                sys.executable,
                str(script_to_use.absolute()),
                "--source_image", str(avatar_path.absolute()),
                "--driving_audio", str(audio_path.absolute()),
                "--output", str(output_path.absolute()),
                "--device", "cuda" if _check_cuda_available() else "cpu",
                "--batch_size", "4",  # 减少批处理大小以节省内存
                "--fps", "25",
                "--quality", "high"
            ]
        else:
            # 自定义MuseTalk参数
            cmd = [
                sys.executable,
                str(script_to_use.absolute()),
                "--source_image", str(avatar_path.absolute()),
                "--driving_audio", str(audio_path.absolute()),
                "--output", str(output_path.absolute()),
                "--device", "cuda" if _check_cuda_available() else "cpu"
            ]

        logger.info(f"执行MuseTalk命令: {' '.join(cmd)}")

        # 根据设备类型调整超时时间
        if _check_cuda_available():
            timeout = 120  # GPU: 2分钟
            logger.info("使用GPU，设置超时时间为2分钟")
        else:
            timeout = 300  # CPU: 5分钟
            logger.info("使用CPU，设置超时时间为5分钟")

        # 执行MuseTalk生成
        result = subprocess.run(
            cmd,
            cwd=str(model_path),
            capture_output=True,
            text=True,
            timeout=timeout
        )

        logger.info(f"MuseTalk执行结果: 返回码={result.returncode}")
        if result.stdout:
            logger.info(f"MuseTalk输出: {result.stdout}")
        if result.stderr:
            logger.warning(f"MuseTalk错误: {result.stderr}")

        # 清理临时音频文件
        if audio_path.exists():
            audio_path.unlink()

        if result.returncode == 0 and output_path.exists():
            file_size = output_path.stat().st_size
            logger.info(f"MuseTalk生成成功: {output_path} ({file_size} bytes)")
            return True
        else:
            logger.error(f"MuseTalk生成失败，返回码: {result.returncode}")
            return False

    except subprocess.TimeoutExpired:
        logger.error("MuseTalk生成超时")
        return False
    except Exception as e:
        logger.error(f"MuseTalk生成异常: {e}")
        return False

def _check_cuda_available() -> bool:
    """检查CUDA是否可用"""
    try:
        import torch
        return torch.cuda.is_available()
    except ImportError:
        return False

def _generate_with_sadtalker(model_path: Path, avatar_path: Path, text: str, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """使用SadTalker生成视频"""
    try:
        import subprocess
        import sys

        tracker.update(93, "使用SadTalker生成数字人视频...")

        if not avatar_path or not avatar_path.exists():
            logger.warning("SadTalker需要头像图片，但未找到")
            return False

        # 检查SadTalker的inference脚本
        inference_script = model_path / "inference.py"
        if not inference_script.exists():
            logger.error(f"SadTalker inference脚本不存在: {inference_script}")
            return False

        # 首先需要生成音频文件
        audio_path = output_path.parent / f"temp_audio_{output_path.stem}.wav"
        logger.info(f"为文本生成音频: {text}")

        if not _generate_audio_from_text(text, audio_path):
            logger.warning("无法生成音频文件，使用静默音频")
            _generate_silent_audio(audio_path, 10)  # 10秒静默音频

        # 构建SadTalker命令 - 使用绝对路径
        cmd = [
            sys.executable,
            "inference.py",  # 使用相对路径
            "--source_image", str(avatar_path.absolute()),
            "--driven_audio", str(audio_path.absolute()),
            "--result_dir", str(output_path.parent.absolute()),
            "--still", "--preprocess", "full"
        ]

        # 执行SadTalker生成
        result = subprocess.run(
            cmd,
            cwd=str(model_path),  # 在模型目录中执行
            capture_output=True,
            text=True,
            timeout=180  # 3分钟超时
        )

        if result.returncode == 0:
            # SadTalker通常会在result_dir中生成文件，需要移动到指定位置
            logger.info(f"SadTalker生成成功")
            return True
        else:
            logger.error(f"SadTalker生成失败: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        logger.error("SadTalker生成超时")
        return False
    except Exception as e:
        logger.error(f"SadTalker生成异常: {e}")
        return False

def _generate_with_wav2lip(model_path: Path, avatar_path: Path, text: str, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """使用Wav2Lip生成视频"""
    try:
        import subprocess
        import sys

        tracker.update(93, "使用Wav2Lip生成数字人视频...")

        if not avatar_path or not avatar_path.exists():
            logger.warning("Wav2Lip需要头像图片，但未找到")
            return False

        # 检查Wav2Lip的inference脚本
        inference_script = model_path / "inference.py"
        if not inference_script.exists():
            logger.error(f"Wav2Lip inference脚本不存在: {inference_script}")
            return False

        # 首先需要生成音频文件
        audio_path = output_path.parent / f"temp_audio_{output_path.stem}.wav"
        logger.info(f"为文本生成音频: {text}")

        if not _generate_audio_from_text(text, audio_path):
            logger.warning("无法生成音频文件，使用静默音频")
            _generate_silent_audio(audio_path, 10)  # 10秒静默音频

        # 确保输出目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 构建Wav2Lip命令 - 使用绝对路径
        cmd = [
            sys.executable,
            "inference.py",  # 相对路径
            "--checkpoint_path", "checkpoints/wav2lip_gan.pth",  # 相对路径
            "--face", str(avatar_path.absolute()),  # 绝对路径
            "--audio", str(audio_path.absolute()),  # 绝对路径
            "--outfile", str(output_path.absolute()),  # 绝对路径
            "--static", "True",  # 使用静态图片
            "--fps", "25",
            "--quality", "high",
            "--size", "512"
        ]

        logger.info(f"执行Wav2Lip命令: {' '.join(cmd)}")

        # 执行Wav2Lip生成
        result = subprocess.run(
            cmd,
            cwd=str(model_path),
            capture_output=True,
            text=True,
            timeout=180  # 3分钟超时
        )

        logger.info(f"Wav2Lip执行结果: 返回码={result.returncode}")
        if result.stdout:
            logger.info(f"Wav2Lip输出: {result.stdout}")
        if result.stderr:
            logger.warning(f"Wav2Lip错误: {result.stderr}")

        # 清理临时音频文件
        if audio_path.exists():
            audio_path.unlink()

        if result.returncode == 0 and output_path.exists():
            logger.info(f"Wav2Lip生成成功: {output_path}")
            return True
        else:
            logger.error(f"Wav2Lip生成失败，返回码: {result.returncode}")
            return False

    except subprocess.TimeoutExpired:
        logger.error("Wav2Lip生成超时")
        return False
    except Exception as e:
        logger.error(f"Wav2Lip生成异常: {e}")
        return False

def _generate_with_liveportrait(model_path: Path, avatar_path: Path, text: str, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """使用LivePortrait生成视频"""
    try:
        import subprocess
        import sys

        tracker.update(93, "使用LivePortrait生成数字人视频...")

        if not avatar_path or not avatar_path.exists():
            logger.warning("LivePortrait需要头像图片，但未找到")
            return False

        # 检查LivePortrait的inference脚本
        inference_script = model_path / "inference.py"
        if not inference_script.exists():
            logger.error(f"LivePortrait inference脚本不存在: {inference_script}")
            return False

        # 构建LivePortrait命令 - 使用绝对路径
        cmd = [
            sys.executable,
            "inference.py",  # 使用相对路径
            "-s", str(avatar_path.absolute()),
            "-d", str(avatar_path.absolute()),  # 使用同一张图片作为驱动
            "--output_dir", str(output_path.parent.absolute())
        ]

        # 执行LivePortrait生成
        result = subprocess.run(
            cmd,
            cwd=str(model_path),  # 在模型目录中执行
            capture_output=True,
            text=True,
            timeout=120  # 2分钟超时
        )

        if result.returncode == 0:
            logger.info(f"LivePortrait生成成功")
            return True
        else:
            logger.error(f"LivePortrait生成失败: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        logger.error("LivePortrait生成超时")
        return False
    except Exception as e:
        logger.error(f"LivePortrait生成异常: {e}")
        return False

# ==================== 音频生成函数 ====================

def _generate_audio_from_text(text: str, output_path: Path) -> bool:
    """从文本生成音频"""
    try:
        # 尝试使用TTS生成音频
        # 这里可以集成各种TTS服务

        # 方法1: 使用pyttsx3 (离线TTS)
        try:
            import pyttsx3
            engine = pyttsx3.init()
            engine.save_to_file(text, str(output_path))
            engine.runAndWait()

            # 验证生成的音频文件
            if output_path.exists() and output_path.stat().st_size > 1000:  # 至少1KB
                logger.info(f"使用pyttsx3生成音频成功: {output_path} ({output_path.stat().st_size} bytes)")
                return True
            else:
                logger.warning(f"pyttsx3生成的音频文件太小或无效: {output_path.stat().st_size if output_path.exists() else 0} bytes")
        except ImportError:
            logger.warning("pyttsx3未安装，无法使用离线TTS")
        except Exception as e:
            logger.warning(f"pyttsx3生成音频失败: {e}")

        # 方法2: 使用gTTS (在线TTS)
        try:
            from gtts import gTTS
            tts = gTTS(text=text, lang='zh-cn', slow=False)
            tts.save(str(output_path))

            if output_path.exists():
                logger.info(f"使用gTTS生成音频成功: {output_path}")
                return True
        except ImportError:
            logger.warning("gTTS未安装，无法使用在线TTS")
        except Exception as e:
            logger.warning(f"gTTS生成音频失败: {e}")

        return False

    except Exception as e:
        logger.error(f"音频生成失败: {e}")
        return False

def _generate_silent_audio(output_path: Path, duration: int = 10):
    """生成静默音频"""
    try:
        import subprocess

        # 使用ffmpeg生成静默音频
        cmd = [
            'ffmpeg', '-y',
            '-f', 'lavfi',
            '-i', f'anullsrc=channel_layout=mono:sample_rate=22050',
            '-t', str(duration),
            '-c:a', 'pcm_s16le',
            str(output_path)
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

        if result.returncode == 0 and output_path.exists() and output_path.stat().st_size > 1000:
            logger.info(f"静默音频生成成功: {output_path} ({output_path.stat().st_size} bytes)")
        else:
            logger.error(f"静默音频生成失败: {result.stderr}")
            # 如果ffmpeg失败，使用numpy生成基础音频文件
            try:
                import numpy as np
                import wave

                sample_rate = 22050
                samples = np.zeros(int(sample_rate * duration), dtype=np.int16)

                with wave.open(str(output_path), 'w') as wav_file:
                    wav_file.setnchannels(1)  # 单声道
                    wav_file.setsampwidth(2)  # 16位
                    wav_file.setframerate(sample_rate)
                    wav_file.writeframes(samples.tobytes())

                logger.info(f"使用numpy生成静默音频成功: {output_path}")
            except Exception as e:
                logger.error(f"numpy生成音频也失败: {e}")
                # 最后的回退方案
                with open(output_path, 'wb') as f:
                    f.write(b'RIFF\x24\x08\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x02\x00\x22\x56\x00\x00\x88\x58\x01\x00\x04\x00\x10\x00data\x00\x08\x00\x00')

    except Exception as e:
        logger.error(f"静默音频生成异常: {e}")
        # 创建最基本的WAV文件头
        with open(output_path, 'wb') as f:
            f.write(b'RIFF\x24\x08\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x02\x00\x22\x56\x00\x00\x88\x58\x01\x00\x04\x00\x10\x00data\x00\x08\x00\x00')

def _generate_talking_video_with_opencv(avatar_path: Path, text: str, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """使用OpenCV生成带说话效果的数字人视频"""
    try:
        import cv2
        import numpy as np

        tracker.update(92, "使用OpenCV生成说话效果...")

        if not avatar_path or not avatar_path.exists():
            logger.error("需要头像图片")
            return False

        # 生成音频
        audio_path = output_path.parent / f"temp_audio_{output_path.stem}.wav"
        if not _generate_audio_from_text(text, audio_path):
            _generate_silent_audio(audio_path, 10)

        # 读取头像图片
        img = cv2.imread(str(avatar_path))
        if img is None:
            logger.error(f"无法读取头像图片: {avatar_path}")
            return False

        # 调整图片大小
        height, width = 720, 1280
        img = cv2.resize(img, (width, height))

        # 视频参数
        fps = 25
        duration = 10  # 秒
        total_frames = fps * duration

        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        temp_video_path = output_path.with_suffix('.temp.mp4')
        video_writer = cv2.VideoWriter(str(temp_video_path), fourcc, fps, (width, height))

        if not video_writer.isOpened():
            logger.error("无法创建视频写入器")
            return False

        # 生成说话效果的帧
        for frame_idx in range(total_frames):
            # 创建说话效果：轻微的缩放和位移
            progress = frame_idx / total_frames

            # 模拟说话的周期性运动
            talk_cycle = np.sin(frame_idx * 0.5) * 0.02  # 说话周期
            zoom_factor = 1.0 + talk_cycle * 0.05  # 轻微缩放

            # 计算变换
            center_x, center_y = width // 2, height // 2

            # 创建变换矩阵
            M = cv2.getRotationMatrix2D((center_x, center_y), 0, zoom_factor)

            # 应用变换
            frame = cv2.warpAffine(img, M, (width, height))

            # 添加轻微的亮度变化模拟说话
            brightness = 1.0 + talk_cycle * 0.1
            frame = cv2.convertScaleAbs(frame, alpha=brightness, beta=0)

            # 写入帧
            video_writer.write(frame)

            # 更新进度
            if frame_idx % (total_frames // 10) == 0:
                progress_percent = 92 + (frame_idx / total_frames) * 3
                tracker.update(int(progress_percent), f"生成说话视频帧 {frame_idx}/{total_frames}")

        video_writer.release()

        # 合并音频和视频
        tracker.update(95, "合并音频和视频...")
        success = _merge_video_audio(temp_video_path, audio_path, output_path)

        # 清理临时文件
        if temp_video_path.exists():
            temp_video_path.unlink()
        if audio_path.exists():
            audio_path.unlink()

        if success:
            logger.info(f"OpenCV数字人视频生成成功: {output_path}")
            return True
        else:
            logger.error("音视频合并失败")
            return False

    except ImportError:
        logger.warning("OpenCV未安装，无法生成说话效果")
        return False
    except Exception as e:
        logger.error(f"OpenCV视频生成异常: {e}")
        return False

def _merge_video_audio(video_path: Path, audio_path: Path, output_path: Path) -> bool:
    """合并视频和音频"""
    try:
        import subprocess

        # 检查输入文件
        if not video_path.exists():
            logger.error(f"视频文件不存在: {video_path}")
            return False

        if not audio_path.exists():
            logger.error(f"音频文件不存在: {audio_path}")
            return False

        logger.info(f"合并视频: {video_path} ({video_path.stat().st_size} bytes)")
        logger.info(f"合并音频: {audio_path} ({audio_path.stat().st_size} bytes)")

        cmd = [
            'ffmpeg', '-y',
            '-i', str(video_path),
            '-i', str(audio_path),
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-pix_fmt', 'yuv420p',
            '-shortest',
            str(output_path)
        ]

        logger.info(f"执行合并命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

        logger.info(f"合并结果: 返回码={result.returncode}")
        if result.stdout:
            logger.info(f"合并输出: {result.stdout}")
        if result.stderr:
            logger.warning(f"合并错误: {result.stderr}")

        if result.returncode == 0 and output_path.exists():
            final_size = output_path.stat().st_size
            logger.info(f"合并成功，输出文件大小: {final_size} bytes")

            if final_size < 1000:  # 如果文件太小，可能有问题
                logger.warning(f"输出文件太小: {final_size} bytes，可能合并失败")
                # 尝试只复制视频文件
                import shutil
                shutil.copy2(video_path, output_path)
                logger.info(f"已复制原视频文件作为输出")

            return True
        else:
            logger.error(f"音视频合并失败: 返回码={result.returncode}")
            # 如果合并失败，尝试只使用视频文件
            if video_path.exists():
                import shutil
                shutil.copy2(video_path, output_path)
                logger.info(f"合并失败，已复制原视频文件")
                return True
            return False

    except Exception as e:
        logger.error(f"音视频合并异常: {e}")
        # 尝试复制原视频文件
        try:
            if video_path.exists():
                import shutil
                shutil.copy2(video_path, output_path)
                logger.info(f"异常情况下已复制原视频文件")
                return True
        except:
            pass
        return False
