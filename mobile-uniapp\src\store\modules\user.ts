import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import user<PERSON><PERSON>, { type UserInfo as ApiUserInfo, type LoginParams, type LoginResponse } from '@/api/user'

export interface UserInfo {
  id: string
  username: string
  email: string
  avatar?: string
  nickname?: string
  phone?: string
  createTime?: string
  lastLoginTime?: string
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  const isLoggedIn = ref<boolean>(false)
  const loginLoading = ref<boolean>(false)

  // 计算属性
  const isAuthenticated = computed(() => {
    return !!token.value && !!userInfo.value
  })

  const userDisplayName = computed(() => {
    if (!userInfo.value) return ''
    return userInfo.value.nickname || userInfo.value.username || '用户'
  })

  // 登录
  const login = async (credentials: { username: string; password: string }) => {
    try {
      loginLoading.value = true
      
      const response = await userApi.login(credentials)
      
      if (response.success) {
        token.value = response.data.token
        userInfo.value = response.data.user
        isLoggedIn.value = true
        
        // 保存到本地存储
        uni.setStorageSync('token', token.value)
        uni.setStorageSync('userInfo', userInfo.value)
        
        return { success: true }
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error: any) {
      console.error('登录失败:', error)
      return { 
        success: false, 
        message: error.message || '登录失败，请检查网络连接' 
      }
    } finally {
      loginLoading.value = false
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      // 调用退出登录API
      await userApi.logout()
    } catch (error) {
      console.error('退出登录API调用失败:', error)
    } finally {
      // 清除本地状态
      token.value = ''
      userInfo.value = null
      isLoggedIn.value = false
      
      // 清除本地存储
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      
      // 跳转到登录页
      uni.reLaunch({
        url: '/pages/user/login/index'
      })
    }
  }

  // 检查登录状态
  const checkLoginStatus = async () => {
    try {
      // 从本地存储获取token
      const localToken = uni.getStorageSync('token')
      const localUserInfo = uni.getStorageSync('userInfo')
      
      if (localToken && localUserInfo) {
        token.value = localToken
        userInfo.value = localUserInfo
        isLoggedIn.value = true
        
        // 验证token是否有效
        try {
          const response = await userApi.getUserInfo()
          if (response.success) {
            userInfo.value = response.data
          } else {
            // token无效，清除登录状态
            await logout()
          }
        } catch (error) {
          console.error('验证token失败:', error)
          // 网络错误时保持本地登录状态，但不清除
        }
      }
    } catch (error) {
      console.error('检查登录状态失败:', error)
    }
  }

  // 更新用户信息
  const updateUserInfo = async (updateData: Partial<UserInfo>) => {
    try {
      const response = await userApi.updateUserInfo(updateData)
      
      if (response.success) {
        userInfo.value = { ...userInfo.value, ...response.data }
        
        // 更新本地存储
        uni.setStorageSync('userInfo', userInfo.value)
        
        return { success: true }
      } else {
        throw new Error(response.message || '更新失败')
      }
    } catch (error: any) {
      console.error('更新用户信息失败:', error)
      return { 
        success: false, 
        message: error.message || '更新失败' 
      }
    }
  }

  // 修改密码
  const changePassword = async (passwordData: {
    oldPassword: string
    newPassword: string
  }) => {
    try {
      const response = await userApi.changePassword(passwordData)
      
      if (response.success) {
        uni.showToast({
          title: '密码修改成功',
          icon: 'success'
        })
        return { success: true }
      } else {
        throw new Error(response.message || '密码修改失败')
      }
    } catch (error: any) {
      console.error('修改密码失败:', error)
      return { 
        success: false, 
        message: error.message || '密码修改失败' 
      }
    }
  }

  // 上传头像
  const uploadAvatar = async (filePath: string) => {
    try {
      const response = await userApi.uploadAvatar(filePath)
      
      if (response.success) {
        if (userInfo.value) {
          userInfo.value.avatar = response.data.avatar
          uni.setStorageSync('userInfo', userInfo.value)
        }
        
        uni.showToast({
          title: '头像上传成功',
          icon: 'success'
        })
        
        return { success: true, avatar: response.data.avatar }
      } else {
        throw new Error(response.message || '头像上传失败')
      }
    } catch (error: any) {
      console.error('上传头像失败:', error)
      return { 
        success: false, 
        message: error.message || '头像上传失败' 
      }
    }
  }

  // 获取用户统计信息
  const getUserStats = async () => {
    try {
      const response = await userApi.getUserStats()
      return response.success ? response.data : null
    } catch (error) {
      console.error('获取用户统计失败:', error)
      return null
    }
  }

  return {
    // 状态
    token,
    userInfo,
    isLoggedIn,
    loginLoading,
    
    // 计算属性
    isAuthenticated,
    userDisplayName,
    
    // 方法
    login,
    logout,
    checkLoginStatus,
    updateUserInfo,
    changePassword,
    uploadAvatar,
    getUserStats
  }
}, {
  // 持久化配置
  persist: {
    key: 'user-store',
    paths: ['token', 'userInfo', 'isLoggedIn']
  }
})
