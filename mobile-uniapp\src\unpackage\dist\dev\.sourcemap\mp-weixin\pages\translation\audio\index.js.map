{"version": 3, "file": "index.js", "sources": ["pages/translation/audio/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdHJhbnNsYXRpb24vYXVkaW8vaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"audio-translation-page\">\n    <!-- 页面标题 -->\n    <view class=\"page-header\">\n      <text class=\"page-title\">音频翻译</text>\n      <text class=\"page-desc\">支持实时录音和音频文件翻译</text>\n    </view>\n\n    <!-- 语言选择 -->\n    <view class=\"language-selector\">\n      <view class=\"language-item\">\n        <text class=\"language-label\">源语言</text>\n        <u-button \n          type=\"text\" \n          :text=\"sourceLanguage?.name || '自动检测'\" \n          @click=\"showSourceLanguagePicker = true\"\n          :custom-style=\"{ color: '#2563EB' }\"\n        >\n          <u-icon name=\"arrow-down\" size=\"12\" color=\"#2563EB\" margin-left=\"4\"></u-icon>\n        </u-button>\n      </view>\n      \n      <view class=\"swap-button\" @click=\"swapLanguages\">\n        <u-icon name=\"arrow-left-right\" size=\"20\" color=\"#6B7280\"></u-icon>\n      </view>\n      \n      <view class=\"language-item\">\n        <text class=\"language-label\">目标语言</text>\n        <u-button \n          type=\"text\" \n          :text=\"targetLanguage?.name || '中文'\" \n          @click=\"showTargetLanguagePicker = true\"\n          :custom-style=\"{ color: '#2563EB' }\"\n        >\n          <u-icon name=\"arrow-down\" size=\"12\" color=\"#2563EB\" margin-left=\"4\"></u-icon>\n        </u-button>\n      </view>\n    </view>\n\n    <!-- 录音模式 -->\n    <view class=\"recording-section\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">实时录音翻译</text>\n        <text class=\"title-desc\">点击开始录音，实时翻译语音内容</text>\n      </view>\n      \n      <view class=\"recording-container\">\n        <view class=\"recording-visualizer\" v-if=\"isRecording\">\n          <view \n            class=\"wave-bar\"\n            v-for=\"(height, index) in waveData\"\n            :key=\"index\"\n            :style=\"{ height: height + 'rpx' }\"\n          ></view>\n        </view>\n        \n        <view class=\"recording-controls\">\n          <view \n            class=\"record-button\"\n            :class=\"{ recording: isRecording, disabled: isProcessing }\"\n            @click=\"toggleRecording\"\n          >\n            <u-icon \n              :name=\"isRecording ? 'stop' : 'mic'\" \n              size=\"32\" \n              color=\"#fff\"\n            ></u-icon>\n          </view>\n          \n          <view class=\"recording-info\" v-if=\"isRecording\">\n            <text class=\"recording-time\">{{ formatTime(recordingTime) }}</text>\n            <text class=\"recording-status\">正在录音...</text>\n          </view>\n          \n          <view class=\"processing-info\" v-if=\"isProcessing\">\n            <u-loading-icon mode=\"flower\"></u-loading-icon>\n            <text class=\"processing-text\">正在处理音频...</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 文件上传模式 -->\n    <view class=\"upload-section\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">音频文件翻译</text>\n        <text class=\"title-desc\">支持MP3、WAV、M4A等格式，最大100MB</text>\n      </view>\n      \n      <view class=\"upload-area\" @click=\"chooseAudioFile\">\n        <view class=\"upload-content\" v-if=\"!selectedAudioFile\">\n          <u-icon name=\"cloud-upload\" size=\"48\" color=\"#2563EB\"></u-icon>\n          <text class=\"upload-text\">选择音频文件</text>\n          <text class=\"upload-desc\">支持 MP3、WAV、M4A、AAC 格式</text>\n        </view>\n        \n        <view class=\"file-info\" v-else>\n          <view class=\"file-icon\">\n            <u-icon name=\"music\" size=\"32\" color=\"#2563EB\"></u-icon>\n          </view>\n          <view class=\"file-details\">\n            <text class=\"file-name\">{{ selectedAudioFile.name }}</text>\n            <text class=\"file-size\">{{ formatFileSize(selectedAudioFile.size) }}</text>\n            <text class=\"file-duration\" v-if=\"selectedAudioFile.duration\">\n              时长: {{ formatDuration(selectedAudioFile.duration) }}\n            </text>\n          </view>\n          <view class=\"file-actions\">\n            <u-icon name=\"play-circle\" size=\"24\" color=\"#2563EB\" @click.stop=\"playAudio\"></u-icon>\n            <u-icon name=\"close-circle\" size=\"24\" color=\"#EF4444\" @click.stop=\"removeAudioFile\"></u-icon>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"upload-options\" v-if=\"selectedAudioFile\">\n        <view class=\"option-item\">\n          <text class=\"option-label\">翻译模式</text>\n          <u-radio-group v-model=\"translationMode\">\n            <u-radio name=\"full\" label=\"完整翻译\"></u-radio>\n            <u-radio name=\"segment\" label=\"分段翻译\"></u-radio>\n          </u-radio-group>\n        </view>\n        \n        <view class=\"option-item\">\n          <text class=\"option-label\">输出格式</text>\n          <u-radio-group v-model=\"outputFormat\">\n            <u-radio name=\"text\" label=\"纯文本\"></u-radio>\n            <u-radio name=\"srt\" label=\"字幕文件\"></u-radio>\n            <u-radio name=\"audio\" label=\"语音合成\"></u-radio>\n          </u-radio-group>\n        </view>\n      </view>\n      \n      <view class=\"translate-button\" v-if=\"selectedAudioFile\">\n        <u-button \n          type=\"primary\" \n          size=\"large\"\n          :loading=\"isTranslating\"\n          :disabled=\"!canTranslate\"\n          @click=\"translateAudioFile\"\n          :custom-style=\"{ width: '100%' }\"\n        >\n          {{ isTranslating ? '翻译中...' : '开始翻译' }}\n        </u-button>\n      </view>\n    </view>\n\n    <!-- 翻译结果 -->\n    <view class=\"translation-result\" v-if=\"translationResult\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">翻译结果</text>\n        <view class=\"result-actions\">\n          <u-icon name=\"copy\" size=\"18\" color=\"#2563EB\" @click=\"copyResult\"></u-icon>\n          <u-icon name=\"share\" size=\"18\" color=\"#2563EB\" margin-left=\"12\" @click=\"shareResult\"></u-icon>\n          <u-icon name=\"download\" size=\"18\" color=\"#2563EB\" margin-left=\"12\" @click=\"downloadResult\"></u-icon>\n        </view>\n      </view>\n      \n      <view class=\"result-content\">\n        <!-- 文本结果 -->\n        <view class=\"text-result\" v-if=\"translationResult.type === 'text'\">\n          <view class=\"original-text\">\n            <text class=\"result-label\">原文:</text>\n            <text class=\"result-text\" selectable>{{ translationResult.originalText }}</text>\n          </view>\n          <view class=\"translated-text\">\n            <text class=\"result-label\">译文:</text>\n            <text class=\"result-text\" selectable>{{ translationResult.translatedText }}</text>\n          </view>\n        </view>\n        \n        <!-- 分段结果 -->\n        <view class=\"segment-result\" v-if=\"translationResult.type === 'segments'\">\n          <view \n            class=\"segment-item\"\n            v-for=\"(segment, index) in translationResult.segments\"\n            :key=\"index\"\n          >\n            <view class=\"segment-time\">{{ formatTime(segment.startTime) }} - {{ formatTime(segment.endTime) }}</view>\n            <view class=\"segment-original\">{{ segment.originalText }}</view>\n            <view class=\"segment-translated\">{{ segment.translatedText }}</view>\n          </view>\n        </view>\n        \n        <!-- 音频结果 -->\n        <view class=\"audio-result\" v-if=\"translationResult.type === 'audio'\">\n          <view class=\"audio-player\">\n            <u-icon \n              :name=\"isPlayingResult ? 'pause-circle-fill' : 'play-circle-fill'\" \n              size=\"48\" \n              color=\"#2563EB\"\n              @click=\"toggleResultAudio\"\n            ></u-icon>\n            <view class=\"audio-info\">\n              <text class=\"audio-title\">翻译语音</text>\n              <text class=\"audio-duration\">{{ formatDuration(translationResult.duration) }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 翻译历史 -->\n    <view class=\"translation-history\" v-if=\"audioHistory.length > 0\">\n      <view class=\"section-title\">\n        <text class=\"title-text\">翻译历史</text>\n        <u-button \n          type=\"text\" \n          text=\"查看全部\" \n          size=\"small\"\n          @click=\"viewAllHistory\"\n          :custom-style=\"{ color: '#2563EB', fontSize: '24rpx' }\"\n        ></u-button>\n      </view>\n      \n      <view class=\"history-list\">\n        <view \n          class=\"history-item\"\n          v-for=\"item in audioHistory.slice(0, 3)\"\n          :key=\"item.id\"\n          @click=\"viewHistoryItem(item)\"\n        >\n          <view class=\"history-icon\">\n            <u-icon name=\"music\" size=\"20\" color=\"#2563EB\"></u-icon>\n          </view>\n          <view class=\"history-content\">\n            <text class=\"history-title\">{{ item.filename || '录音翻译' }}</text>\n            <text class=\"history-desc\">{{ item.originalText.substring(0, 50) }}...</text>\n            <text class=\"history-time\">{{ formatTime(item.createdAt) }}</text>\n          </view>\n          <view class=\"history-actions\">\n            <u-icon name=\"play-circle\" size=\"16\" color=\"#6B7280\"></u-icon>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 语言选择器 -->\n    <u-picker\n      v-model=\"showSourceLanguagePicker\"\n      :columns=\"sourceLanguageColumns\"\n      @confirm=\"onSourceLanguageConfirm\"\n      @cancel=\"showSourceLanguagePicker = false\"\n    ></u-picker>\n\n    <u-picker\n      v-model=\"showTargetLanguagePicker\"\n      :columns=\"targetLanguageColumns\"\n      @confirm=\"onTargetLanguageConfirm\"\n      @cancel=\"showTargetLanguagePicker = false\"\n    ></u-picker>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\nimport { useTranslationStore } from '@/store/modules/translation'\nimport dayjs from 'dayjs'\n\n// Store\nconst translationStore = useTranslationStore()\n\n// 响应式数据\nconst isRecording = ref<boolean>(false)\nconst isProcessing = ref<boolean>(false)\nconst isTranslating = ref<boolean>(false)\nconst isPlayingResult = ref<boolean>(false)\nconst recordingTime = ref<number>(0)\nconst waveData = ref<number[]>(Array(20).fill(20))\nconst selectedAudioFile = ref<any>(null)\nconst translationResult = ref<any>(null)\nconst audioHistory = ref<any[]>([])\nconst showSourceLanguagePicker = ref<boolean>(false)\nconst showTargetLanguagePicker = ref<boolean>(false)\nconst translationMode = ref<string>('full')\nconst outputFormat = ref<string>('text')\n\n// 录音相关\nlet recordingTimer: NodeJS.Timeout | null = null\nlet waveTimer: NodeJS.Timeout | null = null\nlet audioContext: any = null\nlet mediaRecorder: any = null\n\n// 计算属性\nconst {\n  languages,\n  currentSourceLang,\n  currentTargetLang,\n  sourceLanguage,\n  targetLanguage\n} = translationStore\n\nconst canTranslate = computed(() => {\n  return selectedAudioFile.value && currentSourceLang.value && currentTargetLang.value\n})\n\nconst sourceLanguageColumns = computed(() => {\n  const autoDetect = [{ text: '自动检测', value: 'auto' }]\n  const languageOptions = languages.map(lang => ({\n    text: lang.name,\n    value: lang.code\n  }))\n  return [autoDetect.concat(languageOptions)]\n})\n\nconst targetLanguageColumns = computed(() => {\n  const languageOptions = languages.filter(lang => lang.code !== 'auto').map(lang => ({\n    text: lang.name,\n    value: lang.code\n  }))\n  return [languageOptions]\n})\n\n// 页面加载\nonMounted(async () => {\n  await translationStore.loadLanguages()\n  loadAudioHistory()\n})\n\n// 页面卸载\nonUnmounted(() => {\n  stopRecording()\n  clearTimers()\n})\n\n// 切换录音状态\nconst toggleRecording = async () => {\n  if (isProcessing.value) return\n\n  if (isRecording.value) {\n    await stopRecording()\n  } else {\n    await startRecording()\n  }\n}\n\n// 开始录音\nconst startRecording = async () => {\n  try {\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    isRecording.value = true\n    recordingTime.value = 0\n\n    // 开始计时\n    recordingTimer = setInterval(() => {\n      recordingTime.value++\n    }, 1000)\n\n    // 开始波形动画\n    startWaveAnimation()\n\n    uni.showToast({\n      title: '开始录音',\n      icon: 'success'\n    })\n\n  } catch (error) {\n    uni.__f__('error','at pages/translation/audio/index.vue:390','开始录音失败:', error)\n    uni.showToast({\n      title: '录音权限被拒绝',\n      icon: 'error'\n    })\n  }\n}\n\n// 停止录音\nconst stopRecording = async () => {\n  isRecording.value = false\n  isProcessing.value = true\n\n  clearTimers()\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  uni.showToast({\n    title: '录音结束',\n    icon: 'success'\n  })\n}\n\n// 处理录音音频\nconst processRecordedAudio = async (audioBlob: Blob) => {\n  try {\n    // 这里应该调用语音识别API\n    // 模拟处理过程\n    await new Promise(resolve => setTimeout(resolve, 2000))\n\n    const mockResult = {\n      type: 'text',\n      originalText: '这是录音识别的原文内容',\n      translatedText: 'This is the original text content recognized from the recording',\n      confidence: 0.95,\n      duration: recordingTime.value\n    }\n\n    translationResult.value = mockResult\n\n    // 添加到历史记录\n    const historyItem = {\n      id: Date.now().toString(),\n      type: 'recording',\n      originalText: mockResult.originalText,\n      translatedText: mockResult.translatedText,\n      sourceLang: currentSourceLang.value,\n      targetLang: currentTargetLang.value,\n      duration: recordingTime.value,\n      createdAt: new Date().toISOString()\n    }\n\n    audioHistory.value.unshift(historyItem)\n    saveAudioHistory()\n\n  } catch (error) {\n    uni.__f__('error','at pages/translation/audio/index.vue:459','处理录音失败:', error)\n    uni.showToast({\n      title: '处理录音失败',\n      icon: 'error'\n    })\n  } finally {\n    isProcessing.value = false\n  }\n}\n\n// 处理录音文件 (App)\nconst processRecordedAudioFile = async (filePath: string) => {\n  // App环境下处理录音文件\n  await processRecordedAudio(new Blob())\n}\n\n// 开始波形动画\nconst startWaveAnimation = () => {\n  waveTimer = setInterval(() => {\n    waveData.value = waveData.value.map(() =>\n      Math.floor(Math.random() * 60) + 20\n    )\n  }, 100)\n}\n\n// 清除定时器\nconst clearTimers = () => {\n  if (recordingTimer) {\n    clearInterval(recordingTimer)\n    recordingTimer = null\n  }\n\n  if (waveTimer) {\n    clearInterval(waveTimer)\n    waveTimer = null\n  }\n}\n\n// 选择音频文件\nconst chooseAudioFile = () => {\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  uni.chooseFile({\n    count: 1,\n    type: 'file',\n    extension: ['.mp3', '.wav', '.m4a', '.aac'],\n    success: (res) => {\n      const file = res.tempFiles[0]\n      handleSelectedFile(file)\n    },\n    fail: (error) => {\n      uni.__f__('error','at pages/translation/audio/index.vue:522','选择文件失败:', error)\n    }\n  })\n\n}\n\n// 处理选中的文件\nconst handleSelectedFile = (file: any) => {\n  // 检查文件大小\n  if (file.size > 100 * 1024 * 1024) {\n    uni.showToast({\n      title: '文件大小不能超过100MB',\n      icon: 'error'\n    })\n    return\n  }\n\n  selectedAudioFile.value = {\n    name: file.name,\n    size: file.size,\n    path: file.path || URL.createObjectURL(file),\n    duration: null // 这里可以获取音频时长\n  }\n\n  // 获取音频时长\n  getAudioDuration(selectedAudioFile.value.path)\n}\n\n// 获取音频时长\nconst getAudioDuration = (audioPath: string) => {\n\n\n\n\n\n\n\n\n}\n\n// 移除音频文件\nconst removeAudioFile = () => {\n  selectedAudioFile.value = null\n  translationResult.value = null\n}\n\n// 播放音频\nconst playAudio = () => {\n  if (!selectedAudioFile.value) return\n\n\n\n\n\n\n\n\n\n\n\n\n}\n\n// 翻译音频文件\nconst translateAudioFile = async () => {\n  if (!canTranslate.value) return\n\n  try {\n    isTranslating.value = true\n\n    // 模拟翻译过程\n    await new Promise(resolve => setTimeout(resolve, 3000))\n\n    const mockResult = {\n      type: outputFormat.value === 'segment' ? 'segments' : outputFormat.value,\n      originalText: '这是音频文件中识别的原文内容',\n      translatedText: 'This is the original text content recognized from the audio file',\n      segments: outputFormat.value === 'segment' ? [\n        {\n          startTime: 0,\n          endTime: 5,\n          originalText: '第一段原文',\n          translatedText: 'First segment translation'\n        },\n        {\n          startTime: 5,\n          endTime: 10,\n          originalText: '第二段原文',\n          translatedText: 'Second segment translation'\n        }\n      ] : null,\n      duration: selectedAudioFile.value.duration,\n      audioUrl: outputFormat.value === 'audio' ? 'mock-audio-url' : null\n    }\n\n    translationResult.value = mockResult\n\n    // 添加到历史记录\n    const historyItem = {\n      id: Date.now().toString(),\n      type: 'file',\n      filename: selectedAudioFile.value.name,\n      originalText: mockResult.originalText,\n      translatedText: mockResult.translatedText,\n      sourceLang: currentSourceLang.value,\n      targetLang: currentTargetLang.value,\n      duration: selectedAudioFile.value.duration,\n      createdAt: new Date().toISOString()\n    }\n\n    audioHistory.value.unshift(historyItem)\n    saveAudioHistory()\n\n    uni.showToast({\n      title: '翻译完成',\n      icon: 'success'\n    })\n\n  } catch (error) {\n    uni.__f__('error','at pages/translation/audio/index.vue:641','翻译失败:', error)\n    uni.showToast({\n      title: '翻译失败',\n      icon: 'error'\n    })\n  } finally {\n    isTranslating.value = false\n  }\n}\n\n// 交换语言\nconst swapLanguages = () => {\n  translationStore.swapLanguages()\n}\n\n// 语言选择确认\nconst onSourceLanguageConfirm = (value: any) => {\n  translationStore.setSourceLanguage(value[0].value)\n  showSourceLanguagePicker.value = false\n}\n\nconst onTargetLanguageConfirm = (value: any) => {\n  translationStore.setTargetLanguage(value[0].value)\n  showTargetLanguagePicker.value = false\n}\n\n// 复制结果\nconst copyResult = () => {\n  if (!translationResult.value) return\n\n  let textToCopy = ''\n  if (translationResult.value.type === 'text') {\n    textToCopy = `原文: ${translationResult.value.originalText}\\n译文: ${translationResult.value.translatedText}`\n  } else if (translationResult.value.type === 'segments') {\n    textToCopy = translationResult.value.segments.map((seg: any) =>\n      `${formatTime(seg.startTime)}-${formatTime(seg.endTime)}\\n${seg.originalText}\\n${seg.translatedText}`\n    ).join('\\n\\n')\n  }\n\n  uni.setClipboardData({\n    data: textToCopy,\n    success: () => {\n      uni.showToast({\n        title: '复制成功',\n        icon: 'success'\n      })\n    }\n  })\n}\n\n// 分享结果\nconst shareResult = () => {\n  if (!translationResult.value) return\n\n  uni.share({\n    provider: 'weixin',\n    scene: 'WXSceneSession',\n    type: 0,\n    summary: `音频翻译结果: ${translationResult.value.translatedText}`,\n    success: () => {\n      uni.showToast({\n        title: '分享成功',\n        icon: 'success'\n      })\n    }\n  })\n}\n\n// 下载结果\nconst downloadResult = () => {\n  if (!translationResult.value) return\n\n  uni.showToast({\n    title: '下载功能开发中',\n    icon: 'none'\n  })\n}\n\n// 播放结果音频\nconst toggleResultAudio = () => {\n  if (!translationResult.value?.audioUrl) return\n\n  if (isPlayingResult.value) {\n    uni.stopBackgroundAudio()\n    isPlayingResult.value = false\n  } else {\n    uni.playBackgroundAudio({\n      dataUrl: translationResult.value.audioUrl,\n      title: '翻译语音',\n      success: () => {\n        isPlayingResult.value = true\n      }\n    })\n  }\n}\n\n// 工具函数\nconst formatTime = (seconds: number) => {\n  const mins = Math.floor(seconds / 60)\n  const secs = Math.floor(seconds % 60)\n  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n}\n\nconst formatDuration = (seconds: number) => {\n  if (!seconds) return '00:00'\n  return formatTime(seconds)\n}\n\nconst formatFileSize = (bytes: number) => {\n  if (bytes === 0) return '0 B'\n  const k = 1024\n  const sizes = ['B', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// 历史记录相关\nconst saveAudioHistory = () => {\n  uni.setStorageSync('audio_translation_history', audioHistory.value)\n}\n\nconst loadAudioHistory = () => {\n  const history = uni.getStorageSync('audio_translation_history') || []\n  audioHistory.value = history\n}\n\nconst viewAllHistory = () => {\n  uni.navigateTo({\n    url: '/pages/translation/audio/history/index'\n  })\n}\n\nconst viewHistoryItem = (item: any) => {\n  translationResult.value = {\n    type: 'text',\n    originalText: item.originalText,\n    translatedText: item.translatedText\n  }\n}\n\n// 页面下拉刷新\nconst onPullDownRefresh = () => {\n  loadAudioHistory()\n  uni.stopPullDownRefresh()\n}\n\n// 导出方法供页面使用\ndefineExpose({\n  onPullDownRefresh\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.audio-translation-page {\n  min-height: 100vh;\n  background-color: $bg-secondary;\n}\n\n// 页面头部\n.page-header {\n  background-color: $bg-primary;\n  padding: $spacing-6 $spacing-4;\n  text-align: center;\n  border-bottom: 1px solid $border-primary;\n\n  .page-title {\n    display: block;\n    font-size: $font-size-2xl;\n    font-weight: $font-weight-bold;\n    color: $text-primary;\n    margin-bottom: $spacing-2;\n  }\n\n  .page-desc {\n    font-size: $font-size-base;\n    color: $text-secondary;\n  }\n}\n\n// 语言选择器\n.language-selector {\n  background-color: $bg-primary;\n  padding: $spacing-4;\n  border-bottom: 1px solid $border-primary;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  .language-item {\n    flex: 1;\n    text-align: center;\n\n    .language-label {\n      display: block;\n      font-size: $font-size-sm;\n      color: $text-tertiary;\n      margin-bottom: $spacing-2;\n    }\n  }\n\n  .swap-button {\n    width: 80rpx;\n    height: 80rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background-color: $bg-tertiary;\n    border-radius: $radius-full;\n    margin: 0 $spacing-4;\n\n    &:active {\n      background-color: $gray-200;\n    }\n  }\n}\n\n// 通用区块标题\n.section-title {\n  padding: $spacing-4 $spacing-4 $spacing-3;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  .title-text {\n    font-size: $font-size-lg;\n    font-weight: $font-weight-semibold;\n    color: $text-primary;\n  }\n\n  .title-desc {\n    font-size: $font-size-sm;\n    color: $text-tertiary;\n  }\n\n  .result-actions {\n    display: flex;\n    align-items: center;\n    gap: $spacing-2;\n  }\n}\n\n// 录音区域\n.recording-section {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .recording-container {\n    padding: $spacing-6 $spacing-4;\n\n    .recording-visualizer {\n      display: flex;\n      align-items: end;\n      justify-content: center;\n      gap: 4rpx;\n      height: 120rpx;\n      margin-bottom: $spacing-6;\n\n      .wave-bar {\n        width: 8rpx;\n        background: linear-gradient(to top, $primary, $primary-light);\n        border-radius: 4rpx;\n        transition: height 0.1s ease;\n      }\n    }\n\n    .recording-controls {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: $spacing-4;\n\n      .record-button {\n        width: 160rpx;\n        height: 160rpx;\n        border-radius: $radius-full;\n        background-color: $primary;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: all 0.3s ease;\n\n        &.recording {\n          background-color: $error;\n          animation: pulse 2s infinite;\n        }\n\n        &.disabled {\n          background-color: $gray-400;\n          pointer-events: none;\n        }\n\n        &:active {\n          transform: scale(0.95);\n        }\n      }\n\n      .recording-info,\n      .processing-info {\n        text-align: center;\n\n        .recording-time {\n          display: block;\n          font-size: $font-size-xl;\n          font-weight: $font-weight-bold;\n          color: $text-primary;\n          margin-bottom: $spacing-1;\n        }\n\n        .recording-status,\n        .processing-text {\n          font-size: $font-size-base;\n          color: $text-secondary;\n        }\n      }\n    }\n  }\n}\n\n@keyframes pulse {\n  0% {\n    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);\n  }\n  70% {\n    box-shadow: 0 0 0 20rpx rgba(239, 68, 68, 0);\n  }\n  100% {\n    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);\n  }\n}\n\n// 文件上传区域\n.upload-section {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .upload-area {\n    margin: 0 $spacing-4 $spacing-4;\n    border: 2px dashed $border-primary;\n    border-radius: $radius-lg;\n    padding: $spacing-6;\n\n    .upload-content {\n      text-align: center;\n\n      .upload-text {\n        display: block;\n        font-size: $font-size-lg;\n        font-weight: $font-weight-medium;\n        color: $text-primary;\n        margin: $spacing-4 0 $spacing-2;\n      }\n\n      .upload-desc {\n        font-size: $font-size-base;\n        color: $text-secondary;\n      }\n    }\n\n    .file-info {\n      display: flex;\n      align-items: center;\n      gap: $spacing-3;\n\n      .file-icon {\n        width: 80rpx;\n        height: 80rpx;\n        background-color: $primary-50;\n        border-radius: $radius-lg;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n\n      .file-details {\n        flex: 1;\n\n        .file-name {\n          display: block;\n          font-size: $font-size-base;\n          font-weight: $font-weight-medium;\n          color: $text-primary;\n          margin-bottom: $spacing-1;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n\n        .file-size,\n        .file-duration {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n        }\n      }\n\n      .file-actions {\n        display: flex;\n        gap: $spacing-2;\n      }\n    }\n  }\n\n  .upload-options {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .option-item {\n      margin-bottom: $spacing-4;\n\n      .option-label {\n        display: block;\n        font-size: $font-size-base;\n        font-weight: $font-weight-medium;\n        color: $text-primary;\n        margin-bottom: $spacing-3;\n      }\n    }\n  }\n\n  .translate-button {\n    padding: 0 $spacing-4 $spacing-4;\n  }\n}\n\n// 翻译结果\n.translation-result {\n  background-color: $bg-primary;\n  margin-bottom: $spacing-3;\n\n  .result-content {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .text-result {\n      .original-text,\n      .translated-text {\n        margin-bottom: $spacing-4;\n\n        .result-label {\n          display: block;\n          font-size: $font-size-sm;\n          font-weight: $font-weight-medium;\n          color: $text-secondary;\n          margin-bottom: $spacing-2;\n        }\n\n        .result-text {\n          display: block;\n          font-size: $font-size-base;\n          line-height: $line-height-relaxed;\n          color: $text-primary;\n          background-color: $bg-tertiary;\n          padding: $spacing-3;\n          border-radius: $radius-md;\n          word-break: break-word;\n        }\n      }\n    }\n\n    .segment-result {\n      .segment-item {\n        background-color: $bg-tertiary;\n        border-radius: $radius-md;\n        padding: $spacing-3;\n        margin-bottom: $spacing-3;\n\n        .segment-time {\n          font-size: $font-size-xs;\n          color: $text-tertiary;\n          margin-bottom: $spacing-2;\n        }\n\n        .segment-original {\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          margin-bottom: $spacing-2;\n        }\n\n        .segment-translated {\n          font-size: $font-size-base;\n          color: $text-primary;\n          font-weight: $font-weight-medium;\n        }\n      }\n    }\n\n    .audio-result {\n      .audio-player {\n        display: flex;\n        align-items: center;\n        gap: $spacing-4;\n        background-color: $bg-tertiary;\n        border-radius: $radius-lg;\n        padding: $spacing-4;\n\n        .audio-info {\n          flex: 1;\n\n          .audio-title {\n            display: block;\n            font-size: $font-size-base;\n            font-weight: $font-weight-medium;\n            color: $text-primary;\n            margin-bottom: $spacing-1;\n          }\n\n          .audio-duration {\n            font-size: $font-size-sm;\n            color: $text-secondary;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 翻译历史\n.translation-history {\n  background-color: $bg-primary;\n\n  .history-list {\n    padding: 0 $spacing-4 $spacing-4;\n\n    .history-item {\n      display: flex;\n      align-items: center;\n      gap: $spacing-3;\n      padding: $spacing-3;\n      background-color: $bg-tertiary;\n      border-radius: $radius-lg;\n      margin-bottom: $spacing-2;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      &:active {\n        background-color: $gray-200;\n      }\n\n      .history-icon {\n        width: 64rpx;\n        height: 64rpx;\n        background-color: $primary-50;\n        border-radius: $radius-lg;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n\n      .history-content {\n        flex: 1;\n\n        .history-title {\n          display: block;\n          font-size: $font-size-base;\n          font-weight: $font-weight-medium;\n          color: $text-primary;\n          margin-bottom: $spacing-1;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n\n        .history-desc {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          margin-bottom: $spacing-1;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n\n        .history-time {\n          font-size: $font-size-xs;\n          color: $text-tertiary;\n        }\n      }\n\n      .history-actions {\n        padding: $spacing-2;\n      }\n    }\n  }\n}\n\n// 响应式适配\n@media screen and (max-width: 480px) {\n  .recording-container {\n    padding: $spacing-4;\n\n    .record-button {\n      width: 120rpx;\n      height: 120rpx;\n    }\n\n    .recording-visualizer {\n      height: 80rpx;\n    }\n  }\n\n  .language-selector {\n    flex-direction: column;\n    gap: $spacing-3;\n\n    .swap-button {\n      transform: rotate(90deg);\n    }\n  }\n}\n\n// 暗色模式适配\n@media (prefers-color-scheme: dark) {\n  .audio-translation-page {\n    background-color: #1a1a1a;\n  }\n\n  .page-header,\n  .language-selector,\n  .recording-section,\n  .upload-section,\n  .translation-result,\n  .translation-history {\n    background-color: #2d2d2d;\n    border-color: #404040;\n  }\n\n  .upload-area,\n  .result-text,\n  .segment-item,\n  .audio-player,\n  .history-item {\n    background-color: #404040;\n    border-color: #555555;\n  }\n\n  .swap-button,\n  .file-icon,\n  .history-icon {\n    background-color: #404040;\n  }\n\n  .wave-bar {\n    background: linear-gradient(to top, #3b82f6, #60a5fa);\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/AI_system/mobile-uniapp/src/pages/translation/audio/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useTranslationStore", "ref", "computed", "onMounted", "onUnmounted", "uni"], "mappings": ";;;;;;;;;;;;;;;AAoQA,UAAM,mBAAmBA,0BAAAA;AAGnB,UAAA,cAAcC,kBAAa,KAAK;AAChC,UAAA,eAAeA,kBAAa,KAAK;AACjC,UAAA,gBAAgBA,kBAAa,KAAK;AAClC,UAAA,kBAAkBA,kBAAa,KAAK;AACpC,UAAA,gBAAgBA,kBAAY,CAAC;AACnC,UAAM,WAAWA,cAAAA,IAAc,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC;AAC3C,UAAA,oBAAoBA,kBAAS,IAAI;AACjC,UAAA,oBAAoBA,kBAAS,IAAI;AACjC,UAAA,eAAeA,kBAAW,CAAA,CAAE;AAC5B,UAAA,2BAA2BA,kBAAa,KAAK;AAC7C,UAAA,2BAA2BA,kBAAa,KAAK;AAC7C,UAAA,kBAAkBA,kBAAY,MAAM;AACpC,UAAA,eAAeA,kBAAY,MAAM;AAGvC,QAAI,iBAAwC;AAC5C,QAAI,YAAmC;AAKjC,UAAA;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACE,IAAA;AAEE,UAAA,eAAeC,cAAAA,SAAS,MAAM;AAClC,aAAO,kBAAkB,SAAS,kBAAkB,SAAS,kBAAkB;AAAA,IAAA,CAChF;AAEK,UAAA,wBAAwBA,cAAAA,SAAS,MAAM;AAC3C,YAAM,aAAa,CAAC,EAAE,MAAM,QAAQ,OAAO,QAAQ;AAC7C,YAAA,kBAAkB,UAAU,IAAI,CAAS,UAAA;AAAA,QAC7C,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,MACZ,EAAA;AACF,aAAO,CAAC,WAAW,OAAO,eAAe,CAAC;AAAA,IAAA,CAC3C;AAEK,UAAA,wBAAwBA,cAAAA,SAAS,MAAM;AACrC,YAAA,kBAAkB,UAAU,OAAO,CAAA,SAAQ,KAAK,SAAS,MAAM,EAAE,IAAI,CAAS,UAAA;AAAA,QAClF,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,MACZ,EAAA;AACF,aAAO,CAAC,eAAe;AAAA,IAAA,CACxB;AAGDC,kBAAAA,UAAU,YAAY;AACpB,YAAM,iBAAiB;AACN;IAAA,CAClB;AAGDC,kBAAAA,YAAY,MAAM;AACF;AACF;IAAA,CACb;AAGD,UAAM,kBAAkB,YAAY;AAClC,UAAI,aAAa;AAAO;AAExB,UAAI,YAAY,OAAO;AACrB,cAAM,cAAc;AAAA,MAAA,OACf;AACL,cAAM,eAAe;AAAA,MACvB;AAAA,IAAA;AAIF,UAAM,iBAAiB,YAAY;AAC7B,UAAA;AAkCF,oBAAY,QAAQ;AACpB,sBAAc,QAAQ;AAGtB,yBAAiB,YAAY,MAAM;AACnB,wBAAA;AAAA,WACb,GAAI;AAGY;AAEnBC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,eAEM,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,4CAA2C,WAAW,KAAK;AAC7EA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,IAAA;AAIF,UAAM,gBAAgB,YAAY;AAChC,kBAAY,QAAQ;AACpB,mBAAa,QAAQ;AAET;AAiBZA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAqDH,UAAM,qBAAqB,MAAM;AAC/B,kBAAY,YAAY,MAAM;AACnB,iBAAA,QAAQ,SAAS,MAAM;AAAA,UAAI,MAClC,KAAK,MAAM,KAAK,OAAO,IAAI,EAAE,IAAI;AAAA,QAAA;AAAA,SAElC,GAAG;AAAA,IAAA;AAIR,UAAM,cAAc,MAAM;AACxB,UAAI,gBAAgB;AAClB,sBAAc,cAAc;AACX,yBAAA;AAAA,MACnB;AAEA,UAAI,WAAW;AACb,sBAAc,SAAS;AACX,oBAAA;AAAA,MACd;AAAA,IAAA;AAIF,UAAM,kBAAkB,MAAM;AAe5BA,oBAAAA,MAAI,WAAW;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAC1C,SAAS,CAAC,QAAQ;AACV,gBAAA,OAAO,IAAI,UAAU,CAAC;AAC5B,6BAAmB,IAAI;AAAA,QACzB;AAAA,QACA,MAAM,CAAC,UAAU;AACfA,wBAAA,MAAI,MAAM,SAAQ,4CAA2C,WAAW,KAAK;AAAA,QAC/E;AAAA,MAAA,CACD;AAAA,IAAA;AAKG,UAAA,qBAAqB,CAAC,SAAc;AAExC,UAAI,KAAK,OAAO,MAAM,OAAO,MAAM;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MACF;AAEA,wBAAkB,QAAQ;AAAA,QACxB,MAAM,KAAK;AAAA,QACX,MAAM,KAAK;AAAA,QACX,MAAM,KAAK,QAAQ,IAAI,gBAAgB,IAAI;AAAA,QAC3C,UAAU;AAAA;AAAA,MAAA;AAIK,uBAAA,kBAAkB,MAAM,IAAI;AAAA,IAAA;AAIzC,UAAA,mBAAmB,CAAC,cAAsB;AAAA,IAAA;AAYhD,UAAM,kBAAkB,MAAM;AAC5B,wBAAkB,QAAQ;AAC1B,wBAAkB,QAAQ;AAAA,IAAA;AAI5B,UAAM,YAAY,MAAM;AACtB,UAAI,CAAC,kBAAkB;AAAO;AAAA,IAAA;AAgBhC,UAAM,qBAAqB,YAAY;AACrC,UAAI,CAAC,aAAa;AAAO;AAErB,UAAA;AACF,sBAAc,QAAQ;AAGtB,cAAM,IAAI,QAAQ,CAAA,YAAW,WAAW,SAAS,GAAI,CAAC;AAEtD,cAAM,aAAa;AAAA,UACjB,MAAM,aAAa,UAAU,YAAY,aAAa,aAAa;AAAA,UACnE,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,UAAU,aAAa,UAAU,YAAY;AAAA,YAC3C;AAAA,cACE,WAAW;AAAA,cACX,SAAS;AAAA,cACT,cAAc;AAAA,cACd,gBAAgB;AAAA,YAClB;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,SAAS;AAAA,cACT,cAAc;AAAA,cACd,gBAAgB;AAAA,YAClB;AAAA,UAAA,IACE;AAAA,UACJ,UAAU,kBAAkB,MAAM;AAAA,UAClC,UAAU,aAAa,UAAU,UAAU,mBAAmB;AAAA,QAAA;AAGhE,0BAAkB,QAAQ;AAG1B,cAAM,cAAc;AAAA,UAClB,IAAI,KAAK,IAAI,EAAE,SAAS;AAAA,UACxB,MAAM;AAAA,UACN,UAAU,kBAAkB,MAAM;AAAA,UAClC,cAAc,WAAW;AAAA,UACzB,gBAAgB,WAAW;AAAA,UAC3B,YAAY,kBAAkB;AAAA,UAC9B,YAAY,kBAAkB;AAAA,UAC9B,UAAU,kBAAkB,MAAM;AAAA,UAClC,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAAA;AAGvB,qBAAA,MAAM,QAAQ,WAAW;AACrB;AAEjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,eAEM,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,4CAA2C,SAAS,KAAK;AAC3EA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,sBAAc,QAAQ;AAAA,MACxB;AAAA,IAAA;AAIF,UAAM,gBAAgB,MAAM;AAC1B,uBAAiB,cAAc;AAAA,IAAA;AAI3B,UAAA,0BAA0B,CAAC,UAAe;AAC9C,uBAAiB,kBAAkB,MAAM,CAAC,EAAE,KAAK;AACjD,+BAAyB,QAAQ;AAAA,IAAA;AAG7B,UAAA,0BAA0B,CAAC,UAAe;AAC9C,uBAAiB,kBAAkB,MAAM,CAAC,EAAE,KAAK;AACjD,+BAAyB,QAAQ;AAAA,IAAA;AAInC,UAAM,aAAa,MAAM;AACvB,UAAI,CAAC,kBAAkB;AAAO;AAE9B,UAAI,aAAa;AACb,UAAA,kBAAkB,MAAM,SAAS,QAAQ;AAC9B,qBAAA,OAAO,kBAAkB,MAAM,YAAY;AAAA,MAAS,kBAAkB,MAAM,cAAc;AAAA,MAC9F,WAAA,kBAAkB,MAAM,SAAS,YAAY;AACzC,qBAAA,kBAAkB,MAAM,SAAS;AAAA,UAAI,CAAC,QACjD,GAAG,WAAW,IAAI,SAAS,CAAC,IAAI,WAAW,IAAI,OAAO,CAAC;AAAA,EAAK,IAAI,YAAY;AAAA,EAAK,IAAI,cAAc;AAAA,QAAA,EACnG,KAAK,MAAM;AAAA,MACf;AAEAA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM;AAAA,QACN,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,kBAAkB;AAAO;AAE9BA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,SAAS,WAAW,kBAAkB,MAAM,cAAc;AAAA,QAC1D,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,kBAAkB;AAAO;AAE9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIH,UAAM,oBAAoB,MAAM;;AAC1B,UAAA,GAAC,uBAAkB,UAAlB,mBAAyB;AAAU;AAExC,UAAI,gBAAgB,OAAO;AACzBA,sBAAA,MAAI,oBAAoB;AACxB,wBAAgB,QAAQ;AAAA,MAAA,OACnB;AACLA,sBAAAA,MAAI,oBAAoB;AAAA,UACtB,SAAS,kBAAkB,MAAM;AAAA,UACjC,OAAO;AAAA,UACP,SAAS,MAAM;AACb,4BAAgB,QAAQ;AAAA,UAC1B;AAAA,QAAA,CACD;AAAA,MACH;AAAA,IAAA;AAII,UAAA,aAAa,CAAC,YAAoB;AACtC,YAAM,OAAO,KAAK,MAAM,UAAU,EAAE;AACpC,YAAM,OAAO,KAAK,MAAM,UAAU,EAAE;AACpC,aAAO,GAAG,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IAAA;AAG1E,UAAA,iBAAiB,CAAC,YAAoB;AAC1C,UAAI,CAAC;AAAgB,eAAA;AACrB,aAAO,WAAW,OAAO;AAAA,IAAA;AAGrB,UAAA,iBAAiB,CAAC,UAAkB;AACxC,UAAI,UAAU;AAAU,eAAA;AACxB,YAAM,IAAI;AACV,YAAM,QAAQ,CAAC,KAAK,MAAM,MAAM,IAAI;AAC9B,YAAA,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,CAAC;AAClD,aAAO,YAAY,QAAQ,KAAK,IAAI,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC;AAAA,IAAA;AAIxE,UAAM,mBAAmB,MAAM;AACzBA,oBAAAA,MAAA,eAAe,6BAA6B,aAAa,KAAK;AAAA,IAAA;AAGpE,UAAM,mBAAmB,MAAM;AAC7B,YAAM,UAAUA,cAAA,MAAI,eAAe,2BAA2B,KAAK,CAAA;AACnE,mBAAa,QAAQ;AAAA,IAAA;AAGvB,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MAAA,CACN;AAAA,IAAA;AAGG,UAAA,kBAAkB,CAAC,SAAc;AACrC,wBAAkB,QAAQ;AAAA,QACxB,MAAM;AAAA,QACN,cAAc,KAAK;AAAA,QACnB,gBAAgB,KAAK;AAAA,MAAA;AAAA,IACvB;AAIF,UAAM,oBAAoB,MAAM;AACb;AACjBA,oBAAA,MAAI,oBAAoB;AAAA,IAAA;AAIb,aAAA;AAAA,MACX;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpxBD,GAAG,WAAW,eAAe;"}