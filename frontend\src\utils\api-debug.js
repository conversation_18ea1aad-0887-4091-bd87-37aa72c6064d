/**
 * API调试工具
 * 用于调试和监控API请求
 */

/**
 * API调试器类
 */
export class ApiDebugger {
  constructor(options = {}) {
    this.options = {
      enabled: process.env.NODE_ENV === 'development',
      logRequests: true,
      logResponses: true,
      logErrors: true,
      maxLogEntries: 100,
      ...options
    };
    
    this.logs = [];
    this.requestCount = 0;
    this.errorCount = 0;
  }

  /**
   * 记录API请求
   * @param {string} method - HTTP方法
   * @param {string} url - 请求URL
   * @param {any} data - 请求数据
   * @param {Object} headers - 请求头
   */
  logRequest(method, url, data = null, headers = {}) {
    if (!this.options.enabled || !this.options.logRequests) return;
    
    this.requestCount++;
    
    const logEntry = {
      id: this.requestCount,
      type: 'request',
      timestamp: new Date().toISOString(),
      method: method.toUpperCase(),
      url,
      data,
      headers,
      size: this.calculateSize(data)
    };
    
    this.addLog(logEntry);
    
    console.group(`🚀 API Request #${this.requestCount}: ${method.toUpperCase()} ${url}`);
    console.log('📤 Request Data:', data);
    console.log('📋 Headers:', headers);
    console.log('📏 Size:', this.formatSize(logEntry.size));
    console.groupEnd();
  }

  /**
   * 记录API响应
   * @param {string} method - HTTP方法
   * @param {string} url - 请求URL
   * @param {any} response - 响应数据
   * @param {number} status - 状态码
   * @param {number} duration - 请求耗时（毫秒）
   */
  logResponse(method, url, response, status, duration = 0) {
    if (!this.options.enabled || !this.options.logResponses) return;
    
    const logEntry = {
      id: this.requestCount,
      type: 'response',
      timestamp: new Date().toISOString(),
      method: method.toUpperCase(),
      url,
      response,
      status,
      duration,
      size: this.calculateSize(response),
      success: status >= 200 && status < 300
    };
    
    this.addLog(logEntry);
    
    const statusEmoji = logEntry.success ? '✅' : '❌';
    const durationText = duration > 0 ? ` (${duration}ms)` : '';
    
    console.group(`${statusEmoji} API Response #${this.requestCount}: ${status}${durationText}`);
    console.log('📥 Response Data:', response);
    console.log('📏 Size:', this.formatSize(logEntry.size));
    if (duration > 1000) {
      console.warn('⚠️ Slow request detected:', duration + 'ms');
    }
    console.groupEnd();
  }

  /**
   * 记录API错误
   * @param {string} method - HTTP方法
   * @param {string} url - 请求URL
   * @param {Error} error - 错误对象
   * @param {any} requestData - 请求数据
   */
  logError(method, url, error, requestData = null) {
    if (!this.options.enabled || !this.options.logErrors) return;
    
    this.errorCount++;
    
    const logEntry = {
      id: this.requestCount,
      type: 'error',
      timestamp: new Date().toISOString(),
      method: method.toUpperCase(),
      url,
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      requestData,
      status: error.response?.status || 0
    };
    
    this.addLog(logEntry);
    
    console.group(`💥 API Error #${this.requestCount}: ${method.toUpperCase()} ${url}`);
    console.error('❌ Error:', error);
    console.log('📤 Request Data:', requestData);
    if (error.response) {
      console.log('📥 Error Response:', error.response.data);
      console.log('📊 Status:', error.response.status);
    }
    console.groupEnd();
  }

  /**
   * 添加日志条目
   * @param {Object} logEntry - 日志条目
   */
  addLog(logEntry) {
    this.logs.push(logEntry);
    
    // 限制日志数量
    if (this.logs.length > this.options.maxLogEntries) {
      this.logs.shift();
    }
  }

  /**
   * 计算数据大小
   * @param {any} data - 数据
   * @returns {number} 大小（字节）
   */
  calculateSize(data) {
    if (!data) return 0;
    
    try {
      return new Blob([JSON.stringify(data)]).size;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化的大小
   */
  formatSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const requests = this.logs.filter(log => log.type === 'request');
    const responses = this.logs.filter(log => log.type === 'response');
    const errors = this.logs.filter(log => log.type === 'error');
    
    const successfulResponses = responses.filter(log => log.success);
    const failedResponses = responses.filter(log => !log.success);
    
    const totalDuration = responses.reduce((sum, log) => sum + (log.duration || 0), 0);
    const avgDuration = responses.length > 0 ? totalDuration / responses.length : 0;
    
    return {
      totalRequests: this.requestCount,
      totalErrors: this.errorCount,
      successRate: this.requestCount > 0 ? (successfulResponses.length / this.requestCount) * 100 : 0,
      averageDuration: Math.round(avgDuration),
      totalDataSent: requests.reduce((sum, log) => sum + (log.size || 0), 0),
      totalDataReceived: responses.reduce((sum, log) => sum + (log.size || 0), 0)
    };
  }

  /**
   * 获取最近的日志
   * @param {number} count - 日志数量
   * @returns {Array} 日志数组
   */
  getRecentLogs(count = 10) {
    return this.logs.slice(-count);
  }

  /**
   * 按类型过滤日志
   * @param {string} type - 日志类型
   * @returns {Array} 过滤后的日志
   */
  getLogsByType(type) {
    return this.logs.filter(log => log.type === type);
  }

  /**
   * 按URL过滤日志
   * @param {string} url - URL模式
   * @returns {Array} 过滤后的日志
   */
  getLogsByUrl(url) {
    return this.logs.filter(log => log.url && log.url.includes(url));
  }

  /**
   * 清除所有日志
   */
  clearLogs() {
    this.logs = [];
    this.requestCount = 0;
    this.errorCount = 0;
  }

  /**
   * 导出日志
   * @returns {string} JSON格式的日志
   */
  exportLogs() {
    return JSON.stringify({
      stats: this.getStats(),
      logs: this.logs
    }, null, 2);
  }

  /**
   * 启用调试
   */
  enable() {
    this.options.enabled = true;
  }

  /**
   * 禁用调试
   */
  disable() {
    this.options.enabled = false;
  }

  /**
   * 检查是否启用
   * @returns {boolean} 是否启用
   */
  isEnabled() {
    return this.options.enabled;
  }
}

// 创建全局调试器实例
export const apiDebugger = new ApiDebugger();

/**
 * 创建API调试中间件
 * @param {ApiDebugger} apiDebuggerInstance - 调试器实例
 * @returns {Function} 中间件函数
 */
export function createApiDebugMiddleware(apiDebuggerInstance = apiDebugger) {
  return {
    request: (config) => {
      const startTime = Date.now();
      config.metadata = { startTime };
      
      apiDebuggerInstance.logRequest(
        config.method || 'GET',
        config.url,
        config.data,
        config.headers
      );

      return config;
    },

    response: (response) => {
      const duration = response.config.metadata
        ? Date.now() - response.config.metadata.startTime
        : 0;

      apiDebuggerInstance.logResponse(
        response.config.method || 'GET',
        response.config.url,
        response.data,
        response.status,
        duration
      );

      return response;
    },

    error: (error) => {
      apiDebuggerInstance.logError(
        error.config?.method || 'GET',
        error.config?.url || 'unknown',
        error,
        error.config?.data
      );
      
      return Promise.reject(error);
    }
  };
}

/**
 * 性能监控装饰器
 * @param {string} name - 操作名称
 * @returns {Function} 装饰器函数
 */
export function performanceMonitor(name) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function(...args) {
      const startTime = performance.now();
      
      try {
        const result = await originalMethod.apply(this, args);
        const duration = performance.now() - startTime;
        
        if (apiDebugger.isEnabled()) {
          console.log(`⏱️ ${name} completed in ${duration.toFixed(2)}ms`);
        }
        
        return result;
      } catch (error) {
        const duration = performance.now() - startTime;
        
        if (apiDebugger.isEnabled()) {
          console.error(`💥 ${name} failed after ${duration.toFixed(2)}ms:`, error);
        }
        
        throw error;
      }
    };
    
    return descriptor;
  };
}
