"use strict";
const utils_request = require("../utils/request.js");
const digitalHumanApi = {
  // 获取数字人列表
  getDigitalHumans: (params) => {
    return utils_request.http.get("/digital-human/list", params);
  },
  // 获取数字人详情
  getDigitalHuman: (id) => {
    return utils_request.http.get(`/digital-human/${id}`);
  },
  // 创建数字人
  createDigitalHuman: (data) => {
    return utils_request.http.post("/digital-human/create", data);
  },
  // 更新数字人
  updateDigitalHuman: (id, data) => {
    return utils_request.http.put(`/digital-human/${id}`, data);
  },
  // 删除数字人
  deleteDigitalHuman: (id) => {
    return utils_request.http.delete(`/digital-human/${id}`);
  },
  // 上传数字人头像
  uploadAvatar: (filePath) => {
    return utils_request.uploadFile({
      url: "/digital-human/upload-avatar",
      filePath,
      name: "avatar"
    });
  },
  // 获取对话会话列表
  getChatSessions: (params) => {
    return utils_request.http.get("/digital-human/sessions", params);
  },
  // 创建对话会话
  createChatSession: (digitalHumanId, title) => {
    return utils_request.http.post("/digital-human/sessions", {
      digital_human_id: digitalHumanId,
      title: title || "新对话"
    });
  },
  // 获取会话消息
  getChatMessages: (sessionId, params) => {
    return utils_request.http.get(`/digital-human/sessions/${sessionId}/messages`, params);
  },
  // 发送文本消息
  sendTextMessage: (sessionId, content) => {
    return utils_request.http.post(`/digital-human/sessions/${sessionId}/messages`, {
      content,
      message_type: "text"
    });
  },
  // 发送语音消息
  sendAudioMessage: (sessionId, filePath) => {
    return utils_request.uploadFile({
      url: `/digital-human/sessions/${sessionId}/audio`,
      filePath,
      name: "audio",
      formData: {
        message_type: "audio"
      }
    });
  },
  // 获取数字人回复
  getDigitalHumanReply: (sessionId, messageId, options) => {
    return utils_request.http.post(`/digital-human/sessions/${sessionId}/reply`, {
      message_id: messageId,
      ...options
    });
  },
  // 生成数字人视频回复
  generateVideoReply: (sessionId, messageId, voiceConfig) => {
    return utils_request.http.post(`/digital-human/sessions/${sessionId}/video`, {
      message_id: messageId,
      voice_config: voiceConfig
    });
  },
  // 获取视频生成任务状态
  getVideoTaskStatus: (taskId) => {
    return utils_request.http.get(`/digital-human/video-task/${taskId}`);
  },
  // 删除对话会话
  deleteChatSession: (sessionId) => {
    return utils_request.http.delete(`/digital-human/sessions/${sessionId}`);
  },
  // 清空会话消息
  clearChatMessages: (sessionId) => {
    return utils_request.http.delete(`/digital-human/sessions/${sessionId}/messages`);
  },
  // 获取可用语音列表
  getAvailableVoices: (language) => {
    return utils_request.http.get("/digital-human/voices", { language });
  },
  // 语音合成预览
  previewVoice: (text, voiceConfig) => {
    return utils_request.http.post("/digital-human/voice-preview", {
      text,
      voice_config: voiceConfig
    });
  },
  // 获取数字人统计信息
  getDigitalHumanStats: (id) => {
    return utils_request.http.get(`/digital-human/${id}/stats`);
  },
  // 导出对话记录
  exportChatHistory: (sessionId, format) => {
    return utils_request.http.get(
      `/digital-human/sessions/${sessionId}/export`,
      { format },
      { responseType: "blob" }
    );
  },
  // 搜索对话记录
  searchMessages: (query, params) => {
    return utils_request.http.get("/digital-human/search", { query, ...params });
  }
};
exports.digitalHumanApi = digitalHumanApi;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/digital-human.js.map
