{"version": 3, "file": "digitalHuman.js", "sources": ["store/modules/digitalHuman.ts"], "sourcesContent": ["import { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\nimport { \n  digitalHumanApi, \n  type DigitalHuman, \n  type ChatSession, \n  type ChatMessage,\n  type VoiceConfig \n} from '@/api/digital-human'\n\nexport const useDigitalHumanStore = defineStore('digitalHuman', () => {\n  // 状态\n  const digitalHumans = ref<DigitalHuman[]>([])\n  const currentDigitalHuman = ref<DigitalHuman | null>(null)\n  const chatSessions = ref<ChatSession[]>([])\n  const currentSession = ref<ChatSession | null>(null)\n  const messages = ref<ChatMessage[]>([])\n  const isLoading = ref<boolean>(false)\n  const isSending = ref<boolean>(false)\n  const isGeneratingVideo = ref<boolean>(false)\n  const voiceConfig = ref<VoiceConfig>({\n    voice_id: 'default',\n    speed: 1.0,\n    pitch: 1.0,\n    volume: 1.0,\n    emotion: 'neutral'\n  })\n\n  // 计算属性\n  const hasDigitalHumans = computed(() => digitalHumans.value.length > 0)\n  \n  const currentSessionMessages = computed(() => {\n    if (!currentSession.value) return []\n    return messages.value.filter(msg => msg.id.includes(currentSession.value!.id))\n  })\n\n  const recentSessions = computed(() => {\n    return chatSessions.value\n      .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())\n      .slice(0, 5)\n  })\n\n  // 加载数字人列表\n  const loadDigitalHumans = async (page = 1, limit = 20) => {\n    try {\n      isLoading.value = true\n      const response = await digitalHumanApi.getDigitalHumans({ page, limit })\n      \n      if (response.success) {\n        if (page === 1) {\n          digitalHumans.value = response.data.items\n        } else {\n          digitalHumans.value.push(...response.data.items)\n        }\n        return response.data\n      }\n    } catch (error) {\n      console.error('加载数字人列表失败:', error)\n      throw error\n    } finally {\n      isLoading.value = false\n    }\n  }\n\n  // 选择数字人\n  const selectDigitalHuman = async (digitalHuman: DigitalHuman) => {\n    currentDigitalHuman.value = digitalHuman\n    \n    // 加载该数字人的对话会话\n    await loadChatSessions()\n  }\n\n  // 加载对话会话\n  const loadChatSessions = async (page = 1, limit = 20) => {\n    if (!currentDigitalHuman.value) return\n    \n    try {\n      const response = await digitalHumanApi.getChatSessions({ page, limit })\n      \n      if (response.success) {\n        if (page === 1) {\n          chatSessions.value = response.data.items\n        } else {\n          chatSessions.value.push(...response.data.items)\n        }\n        return response.data\n      }\n    } catch (error) {\n      console.error('加载对话会话失败:', error)\n      throw error\n    }\n  }\n\n  // 创建新会话\n  const createNewSession = async (title?: string) => {\n    if (!currentDigitalHuman.value) {\n      throw new Error('请先选择数字人')\n    }\n\n    try {\n      const response = await digitalHumanApi.createChatSession(\n        currentDigitalHuman.value.id,\n        title\n      )\n      \n      if (response.success) {\n        const newSession = response.data\n        chatSessions.value.unshift(newSession)\n        currentSession.value = newSession\n        messages.value = []\n        \n        return newSession\n      } else {\n        throw new Error(response.message || '创建会话失败')\n      }\n    } catch (error) {\n      console.error('创建会话失败:', error)\n      throw error\n    }\n  }\n\n  // 选择会话\n  const selectSession = async (session: ChatSession) => {\n    currentSession.value = session\n    await loadMessages()\n  }\n\n  // 加载消息\n  const loadMessages = async (page = 1, limit = 50) => {\n    if (!currentSession.value) return\n    \n    try {\n      const response = await digitalHumanApi.getChatMessages(\n        currentSession.value.id,\n        { page, limit }\n      )\n      \n      if (response.success) {\n        if (page === 1) {\n          messages.value = response.data.items.reverse() // 消息按时间正序显示\n        } else {\n          messages.value.unshift(...response.data.items.reverse())\n        }\n        return response.data\n      }\n    } catch (error) {\n      console.error('加载消息失败:', error)\n      throw error\n    }\n  }\n\n  // 发送文本消息\n  const sendTextMessage = async (content: string) => {\n    if (!currentSession.value || !content.trim()) {\n      throw new Error('会话或消息内容不能为空')\n    }\n\n    try {\n      isSending.value = true\n      \n      // 添加用户消息到本地\n      const userMessage: ChatMessage = {\n        id: `temp_${Date.now()}`,\n        role: 'user',\n        content: content.trim(),\n        timestamp: new Date().toISOString(),\n        message_type: 'text'\n      }\n      messages.value.push(userMessage)\n\n      // 发送消息到服务器\n      const response = await digitalHumanApi.sendTextMessage(\n        currentSession.value.id,\n        content.trim()\n      )\n      \n      if (response.success) {\n        // 更新用户消息ID\n        const messageIndex = messages.value.findIndex(msg => msg.id === userMessage.id)\n        if (messageIndex !== -1) {\n          messages.value[messageIndex] = response.data\n        }\n\n        // 获取数字人回复\n        await getDigitalHumanReply(response.data.id)\n        \n        return response.data\n      } else {\n        throw new Error(response.message || '发送消息失败')\n      }\n    } catch (error) {\n      console.error('发送消息失败:', error)\n      \n      // 移除失败的消息\n      messages.value = messages.value.filter(msg => !msg.id.startsWith('temp_'))\n      \n      throw error\n    } finally {\n      isSending.value = false\n    }\n  }\n\n  // 发送语音消息\n  const sendAudioMessage = async (filePath: string) => {\n    if (!currentSession.value) {\n      throw new Error('请先选择会话')\n    }\n\n    try {\n      isSending.value = true\n      \n      const response = await digitalHumanApi.sendAudioMessage(\n        currentSession.value.id,\n        filePath\n      )\n      \n      if (response.success) {\n        messages.value.push(response.data)\n        \n        // 获取数字人回复\n        await getDigitalHumanReply(response.data.id)\n        \n        return response.data\n      } else {\n        throw new Error(response.message || '发送语音消息失败')\n      }\n    } catch (error) {\n      console.error('发送语音消息失败:', error)\n      throw error\n    } finally {\n      isSending.value = false\n    }\n  }\n\n  // 获取数字人回复\n  const getDigitalHumanReply = async (messageId: string, responseType: 'text' | 'audio' | 'video' = 'text') => {\n    if (!currentSession.value) return\n\n    try {\n      const response = await digitalHumanApi.getDigitalHumanReply(\n        currentSession.value.id,\n        messageId,\n        {\n          voice_config: voiceConfig.value,\n          response_type: responseType\n        }\n      )\n      \n      if (response.success) {\n        messages.value.push(response.data)\n        \n        // 更新会话的最后消息时间\n        if (currentSession.value) {\n          currentSession.value.updated_at = new Date().toISOString()\n          currentSession.value.last_message = response.data.content\n        }\n        \n        return response.data\n      }\n    } catch (error) {\n      console.error('获取数字人回复失败:', error)\n      \n      // 添加错误消息\n      const errorMessage: ChatMessage = {\n        id: `error_${Date.now()}`,\n        role: 'assistant',\n        content: '抱歉，我现在无法回复您的消息，请稍后再试。',\n        timestamp: new Date().toISOString(),\n        message_type: 'text'\n      }\n      messages.value.push(errorMessage)\n    }\n  }\n\n  // 生成视频回复\n  const generateVideoReply = async (messageId: string) => {\n    if (!currentSession.value) return\n\n    try {\n      isGeneratingVideo.value = true\n      \n      const response = await digitalHumanApi.generateVideoReply(\n        currentSession.value.id,\n        messageId,\n        voiceConfig.value\n      )\n      \n      if (response.success) {\n        const taskId = response.data.task_id\n        \n        // 轮询任务状态\n        return await pollVideoTaskStatus(taskId)\n      } else {\n        throw new Error(response.message || '生成视频失败')\n      }\n    } catch (error) {\n      console.error('生成视频回复失败:', error)\n      throw error\n    } finally {\n      isGeneratingVideo.value = false\n    }\n  }\n\n  // 轮询视频任务状态\n  const pollVideoTaskStatus = async (taskId: string): Promise<string | null> => {\n    const maxAttempts = 30 // 最多轮询30次\n    let attempts = 0\n    \n    return new Promise((resolve, reject) => {\n      const poll = async () => {\n        try {\n          attempts++\n          const response = await digitalHumanApi.getVideoTaskStatus(taskId)\n          \n          if (response.success) {\n            const { status, video_url, error_message } = response.data\n            \n            if (status === 'completed' && video_url) {\n              resolve(video_url)\n            } else if (status === 'failed') {\n              reject(new Error(error_message || '视频生成失败'))\n            } else if (attempts >= maxAttempts) {\n              reject(new Error('视频生成超时'))\n            } else {\n              // 继续轮询\n              setTimeout(poll, 2000) // 2秒后再次检查\n            }\n          } else {\n            reject(new Error('获取任务状态失败'))\n          }\n        } catch (error) {\n          reject(error)\n        }\n      }\n      \n      poll()\n    })\n  }\n\n  // 删除会话\n  const deleteSession = async (sessionId: string) => {\n    try {\n      const response = await digitalHumanApi.deleteChatSession(sessionId)\n      \n      if (response.success) {\n        chatSessions.value = chatSessions.value.filter(session => session.id !== sessionId)\n        \n        // 如果删除的是当前会话，清空当前会话\n        if (currentSession.value?.id === sessionId) {\n          currentSession.value = null\n          messages.value = []\n        }\n        \n        uni.showToast({\n          title: '删除成功',\n          icon: 'success'\n        })\n      }\n    } catch (error) {\n      console.error('删除会话失败:', error)\n      uni.showToast({\n        title: '删除失败',\n        icon: 'error'\n      })\n    }\n  }\n\n  // 清空消息\n  const clearMessages = async () => {\n    if (!currentSession.value) return\n\n    try {\n      const response = await digitalHumanApi.clearChatMessages(currentSession.value.id)\n      \n      if (response.success) {\n        messages.value = []\n        uni.showToast({\n          title: '清空成功',\n          icon: 'success'\n        })\n      }\n    } catch (error) {\n      console.error('清空消息失败:', error)\n      uni.showToast({\n        title: '清空失败',\n        icon: 'error'\n      })\n    }\n  }\n\n  // 更新语音配置\n  const updateVoiceConfig = (config: Partial<VoiceConfig>) => {\n    voiceConfig.value = { ...voiceConfig.value, ...config }\n  }\n\n  return {\n    // 状态\n    digitalHumans,\n    currentDigitalHuman,\n    chatSessions,\n    currentSession,\n    messages,\n    isLoading,\n    isSending,\n    isGeneratingVideo,\n    voiceConfig,\n\n    // 计算属性\n    hasDigitalHumans,\n    currentSessionMessages,\n    recentSessions,\n\n    // 方法\n    loadDigitalHumans,\n    selectDigitalHuman,\n    loadChatSessions,\n    createNewSession,\n    selectSession,\n    loadMessages,\n    sendTextMessage,\n    sendAudioMessage,\n    getDigitalHumanReply,\n    generateVideoReply,\n    deleteSession,\n    clearMessages,\n    updateVoiceConfig\n  }\n}, {\n  persist: {\n    key: 'digital-human-store',\n    paths: ['currentDigitalHuman', 'voiceConfig']\n  }\n})\n"], "names": ["defineStore", "ref", "computed", "digitalHumanApi", "uni"], "mappings": ";;;AAUa,MAAA,uBAAuBA,cAAAA,YAAY,gBAAgB,MAAM;AAE9D,QAAA,gBAAgBC,kBAAoB,CAAA,CAAE;AACtC,QAAA,sBAAsBA,kBAAyB,IAAI;AACnD,QAAA,eAAeA,kBAAmB,CAAA,CAAE;AACpC,QAAA,iBAAiBA,kBAAwB,IAAI;AAC7C,QAAA,WAAWA,kBAAmB,CAAA,CAAE;AAChC,QAAA,YAAYA,kBAAa,KAAK;AAC9B,QAAA,YAAYA,kBAAa,KAAK;AAC9B,QAAA,oBAAoBA,kBAAa,KAAK;AAC5C,QAAM,cAAcA,cAAAA,IAAiB;AAAA,IACnC,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,EAAA,CACV;AAGD,QAAM,mBAAmBC,cAAAA,SAAS,MAAM,cAAc,MAAM,SAAS,CAAC;AAEhE,QAAA,yBAAyBA,cAAAA,SAAS,MAAM;AAC5C,QAAI,CAAC,eAAe;AAAO,aAAO;AAC3B,WAAA,SAAS,MAAM,OAAO,CAAO,QAAA,IAAI,GAAG,SAAS,eAAe,MAAO,EAAE,CAAC;AAAA,EAAA,CAC9E;AAEK,QAAA,iBAAiBA,cAAAA,SAAS,MAAM;AAC7B,WAAA,aAAa,MACjB,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,UAAU,EAAE,YAAY,IAAI,KAAK,EAAE,UAAU,EAAE,QAAS,CAAA,EAClF,MAAM,GAAG,CAAC;AAAA,EAAA,CACd;AAGD,QAAM,oBAAoB,OAAO,OAAO,GAAG,QAAQ,OAAO;AACpD,QAAA;AACF,gBAAU,QAAQ;AAClB,YAAM,WAAW,MAAMC,iCAAgB,iBAAiB,EAAE,MAAM,OAAO;AAEvE,UAAI,SAAS,SAAS;AACpB,YAAI,SAAS,GAAG;AACA,wBAAA,QAAQ,SAAS,KAAK;AAAA,QAAA,OAC/B;AACL,wBAAc,MAAM,KAAK,GAAG,SAAS,KAAK,KAAK;AAAA,QACjD;AACA,eAAO,SAAS;AAAA,MAClB;AAAA,aACO,OAAO;AACdC,oBAAA,4DAAc,cAAc,KAAK;AAC3B,YAAA;AAAA,IAAA,UACN;AACA,gBAAU,QAAQ;AAAA,IACpB;AAAA,EAAA;AAII,QAAA,qBAAqB,OAAO,iBAA+B;AAC/D,wBAAoB,QAAQ;AAG5B,UAAM,iBAAiB;AAAA,EAAA;AAIzB,QAAM,mBAAmB,OAAO,OAAO,GAAG,QAAQ,OAAO;AACvD,QAAI,CAAC,oBAAoB;AAAO;AAE5B,QAAA;AACF,YAAM,WAAW,MAAMD,iCAAgB,gBAAgB,EAAE,MAAM,OAAO;AAEtE,UAAI,SAAS,SAAS;AACpB,YAAI,SAAS,GAAG;AACD,uBAAA,QAAQ,SAAS,KAAK;AAAA,QAAA,OAC9B;AACL,uBAAa,MAAM,KAAK,GAAG,SAAS,KAAK,KAAK;AAAA,QAChD;AACA,eAAO,SAAS;AAAA,MAClB;AAAA,aACO,OAAO;AACdC,oBAAA,4DAAc,aAAa,KAAK;AAC1B,YAAA;AAAA,IACR;AAAA,EAAA;AAII,QAAA,mBAAmB,OAAO,UAAmB;AAC7C,QAAA,CAAC,oBAAoB,OAAO;AACxB,YAAA,IAAI,MAAM,SAAS;AAAA,IAC3B;AAEI,QAAA;AACI,YAAA,WAAW,MAAMD,iBAAAA,gBAAgB;AAAA,QACrC,oBAAoB,MAAM;AAAA,QAC1B;AAAA,MAAA;AAGF,UAAI,SAAS,SAAS;AACpB,cAAM,aAAa,SAAS;AACf,qBAAA,MAAM,QAAQ,UAAU;AACrC,uBAAe,QAAQ;AACvB,iBAAS,QAAQ;AAEV,eAAA;AAAA,MAAA,OACF;AACL,cAAM,IAAI,MAAM,SAAS,WAAW,QAAQ;AAAA,MAC9C;AAAA,aACO,OAAO;AACdC,oBAAA,MAAc,MAAA,SAAA,wCAAA,WAAW,KAAK;AACxB,YAAA;AAAA,IACR;AAAA,EAAA;AAII,QAAA,gBAAgB,OAAO,YAAyB;AACpD,mBAAe,QAAQ;AACvB,UAAM,aAAa;AAAA,EAAA;AAIrB,QAAM,eAAe,OAAO,OAAO,GAAG,QAAQ,OAAO;AACnD,QAAI,CAAC,eAAe;AAAO;AAEvB,QAAA;AACI,YAAA,WAAW,MAAMD,iBAAAA,gBAAgB;AAAA,QACrC,eAAe,MAAM;AAAA,QACrB,EAAE,MAAM,MAAM;AAAA,MAAA;AAGhB,UAAI,SAAS,SAAS;AACpB,YAAI,SAAS,GAAG;AACd,mBAAS,QAAQ,SAAS,KAAK,MAAM,QAAQ;AAAA,QAAA,OACxC;AACL,mBAAS,MAAM,QAAQ,GAAG,SAAS,KAAK,MAAM,SAAS;AAAA,QACzD;AACA,eAAO,SAAS;AAAA,MAClB;AAAA,aACO,OAAO;AACdC,oBAAA,MAAc,MAAA,SAAA,wCAAA,WAAW,KAAK;AACxB,YAAA;AAAA,IACR;AAAA,EAAA;AAII,QAAA,kBAAkB,OAAO,YAAoB;AACjD,QAAI,CAAC,eAAe,SAAS,CAAC,QAAQ,QAAQ;AACtC,YAAA,IAAI,MAAM,aAAa;AAAA,IAC/B;AAEI,QAAA;AACF,gBAAU,QAAQ;AAGlB,YAAM,cAA2B;AAAA,QAC/B,IAAI,QAAQ,KAAK,IAAK,CAAA;AAAA,QACtB,MAAM;AAAA,QACN,SAAS,QAAQ,KAAK;AAAA,QACtB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAClC,cAAc;AAAA,MAAA;AAEP,eAAA,MAAM,KAAK,WAAW;AAGzB,YAAA,WAAW,MAAMD,iBAAAA,gBAAgB;AAAA,QACrC,eAAe,MAAM;AAAA,QACrB,QAAQ,KAAK;AAAA,MAAA;AAGf,UAAI,SAAS,SAAS;AAEd,cAAA,eAAe,SAAS,MAAM,UAAU,SAAO,IAAI,OAAO,YAAY,EAAE;AAC9E,YAAI,iBAAiB,IAAI;AACd,mBAAA,MAAM,YAAY,IAAI,SAAS;AAAA,QAC1C;AAGM,cAAA,qBAAqB,SAAS,KAAK,EAAE;AAE3C,eAAO,SAAS;AAAA,MAAA,OACX;AACL,cAAM,IAAI,MAAM,SAAS,WAAW,QAAQ;AAAA,MAC9C;AAAA,aACO,OAAO;AACdC,oBAAA,MAAc,MAAA,SAAA,wCAAA,WAAW,KAAK;AAGrB,eAAA,QAAQ,SAAS,MAAM,OAAO,CAAA,QAAO,CAAC,IAAI,GAAG,WAAW,OAAO,CAAC;AAEnE,YAAA;AAAA,IAAA,UACN;AACA,gBAAU,QAAQ;AAAA,IACpB;AAAA,EAAA;AAII,QAAA,mBAAmB,OAAO,aAAqB;AAC/C,QAAA,CAAC,eAAe,OAAO;AACnB,YAAA,IAAI,MAAM,QAAQ;AAAA,IAC1B;AAEI,QAAA;AACF,gBAAU,QAAQ;AAEZ,YAAA,WAAW,MAAMD,iBAAAA,gBAAgB;AAAA,QACrC,eAAe,MAAM;AAAA,QACrB;AAAA,MAAA;AAGF,UAAI,SAAS,SAAS;AACX,iBAAA,MAAM,KAAK,SAAS,IAAI;AAG3B,cAAA,qBAAqB,SAAS,KAAK,EAAE;AAE3C,eAAO,SAAS;AAAA,MAAA,OACX;AACL,cAAM,IAAI,MAAM,SAAS,WAAW,UAAU;AAAA,MAChD;AAAA,aACO,OAAO;AACdC,oBAAA,6DAAc,aAAa,KAAK;AAC1B,YAAA;AAAA,IAAA,UACN;AACA,gBAAU,QAAQ;AAAA,IACpB;AAAA,EAAA;AAIF,QAAM,uBAAuB,OAAO,WAAmB,eAA2C,WAAW;AAC3G,QAAI,CAAC,eAAe;AAAO;AAEvB,QAAA;AACI,YAAA,WAAW,MAAMD,iBAAAA,gBAAgB;AAAA,QACrC,eAAe,MAAM;AAAA,QACrB;AAAA,QACA;AAAA,UACE,cAAc,YAAY;AAAA,UAC1B,eAAe;AAAA,QACjB;AAAA,MAAA;AAGF,UAAI,SAAS,SAAS;AACX,iBAAA,MAAM,KAAK,SAAS,IAAI;AAGjC,YAAI,eAAe,OAAO;AACxB,yBAAe,MAAM,cAAiB,oBAAA,KAAA,GAAO;AAC9B,yBAAA,MAAM,eAAe,SAAS,KAAK;AAAA,QACpD;AAEA,eAAO,SAAS;AAAA,MAClB;AAAA,aACO,OAAO;AACdC,oBAAA,6DAAc,cAAc,KAAK;AAGjC,YAAM,eAA4B;AAAA,QAChC,IAAI,SAAS,KAAK,IAAK,CAAA;AAAA,QACvB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAClC,cAAc;AAAA,MAAA;AAEP,eAAA,MAAM,KAAK,YAAY;AAAA,IAClC;AAAA,EAAA;AAII,QAAA,qBAAqB,OAAO,cAAsB;AACtD,QAAI,CAAC,eAAe;AAAO;AAEvB,QAAA;AACF,wBAAkB,QAAQ;AAEpB,YAAA,WAAW,MAAMD,iBAAAA,gBAAgB;AAAA,QACrC,eAAe,MAAM;AAAA,QACrB;AAAA,QACA,YAAY;AAAA,MAAA;AAGd,UAAI,SAAS,SAAS;AACd,cAAA,SAAS,SAAS,KAAK;AAGtB,eAAA,MAAM,oBAAoB,MAAM;AAAA,MAAA,OAClC;AACL,cAAM,IAAI,MAAM,SAAS,WAAW,QAAQ;AAAA,MAC9C;AAAA,aACO,OAAO;AACdC,oBAAA,6DAAc,aAAa,KAAK;AAC1B,YAAA;AAAA,IAAA,UACN;AACA,wBAAkB,QAAQ;AAAA,IAC5B;AAAA,EAAA;AAII,QAAA,sBAAsB,OAAO,WAA2C;AAC5E,UAAM,cAAc;AACpB,QAAI,WAAW;AAEf,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,OAAO,YAAY;AACnB,YAAA;AACF;AACA,gBAAM,WAAW,MAAMD,iBAAAA,gBAAgB,mBAAmB,MAAM;AAEhE,cAAI,SAAS,SAAS;AACpB,kBAAM,EAAE,QAAQ,WAAW,cAAA,IAAkB,SAAS;AAElD,gBAAA,WAAW,eAAe,WAAW;AACvC,sBAAQ,SAAS;AAAA,YAAA,WACR,WAAW,UAAU;AAC9B,qBAAO,IAAI,MAAM,iBAAiB,QAAQ,CAAC;AAAA,YAAA,WAClC,YAAY,aAAa;AAC3B,qBAAA,IAAI,MAAM,QAAQ,CAAC;AAAA,YAAA,OACrB;AAEL,yBAAW,MAAM,GAAI;AAAA,YACvB;AAAA,UAAA,OACK;AACE,mBAAA,IAAI,MAAM,UAAU,CAAC;AAAA,UAC9B;AAAA,iBACO,OAAO;AACd,iBAAO,KAAK;AAAA,QACd;AAAA,MAAA;AAGG;IAAA,CACN;AAAA,EAAA;AAIG,QAAA,gBAAgB,OAAO,cAAsB;;AAC7C,QAAA;AACF,YAAM,WAAW,MAAMA,iBAAAA,gBAAgB,kBAAkB,SAAS;AAElE,UAAI,SAAS,SAAS;AACpB,qBAAa,QAAQ,aAAa,MAAM,OAAO,CAAW,YAAA,QAAQ,OAAO,SAAS;AAG9E,cAAA,oBAAe,UAAf,mBAAsB,QAAO,WAAW;AAC1C,yBAAe,QAAQ;AACvB,mBAAS,QAAQ;QACnB;AAEAC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,aACO,OAAO;AACdA,oBAAA,MAAc,MAAA,SAAA,wCAAA,WAAW,KAAK;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IACH;AAAA,EAAA;AAIF,QAAM,gBAAgB,YAAY;AAChC,QAAI,CAAC,eAAe;AAAO;AAEvB,QAAA;AACF,YAAM,WAAW,MAAMD,iCAAgB,kBAAkB,eAAe,MAAM,EAAE;AAEhF,UAAI,SAAS,SAAS;AACpB,iBAAS,QAAQ;AACjBC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,aACO,OAAO;AACdA,oBAAA,MAAc,MAAA,SAAA,wCAAA,WAAW,KAAK;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IACH;AAAA,EAAA;AAII,QAAA,oBAAoB,CAAC,WAAiC;AAC1D,gBAAY,QAAQ,EAAE,GAAG,YAAY,OAAO,GAAG;EAAO;AAGjD,SAAA;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ,GAAG;AAAA,EACD,SAAS;AAAA,IACP,KAAK;AAAA,IACL,OAAO,CAAC,uBAAuB,aAAa;AAAA,EAC9C;AACF,CAAC;;"}