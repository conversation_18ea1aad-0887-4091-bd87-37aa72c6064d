/**
 * 唇形同步渲染器
 * 专门用于数字人聊天界面的唇形同步动画
 */

/**
 * 唇形同步渲染器类
 */
export class LipSyncRenderer {
  constructor(canvas, options = {}) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.options = {
      width: 400,
      height: 300,
      frameRate: 30,
      smoothing: true,
      debug: false,
      ...options
    };
    
    this.frames = [];
    this.currentFrame = 0;
    this.isPlaying = false;
    this.animationId = null;
    this.startTime = 0;
  }

  /**
   * 设置帧数据
   * @param {Array} frameData - 帧数据数组
   */
  setFrames(frameData) {
    this.frames = frameData || [];
    this.currentFrame = 0;
  }

  /**
   * 开始播放动画
   */
  play() {
    if (this.isPlaying || this.frames.length === 0) return;
    
    this.isPlaying = true;
    this.currentFrame = 0;
    this.startTime = performance.now();
    
    this.animate();
  }

  /**
   * 动画循环
   */
  animate() {
    if (!this.isPlaying) return;
    
    const currentTime = performance.now();
    const elapsed = (currentTime - this.startTime) / 1000; // 转换为秒
    
    // 根据时间计算当前帧
    const targetFrame = Math.floor(elapsed * this.options.frameRate);
    this.currentFrame = Math.min(targetFrame, this.frames.length - 1);
    
    // 清除画布
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    // 绘制当前帧
    if (this.frames[this.currentFrame]) {
      this.drawFrame(this.frames[this.currentFrame]);
    }
    
    // 如果还有帧要播放，继续动画
    if (this.currentFrame < this.frames.length - 1) {
      this.animationId = requestAnimationFrame(() => this.animate());
    } else {
      this.isPlaying = false;
    }
  }

  /**
   * 绘制单帧
   * @param {Object} frameData - 帧数据
   */
  drawFrame(frameData) {
    const { width, height } = this.options;
    
    // 设置基本样式
    this.ctx.fillStyle = '#f8f9fa';
    this.ctx.fillRect(0, 0, width, height);
    
    // 绘制嘴巴
    if (frameData.mouth) {
      this.drawMouth(frameData.mouth);
    }
    
    // 调试信息
    if (this.options.debug && frameData.debug) {
      this.drawDebugInfo(frameData.debug);
    }
  }

  /**
   * 绘制嘴巴
   * @param {Object} mouthData - 嘴巴数据
   */
  drawMouth(mouthData) {
    const centerX = this.canvas.width / 2;
    const centerY = this.canvas.height * 0.7;
    
    this.ctx.save();
    
    // 设置嘴巴样式
    this.ctx.strokeStyle = '#333';
    this.ctx.fillStyle = mouthData.openness > 0.3 ? '#2c3e50' : 'transparent';
    this.ctx.lineWidth = 2;
    
    // 绘制嘴巴椭圆
    this.ctx.beginPath();
    this.ctx.ellipse(
      centerX,
      centerY,
      mouthData.width || 30,
      mouthData.height || 15,
      0,
      0,
      2 * Math.PI
    );
    
    this.ctx.stroke();
    if (mouthData.openness > 0.3) {
      this.ctx.fill();
    }
    
    this.ctx.restore();
  }

  /**
   * 绘制调试信息
   * @param {Object} debugData - 调试数据
   */
  drawDebugInfo(debugData) {
    this.ctx.save();
    this.ctx.fillStyle = '#666';
    this.ctx.font = '12px Arial';
    this.ctx.fillText(`Frame: ${this.currentFrame}`, 10, 20);
    this.ctx.fillText(`Openness: ${debugData.openness?.toFixed(2)}`, 10, 35);
    this.ctx.fillText(`Amplitude: ${debugData.amplitude?.toFixed(2)}`, 10, 50);
    this.ctx.restore();
  }

  /**
   * 停止播放
   */
  stop() {
    this.isPlaying = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  /**
   * 暂停播放
   */
  pause() {
    this.isPlaying = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  /**
   * 恢复播放
   */
  resume() {
    if (!this.isPlaying && this.frames.length > 0) {
      this.isPlaying = true;
      this.startTime = performance.now() - (this.currentFrame / this.options.frameRate * 1000);
      this.animate();
    }
  }

  /**
   * 跳转到指定帧
   * @param {number} frameIndex - 帧索引
   */
  seekToFrame(frameIndex) {
    this.currentFrame = Math.max(0, Math.min(frameIndex, this.frames.length - 1));
    if (this.frames[this.currentFrame]) {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      this.drawFrame(this.frames[this.currentFrame]);
    }
  }

  /**
   * 获取当前状态
   */
  getState() {
    return {
      isPlaying: this.isPlaying,
      currentFrame: this.currentFrame,
      totalFrames: this.frames.length,
      progress: this.frames.length > 0 ? this.currentFrame / this.frames.length : 0
    };
  }

  /**
   * 销毁渲染器
   */
  destroy() {
    this.stop();
    this.frames = [];
    this.currentFrame = 0;
  }
}

/**
 * 创建唇形同步渲染器（工厂函数）
 * @param {HTMLCanvasElement} canvas - 画布元素
 * @param {Object} options - 配置选项
 * @returns {LipSyncRenderer} 渲染器实例
 */
export function createLipSyncRenderer(canvas, options = {}) {
  return new LipSyncRenderer(canvas, options);
}

/**
 * 从音频数据创建唇形同步帧
 * @param {ArrayBuffer} audioData - 音频数据
 * @param {Object} options - 配置选项
 * @returns {Promise<Array>} 帧数据数组
 */
export async function createLipSyncFrames(audioData, options = {}) {
  const defaultOptions = {
    frameRate: 30,
    duration: 3.0,
    sensitivity: 0.8,
    ...options
  };

  try {
    // 创建音频上下文
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const audioBuffer = await audioContext.decodeAudioData(audioData.slice());
    
    // 分析音频数据
    const channelData = audioBuffer.getChannelData(0);
    const sampleRate = audioBuffer.sampleRate;
    const totalFrames = Math.floor(defaultOptions.frameRate * defaultOptions.duration);
    const samplesPerFrame = Math.floor(channelData.length / totalFrames);
    
    const frames = [];
    
    for (let i = 0; i < totalFrames; i++) {
      const startSample = i * samplesPerFrame;
      const endSample = Math.min(startSample + samplesPerFrame, channelData.length);
      
      // 计算当前帧的音频强度
      let sum = 0;
      let maxAmplitude = 0;
      for (let j = startSample; j < endSample; j++) {
        const amplitude = Math.abs(channelData[j]);
        sum += amplitude;
        maxAmplitude = Math.max(maxAmplitude, amplitude);
      }
      const avgAmplitude = sum / (endSample - startSample);
      
      // 根据音频强度计算嘴巴开合程度
      const openness = Math.min(avgAmplitude * defaultOptions.sensitivity * 15, 1.0);
      const intensity = Math.min(maxAmplitude * defaultOptions.sensitivity * 10, 1.0);
      
      frames.push({
        timestamp: i / defaultOptions.frameRate,
        mouth: {
          width: 25 + openness * 25,
          height: 12 + openness * 20,
          openness: openness
        },
        amplitude: avgAmplitude,
        intensity: intensity,
        debug: {
          openness: openness,
          amplitude: avgAmplitude,
          intensity: intensity
        }
      });
    }
    
    return frames;
    
  } catch (error) {
    console.warn('音频分析失败，使用默认动画:', error);
    
    // 返回默认的简单动画帧
    const totalFrames = Math.floor(defaultOptions.frameRate * defaultOptions.duration);
    const frames = [];
    
    for (let i = 0; i < totalFrames; i++) {
      const t = i / totalFrames;
      const openness = (Math.sin(t * Math.PI * 6) + 1) * 0.3 + 0.2; // 更自然的波动
      
      frames.push({
        timestamp: i / defaultOptions.frameRate,
        mouth: {
          width: 25 + openness * 20,
          height: 12 + openness * 18,
          openness: openness
        },
        amplitude: openness,
        intensity: openness,
        debug: {
          openness: openness,
          amplitude: openness,
          intensity: openness
        }
      });
    }
    
    return frames;
  }
}

/**
 * 从文本创建简单的唇形同步帧
 * @param {string} text - 文本内容
 * @param {Object} options - 配置选项
 * @returns {Array} 帧数据数组
 */
export function createLipSyncFramesFromText(text, options = {}) {
  const defaultOptions = {
    frameRate: 30,
    wordsPerSecond: 2.5,
    ...options
  };

  const words = text.split(/\s+/).filter(word => word.length > 0);
  const totalDuration = Math.max(words.length / defaultOptions.wordsPerSecond, 2.0);
  const totalFrames = Math.floor(defaultOptions.frameRate * totalDuration);
  
  const frames = [];
  
  for (let i = 0; i < totalFrames; i++) {
    const t = i / totalFrames;
    const wordIndex = Math.floor(t * words.length);
    const word = words[wordIndex] || '';
    
    // 根据字符类型调整嘴型
    let openness = 0.3;
    
    if (word.length > 0) {
      const char = word[0].toLowerCase();
      if ('aeiou'.includes(char)) {
        openness = 0.7 + Math.random() * 0.2;
      } else if ('bpmf'.includes(char)) {
        openness = 0.1 + Math.random() * 0.1;
      } else {
        openness = 0.4 + Math.random() * 0.3;
      }
    }
    
    // 添加时间变化使动画更自然
    const timeVariation = Math.sin(t * Math.PI * 8) * 0.1;
    openness = Math.max(0, Math.min(1, openness + timeVariation));
    
    frames.push({
      timestamp: i / defaultOptions.frameRate,
      mouth: {
        width: 25 + openness * 25,
        height: 12 + openness * 20,
        openness: openness
      },
      word: word,
      amplitude: openness,
      intensity: openness,
      debug: {
        openness: openness,
        amplitude: openness,
        word: word
      }
    });
  }
  
  return frames;
}
