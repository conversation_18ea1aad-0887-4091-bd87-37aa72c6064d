<template>
  <view class="modern-home">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 头部区域 -->
    <view class="header-section">
      <view class="header-background">
        <view class="gradient-bg"></view>
        <view class="pattern-overlay"></view>
      </view>
      
      <view class="header-content">
        <!-- 用户信息区 -->
        <view class="user-area">
          <view class="user-info">
            <view class="avatar-container" @click="goToProfile">
              <image class="user-avatar" :src="userInfo.avatar" mode="aspectFill"></image>
              <view class="avatar-ring"></view>
              <view class="online-indicator"></view>
            </view>
            <view class="user-details">
              <text class="greeting">{{ getGreeting() }}</text>
              <text class="username">{{ userInfo.nickname || 'AI Explorer' }}</text>
              <view class="user-badge">
                <text class="badge-text">Pro</text>
                <u-icon name="star-fill" size="10" color="#FFD700"></u-icon>
              </view>
            </view>
          </view>
          
          <view class="header-actions">
            <view class="action-item" @click="showNotifications">
              <u-icon name="bell" size="20" color="#FFFFFF"></u-icon>
              <view class="notification-dot" v-if="hasNotifications"></view>
            </view>
            <view class="action-item" @click="showQRCode">
              <u-icon name="qrcode-scan" size="20" color="#FFFFFF"></u-icon>
            </view>
          </view>
        </view>
        
        <!-- 搜索区域 -->
        <view class="search-container">
          <view class="search-box" @click="goToSearch">
            <u-icon name="search" size="16" color="#8E8E93"></u-icon>
            <text class="search-text">搜索AI功能、智能体...</text>
            <view class="voice-search" @click.stop="startVoiceSearch">
              <u-icon name="mic" size="14" color="#667eea"></u-icon>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 主要内容区域 -->
    <scroll-view class="main-content" scroll-y="true" :style="{ height: mainContentHeight + 'px' }">
      <!-- AI能力卡片 -->
      <view class="ai-capabilities">
        <view class="section-header">
          <text class="section-title">AI能力中心</text>
          <text class="section-subtitle">探索强大的AI功能</text>
        </view>
        
        <view class="capabilities-grid">
          <view 
            class="capability-card"
            v-for="(item, index) in aiCapabilities"
            :key="index"
            @click="navigateToFeature(item)"
          >
            <view class="card-header">
              <view class="card-icon" :style="{ background: item.gradient }">
                <text class="iconfont" :class="item.icon"></text>
              </view>
              <view class="card-badge" v-if="item.isNew">NEW</view>
            </view>
            <view class="card-content">
              <text class="card-title">{{ item.title }}</text>
              <text class="card-desc">{{ item.description }}</text>
              <view class="card-stats">
                <text class="stat-item">{{ item.usage }}次使用</text>
                <text class="stat-rating">{{ item.rating }}⭐</text>
              </view>
            </view>
            <view class="card-action">
              <text class="action-text">立即体验</text>
              <u-icon name="arrow-right" size="12" color="#667eea"></u-icon>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 智能推荐 -->
      <view class="smart-recommendations">
        <view class="section-header">
          <text class="section-title">为您推荐</text>
          <text class="section-subtitle">基于您的使用习惯</text>
        </view>
        
        <scroll-view class="recommendations-scroll" scroll-x="true">
          <view class="recommendations-list">
            <view 
              class="recommendation-card"
              v-for="(item, index) in recommendations"
              :key="index"
              @click="useRecommendation(item)"
            >
              <view class="rec-image">
                <image :src="item.image" mode="aspectFill"></image>
                <view class="rec-overlay">
                  <u-icon name="play-circle" size="24" color="#FFFFFF"></u-icon>
                </view>
              </view>
              <view class="rec-info">
                <text class="rec-title">{{ item.title }}</text>
                <text class="rec-desc">{{ item.description }}</text>
                <view class="rec-meta">
                  <text class="rec-category">{{ item.category }}</text>
                  <text class="rec-time">{{ item.duration }}</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 最近使用 -->
      <view class="recent-usage" v-if="recentItems.length > 0">
        <view class="section-header">
          <text class="section-title">最近使用</text>
          <text class="section-link" @click="viewAllRecent">查看全部</text>
        </view>
        
        <view class="recent-list">
          <view 
            class="recent-item"
            v-for="(item, index) in recentItems"
            :key="index"
            @click="continueTask(item)"
          >
            <view class="recent-icon">
              <text class="iconfont" :class="item.icon"></text>
            </view>
            <view class="recent-content">
              <text class="recent-title">{{ item.title }}</text>
              <text class="recent-desc">{{ item.description }}</text>
              <text class="recent-time">{{ formatTime(item.lastUsed) }}</text>
            </view>
            <view class="recent-action">
              <u-icon name="more-dot-fill" size="16" color="#C7C7CC"></u-icon>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="bottom-safe-area"></view>
    </scroll-view>
    
    <!-- 自定义底部导航 -->
    <TabBar />
    
    <!-- 浮动操作按钮 -->
    <view class="floating-action" @click="showQuickActions">
      <view class="fab-button">
        <u-icon name="plus" size="24" color="#FFFFFF"></u-icon>
      </view>
      <view class="fab-ripple"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import TabBar from '@/components/TabBar/index.vue'

// 响应式数据
const statusBarHeight = ref<number>(0)
const mainContentHeight = ref<number>(0)
const hasNotifications = ref<boolean>(true)

const userInfo = ref({
  nickname: 'Alex Chen',
  avatar: '/static/images/avatar-default.png'
})

const aiCapabilities = ref([
  {
    title: '智能翻译',
    description: '支持100+语言实时翻译',
    icon: 'icon-translate',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    usage: '1.2万',
    rating: '4.9',
    isNew: false,
    path: '/pages/translation/text/index'
  },
  {
    title: 'AI对话',
    description: '智能助手24小时在线',
    icon: 'icon-chat',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    usage: '8.5千',
    rating: '4.8',
    isNew: true,
    path: '/pages/digital-human/chat/index'
  },
  {
    title: '文档处理',
    description: '智能文档分析与转换',
    icon: 'icon-document',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    usage: '5.3千',
    rating: '4.7',
    isNew: false,
    path: '/pages/utilities/document/converter/index'
  },
  {
    title: '图像识别',
    description: '强大的视觉AI能力',
    icon: 'icon-image',
    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    usage: '3.1千',
    rating: '4.6',
    isNew: true,
    path: '/pages/utilities/image/processor/index'
  }
])

const recommendations = ref([
  {
    title: '语音转文字',
    description: '高精度语音识别',
    image: '/static/images/rec-voice.jpg',
    category: '语音处理',
    duration: '2分钟',
    path: '/pages/translation/audio/index'
  },
  {
    title: '智能摘要',
    description: '长文档快速摘要',
    image: '/static/images/rec-summary.jpg',
    category: '文本处理',
    duration: '30秒',
    path: '/pages/utilities/text/processor/index'
  }
])

const recentItems = ref([
  {
    title: '英文翻译',
    description: '商务邮件翻译',
    icon: 'icon-translate',
    lastUsed: new Date(Date.now() - 2 * 60 * 60 * 1000),
    path: '/pages/translation/text/index'
  },
  {
    title: 'PDF转换',
    description: '合同文档转换',
    icon: 'icon-pdf',
    lastUsed: new Date(Date.now() - 5 * 60 * 60 * 1000),
    path: '/pages/utilities/document/converter/index'
  }
])

// 计算属性
const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
}

// 生命周期
onMounted(() => {
  // 获取系统信息
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight || 0
      mainContentHeight.value = res.windowHeight - 120 - statusBarHeight.value
    }
  })
})

// 方法
const goToProfile = () => {
  uni.navigateTo({ url: '/pages/user/profile/index' })
}

const showNotifications = () => {
  uni.showToast({ title: '通知中心', icon: 'none' })
}

const showQRCode = () => {
  uni.showToast({ title: '扫一扫', icon: 'none' })
}

const goToSearch = () => {
  uni.navigateTo({ url: '/pages/search/index' })
}

const startVoiceSearch = () => {
  uni.showToast({ title: '语音搜索', icon: 'none' })
}

const navigateToFeature = (item: any) => {
  uni.navigateTo({ url: item.path })
}

const useRecommendation = (item: any) => {
  uni.navigateTo({ url: item.path })
}

const viewAllRecent = () => {
  uni.navigateTo({ url: '/pages/recent/index' })
}

const continueTask = (item: any) => {
  uni.navigateTo({ url: item.path })
}

const showQuickActions = () => {
  uni.showActionSheet({
    itemList: ['新建翻译', '语音输入', '扫描文档', '智能对话'],
    success: (res) => {
      console.log('选择了第' + (res.tapIndex + 1) + '个按钮')
    }
  })
}

const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))
  
  if (hours < 1) return '刚刚'
  if (hours < 24) return `${hours}小时前`
  return `${Math.floor(hours / 24)}天前`
}
</script>

<style lang="scss" scoped>
.modern-home {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
  position: relative;
}

// 状态栏
.status-bar {
  background: transparent;
}

// 头部区域
.header-section {
  position: relative;
  padding-bottom: 40rpx;

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    overflow: hidden;

    .gradient-bg {
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .pattern-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image:
        radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(255,255,255,0.1) 0%, transparent 50%);
    }
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 40rpx 32rpx 0;

    .user-area {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 40rpx;

      .user-info {
        display: flex;
        align-items: center;

        .avatar-container {
          position: relative;
          margin-right: 24rpx;

          .user-avatar {
            width: 96rpx;
            height: 96rpx;
            border-radius: 48rpx;
            border: 4rpx solid rgba(255,255,255,0.3);
          }

          .avatar-ring {
            position: absolute;
            top: -4rpx;
            left: -4rpx;
            right: -4rpx;
            bottom: -4rpx;
            border: 2rpx solid rgba(255,255,255,0.5);
            border-radius: 52rpx;
            animation: pulse 2s infinite;
          }

          .online-indicator {
            position: absolute;
            bottom: 8rpx;
            right: 8rpx;
            width: 20rpx;
            height: 20rpx;
            background: #00D4AA;
            border: 3rpx solid #FFFFFF;
            border-radius: 50%;
          }
        }

        .user-details {
          .greeting {
            display: block;
            font-size: 28rpx;
            color: rgba(255,255,255,0.8);
            margin-bottom: 8rpx;
          }

          .username {
            display: block;
            font-size: 36rpx;
            font-weight: 600;
            color: #FFFFFF;
            margin-bottom: 12rpx;
          }

          .user-badge {
            display: flex;
            align-items: center;
            background: rgba(255,215,0,0.2);
            padding: 6rpx 16rpx;
            border-radius: 20rpx;
            border: 1rpx solid rgba(255,215,0,0.3);

            .badge-text {
              font-size: 20rpx;
              color: #FFD700;
              margin-right: 6rpx;
              font-weight: 600;
            }
          }
        }
      }

      .header-actions {
        display: flex;
        gap: 20rpx;

        .action-item {
          position: relative;
          width: 72rpx;
          height: 72rpx;
          background: rgba(255,255,255,0.15);
          border-radius: 36rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          backdrop-filter: blur(10rpx);

          &:active {
            transform: scale(0.95);
          }

          .notification-dot {
            position: absolute;
            top: 16rpx;
            right: 16rpx;
            width: 16rpx;
            height: 16rpx;
            background: #FF3B30;
            border-radius: 50%;
            border: 2rpx solid #FFFFFF;
          }
        }
      }
    }

    .search-container {
      .search-box {
        background: rgba(255,255,255,0.95);
        border-radius: 28rpx;
        padding: 24rpx 32rpx;
        display: flex;
        align-items: center;
        backdrop-filter: blur(10rpx);
        box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);

        .search-text {
          flex: 1;
          font-size: 28rpx;
          color: #8E8E93;
          margin-left: 16rpx;
        }

        .voice-search {
          width: 56rpx;
          height: 56rpx;
          background: linear-gradient(135deg, #667eea, #764ba2);
          border-radius: 28rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          &:active {
            transform: scale(0.95);
          }
        }
      }
    }
  }
}

// 主要内容区域
.main-content {
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 0 0;
  margin-top: -32rpx;
  position: relative;
  z-index: 3;

  .section-header {
    padding: 48rpx 32rpx 32rpx;

    .section-title {
      display: block;
      font-size: 40rpx;
      font-weight: 700;
      color: #1D1D1F;
      margin-bottom: 8rpx;
    }

    .section-subtitle {
      font-size: 28rpx;
      color: #8E8E93;
    }

    .section-link {
      font-size: 28rpx;
      color: #667eea;
      font-weight: 500;
    }
  }
}

// AI能力卡片
.ai-capabilities {
  .capabilities-grid {
    padding: 0 32rpx;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;

    .capability-card {
      background: #FFFFFF;
      border-radius: 24rpx;
      padding: 32rpx;
      box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
      border: 1rpx solid rgba(0,0,0,0.05);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.12);
      }

      .card-header {
        position: relative;
        margin-bottom: 24rpx;

        .card-icon {
          width: 80rpx;
          height: 80rpx;
          border-radius: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .iconfont {
            font-size: 32rpx;
            color: #FFFFFF;
          }
        }

        .card-badge {
          position: absolute;
          top: -8rpx;
          right: -8rpx;
          background: linear-gradient(45deg, #FF6B6B, #FF8E53);
          color: #FFFFFF;
          font-size: 18rpx;
          font-weight: 600;
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
          box-shadow: 0 2rpx 8rpx rgba(255,107,107,0.4);
        }
      }

      .card-content {
        margin-bottom: 24rpx;

        .card-title {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
          color: #1D1D1F;
          margin-bottom: 8rpx;
        }

        .card-desc {
          display: block;
          font-size: 24rpx;
          color: #8E8E93;
          line-height: 1.4;
          margin-bottom: 16rpx;
        }

        .card-stats {
          display: flex;
          justify-content: space-between;

          .stat-item,
          .stat-rating {
            font-size: 22rpx;
            color: #C7C7CC;
          }
        }
      }

      .card-action {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .action-text {
          font-size: 26rpx;
          color: #667eea;
          font-weight: 500;
        }
      }
    }
  }
}

// 智能推荐
.smart-recommendations {
  margin-top: 48rpx;

  .recommendations-scroll {
    white-space: nowrap;

    .recommendations-list {
      display: flex;
      padding: 0 32rpx;
      gap: 24rpx;

      .recommendation-card {
        flex-shrink: 0;
        width: 320rpx;
        background: #FFFFFF;
        border-radius: 20rpx;
        overflow: hidden;
        box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);

        .rec-image {
          position: relative;
          height: 180rpx;

          image {
            width: 100%;
            height: 100%;
          }

          .rec-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80rpx;
            height: 80rpx;
            background: rgba(0,0,0,0.6);
            border-radius: 40rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10rpx);
          }
        }

        .rec-info {
          padding: 24rpx;

          .rec-title {
            display: block;
            font-size: 28rpx;
            font-weight: 600;
            color: #1D1D1F;
            margin-bottom: 8rpx;
          }

          .rec-desc {
            display: block;
            font-size: 24rpx;
            color: #8E8E93;
            margin-bottom: 16rpx;
            line-height: 1.4;
          }

          .rec-meta {
            display: flex;
            justify-content: space-between;

            .rec-category,
            .rec-time {
              font-size: 22rpx;
              color: #C7C7CC;
            }
          }
        }
      }
    }
  }
}

// 最近使用
.recent-usage {
  margin-top: 48rpx;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .recent-list {
    padding: 0 32rpx;

    .recent-item {
      display: flex;
      align-items: center;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #F2F2F7;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background: #F2F2F7;
        margin: 0 -32rpx;
        padding: 24rpx 32rpx;
        border-radius: 16rpx;
      }

      .recent-icon {
        width: 80rpx;
        height: 80rpx;
        background: #F2F2F7;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;

        .iconfont {
          font-size: 28rpx;
          color: #8E8E93;
        }
      }

      .recent-content {
        flex: 1;

        .recent-title {
          display: block;
          font-size: 28rpx;
          font-weight: 500;
          color: #1D1D1F;
          margin-bottom: 6rpx;
        }

        .recent-desc {
          display: block;
          font-size: 24rpx;
          color: #8E8E93;
          margin-bottom: 8rpx;
        }

        .recent-time {
          font-size: 22rpx;
          color: #C7C7CC;
        }
      }

      .recent-action {
        padding: 16rpx;
      }
    }
  }
}

// 浮动操作按钮
.floating-action {
  position: fixed;
  bottom: 200rpx;
  right: 32rpx;
  z-index: 999;

  .fab-button {
    width: 112rpx;
    height: 112rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 56rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:active {
      transform: scale(0.95);
    }
  }

  .fab-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 112rpx;
    height: 112rpx;
    border: 2rpx solid rgba(102, 126, 234, 0.3);
    border-radius: 56rpx;
    animation: ripple 2s infinite;
  }
}

// 底部安全区域
.bottom-safe-area {
  height: calc(120rpx + env(safe-area-inset-bottom));
}

// 动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes ripple {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.5);
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .modern-home {
    background: linear-gradient(180deg, #1c1c1e 0%, #000000 100%);
  }

  .main-content {
    background: #1c1c1e;
  }

  .capability-card,
  .recommendation-card {
    background: #2c2c2e;
    border-color: #38383a;
  }

  .section-title {
    color: #ffffff !important;
  }

  .card-title,
  .recent-title {
    color: #ffffff !important;
  }

  .recent-item {
    border-bottom-color: #38383a;

    &:active {
      background: #38383a;
    }
  }

  .recent-icon {
    background: #38383a;
  }
}

// 响应式适配
@media screen and (max-width: 480px) {
  .capabilities-grid {
    grid-template-columns: 1fr;
  }

  .user-area {
    flex-direction: column;
    align-items: flex-start;
    gap: 24rpx;
  }

  .header-actions {
    align-self: flex-end;
  }
}
</style>
