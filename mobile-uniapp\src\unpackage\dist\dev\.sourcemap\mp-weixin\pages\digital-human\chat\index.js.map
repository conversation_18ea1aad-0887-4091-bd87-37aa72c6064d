{"version": 3, "file": "index.js", "sources": ["pages/digital-human/chat/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZGlnaXRhbC1odW1hbi9jaGF0L2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"chat-page\">\n    <!-- 顶部数字人信息 -->\n    <view class=\"chat-header\" v-if=\"currentDigitalHuman\">\n      <view class=\"digital-human-info\">\n        <image \n          class=\"avatar\" \n          :src=\"currentDigitalHuman.avatar || '/static/images/default-avatar.png'\"\n          mode=\"aspectFill\"\n        ></image>\n        <view class=\"info-content\">\n          <text class=\"name\">{{ currentDigitalHuman.name }}</text>\n          <text class=\"status\">在线</text>\n        </view>\n      </view>\n      \n      <view class=\"header-actions\">\n        <u-icon \n          name=\"more-dot-fill\" \n          size=\"20\" \n          color=\"#6B7280\"\n          @click=\"showActionSheet = true\"\n        ></u-icon>\n      </view>\n    </view>\n\n    <!-- 消息列表 -->\n    <scroll-view \n      class=\"message-list\"\n      scroll-y=\"true\"\n      :scroll-top=\"scrollTop\"\n      :scroll-with-animation=\"true\"\n      @scrolltoupper=\"loadMoreMessages\"\n    >\n      <!-- 加载更多提示 -->\n      <view class=\"load-more\" v-if=\"hasMoreMessages\">\n        <u-loading-icon v-if=\"isLoadingMore\"></u-loading-icon>\n        <text class=\"load-text\">{{ isLoadingMore ? '加载中...' : '上拉加载更多' }}</text>\n      </view>\n\n      <!-- 消息项 -->\n      <view \n        class=\"message-item\"\n        v-for=\"(message, index) in currentSessionMessages\"\n        :key=\"message.id\"\n        :class=\"{ 'user-message': message.role === 'user', 'assistant-message': message.role === 'assistant' }\"\n      >\n        <!-- 用户消息 -->\n        <view class=\"message-content\" v-if=\"message.role === 'user'\">\n          <view class=\"message-bubble user-bubble\">\n            <text class=\"message-text\" v-if=\"message.message_type === 'text'\">{{ message.content }}</text>\n            \n            <!-- 语音消息 -->\n            <view class=\"audio-message\" v-if=\"message.message_type === 'audio'\">\n              <u-icon name=\"mic-fill\" size=\"16\" color=\"#ffffff\"></u-icon>\n              <text class=\"audio-duration\">{{ formatDuration(message.duration) }}</text>\n              <u-icon \n                :name=\"playingAudio === message.id ? 'pause' : 'play'\" \n                size=\"16\" \n                color=\"#ffffff\"\n                @click=\"toggleAudioPlay(message)\"\n              ></u-icon>\n            </view>\n          </view>\n          <text class=\"message-time\">{{ formatTime(message.timestamp) }}</text>\n        </view>\n\n        <!-- 数字人消息 -->\n        <view class=\"message-content\" v-else>\n          <image \n            class=\"message-avatar\" \n            :src=\"currentDigitalHuman?.avatar || '/static/images/default-avatar.png'\"\n            mode=\"aspectFill\"\n          ></image>\n          \n          <view class=\"message-bubble assistant-bubble\">\n            <text class=\"message-text\" v-if=\"message.message_type === 'text'\">{{ message.content }}</text>\n            \n            <!-- 语音回复 -->\n            <view class=\"audio-message\" v-if=\"message.message_type === 'audio'\">\n              <u-icon \n                :name=\"playingAudio === message.id ? 'pause-circle-fill' : 'play-circle-fill'\" \n                size=\"32\" \n                color=\"#2563EB\"\n                @click=\"toggleAudioPlay(message)\"\n              ></u-icon>\n              <text class=\"audio-duration\">{{ formatDuration(message.duration) }}</text>\n            </view>\n            \n            <!-- 视频回复 -->\n            <view class=\"video-message\" v-if=\"message.message_type === 'video'\">\n              <video \n                class=\"message-video\"\n                :src=\"message.video_url\"\n                :poster=\"currentDigitalHuman?.avatar\"\n                controls\n                @play=\"onVideoPlay\"\n                @pause=\"onVideoPause\"\n              ></video>\n            </view>\n            \n            <!-- 消息操作 -->\n            <view class=\"message-actions\">\n              <u-icon \n                name=\"volume-up\" \n                size=\"14\" \n                color=\"#6B7280\"\n                @click=\"playTextAudio(message.content)\"\n                v-if=\"message.message_type === 'text'\"\n              ></u-icon>\n              <u-icon \n                name=\"video\" \n                size=\"14\" \n                color=\"#6B7280\"\n                margin-left=\"8\"\n                @click=\"generateVideoReply(message.id)\"\n                v-if=\"message.message_type === 'text'\"\n              ></u-icon>\n            </view>\n          </view>\n          \n          <text class=\"message-time\">{{ formatTime(message.timestamp) }}</text>\n        </view>\n      </view>\n\n      <!-- 正在生成视频提示 -->\n      <view class=\"generating-video\" v-if=\"isGeneratingVideo\">\n        <view class=\"generating-content\">\n          <u-loading-icon mode=\"flower\"></u-loading-icon>\n          <text class=\"generating-text\">正在生成数字人视频回复...</text>\n        </view>\n      </view>\n\n      <!-- 正在输入提示 -->\n      <view class=\"typing-indicator\" v-if=\"isSending\">\n        <image \n          class=\"typing-avatar\" \n          :src=\"currentDigitalHuman?.avatar || '/static/images/default-avatar.png'\"\n          mode=\"aspectFill\"\n        ></image>\n        <view class=\"typing-bubble\">\n          <view class=\"typing-dots\">\n            <view class=\"dot\"></view>\n            <view class=\"dot\"></view>\n            <view class=\"dot\"></view>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n\n    <!-- 输入区域 -->\n    <view class=\"input-area\">\n      <view class=\"input-container\">\n        <u-icon \n          name=\"plus\" \n          size=\"24\" \n          color=\"#6B7280\"\n          @click=\"showInputOptions = true\"\n        ></u-icon>\n        \n        <input \n          class=\"text-input\"\n          v-model=\"inputText\"\n          placeholder=\"输入消息...\"\n          :disabled=\"isSending\"\n          @confirm=\"sendMessage\"\n          confirm-type=\"send\"\n        />\n        \n        <view class=\"input-actions\">\n          <u-icon \n            v-if=\"!inputText.trim()\"\n            name=\"mic\" \n            size=\"24\" \n            color=\"#2563EB\"\n            @click=\"startVoiceInput\"\n          ></u-icon>\n          \n          <u-button \n            v-else\n            type=\"primary\" \n            size=\"small\"\n            :loading=\"isSending\"\n            :disabled=\"!inputText.trim()\"\n            @click=\"sendMessage\"\n          >\n            发送\n          </u-button>\n        </view>\n      </view>\n    </view>\n\n    <!-- 数字人选择弹窗 -->\n    <u-modal \n      v-model=\"showDigitalHumanPicker\" \n      title=\"选择数字人\"\n      :show-cancel-button=\"false\"\n      confirm-text=\"关闭\"\n      @confirm=\"showDigitalHumanPicker = false\"\n    >\n      <view class=\"digital-human-list\">\n        <view \n          class=\"digital-human-item\"\n          v-for=\"human in digitalHumans\"\n          :key=\"human.id\"\n          :class=\"{ active: currentDigitalHuman?.id === human.id }\"\n          @click=\"selectDigitalHuman(human)\"\n        >\n          <image class=\"item-avatar\" :src=\"human.avatar\" mode=\"aspectFill\"></image>\n          <view class=\"item-info\">\n            <text class=\"item-name\">{{ human.name }}</text>\n            <text class=\"item-desc\">{{ human.description }}</text>\n          </view>\n          <u-icon \n            v-if=\"currentDigitalHuman?.id === human.id\"\n            name=\"checkmark\" \n            size=\"16\" \n            color=\"#2563EB\"\n          ></u-icon>\n        </view>\n      </view>\n    </u-modal>\n\n    <!-- 输入选项弹窗 -->\n    <u-action-sheet \n      v-model=\"showInputOptions\"\n      :actions=\"inputOptionActions\"\n      @click=\"handleInputOption\"\n    ></u-action-sheet>\n\n    <!-- 更多操作弹窗 -->\n    <u-action-sheet \n      v-model=\"showActionSheet\"\n      :actions=\"actionSheetActions\"\n      @click=\"handleActionSheet\"\n    ></u-action-sheet>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted, nextTick, watch } from 'vue'\nimport { useDigitalHumanStore } from '@/store/modules/digitalHuman'\nimport { type DigitalHuman, type ChatMessage } from '@/api/digital-human'\nimport dayjs from 'dayjs'\n\n// Store\nconst digitalHumanStore = useDigitalHumanStore()\n\n// 响应式数据\nconst inputText = ref<string>('')\nconst scrollTop = ref<number>(0)\nconst playingAudio = ref<string>('')\nconst showDigitalHumanPicker = ref<boolean>(false)\nconst showInputOptions = ref<boolean>(false)\nconst showActionSheet = ref<boolean>(false)\nconst hasMoreMessages = ref<boolean>(true)\nconst isLoadingMore = ref<boolean>(false)\n\n// 计算属性\nconst {\n  digitalHumans,\n  currentDigitalHuman,\n  currentSession,\n  currentSessionMessages,\n  isSending,\n  isGeneratingVideo\n} = digitalHumanStore\n\n// 输入选项\nconst inputOptionActions = ref([\n  { name: '拍照', value: 'camera' },\n  { name: '从相册选择', value: 'album' },\n  { name: '语音输入', value: 'voice' }\n])\n\n// 更多操作选项\nconst actionSheetActions = ref([\n  { name: '切换数字人', value: 'switch' },\n  { name: '新建对话', value: 'new' },\n  { name: '对话历史', value: 'history' },\n  { name: '清空消息', value: 'clear' },\n  { name: '语音设置', value: 'voice_settings' }\n])\n\n// 页面加载\nonMounted(async () => {\n  // 加载数字人列表\n  await digitalHumanStore.loadDigitalHumans()\n\n  // 如果没有选择数字人，显示选择弹窗\n  if (!currentDigitalHuman.value && digitalHumans.value.length > 0) {\n    showDigitalHumanPicker.value = true\n  } else if (currentDigitalHuman.value) {\n    // 加载对话会话\n    await digitalHumanStore.loadChatSessions()\n\n    // 如果没有当前会话，创建新会话\n    if (!currentSession.value) {\n      await digitalHumanStore.createNewSession()\n    } else {\n      // 加载消息\n      await digitalHumanStore.loadMessages()\n    }\n  }\n\n  // 滚动到底部\n  scrollToBottom()\n})\n\n// 监听消息变化，自动滚动到底部\nwatch(\n  () => currentSessionMessages.value.length,\n  () => {\n    nextTick(() => {\n      scrollToBottom()\n    })\n  }\n)\n\n// 选择数字人\nconst selectDigitalHuman = async (human: DigitalHuman) => {\n  await digitalHumanStore.selectDigitalHuman(human)\n  showDigitalHumanPicker.value = false\n\n  // 创建新会话\n  await digitalHumanStore.createNewSession()\n\n  // 滚动到底部\n  scrollToBottom()\n}\n\n// 发送消息\nconst sendMessage = async () => {\n  if (!inputText.value.trim() || isSending.value) return\n\n  const message = inputText.value.trim()\n  inputText.value = ''\n\n  try {\n    await digitalHumanStore.sendTextMessage(message)\n\n    // 滚动到底部\n    scrollToBottom()\n  } catch (error: any) {\n    uni.__f__('error','at pages/digital-human/chat/index.vue:345','发送消息失败:', error)\n    uni.showToast({\n      title: error.message || '发送失败',\n      icon: 'error'\n    })\n\n    // 恢复输入内容\n    inputText.value = message\n  }\n}\n\n// 开始语音输入\nconst startVoiceInput = () => {\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  uni.showToast({\n    title: '当前平台暂不支持语音输入',\n    icon: 'none'\n  })\n\n}\n\n// 播放/暂停音频\nconst toggleAudioPlay = (message: ChatMessage) => {\n  if (!message.audio_url) return\n\n  if (playingAudio.value === message.id) {\n    // 暂停播放\n    uni.stopBackgroundAudio()\n    playingAudio.value = ''\n  } else {\n    // 开始播放\n    uni.playBackgroundAudio({\n      dataUrl: message.audio_url,\n      title: '数字人语音',\n      success: () => {\n        playingAudio.value = message.id\n      },\n      fail: (error) => {\n        uni.__f__('error','at pages/digital-human/chat/index.vue:408','播放音频失败:', error)\n        uni.showToast({\n          title: '播放失败',\n          icon: 'error'\n        })\n      }\n    })\n  }\n}\n\n// 播放文本语音\nconst playTextAudio = async (text: string) => {\n  try {\n    // 这里可以调用TTS接口生成语音\n    uni.showToast({\n      title: '正在生成语音...',\n      icon: 'loading'\n    })\n\n    // 模拟语音生成\n    setTimeout(() => {\n      uni.showToast({\n        title: '语音播放功能开发中',\n        icon: 'none'\n      })\n    }, 1000)\n  } catch (error) {\n    uni.__f__('error','at pages/digital-human/chat/index.vue:435','播放文本语音失败:', error)\n    uni.showToast({\n      title: '播放失败',\n      icon: 'error'\n    })\n  }\n}\n\n// 生成视频回复\nconst generateVideoReply = async (messageId: string) => {\n  try {\n    uni.showLoading({\n      title: '生成视频中...'\n    })\n\n    const videoUrl = await digitalHumanStore.generateVideoReply(messageId)\n\n    uni.hideLoading()\n\n    if (videoUrl) {\n      uni.showToast({\n        title: '视频生成成功',\n        icon: 'success'\n      })\n\n      // 滚动到底部显示新的视频消息\n      scrollToBottom()\n    }\n  } catch (error: any) {\n    uni.hideLoading()\n    uni.__f__('error','at pages/digital-human/chat/index.vue:465','生成视频失败:', error)\n    uni.showToast({\n      title: error.message || '生成视频失败',\n      icon: 'error'\n    })\n  }\n}\n\n// 视频播放事件\nconst onVideoPlay = () => {\n  uni.__f__('log','at pages/digital-human/chat/index.vue:475','视频开始播放')\n}\n\nconst onVideoPause = () => {\n  uni.__f__('log','at pages/digital-human/chat/index.vue:479','视频暂停播放')\n}\n\n// 加载更多消息\nconst loadMoreMessages = async () => {\n  if (isLoadingMore.value || !hasMoreMessages.value) return\n\n  try {\n    isLoadingMore.value = true\n\n    // 这里应该加载更早的消息\n    // 暂时模拟没有更多消息\n    setTimeout(() => {\n      hasMoreMessages.value = false\n      isLoadingMore.value = false\n    }, 1000)\n  } catch (error) {\n    uni.__f__('error','at pages/digital-human/chat/index.vue:496','加载更多消息失败:', error)\n    isLoadingMore.value = false\n  }\n}\n\n// 滚动到底部\nconst scrollToBottom = () => {\n  nextTick(() => {\n    scrollTop.value = 999999\n  })\n}\n\n// 处理输入选项\nconst handleInputOption = (action: any) => {\n  showInputOptions.value = false\n\n  switch (action.value) {\n    case 'camera':\n      chooseImage('camera')\n      break\n    case 'album':\n      chooseImage('album')\n      break\n    case 'voice':\n      startVoiceInput()\n      break\n  }\n}\n\n// 选择图片\nconst chooseImage = (sourceType: 'camera' | 'album') => {\n  uni.chooseImage({\n    count: 1,\n    sourceType: [sourceType],\n    success: (res) => {\n      const filePath = res.tempFilePaths[0]\n      // 这里可以发送图片消息\n      uni.showToast({\n        title: '图片消息功能开发中',\n        icon: 'none'\n      })\n    },\n    fail: (error) => {\n      uni.__f__('error','at pages/digital-human/chat/index.vue:539','选择图片失败:', error)\n    }\n  })\n}\n\n// 处理更多操作\nconst handleActionSheet = async (action: any) => {\n  showActionSheet.value = false\n\n  switch (action.value) {\n    case 'switch':\n      showDigitalHumanPicker.value = true\n      break\n    case 'new':\n      await createNewSession()\n      break\n    case 'history':\n      navigateToHistory()\n      break\n    case 'clear':\n      await clearMessages()\n      break\n    case 'voice_settings':\n      navigateToVoiceSettings()\n      break\n  }\n}\n\n// 创建新会话\nconst createNewSession = async () => {\n  try {\n    await digitalHumanStore.createNewSession()\n    uni.showToast({\n      title: '新对话已创建',\n      icon: 'success'\n    })\n    scrollToBottom()\n  } catch (error: any) {\n    uni.showToast({\n      title: error.message || '创建失败',\n      icon: 'error'\n    })\n  }\n}\n\n// 跳转到历史记录\nconst navigateToHistory = () => {\n  uni.navigateTo({\n    url: '/pages/digital-human/history/index'\n  })\n}\n\n// 清空消息\nconst clearMessages = async () => {\n  uni.showModal({\n    title: '确认清空',\n    content: '确定要清空当前对话的所有消息吗？',\n    success: async (res) => {\n      if (res.confirm) {\n        await digitalHumanStore.clearMessages()\n      }\n    }\n  })\n}\n\n// 跳转到语音设置\nconst navigateToVoiceSettings = () => {\n  uni.navigateTo({\n    url: '/pages/digital-human/voice-settings/index'\n  })\n}\n\n// 格式化时间\nconst formatTime = (timestamp: string) => {\n  const now = dayjs()\n  const time = dayjs(timestamp)\n\n  if (now.diff(time, 'day') === 0) {\n    return time.format('HH:mm')\n  } else if (now.diff(time, 'day') === 1) {\n    return '昨天 ' + time.format('HH:mm')\n  } else {\n    return time.format('MM-DD HH:mm')\n  }\n}\n\n// 格式化音频时长\nconst formatDuration = (duration?: number) => {\n  if (!duration) return '0:00'\n\n  const minutes = Math.floor(duration / 60)\n  const seconds = Math.floor(duration % 60)\n  return `${minutes}:${seconds.toString().padStart(2, '0')}`\n}\n\n// 页面下拉刷新\nconst onPullDownRefresh = async () => {\n  try {\n    if (currentSession.value) {\n      await digitalHumanStore.loadMessages()\n    }\n    uni.showToast({\n      title: '刷新成功',\n      icon: 'success'\n    })\n  } catch (error) {\n    uni.showToast({\n      title: '刷新失败',\n      icon: 'error'\n    })\n  } finally {\n    uni.stopPullDownRefresh()\n  }\n}\n\n// 导出方法供页面使用\ndefineExpose({\n  onPullDownRefresh\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.chat-page {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: $bg-secondary;\n}\n\n// 聊天头部\n.chat-header {\n  background-color: $bg-primary;\n  padding: $spacing-4;\n  border-bottom: 1px solid $border-primary;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  .digital-human-info {\n    display: flex;\n    align-items: center;\n\n    .avatar {\n      width: 80rpx;\n      height: 80rpx;\n      border-radius: $radius-full;\n      margin-right: $spacing-3;\n    }\n\n    .info-content {\n      .name {\n        display: block;\n        font-size: $font-size-md;\n        font-weight: $font-weight-semibold;\n        color: $text-primary;\n        margin-bottom: $spacing-1;\n      }\n\n      .status {\n        font-size: $font-size-xs;\n        color: $success;\n      }\n    }\n  }\n\n  .header-actions {\n    padding: $spacing-2;\n  }\n}\n\n// 消息列表\n.message-list {\n  flex: 1;\n  padding: $spacing-4;\n\n  .load-more {\n    text-align: center;\n    padding: $spacing-4;\n\n    .load-text {\n      font-size: $font-size-sm;\n      color: $text-tertiary;\n      margin-left: $spacing-2;\n    }\n  }\n}\n\n// 消息项\n.message-item {\n  margin-bottom: $spacing-6;\n\n  &.user-message {\n    .message-content {\n      flex-direction: row-reverse;\n\n      .message-bubble {\n        margin-left: $spacing-12;\n        margin-right: 0;\n      }\n\n      .message-time {\n        text-align: right;\n        margin-right: $spacing-2;\n      }\n    }\n  }\n\n  &.assistant-message {\n    .message-content {\n      flex-direction: row;\n\n      .message-bubble {\n        margin-right: $spacing-12;\n        margin-left: $spacing-2;\n      }\n\n      .message-time {\n        text-align: left;\n        margin-left: 88rpx;\n      }\n    }\n  }\n\n  .message-content {\n    display: flex;\n    align-items: flex-start;\n\n    .message-avatar {\n      width: 64rpx;\n      height: 64rpx;\n      border-radius: $radius-full;\n      flex-shrink: 0;\n    }\n\n    .message-bubble {\n      max-width: 70%;\n      padding: $spacing-3 $spacing-4;\n      border-radius: $radius-lg;\n      position: relative;\n\n      &.user-bubble {\n        background-color: $primary;\n        color: white;\n\n        &::after {\n          content: '';\n          position: absolute;\n          right: -12rpx;\n          top: 20rpx;\n          width: 0;\n          height: 0;\n          border: 12rpx solid transparent;\n          border-left-color: $primary;\n        }\n      }\n\n      &.assistant-bubble {\n        background-color: $bg-primary;\n        border: 1px solid $border-primary;\n        color: $text-primary;\n\n        &::after {\n          content: '';\n          position: absolute;\n          left: -12rpx;\n          top: 20rpx;\n          width: 0;\n          height: 0;\n          border: 12rpx solid transparent;\n          border-right-color: $bg-primary;\n        }\n      }\n\n      .message-text {\n        font-size: $font-size-base;\n        line-height: $line-height-relaxed;\n        word-break: break-word;\n      }\n\n      .audio-message {\n        display: flex;\n        align-items: center;\n        gap: $spacing-2;\n\n        .audio-duration {\n          font-size: $font-size-sm;\n          min-width: 60rpx;\n        }\n      }\n\n      .video-message {\n        .message-video {\n          width: 100%;\n          max-width: 400rpx;\n          height: 300rpx;\n          border-radius: $radius-md;\n        }\n      }\n\n      .message-actions {\n        display: flex;\n        align-items: center;\n        gap: $spacing-2;\n        margin-top: $spacing-2;\n        padding-top: $spacing-2;\n        border-top: 1px solid $border-primary;\n      }\n    }\n\n    .message-time {\n      font-size: $font-size-xs;\n      color: $text-quaternary;\n      margin-top: $spacing-1;\n    }\n  }\n}\n\n// 正在生成视频提示\n.generating-video {\n  text-align: center;\n  padding: $spacing-6;\n\n  .generating-content {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: $spacing-3;\n\n    .generating-text {\n      font-size: $font-size-sm;\n      color: $text-secondary;\n    }\n  }\n}\n\n// 正在输入提示\n.typing-indicator {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: $spacing-4;\n\n  .typing-avatar {\n    width: 64rpx;\n    height: 64rpx;\n    border-radius: $radius-full;\n    margin-right: $spacing-2;\n  }\n\n  .typing-bubble {\n    background-color: $bg-primary;\n    border: 1px solid $border-primary;\n    border-radius: $radius-lg;\n    padding: $spacing-3 $spacing-4;\n    position: relative;\n\n    &::after {\n      content: '';\n      position: absolute;\n      left: -12rpx;\n      top: 20rpx;\n      width: 0;\n      height: 0;\n      border: 12rpx solid transparent;\n      border-right-color: $bg-primary;\n    }\n\n    .typing-dots {\n      display: flex;\n      gap: 8rpx;\n\n      .dot {\n        width: 12rpx;\n        height: 12rpx;\n        background-color: $text-tertiary;\n        border-radius: $radius-full;\n        animation: typing 1.4s infinite ease-in-out;\n\n        &:nth-child(1) { animation-delay: -0.32s; }\n        &:nth-child(2) { animation-delay: -0.16s; }\n        &:nth-child(3) { animation-delay: 0s; }\n      }\n    }\n  }\n}\n\n@keyframes typing {\n  0%, 80%, 100% {\n    transform: scale(0.8);\n    opacity: 0.5;\n  }\n  40% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n// 输入区域\n.input-area {\n  background-color: $bg-primary;\n  border-top: 1px solid $border-primary;\n  padding: $spacing-4;\n  padding-bottom: calc($spacing-4 + env(safe-area-inset-bottom));\n\n  .input-container {\n    display: flex;\n    align-items: center;\n    gap: $spacing-3;\n\n    .text-input {\n      flex: 1;\n      background-color: $bg-tertiary;\n      border: 1px solid $border-primary;\n      border-radius: $radius-full;\n      padding: $spacing-3 $spacing-4;\n      font-size: $font-size-base;\n      color: $text-primary;\n\n      &::placeholder {\n        color: $text-quaternary;\n      }\n\n      &:focus {\n        border-color: $primary;\n      }\n    }\n\n    .input-actions {\n      display: flex;\n      align-items: center;\n    }\n  }\n}\n\n// 数字人列表弹窗\n.digital-human-list {\n  max-height: 600rpx;\n\n  .digital-human-item {\n    display: flex;\n    align-items: center;\n    padding: $spacing-4;\n    border-bottom: 1px solid $border-primary;\n\n    &:last-child {\n      border-bottom: none;\n    }\n\n    &.active {\n      background-color: $primary-50;\n    }\n\n    &:active {\n      background-color: $bg-tertiary;\n    }\n\n    .item-avatar {\n      width: 80rpx;\n      height: 80rpx;\n      border-radius: $radius-full;\n      margin-right: $spacing-3;\n    }\n\n    .item-info {\n      flex: 1;\n\n      .item-name {\n        display: block;\n        font-size: $font-size-base;\n        font-weight: $font-weight-medium;\n        color: $text-primary;\n        margin-bottom: $spacing-1;\n      }\n\n      .item-desc {\n        font-size: $font-size-sm;\n        color: $text-secondary;\n        line-height: $line-height-snug;\n      }\n    }\n  }\n}\n\n// 响应式适配\n@media screen and (max-width: 480px) {\n  .message-item {\n    .message-bubble {\n      max-width: 80%;\n    }\n  }\n\n  .input-area {\n    padding: $spacing-3;\n    padding-bottom: calc($spacing-3 + env(safe-area-inset-bottom));\n  }\n}\n\n// 暗色模式适配\n@media (prefers-color-scheme: dark) {\n  .chat-page {\n    background-color: #1a1a1a;\n  }\n\n  .chat-header {\n    background-color: #2d2d2d;\n    border-color: #404040;\n  }\n\n  .message-bubble.assistant-bubble {\n    background-color: #2d2d2d;\n    border-color: #404040;\n    color: #ffffff;\n\n    &::after {\n      border-right-color: #2d2d2d;\n    }\n  }\n\n  .typing-bubble {\n    background-color: #2d2d2d;\n    border-color: #404040;\n\n    &::after {\n      border-right-color: #2d2d2d;\n    }\n  }\n\n  .input-area {\n    background-color: #2d2d2d;\n    border-color: #404040;\n  }\n\n  .text-input {\n    background-color: #404040;\n    border-color: #555555;\n    color: #ffffff;\n\n    &::placeholder {\n      color: #888888;\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/AI_system/mobile-uniapp/src/pages/digital-human/chat/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useDigitalHumanStore", "ref", "onMounted", "watch", "nextTick", "uni", "dayjs"], "mappings": ";;;;;;;;;;;;;;;AAsPA,UAAM,oBAAoBA,2BAAAA;AAGpB,UAAA,YAAYC,kBAAY,EAAE;AAC1B,UAAA,YAAYA,kBAAY,CAAC;AACzB,UAAA,eAAeA,kBAAY,EAAE;AAC7B,UAAA,yBAAyBA,kBAAa,KAAK;AAC3C,UAAA,mBAAmBA,kBAAa,KAAK;AACrC,UAAA,kBAAkBA,kBAAa,KAAK;AACpC,UAAA,kBAAkBA,kBAAa,IAAI;AACnC,UAAA,gBAAgBA,kBAAa,KAAK;AAGlC,UAAA;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACE,IAAA;AAGJ,UAAM,qBAAqBA,cAAAA,IAAI;AAAA,MAC7B,EAAE,MAAM,MAAM,OAAO,SAAS;AAAA,MAC9B,EAAE,MAAM,SAAS,OAAO,QAAQ;AAAA,MAChC,EAAE,MAAM,QAAQ,OAAO,QAAQ;AAAA,IAAA,CAChC;AAGD,UAAM,qBAAqBA,cAAAA,IAAI;AAAA,MAC7B,EAAE,MAAM,SAAS,OAAO,SAAS;AAAA,MACjC,EAAE,MAAM,QAAQ,OAAO,MAAM;AAAA,MAC7B,EAAE,MAAM,QAAQ,OAAO,UAAU;AAAA,MACjC,EAAE,MAAM,QAAQ,OAAO,QAAQ;AAAA,MAC/B,EAAE,MAAM,QAAQ,OAAO,iBAAiB;AAAA,IAAA,CACzC;AAGDC,kBAAAA,UAAU,YAAY;AAEpB,YAAM,kBAAkB;AAGxB,UAAI,CAAC,oBAAoB,SAAS,cAAc,MAAM,SAAS,GAAG;AAChE,+BAAuB,QAAQ;AAAA,MAAA,WACtB,oBAAoB,OAAO;AAEpC,cAAM,kBAAkB;AAGpB,YAAA,CAAC,eAAe,OAAO;AACzB,gBAAM,kBAAkB;QAAiB,OACpC;AAEL,gBAAM,kBAAkB;QAC1B;AAAA,MACF;AAGe;IAAA,CAChB;AAGDC,kBAAA;AAAA,MACE,MAAM,uBAAuB,MAAM;AAAA,MACnC,MAAM;AACJC,sBAAAA,WAAS,MAAM;AACE;QAAA,CAChB;AAAA,MACH;AAAA,IAAA;AAII,UAAA,qBAAqB,OAAO,UAAwB;AAClD,YAAA,kBAAkB,mBAAmB,KAAK;AAChD,6BAAuB,QAAQ;AAG/B,YAAM,kBAAkB;AAGT;IAAA;AAIjB,UAAM,cAAc,YAAY;AAC9B,UAAI,CAAC,UAAU,MAAM,UAAU,UAAU;AAAO;AAE1C,YAAA,UAAU,UAAU,MAAM,KAAK;AACrC,gBAAU,QAAQ;AAEd,UAAA;AACI,cAAA,kBAAkB,gBAAgB,OAAO;AAGhC;eACR,OAAY;AACnBC,sBAAA,MAAI,MAAM,SAAQ,6CAA4C,WAAW,KAAK;AAC9EA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QAAA,CACP;AAGD,kBAAU,QAAQ;AAAA,MACpB;AAAA,IAAA;AAIF,UAAM,kBAAkB,MAAM;AA2B5BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAKG,UAAA,kBAAkB,CAAC,YAAyB;AAChD,UAAI,CAAC,QAAQ;AAAW;AAEpB,UAAA,aAAa,UAAU,QAAQ,IAAI;AAErCA,sBAAA,MAAI,oBAAoB;AACxB,qBAAa,QAAQ;AAAA,MAAA,OAChB;AAELA,sBAAAA,MAAI,oBAAoB;AAAA,UACtB,SAAS,QAAQ;AAAA,UACjB,OAAO;AAAA,UACP,SAAS,MAAM;AACb,yBAAa,QAAQ,QAAQ;AAAA,UAC/B;AAAA,UACA,MAAM,CAAC,UAAU;AACfA,0BAAA,MAAI,MAAM,SAAQ,6CAA4C,WAAW,KAAK;AAC9EA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAAA,CACP;AAAA,UACH;AAAA,QAAA,CACD;AAAA,MACH;AAAA,IAAA;AAII,UAAA,gBAAgB,OAAO,SAAiB;AACxC,UAAA;AAEFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAGD,mBAAW,MAAM;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,WACA,GAAI;AAAA,eACA,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,6CAA4C,aAAa,KAAK;AAChFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,IAAA;AAII,UAAA,qBAAqB,OAAO,cAAsB;AAClD,UAAA;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,QAAA,CACR;AAED,cAAM,WAAW,MAAM,kBAAkB,mBAAmB,SAAS;AAErEA,sBAAA,MAAI,YAAY;AAEhB,YAAI,UAAU;AACZA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAGc;QACjB;AAAA,eACO,OAAY;AACnBA,sBAAA,MAAI,YAAY;AAChBA,sBAAA,MAAI,MAAM,SAAQ,6CAA4C,WAAW,KAAK;AAC9EA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,IAAA;AAIF,UAAM,cAAc,MAAM;AACpBA,oBAAAA,MAAA,MAAM,OAAM,6CAA4C,QAAQ;AAAA,IAAA;AAGtE,UAAM,eAAe,MAAM;AACrBA,oBAAAA,MAAA,MAAM,OAAM,6CAA4C,QAAQ;AAAA,IAAA;AAItE,UAAM,mBAAmB,YAAY;AAC/B,UAAA,cAAc,SAAS,CAAC,gBAAgB;AAAO;AAE/C,UAAA;AACF,sBAAc,QAAQ;AAItB,mBAAW,MAAM;AACf,0BAAgB,QAAQ;AACxB,wBAAc,QAAQ;AAAA,WACrB,GAAI;AAAA,eACA,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,6CAA4C,aAAa,KAAK;AAChF,sBAAc,QAAQ;AAAA,MACxB;AAAA,IAAA;AAIF,UAAM,iBAAiB,MAAM;AAC3BD,oBAAAA,WAAS,MAAM;AACb,kBAAU,QAAQ;AAAA,MAAA,CACnB;AAAA,IAAA;AAIG,UAAA,oBAAoB,CAAC,WAAgB;AACzC,uBAAiB,QAAQ;AAEzB,cAAQ,OAAO,OAAO;AAAA,QACpB,KAAK;AACH,sBAAY,QAAQ;AACpB;AAAA,QACF,KAAK;AACH,sBAAY,OAAO;AACnB;AAAA,QACF,KAAK;AACa;AAChB;AAAA,MACJ;AAAA,IAAA;AAII,UAAA,cAAc,CAAC,eAAmC;AACtDC,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,YAAY,CAAC,UAAU;AAAA,QACvB,SAAS,CAAC,QAAQ;AACC,cAAI,cAAc,CAAC;AAEpCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,QACA,MAAM,CAAC,UAAU;AACfA,wBAAA,MAAI,MAAM,SAAQ,6CAA4C,WAAW,KAAK;AAAA,QAChF;AAAA,MAAA,CACD;AAAA,IAAA;AAIG,UAAA,oBAAoB,OAAO,WAAgB;AAC/C,sBAAgB,QAAQ;AAExB,cAAQ,OAAO,OAAO;AAAA,QACpB,KAAK;AACH,iCAAuB,QAAQ;AAC/B;AAAA,QACF,KAAK;AACH,gBAAM,iBAAiB;AACvB;AAAA,QACF,KAAK;AACe;AAClB;AAAA,QACF,KAAK;AACH,gBAAM,cAAc;AACpB;AAAA,QACF,KAAK;AACqB;AACxB;AAAA,MACJ;AAAA,IAAA;AAIF,UAAM,mBAAmB,YAAY;AAC/B,UAAA;AACF,cAAM,kBAAkB;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACc;eACR,OAAY;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,IAAA;AAIF,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MAAA,CACN;AAAA,IAAA;AAIH,UAAM,gBAAgB,YAAY;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,kBAAM,kBAAkB;UAC1B;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,0BAA0B,MAAM;AACpCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MAAA,CACN;AAAA,IAAA;AAIG,UAAA,aAAa,CAAC,cAAsB;AACxC,YAAM,MAAMC,cAAAA;AACN,YAAA,OAAOA,oBAAM,SAAS;AAE5B,UAAI,IAAI,KAAK,MAAM,KAAK,MAAM,GAAG;AACxB,eAAA,KAAK,OAAO,OAAO;AAAA,MAAA,WACjB,IAAI,KAAK,MAAM,KAAK,MAAM,GAAG;AAC/B,eAAA,QAAQ,KAAK,OAAO,OAAO;AAAA,MAAA,OAC7B;AACE,eAAA,KAAK,OAAO,aAAa;AAAA,MAClC;AAAA,IAAA;AAII,UAAA,iBAAiB,CAAC,aAAsB;AAC5C,UAAI,CAAC;AAAiB,eAAA;AAEtB,YAAM,UAAU,KAAK,MAAM,WAAW,EAAE;AACxC,YAAM,UAAU,KAAK,MAAM,WAAW,EAAE;AACjC,aAAA,GAAG,OAAO,IAAI,QAAQ,WAAW,SAAS,GAAG,GAAG,CAAC;AAAA,IAAA;AAI1D,UAAM,oBAAoB,YAAY;AAChC,UAAA;AACF,YAAI,eAAe,OAAO;AACxB,gBAAM,kBAAkB;QAC1B;AACAD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,eACM,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACAA,sBAAA,MAAI,oBAAoB;AAAA,MAC1B;AAAA,IAAA;AAIW,aAAA;AAAA,MACX;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/oBD,GAAG,WAAW,eAAe;"}