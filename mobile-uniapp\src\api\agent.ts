/**
 * AI智能体相关API接口
 */
import request from '@/utils/request'

// 智能体信息 (从chat.ts导入相同的类型)
export interface AIAgent {
  id: string
  name: string
  description: string
  avatar: string
  category: string
  tags: string[]
  capabilities: string[]
  model: string
  temperature: number
  maxTokens: number
  systemPrompt: string
  isPublic: boolean
  isPremium: boolean
  price: number
  rating: number
  usageCount: number
  createdBy: string
  createdAt: string
  updatedAt: string
}

// 智能体市场信息
export interface AgentMarketInfo extends AIAgent {
  author: {
    id: string
    username: string
    avatar: string
    verified: boolean
  }
  stats: {
    totalUsers: number
    activeUsers: number
    totalConversations: number
    averageRating: number
    ratingCount: number
    revenue?: number
  }
  reviews: Array<{
    id: string
    userId: string
    username: string
    avatar: string
    rating: number
    comment: string
    createdAt: string
  }>
  isOwned: boolean
  isFavorited: boolean
  isPurchased: boolean
}

// 智能体分类
export interface AgentCategory {
  id: string
  name: string
  description: string
  icon: string
  color: string
  agentCount: number
  featured: boolean
  sortOrder: number
}

/**
 * 获取智能体市场列表
 */
export const getMarketAgents = (params: {
  page?: number
  limit?: number
  category?: string
  keyword?: string
  tags?: string[]
  priceType?: 'free' | 'paid' | 'all'
  sortBy?: 'rating' | 'usage' | 'created' | 'updated' | 'price'
  sortOrder?: 'asc' | 'desc'
  featured?: boolean
}): Promise<{
  list: AgentMarketInfo[]
  total: number
  page: number
  limit: number
  categories: AgentCategory[]
  tags: string[]
  featured: AgentMarketInfo[]
}> => {
  return request.get('/agent/market', { params })
}

/**
 * 获取智能体详情
 */
export const getAgentDetail = (agentId: string): Promise<AgentMarketInfo> => {
  return request.get(`/agent/market/${agentId}`)
}

/**
 * 搜索智能体
 */
export const searchAgents = (params: {
  query: string
  category?: string
  tags?: string[]
  priceType?: 'free' | 'paid' | 'all'
  page?: number
  limit?: number
}): Promise<{
  list: AgentMarketInfo[]
  total: number
  page: number
  limit: number
  suggestions: string[]
  relatedTags: string[]
}> => {
  return request.get('/agent/search', { params })
}

/**
 * 获取智能体分类
 */
export const getAgentCategories = (): Promise<AgentCategory[]> => {
  return request.get('/agent/categories')
}

/**
 * 获取热门标签
 */
export const getPopularTags = (params?: {
  category?: string
  limit?: number
}): Promise<Array<{
  tag: string
  count: number
  trending: boolean
}>> => {
  return request.get('/agent/tags/popular', { params })
}

/**
 * 获取推荐智能体
 */
export const getRecommendedAgents = (params?: {
  limit?: number
  category?: string
  excludeOwned?: boolean
}): Promise<AgentMarketInfo[]> => {
  return request.get('/agent/recommended', { params })
}

/**
 * 获取热门智能体
 */
export const getTrendingAgents = (params?: {
  period?: 'day' | 'week' | 'month'
  limit?: number
  category?: string
}): Promise<AgentMarketInfo[]> => {
  return request.get('/agent/trending', { params })
}

/**
 * 购买智能体
 */
export const purchaseAgent = (agentId: string, params?: {
  paymentMethod?: 'alipay' | 'wechat' | 'balance'
  couponCode?: string
}): Promise<{
  orderId: string
  paymentUrl?: string
  success: boolean
  message: string
}> => {
  return request.post(`/agent/${agentId}/purchase`, params)
}

/**
 * 试用智能体
 */
export const trialAgent = (agentId: string): Promise<{
  trialId: string
  expiresAt: string
  remainingUsage: number
  message: string
}> => {
  return request.post(`/agent/${agentId}/trial`)
}

/**
 * 收藏智能体
 */
export const favoriteAgent = (agentId: string): Promise<{ message: string }> => {
  return request.post(`/agent/${agentId}/favorite`)
}

/**
 * 取消收藏智能体
 */
export const unfavoriteAgent = (agentId: string): Promise<{ message: string }> => {
  return request.delete(`/agent/${agentId}/favorite`)
}

/**
 * 获取收藏的智能体
 */
export const getFavoriteAgents = (params: {
  page?: number
  limit?: number
  category?: string
}): Promise<{
  list: AgentMarketInfo[]
  total: number
  page: number
  limit: number
}> => {
  return request.get('/agent/favorites', { params })
}

/**
 * 获取已购买的智能体
 */
export const getPurchasedAgents = (params: {
  page?: number
  limit?: number
  category?: string
}): Promise<{
  list: AgentMarketInfo[]
  total: number
  page: number
  limit: number
}> => {
  return request.get('/agent/purchased', { params })
}

/**
 * 评价智能体
 */
export const rateAgent = (agentId: string, params: {
  rating: number
  comment?: string
  anonymous?: boolean
}): Promise<{ message: string }> => {
  return request.post(`/agent/${agentId}/rate`, params)
}

/**
 * 获取智能体评价
 */
export const getAgentReviews = (agentId: string, params: {
  page?: number
  limit?: number
  rating?: number
  sortBy?: 'created' | 'rating' | 'helpful'
  sortOrder?: 'asc' | 'desc'
}): Promise<{
  list: Array<{
    id: string
    userId: string
    username: string
    avatar: string
    rating: number
    comment: string
    helpful: number
    createdAt: string
    isHelpful?: boolean
  }>
  total: number
  page: number
  limit: number
  averageRating: number
  ratingDistribution: Record<string, number>
}> => {
  return request.get(`/agent/${agentId}/reviews`, { params })
}

/**
 * 点赞评价
 */
export const likeReview = (reviewId: string): Promise<{ message: string }> => {
  return request.post(`/agent/review/${reviewId}/like`)
}

/**
 * 举报智能体
 */
export const reportAgent = (agentId: string, params: {
  reason: 'inappropriate' | 'spam' | 'copyright' | 'misleading' | 'other'
  description: string
  evidence?: string[]
}): Promise<{ message: string }> => {
  return request.post(`/agent/${agentId}/report`, params)
}

/**
 * 分享智能体
 */
export const shareAgent = (agentId: string, params: {
  platform: 'wechat' | 'weibo' | 'qq' | 'link'
  message?: string
}): Promise<{
  shareUrl: string
  shareCode: string
  qrCode?: string
}> => {
  return request.post(`/agent/${agentId}/share`, params)
}

/**
 * 创建自定义智能体
 */
export const createCustomAgent = (params: {
  name: string
  description: string
  avatar?: string
  category: string
  tags: string[]
  capabilities: string[]
  model: string
  temperature: number
  maxTokens: number
  systemPrompt: string
  isPublic: boolean
  isPremium?: boolean
  price?: number
}): Promise<AIAgent> => {
  return request.post('/agent/create', params)
}

/**
 * 更新自定义智能体
 */
export const updateCustomAgent = (agentId: string, params: Partial<AIAgent>): Promise<AIAgent> => {
  return request.put(`/agent/${agentId}`, params)
}

/**
 * 删除自定义智能体
 */
export const deleteCustomAgent = (agentId: string): Promise<{ message: string }> => {
  return request.delete(`/agent/${agentId}`)
}

/**
 * 获取我创建的智能体
 */
export const getMyAgents = (params: {
  page?: number
  limit?: number
  status?: 'draft' | 'published' | 'rejected' | 'all'
}): Promise<{
  list: AIAgent[]
  total: number
  page: number
  limit: number
}> => {
  return request.get('/agent/my-agents', { params })
}

/**
 * 发布智能体到市场
 */
export const publishAgent = (agentId: string, params?: {
  isPremium?: boolean
  price?: number
  description?: string
}): Promise<{ message: string }> => {
  return request.post(`/agent/${agentId}/publish`, params)
}

/**
 * 下架智能体
 */
export const unpublishAgent = (agentId: string): Promise<{ message: string }> => {
  return request.post(`/agent/${agentId}/unpublish`)
}

/**
 * 获取智能体统计
 */
export const getAgentStats = (agentId?: string): Promise<{
  totalAgents: number
  totalUsers: number
  totalConversations: number
  totalRevenue?: number
  byCategory: Array<{
    category: string
    count: number
    users: number
  }>
  recentActivity: Array<{
    date: string
    newAgents: number
    activeUsers: number
    conversations: number
  }>
  topAgents?: Array<{
    id: string
    name: string
    users: number
    conversations: number
    rating: number
  }>
}> => {
  const url = agentId ? `/agent/${agentId}/stats` : '/agent/stats'
  return request.get(url)
}

// 默认导出
export default {
  getMarketAgents,
  getAgentDetail,
  searchAgents,
  getAgentCategories,
  getPopularTags,
  getRecommendedAgents,
  getTrendingAgents,
  purchaseAgent,
  trialAgent,
  favoriteAgent,
  unfavoriteAgent,
  getFavoriteAgents,
  getPurchasedAgents,
  rateAgent,
  getAgentReviews,
  likeReview,
  reportAgent,
  shareAgent,
  createCustomAgent,
  updateCustomAgent,
  deleteCustomAgent,
  getMyAgents,
  publishAgent,
  unpublishAgent,
  getAgentStats
}
