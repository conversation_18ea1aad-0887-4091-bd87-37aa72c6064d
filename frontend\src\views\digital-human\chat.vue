<template>
  <div class="chat-container">
    <!-- 数字人信息头部 -->
    <div class="chat-header">
      <div class="digital-human-info">
        <div class="avatar-container">
          <img :src="digitalHuman.avatar_url" :alt="digitalHuman.name" class="avatar" />
          <div class="status-indicator" :class="{ online: isOnline }"></div>
        </div>
        <div class="info">
          <h2 class="name">{{ digitalHuman.name }}</h2>
          <p class="description">{{ digitalHuman.description }}</p>
          <div class="status">
            <span class="status-text">{{ isOnline ? '在线' : '离线' }}</span>
            <span class="separator">•</span>
            <span class="type">{{ getTypeLabel(digitalHuman.type) }}</span>
          </div>
        </div>
      </div>
      <div class="actions">
        <el-button @click="showSettings = true" :icon="Setting" circle />
        <el-button @click="goBack" :icon="ArrowLeft" circle />
      </div>
    </div>

    <!-- 聊天区域 -->
    <div class="chat-content" ref="chatContentRef">
      <div class="messages-container">
        <div
          v-for="message in messages"
          :key="message.id"
          class="message-item"
          :class="{ 'user-message': message.sender === 'user', 'ai-message': message.sender === 'ai' }"
        >
          <div class="message-avatar">
            <img
              :src="message.sender === 'user' ? '/assets/images/user-avatar.png' : digitalHuman.avatar_url"
              :alt="message.sender === 'user' ? '用户' : digitalHuman.name"
            />
          </div>
          <div class="message-content">
            <div class="message-bubble">
              <p class="message-text">{{ message.content }}</p>
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
            </div>
          </div>
        </div>
        
        <!-- 加载中指示器 -->
        <div v-if="isLoading" class="message-item ai-message">
          <div class="message-avatar">
            <img :src="digitalHuman.avatar_url" :alt="digitalHuman.name" />
          </div>
          <div class="message-content">
            <div class="message-bubble loading">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input">
      <div class="input-container">
        <el-input
          v-model="inputMessage"
          placeholder="输入消息..."
          @keyup.enter="sendMessage"
          :disabled="isLoading"
          class="message-input"
        >
          <template #append>
            <el-button @click="sendMessage" :loading="isLoading" type="primary">
              发送
            </el-button>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 设置弹窗 -->
    <el-dialog v-model="showSettings" title="数字人设置" width="500px">
      <div class="settings-content">
        <el-form label-width="100px">
          <el-form-item label="语音速度">
            <el-slider v-model="voiceSettings.speed" :min="0.5" :max="2" :step="0.1" />
          </el-form-item>
          <el-form-item label="语音音调">
            <el-slider v-model="voiceSettings.pitch" :min="0.5" :max="2" :step="0.1" />
          </el-form-item>
          <el-form-item label="自动播放">
            <el-switch v-model="voiceSettings.autoPlay" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, nextTick, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Setting, ArrowLeft } from '@element-plus/icons-vue';

export default defineComponent({
  name: 'DigitalHumanChat',
  components: {
    Setting,
    ArrowLeft
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const chatContentRef = ref(null);
    
    // 数字人信息
    const digitalHuman = ref({
      id: null,
      name: '加载中...',
      description: '',
      avatar_url: '/assets/images/default-avatar.png',
      type: 'teacher'
    });
    
    // 聊天状态
    const isOnline = ref(true);
    const isLoading = ref(false);
    const showSettings = ref(false);
    const inputMessage = ref('');
    
    // 消息列表
    const messages = ref([]);
    
    // 语音设置
    const voiceSettings = reactive({
      speed: 1.0,
      pitch: 1.0,
      autoPlay: true
    });
    
    // 获取类型标签
    const getTypeLabel = (type) => {
      const typeMap = {
        teacher: '教师',
        assistant: '助手',
        customer_service: '客服',
        presenter: '主持人',
        custom: '自定义'
      };
      return typeMap[type] || '未知';
    };
    
    // 格式化时间
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    };
    
    // 滚动到底部
    const scrollToBottom = () => {
      nextTick(() => {
        if (chatContentRef.value) {
          chatContentRef.value.scrollTop = chatContentRef.value.scrollHeight;
        }
      });
    };
    
    // 加载数字人信息
    const loadDigitalHuman = async () => {
      try {
        const digitalHumanId = route.params.id;
        if (!digitalHumanId) {
          ElMessage.error('数字人ID不存在');
          router.push('/digital-human');
          return;
        }
        
        // 尝试从API获取数字人信息
        const response = await fetch(`/api/v1/digital-human/${digitalHumanId}`);
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            digitalHuman.value = result.data;
            
            // 添加欢迎消息
            if (digitalHuman.value.welcome_text) {
              messages.value.push({
                id: Date.now(),
                sender: 'ai',
                content: digitalHuman.value.welcome_text,
                timestamp: Date.now()
              });
            }
          }
        } else {
          throw new Error('获取数字人信息失败');
        }
      } catch (error) {
        console.error('加载数字人信息失败:', error);
        ElMessage.error('加载数字人信息失败');
        
        // 使用默认信息
        digitalHuman.value = {
          id: route.params.id,
          name: '数字人助手',
          description: '我是您的AI助手，很高兴为您服务！',
          avatar_url: '/assets/images/default-avatar.png',
          type: 'assistant',
          welcome_text: '您好！我是您的数字人助手，有什么可以帮助您的吗？'
        };
        
        // 添加欢迎消息
        messages.value.push({
          id: Date.now(),
          sender: 'ai',
          content: digitalHuman.value.welcome_text,
          timestamp: Date.now()
        });
      }
    };
    
    // 发送消息
    const sendMessage = async () => {
      if (!inputMessage.value.trim() || isLoading.value) return;
      
      const userMessage = {
        id: Date.now(),
        sender: 'user',
        content: inputMessage.value.trim(),
        timestamp: Date.now()
      };
      
      messages.value.push(userMessage);
      const messageContent = inputMessage.value.trim();
      inputMessage.value = '';
      
      scrollToBottom();
      isLoading.value = true;
      
      try {
        // 调用聊天API
        const response = await fetch(`/api/v1/digital-human/${digitalHuman.value.id}/chat`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            message: messageContent,
            voice_settings: voiceSettings
          })
        });
        
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            const aiMessage = {
              id: Date.now() + 1,
              sender: 'ai',
              content: result.data.response,
              timestamp: Date.now(),
              audio_url: result.data.audio_url
            };
            
            messages.value.push(aiMessage);
            
            // 自动播放语音
            if (voiceSettings.autoPlay && aiMessage.audio_url) {
              playAudio(aiMessage.audio_url);
            }
          } else {
            throw new Error(result.message || '聊天失败');
          }
        } else {
          throw new Error('网络请求失败');
        }
      } catch (error) {
        console.error('发送消息失败:', error);
        
        // 模拟回复
        const aiMessage = {
          id: Date.now() + 1,
          sender: 'ai',
          content: '抱歉，我现在无法正常回复。请稍后再试。',
          timestamp: Date.now()
        };
        
        messages.value.push(aiMessage);
        ElMessage.error('发送消息失败，请稍后重试');
      } finally {
        isLoading.value = false;
        scrollToBottom();
      }
    };
    
    // 播放音频
    const playAudio = (audioUrl) => {
      try {
        const audio = new Audio(audioUrl);
        audio.play().catch(error => {
          console.warn('音频播放失败:', error);
        });
      } catch (error) {
        console.warn('音频播放失败:', error);
      }
    };
    
    // 保存设置
    const saveSettings = () => {
      localStorage.setItem('voiceSettings', JSON.stringify(voiceSettings));
      showSettings.value = false;
      ElMessage.success('设置已保存');
    };
    
    // 返回
    const goBack = () => {
      router.push('/digital-human');
    };
    
    // 组件挂载
    onMounted(() => {
      loadDigitalHuman();
      
      // 加载语音设置
      const savedSettings = localStorage.getItem('voiceSettings');
      if (savedSettings) {
        Object.assign(voiceSettings, JSON.parse(savedSettings));
      }
    });
    
    return {
      digitalHuman,
      isOnline,
      isLoading,
      showSettings,
      inputMessage,
      messages,
      voiceSettings,
      chatContentRef,
      getTypeLabel,
      formatTime,
      sendMessage,
      saveSettings,
      goBack,
      Setting,
      ArrowLeft
    };
  }
});
</script>

<style lang="scss" scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f7fa;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  .digital-human-info {
    display: flex;
    align-items: center;

    .avatar-container {
      position: relative;
      margin-right: 16px;

      .avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #e4e7ed;
      }

      .status-indicator {
        position: absolute;
        bottom: 2px;
        right: 2px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #f56c6c;
        border: 2px solid white;

        &.online {
          background: #67c23a;
        }
      }
    }

    .info {
      .name {
        margin: 0 0 4px 0;
        font-size: 20px;
        font-weight: 600;
        color: #303133;
      }

      .description {
        margin: 0 0 8px 0;
        font-size: 14px;
        color: #606266;
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .status {
        font-size: 12px;
        color: #909399;

        .separator {
          margin: 0 8px;
        }
      }
    }
  }

  .actions {
    display: flex;
    gap: 8px;
  }
}

.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;

  .messages-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .message-item {
    display: flex;
    margin-bottom: 20px;

    &.user-message {
      flex-direction: row-reverse;

      .message-content {
        align-items: flex-end;

        .message-bubble {
          background: #409eff;
          color: white;

          .message-time {
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }
    }

    &.ai-message {
      .message-bubble {
        background: white;
        color: #303133;
        border: 1px solid #e4e7ed;

        &.loading {
          padding: 16px 20px;
        }
      }
    }

    .message-avatar {
      flex-shrink: 0;
      margin: 0 12px;

      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
      }
    }

    .message-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      max-width: 70%;

      .message-bubble {
        padding: 12px 16px;
        border-radius: 18px;
        position: relative;

        .message-text {
          margin: 0;
          line-height: 1.5;
          word-wrap: break-word;
        }

        .message-time {
          font-size: 11px;
          color: #909399;
          margin-top: 4px;
          display: block;
        }
      }
    }
  }
}

.chat-input {
  padding: 20px 24px;
  background: white;
  border-top: 1px solid #e4e7ed;

  .input-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .message-input {
    :deep(.el-input__wrapper) {
      border-radius: 24px;
      padding: 8px 16px;
    }

    :deep(.el-input-group__append) {
      border-radius: 0 24px 24px 0;
      border-left: none;

      .el-button {
        border-radius: 0 20px 20px 0;
      }
    }
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;

  span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #c0c4cc;
    animation: typing 1.4s infinite ease-in-out;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.settings-content {
  padding: 20px 0;
}

// 响应式设计
@media (max-width: 768px) {
  .chat-header {
    padding: 16px;

    .digital-human-info {
      .avatar-container .avatar {
        width: 50px;
        height: 50px;
      }

      .info {
        .name {
          font-size: 18px;
        }

        .description {
          max-width: 200px;
        }
      }
    }
  }

  .chat-content {
    padding: 16px;

    .message-item .message-content {
      max-width: 85%;
    }
  }

  .chat-input {
    padding: 16px;
  }
}
</style>
