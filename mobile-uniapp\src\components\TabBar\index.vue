<template>
  <view class="custom-tabbar">
    <view 
      class="tab-item"
      v-for="(item, index) in tabList"
      :key="index"
      :class="{ active: currentTab === index }"
      @click="switchTab(index)"
    >
      <view class="tab-icon">
        <view class="icon-wrapper" :class="{ active: currentTab === index }">
          <text class="iconfont" :class="currentTab === index ? item.selectedIcon : item.icon"></text>
          <view class="icon-badge" v-if="item.badge">{{ item.badge }}</view>
        </view>
      </view>
      <text class="tab-text" :class="{ active: currentTab === index }">{{ item.text }}</text>
      <view class="tab-indicator" v-if="currentTab === index"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface TabItem {
  text: string
  icon: string
  selectedIcon: string
  pagePath: string
  badge?: string | number
}

const currentTab = ref<number>(0)

const tabList: TabItem[] = [
  {
    text: '首页',
    icon: 'icon-home',
    selectedIcon: 'icon-home-fill',
    pagePath: '/pages/index/index'
  },
  {
    text: '翻译',
    icon: 'icon-translate',
    selectedIcon: 'icon-translate-fill',
    pagePath: '/pages/translation/text/index'
  },
  {
    text: 'AI助手',
    icon: 'icon-robot',
    selectedIcon: 'icon-robot-fill',
    pagePath: '/pages/digital-human/chat/index',
    badge: 'New'
  },
  {
    text: '智能体',
    icon: 'icon-apps',
    selectedIcon: 'icon-apps-fill',
    pagePath: '/pages/ai-agents/market/index'
  },
  {
    text: '我的',
    icon: 'icon-user',
    selectedIcon: 'icon-user-fill',
    pagePath: '/pages/user/profile/index'
  }
]

onMounted(() => {
  // 获取当前页面路径，设置对应的tab
  const pages = getCurrentPages()
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1]
    const currentRoute = '/' + currentPage.route
    const tabIndex = tabList.findIndex(item => item.pagePath === currentRoute)
    if (tabIndex !== -1) {
      currentTab.value = tabIndex
    }
  }
})

const switchTab = (index: number) => {
  if (currentTab.value === index) return
  
  currentTab.value = index
  const targetPage = tabList[index].pagePath
  
  uni.switchTab({
    url: targetPage,
    fail: (err) => {
      console.error('Tab switch failed:', err)
      // 如果switchTab失败，尝试使用navigateTo
      uni.reLaunch({
        url: targetPage
      })
    }
  })
}
</script>

<style lang="scss" scoped>
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 1) 100%);
  backdrop-filter: blur(20rpx);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  padding: 0 20rpx 20rpx;
  z-index: 1000;
  
  // 安全区域适配
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  
  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 8rpx 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    &:active {
      transform: scale(0.95);
    }
    
    .tab-icon {
      position: relative;
      margin-bottom: 4rpx;
      
      .icon-wrapper {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 24rpx;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        
        &.active {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          transform: translateY(-2rpx);
          box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
        }
        
        .iconfont {
          font-size: 24rpx;
          color: #8E8E93;
          transition: all 0.3s ease;
        }
        
        &.active .iconfont {
          color: #FFFFFF;
          font-size: 26rpx;
        }
        
        .icon-badge {
          position: absolute;
          top: -6rpx;
          right: -8rpx;
          background: linear-gradient(45deg, #FF6B6B, #FF8E53);
          color: white;
          font-size: 18rpx;
          padding: 2rpx 6rpx;
          border-radius: 10rpx;
          min-width: 20rpx;
          height: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);
          animation: pulse 2s infinite;
        }
      }
    }
    
    .tab-text {
      font-size: 20rpx;
      color: #8E8E93;
      font-weight: 500;
      transition: all 0.3s ease;
      
      &.active {
        color: #667eea;
        font-weight: 600;
        transform: scale(1.05);
      }
    }
    
    .tab-indicator {
      position: absolute;
      bottom: -2rpx;
      width: 20rpx;
      height: 4rpx;
      background: linear-gradient(90deg, #667eea, #764ba2);
      border-radius: 2rpx;
      animation: slideIn 0.3s ease;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 20rpx;
    opacity: 1;
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .custom-tabbar {
    background: linear-gradient(180deg, rgba(28, 28, 30, 0.95) 0%, rgba(28, 28, 30, 1) 100%);
    border-top-color: rgba(255, 255, 255, 0.1);
    
    .tab-item {
      .tab-icon .icon-wrapper {
        .iconfont {
          color: #8E8E93;
        }
        
        &.active .iconfont {
          color: #FFFFFF;
        }
      }
      
      .tab-text {
        color: #8E8E93;
        
        &.active {
          color: #667eea;
        }
      }
    }
  }
}
</style>
