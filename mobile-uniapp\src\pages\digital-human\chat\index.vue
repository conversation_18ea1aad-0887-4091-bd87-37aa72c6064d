<template>
  <view class="chat-page">
    <!-- 顶部数字人信息 -->
    <view class="chat-header" v-if="currentDigitalHuman">
      <view class="digital-human-info">
        <image 
          class="avatar" 
          :src="currentDigitalHuman.avatar || '/static/images/default-avatar.png'"
          mode="aspectFill"
        ></image>
        <view class="info-content">
          <text class="name">{{ currentDigitalHuman.name }}</text>
          <text class="status">在线</text>
        </view>
      </view>
      
      <view class="header-actions">
        <u-icon 
          name="more-dot-fill" 
          size="20" 
          color="#6B7280"
          @click="showActionSheet = true"
        ></u-icon>
      </view>
    </view>

    <!-- 消息列表 -->
    <scroll-view 
      class="message-list"
      scroll-y="true"
      :scroll-top="scrollTop"
      :scroll-with-animation="true"
      @scrolltoupper="loadMoreMessages"
    >
      <!-- 加载更多提示 -->
      <view class="load-more" v-if="hasMoreMessages">
        <u-loading-icon v-if="isLoadingMore"></u-loading-icon>
        <text class="load-text">{{ isLoadingMore ? '加载中...' : '上拉加载更多' }}</text>
      </view>

      <!-- 消息项 -->
      <view 
        class="message-item"
        v-for="(message, index) in currentSessionMessages"
        :key="message.id"
        :class="{ 'user-message': message.role === 'user', 'assistant-message': message.role === 'assistant' }"
      >
        <!-- 用户消息 -->
        <view class="message-content" v-if="message.role === 'user'">
          <view class="message-bubble user-bubble">
            <text class="message-text" v-if="message.message_type === 'text'">{{ message.content }}</text>
            
            <!-- 语音消息 -->
            <view class="audio-message" v-if="message.message_type === 'audio'">
              <u-icon name="mic-fill" size="16" color="#ffffff"></u-icon>
              <text class="audio-duration">{{ formatDuration(message.duration) }}</text>
              <u-icon 
                :name="playingAudio === message.id ? 'pause' : 'play'" 
                size="16" 
                color="#ffffff"
                @click="toggleAudioPlay(message)"
              ></u-icon>
            </view>
          </view>
          <text class="message-time">{{ formatTime(message.timestamp) }}</text>
        </view>

        <!-- 数字人消息 -->
        <view class="message-content" v-else>
          <image 
            class="message-avatar" 
            :src="currentDigitalHuman?.avatar || '/static/images/default-avatar.png'"
            mode="aspectFill"
          ></image>
          
          <view class="message-bubble assistant-bubble">
            <text class="message-text" v-if="message.message_type === 'text'">{{ message.content }}</text>
            
            <!-- 语音回复 -->
            <view class="audio-message" v-if="message.message_type === 'audio'">
              <u-icon 
                :name="playingAudio === message.id ? 'pause-circle-fill' : 'play-circle-fill'" 
                size="32" 
                color="#2563EB"
                @click="toggleAudioPlay(message)"
              ></u-icon>
              <text class="audio-duration">{{ formatDuration(message.duration) }}</text>
            </view>
            
            <!-- 视频回复 -->
            <view class="video-message" v-if="message.message_type === 'video'">
              <video 
                class="message-video"
                :src="message.video_url"
                :poster="currentDigitalHuman?.avatar"
                controls
                @play="onVideoPlay"
                @pause="onVideoPause"
              ></video>
            </view>
            
            <!-- 消息操作 -->
            <view class="message-actions">
              <u-icon 
                name="volume-up" 
                size="14" 
                color="#6B7280"
                @click="playTextAudio(message.content)"
                v-if="message.message_type === 'text'"
              ></u-icon>
              <u-icon 
                name="video" 
                size="14" 
                color="#6B7280"
                margin-left="8"
                @click="generateVideoReply(message.id)"
                v-if="message.message_type === 'text'"
              ></u-icon>
            </view>
          </view>
          
          <text class="message-time">{{ formatTime(message.timestamp) }}</text>
        </view>
      </view>

      <!-- 正在生成视频提示 -->
      <view class="generating-video" v-if="isGeneratingVideo">
        <view class="generating-content">
          <u-loading-icon mode="flower"></u-loading-icon>
          <text class="generating-text">正在生成数字人视频回复...</text>
        </view>
      </view>

      <!-- 正在输入提示 -->
      <view class="typing-indicator" v-if="isSending">
        <image 
          class="typing-avatar" 
          :src="currentDigitalHuman?.avatar || '/static/images/default-avatar.png'"
          mode="aspectFill"
        ></image>
        <view class="typing-bubble">
          <view class="typing-dots">
            <view class="dot"></view>
            <view class="dot"></view>
            <view class="dot"></view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 输入区域 -->
    <view class="input-area">
      <view class="input-container">
        <u-icon 
          name="plus" 
          size="24" 
          color="#6B7280"
          @click="showInputOptions = true"
        ></u-icon>
        
        <input 
          class="text-input"
          v-model="inputText"
          placeholder="输入消息..."
          :disabled="isSending"
          @confirm="sendMessage"
          confirm-type="send"
        />
        
        <view class="input-actions">
          <u-icon 
            v-if="!inputText.trim()"
            name="mic" 
            size="24" 
            color="#2563EB"
            @click="startVoiceInput"
          ></u-icon>
          
          <u-button 
            v-else
            type="primary" 
            size="small"
            :loading="isSending"
            :disabled="!inputText.trim()"
            @click="sendMessage"
          >
            发送
          </u-button>
        </view>
      </view>
    </view>

    <!-- 数字人选择弹窗 -->
    <u-modal 
      v-model="showDigitalHumanPicker" 
      title="选择数字人"
      :show-cancel-button="false"
      confirm-text="关闭"
      @confirm="showDigitalHumanPicker = false"
    >
      <view class="digital-human-list">
        <view 
          class="digital-human-item"
          v-for="human in digitalHumans"
          :key="human.id"
          :class="{ active: currentDigitalHuman?.id === human.id }"
          @click="selectDigitalHuman(human)"
        >
          <image class="item-avatar" :src="human.avatar" mode="aspectFill"></image>
          <view class="item-info">
            <text class="item-name">{{ human.name }}</text>
            <text class="item-desc">{{ human.description }}</text>
          </view>
          <u-icon 
            v-if="currentDigitalHuman?.id === human.id"
            name="checkmark" 
            size="16" 
            color="#2563EB"
          ></u-icon>
        </view>
      </view>
    </u-modal>

    <!-- 输入选项弹窗 -->
    <u-action-sheet 
      v-model="showInputOptions"
      :actions="inputOptionActions"
      @click="handleInputOption"
    ></u-action-sheet>

    <!-- 更多操作弹窗 -->
    <u-action-sheet 
      v-model="showActionSheet"
      :actions="actionSheetActions"
      @click="handleActionSheet"
    ></u-action-sheet>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useDigitalHumanStore } from '@/store/modules/digitalHuman'
import { type DigitalHuman, type ChatMessage } from '@/api/digital-human'
import dayjs from 'dayjs'

// Store
const digitalHumanStore = useDigitalHumanStore()

// 响应式数据
const inputText = ref<string>('')
const scrollTop = ref<number>(0)
const playingAudio = ref<string>('')
const showDigitalHumanPicker = ref<boolean>(false)
const showInputOptions = ref<boolean>(false)
const showActionSheet = ref<boolean>(false)
const hasMoreMessages = ref<boolean>(true)
const isLoadingMore = ref<boolean>(false)

// 计算属性
const {
  digitalHumans,
  currentDigitalHuman,
  currentSession,
  currentSessionMessages,
  isSending,
  isGeneratingVideo
} = digitalHumanStore

// 输入选项
const inputOptionActions = ref([
  { name: '拍照', value: 'camera' },
  { name: '从相册选择', value: 'album' },
  { name: '语音输入', value: 'voice' }
])

// 更多操作选项
const actionSheetActions = ref([
  { name: '切换数字人', value: 'switch' },
  { name: '新建对话', value: 'new' },
  { name: '对话历史', value: 'history' },
  { name: '清空消息', value: 'clear' },
  { name: '语音设置', value: 'voice_settings' }
])

// 页面加载
onMounted(async () => {
  // 加载数字人列表
  await digitalHumanStore.loadDigitalHumans()

  // 如果没有选择数字人，显示选择弹窗
  if (!currentDigitalHuman.value && digitalHumans.value.length > 0) {
    showDigitalHumanPicker.value = true
  } else if (currentDigitalHuman.value) {
    // 加载对话会话
    await digitalHumanStore.loadChatSessions()

    // 如果没有当前会话，创建新会话
    if (!currentSession.value) {
      await digitalHumanStore.createNewSession()
    } else {
      // 加载消息
      await digitalHumanStore.loadMessages()
    }
  }

  // 滚动到底部
  scrollToBottom()
})

// 监听消息变化，自动滚动到底部
watch(
  () => currentSessionMessages.value.length,
  () => {
    nextTick(() => {
      scrollToBottom()
    })
  }
)

// 选择数字人
const selectDigitalHuman = async (human: DigitalHuman) => {
  await digitalHumanStore.selectDigitalHuman(human)
  showDigitalHumanPicker.value = false

  // 创建新会话
  await digitalHumanStore.createNewSession()

  // 滚动到底部
  scrollToBottom()
}

// 发送消息
const sendMessage = async () => {
  if (!inputText.value.trim() || isSending.value) return

  const message = inputText.value.trim()
  inputText.value = ''

  try {
    await digitalHumanStore.sendTextMessage(message)

    // 滚动到底部
    scrollToBottom()
  } catch (error: any) {
    console.error('发送消息失败:', error)
    uni.showToast({
      title: error.message || '发送失败',
      icon: 'error'
    })

    // 恢复输入内容
    inputText.value = message
  }
}

// 开始语音输入
const startVoiceInput = () => {
  // #ifdef APP-PLUS
  plus.speech.startRecognize({
    engine: 'iFly',
    lang: 'zh-cn',
    onstart: () => {
      uni.showToast({
        title: '开始录音...',
        icon: 'none'
      })
    },
    onend: (result: string) => {
      if (result) {
        inputText.value = result
      }
    },
    onerror: (error: any) => {
      console.error('语音识别失败:', error)
      uni.showToast({
        title: '语音识别失败',
        icon: 'error'
      })
    }
  })
  // #endif

  // #ifdef H5 || MP
  uni.showToast({
    title: '当前平台暂不支持语音输入',
    icon: 'none'
  })
  // #endif
}

// 播放/暂停音频
const toggleAudioPlay = (message: ChatMessage) => {
  if (!message.audio_url) return

  if (playingAudio.value === message.id) {
    // 暂停播放
    uni.stopBackgroundAudio()
    playingAudio.value = ''
  } else {
    // 开始播放
    uni.playBackgroundAudio({
      dataUrl: message.audio_url,
      title: '数字人语音',
      success: () => {
        playingAudio.value = message.id
      },
      fail: (error) => {
        console.error('播放音频失败:', error)
        uni.showToast({
          title: '播放失败',
          icon: 'error'
        })
      }
    })
  }
}

// 播放文本语音
const playTextAudio = async (text: string) => {
  try {
    // 这里可以调用TTS接口生成语音
    uni.showToast({
      title: '正在生成语音...',
      icon: 'loading'
    })

    // 模拟语音生成
    setTimeout(() => {
      uni.showToast({
        title: '语音播放功能开发中',
        icon: 'none'
      })
    }, 1000)
  } catch (error) {
    console.error('播放文本语音失败:', error)
    uni.showToast({
      title: '播放失败',
      icon: 'error'
    })
  }
}

// 生成视频回复
const generateVideoReply = async (messageId: string) => {
  try {
    uni.showLoading({
      title: '生成视频中...'
    })

    const videoUrl = await digitalHumanStore.generateVideoReply(messageId)

    uni.hideLoading()

    if (videoUrl) {
      uni.showToast({
        title: '视频生成成功',
        icon: 'success'
      })

      // 滚动到底部显示新的视频消息
      scrollToBottom()
    }
  } catch (error: any) {
    uni.hideLoading()
    console.error('生成视频失败:', error)
    uni.showToast({
      title: error.message || '生成视频失败',
      icon: 'error'
    })
  }
}

// 视频播放事件
const onVideoPlay = () => {
  console.log('视频开始播放')
}

const onVideoPause = () => {
  console.log('视频暂停播放')
}

// 加载更多消息
const loadMoreMessages = async () => {
  if (isLoadingMore.value || !hasMoreMessages.value) return

  try {
    isLoadingMore.value = true

    // 这里应该加载更早的消息
    // 暂时模拟没有更多消息
    setTimeout(() => {
      hasMoreMessages.value = false
      isLoadingMore.value = false
    }, 1000)
  } catch (error) {
    console.error('加载更多消息失败:', error)
    isLoadingMore.value = false
  }
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    scrollTop.value = 999999
  })
}

// 处理输入选项
const handleInputOption = (action: any) => {
  showInputOptions.value = false

  switch (action.value) {
    case 'camera':
      chooseImage('camera')
      break
    case 'album':
      chooseImage('album')
      break
    case 'voice':
      startVoiceInput()
      break
  }
}

// 选择图片
const chooseImage = (sourceType: 'camera' | 'album') => {
  uni.chooseImage({
    count: 1,
    sourceType: [sourceType],
    success: (res) => {
      const filePath = res.tempFilePaths[0]
      // 这里可以发送图片消息
      uni.showToast({
        title: '图片消息功能开发中',
        icon: 'none'
      })
    },
    fail: (error) => {
      console.error('选择图片失败:', error)
    }
  })
}

// 处理更多操作
const handleActionSheet = async (action: any) => {
  showActionSheet.value = false

  switch (action.value) {
    case 'switch':
      showDigitalHumanPicker.value = true
      break
    case 'new':
      await createNewSession()
      break
    case 'history':
      navigateToHistory()
      break
    case 'clear':
      await clearMessages()
      break
    case 'voice_settings':
      navigateToVoiceSettings()
      break
  }
}

// 创建新会话
const createNewSession = async () => {
  try {
    await digitalHumanStore.createNewSession()
    uni.showToast({
      title: '新对话已创建',
      icon: 'success'
    })
    scrollToBottom()
  } catch (error: any) {
    uni.showToast({
      title: error.message || '创建失败',
      icon: 'error'
    })
  }
}

// 跳转到历史记录
const navigateToHistory = () => {
  uni.navigateTo({
    url: '/pages/digital-human/history/index'
  })
}

// 清空消息
const clearMessages = async () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空当前对话的所有消息吗？',
    success: async (res) => {
      if (res.confirm) {
        await digitalHumanStore.clearMessages()
      }
    }
  })
}

// 跳转到语音设置
const navigateToVoiceSettings = () => {
  uni.navigateTo({
    url: '/pages/digital-human/voice-settings/index'
  })
}

// 格式化时间
const formatTime = (timestamp: string) => {
  const now = dayjs()
  const time = dayjs(timestamp)

  if (now.diff(time, 'day') === 0) {
    return time.format('HH:mm')
  } else if (now.diff(time, 'day') === 1) {
    return '昨天 ' + time.format('HH:mm')
  } else {
    return time.format('MM-DD HH:mm')
  }
}

// 格式化音频时长
const formatDuration = (duration?: number) => {
  if (!duration) return '0:00'

  const minutes = Math.floor(duration / 60)
  const seconds = Math.floor(duration % 60)
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

// 页面下拉刷新
const onPullDownRefresh = async () => {
  try {
    if (currentSession.value) {
      await digitalHumanStore.loadMessages()
    }
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '刷新失败',
      icon: 'error'
    })
  } finally {
    uni.stopPullDownRefresh()
  }
}

// 导出方法供页面使用
defineExpose({
  onPullDownRefresh
})
</script>

<style lang="scss" scoped>
.chat-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $bg-secondary;
}

// 聊天头部
.chat-header {
  background-color: $bg-primary;
  padding: $spacing-4;
  border-bottom: 1px solid $border-primary;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .digital-human-info {
    display: flex;
    align-items: center;

    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: $radius-full;
      margin-right: $spacing-3;
    }

    .info-content {
      .name {
        display: block;
        font-size: $font-size-md;
        font-weight: $font-weight-semibold;
        color: $text-primary;
        margin-bottom: $spacing-1;
      }

      .status {
        font-size: $font-size-xs;
        color: $success;
      }
    }
  }

  .header-actions {
    padding: $spacing-2;
  }
}

// 消息列表
.message-list {
  flex: 1;
  padding: $spacing-4;

  .load-more {
    text-align: center;
    padding: $spacing-4;

    .load-text {
      font-size: $font-size-sm;
      color: $text-tertiary;
      margin-left: $spacing-2;
    }
  }
}

// 消息项
.message-item {
  margin-bottom: $spacing-6;

  &.user-message {
    .message-content {
      flex-direction: row-reverse;

      .message-bubble {
        margin-left: $spacing-12;
        margin-right: 0;
      }

      .message-time {
        text-align: right;
        margin-right: $spacing-2;
      }
    }
  }

  &.assistant-message {
    .message-content {
      flex-direction: row;

      .message-bubble {
        margin-right: $spacing-12;
        margin-left: $spacing-2;
      }

      .message-time {
        text-align: left;
        margin-left: 88rpx;
      }
    }
  }

  .message-content {
    display: flex;
    align-items: flex-start;

    .message-avatar {
      width: 64rpx;
      height: 64rpx;
      border-radius: $radius-full;
      flex-shrink: 0;
    }

    .message-bubble {
      max-width: 70%;
      padding: $spacing-3 $spacing-4;
      border-radius: $radius-lg;
      position: relative;

      &.user-bubble {
        background-color: $primary;
        color: white;

        &::after {
          content: '';
          position: absolute;
          right: -12rpx;
          top: 20rpx;
          width: 0;
          height: 0;
          border: 12rpx solid transparent;
          border-left-color: $primary;
        }
      }

      &.assistant-bubble {
        background-color: $bg-primary;
        border: 1px solid $border-primary;
        color: $text-primary;

        &::after {
          content: '';
          position: absolute;
          left: -12rpx;
          top: 20rpx;
          width: 0;
          height: 0;
          border: 12rpx solid transparent;
          border-right-color: $bg-primary;
        }
      }

      .message-text {
        font-size: $font-size-base;
        line-height: $line-height-relaxed;
        word-break: break-word;
      }

      .audio-message {
        display: flex;
        align-items: center;
        gap: $spacing-2;

        .audio-duration {
          font-size: $font-size-sm;
          min-width: 60rpx;
        }
      }

      .video-message {
        .message-video {
          width: 100%;
          max-width: 400rpx;
          height: 300rpx;
          border-radius: $radius-md;
        }
      }

      .message-actions {
        display: flex;
        align-items: center;
        gap: $spacing-2;
        margin-top: $spacing-2;
        padding-top: $spacing-2;
        border-top: 1px solid $border-primary;
      }
    }

    .message-time {
      font-size: $font-size-xs;
      color: $text-quaternary;
      margin-top: $spacing-1;
    }
  }
}

// 正在生成视频提示
.generating-video {
  text-align: center;
  padding: $spacing-6;

  .generating-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacing-3;

    .generating-text {
      font-size: $font-size-sm;
      color: $text-secondary;
    }
  }
}

// 正在输入提示
.typing-indicator {
  display: flex;
  align-items: flex-start;
  margin-bottom: $spacing-4;

  .typing-avatar {
    width: 64rpx;
    height: 64rpx;
    border-radius: $radius-full;
    margin-right: $spacing-2;
  }

  .typing-bubble {
    background-color: $bg-primary;
    border: 1px solid $border-primary;
    border-radius: $radius-lg;
    padding: $spacing-3 $spacing-4;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      left: -12rpx;
      top: 20rpx;
      width: 0;
      height: 0;
      border: 12rpx solid transparent;
      border-right-color: $bg-primary;
    }

    .typing-dots {
      display: flex;
      gap: 8rpx;

      .dot {
        width: 12rpx;
        height: 12rpx;
        background-color: $text-tertiary;
        border-radius: $radius-full;
        animation: typing 1.4s infinite ease-in-out;

        &:nth-child(1) { animation-delay: -0.32s; }
        &:nth-child(2) { animation-delay: -0.16s; }
        &:nth-child(3) { animation-delay: 0s; }
      }
    }
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 输入区域
.input-area {
  background-color: $bg-primary;
  border-top: 1px solid $border-primary;
  padding: $spacing-4;
  padding-bottom: calc($spacing-4 + env(safe-area-inset-bottom));

  .input-container {
    display: flex;
    align-items: center;
    gap: $spacing-3;

    .text-input {
      flex: 1;
      background-color: $bg-tertiary;
      border: 1px solid $border-primary;
      border-radius: $radius-full;
      padding: $spacing-3 $spacing-4;
      font-size: $font-size-base;
      color: $text-primary;

      &::placeholder {
        color: $text-quaternary;
      }

      &:focus {
        border-color: $primary;
      }
    }

    .input-actions {
      display: flex;
      align-items: center;
    }
  }
}

// 数字人列表弹窗
.digital-human-list {
  max-height: 600rpx;

  .digital-human-item {
    display: flex;
    align-items: center;
    padding: $spacing-4;
    border-bottom: 1px solid $border-primary;

    &:last-child {
      border-bottom: none;
    }

    &.active {
      background-color: $primary-50;
    }

    &:active {
      background-color: $bg-tertiary;
    }

    .item-avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: $radius-full;
      margin-right: $spacing-3;
    }

    .item-info {
      flex: 1;

      .item-name {
        display: block;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-1;
      }

      .item-desc {
        font-size: $font-size-sm;
        color: $text-secondary;
        line-height: $line-height-snug;
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 480px) {
  .message-item {
    .message-bubble {
      max-width: 80%;
    }
  }

  .input-area {
    padding: $spacing-3;
    padding-bottom: calc($spacing-3 + env(safe-area-inset-bottom));
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .chat-page {
    background-color: #1a1a1a;
  }

  .chat-header {
    background-color: #2d2d2d;
    border-color: #404040;
  }

  .message-bubble.assistant-bubble {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #ffffff;

    &::after {
      border-right-color: #2d2d2d;
    }
  }

  .typing-bubble {
    background-color: #2d2d2d;
    border-color: #404040;

    &::after {
      border-right-color: #2d2d2d;
    }
  }

  .input-area {
    background-color: #2d2d2d;
    border-color: #404040;
  }

  .text-input {
    background-color: #404040;
    border-color: #555555;
    color: #ffffff;

    &::placeholder {
      color: #888888;
    }
  }
}
</style>
