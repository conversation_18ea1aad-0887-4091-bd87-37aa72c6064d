"use strict";
const common_vendor = require("../common/vendor.js");
const store_modules_user = require("../store/modules/user.js");
const getBaseURL = () => {
  return "https://your-api-domain.com/api";
};
const BASE_URL = getBaseURL();
const TIMEOUT = 1e4;
const requestInterceptor = (config) => {
  if (!config.url.startsWith("http")) {
    config.url = BASE_URL + config.url;
  }
  config.header = {
    "Content-Type": "application/json",
    ...config.header
  };
  const userStore = store_modules_user.useUserStore();
  if (userStore.token) {
    config.header.Authorization = `Bearer ${userStore.token}`;
  }
  config.timeout = config.timeout || TIMEOUT;
  common_vendor.index.__f__("log", "at utils/request.ts:76", `[REQUEST] ${config.method || "GET"} ${config.url}`, config.data);
  return config;
};
const responseInterceptor = (response, config) => {
  common_vendor.index.__f__("log", "at utils/request.ts:83", `[RESPONSE] ${config.url}`, response);
  const { statusCode, data } = response;
  if (statusCode >= 200 && statusCode < 300) {
    if (data.code === 0 || data.success === true) {
      return Promise.resolve(data);
    } else {
      handleBusinessError(data);
      return Promise.reject(data);
    }
  } else {
    handleHttpError(statusCode);
    return Promise.reject(response);
  }
};
const errorInterceptor = (error, config) => {
  common_vendor.index.__f__("error", "at utils/request.ts:106", `[REQUEST ERROR] ${config.url}`, error);
  if (error.errMsg) {
    if (error.errMsg.includes("timeout")) {
      showError("请求超时，请检查网络连接");
    } else if (error.errMsg.includes("fail")) {
      showError("网络连接失败，请检查网络设置");
    } else {
      showError("网络请求失败");
    }
  }
  return Promise.reject(error);
};
const handleBusinessError = (data, config) => {
  const { code, message } = data;
  switch (code) {
    case 401:
      handleUnauthorized();
      break;
    case 403:
      showError("没有权限访问");
      break;
    case 404:
      showError("请求的资源不存在");
      break;
    case 500:
      showError("服务器内部错误");
      break;
    default:
      if (message) {
        showError(message);
      }
  }
};
const handleHttpError = (statusCode, config) => {
  switch (statusCode) {
    case 400:
      showError("请求参数错误");
      break;
    case 401:
      handleUnauthorized();
      break;
    case 403:
      showError("禁止访问");
      break;
    case 404:
      showError("请求地址不存在");
      break;
    case 500:
      showError("服务器内部错误");
      break;
    case 502:
      showError("网关错误");
      break;
    case 503:
      showError("服务不可用");
      break;
    case 504:
      showError("网关超时");
      break;
    default:
      showError(`请求失败 (${statusCode})`);
  }
};
const handleUnauthorized = () => {
  const userStore = store_modules_user.useUserStore();
  userStore.logout();
  showError("登录已过期，请重新登录");
  common_vendor.index.reLaunch({
    url: "/pages/user/login/index"
  });
};
const showError = (message) => {
  common_vendor.index.showToast({
    title: message,
    icon: "error",
    duration: 2e3
  });
};
const request = (config) => {
  return new Promise((resolve, reject) => {
    const finalConfig = requestInterceptor(config);
    common_vendor.index.request({
      ...finalConfig,
      success: (response) => {
        responseInterceptor(response, finalConfig).then(resolve).catch(reject);
      },
      fail: (error) => {
        errorInterceptor(error, finalConfig).then(resolve).catch(reject);
      }
    });
  });
};
const http = {
  get: (url, params, config) => {
    return request({
      url: params ? `${url}?${new URLSearchParams(params).toString()}` : url,
      method: "GET",
      ...config
    });
  },
  post: (url, data, config) => {
    return request({
      url,
      method: "POST",
      data,
      ...config
    });
  },
  put: (url, data, config) => {
    return request({
      url,
      method: "PUT",
      data,
      ...config
    });
  },
  delete: (url, config) => {
    return request({
      url,
      method: "DELETE",
      ...config
    });
  },
  patch: (url, data, config) => {
    return request({
      url,
      method: "PATCH",
      data,
      ...config
    });
  }
};
const uploadFile = (config) => {
  return new Promise((resolve, reject) => {
    const userStore = store_modules_user.useUserStore();
    const uploadConfig = {
      url: config.url.startsWith("http") ? config.url : BASE_URL + config.url,
      filePath: config.filePath,
      name: config.name,
      formData: config.formData,
      header: {
        ...config.header,
        Authorization: userStore.token ? `Bearer ${userStore.token}` : ""
      },
      success: (response) => {
        common_vendor.index.__f__("log", "at utils/request.ts:292", "[UPLOAD SUCCESS]", response);
        try {
          const data = typeof response.data === "string" ? JSON.parse(response.data) : response.data;
          if (data.success || data.code === 0) {
            resolve(data);
          } else {
            showError(data.message || "上传失败");
            reject(data);
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at utils/request.ts:306", "解析上传响应失败:", error);
          reject(error);
        }
      },
      fail: (error) => {
        common_vendor.index.__f__("error", "at utils/request.ts:311", "[UPLOAD ERROR]", error);
        showError("文件上传失败");
        reject(error);
      }
    };
    common_vendor.index.uploadFile(uploadConfig);
  });
};
exports.http = http;
exports.request = request;
exports.uploadFile = uploadFile;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/request.js.map
