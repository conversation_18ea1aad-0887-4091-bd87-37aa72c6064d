<template>
  <div class="customer-service-features">
    <h4 class="features-title">客服专员功能</h4>
    <div class="features-grid">
      <div class="feature-item" @click="sendAction('order')">
        <el-icon><ShoppingBag /></el-icon>
        <span>订单查询</span>
      </div>
      <div class="feature-item" @click="sendAction('product')">
        <el-icon><Grid /></el-icon>
        <span>产品咨询</span>
      </div>
      <div class="feature-item" @click="sendAction('refund')">
        <el-icon><CreditCard /></el-icon>
        <span>退款流程</span>
      </div>
      <div class="feature-item" @click="sendAction('human')">
        <el-icon><User /></el-icon>
        <span>人工客服</span>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { ShoppingBag, Grid, CreditCard, User } from '@element-plus/icons-vue';

export default defineComponent({
  name: 'CustomerServiceFeatures',
  
  components: {
    ShoppingBag,
    Grid,
    CreditCard,
    User
  },
  
  props: {
    digitalHuman: {
      type: Object,
      required: true,
      default: () => ({
        name: '',
        type: '',
        avatar_url: '',
        description: '',
        apiId: '',
        digital_human_id: '',
        voice_id: '',
        welcomeText: ''
      })
    }
  },
  
  emits: ['send-message'],
  
  setup(props, { emit }) {
    const sendAction = (action) => {
      switch(action) {
        case 'order':
          emit('send-message', {
            type: 'sendMessage',
            content: '查询我的订单状态'
          });
          break;
        case 'product':
          emit('send-message', {
            type: 'sendMessage',
            content: '我想了解产品的功能和规格'
          });
          break;
        case 'refund':
          emit('send-message', {
            type: 'sendMessage',
            content: '如何申请退款？'
          });
          break;
        case 'human':
          emit('send-message', {
            type: 'showCard',
            title: '人工客服',
            content: '工作时间：周一至周五 9:00-18:00<br>客服热线：400-123-4567<br>邮箱：<EMAIL>',
            actions: [
              { text: '拨打电话', primary: true },
              { text: '发送邮件', primary: false }
            ]
          });
          break;
      }
    };
    
    return {
      sendAction
    };
  }
});
</script>

<style scoped>
.customer-service-features {
  width: 100%;
}

.features-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #10b981;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.feature-item {
  background-color: rgba(16, 185, 129, 0.1);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.feature-item:hover {
  background-color: rgba(16, 185, 129, 0.2);
  transform: translateY(-2px);
}

.feature-item span {
  font-size: 14px;
  color: #f9fafb;
}

:deep(.light-theme) .feature-item {
  background-color: rgba(16, 185, 129, 0.05);
}

:deep(.light-theme) .feature-item span {
  color: #1f2937;
}

:deep(.light-theme) .feature-item:hover {
  background-color: rgba(16, 185, 129, 0.1);
}

:deep(.light-theme) .features-title {
  color: #047857;
}
</style> 