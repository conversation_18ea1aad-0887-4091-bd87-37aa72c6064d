{"name": "AI系统移动端", "appid": "__UNI__AI_SYSTEM", "description": "AI专业平台移动端应用 - 支持翻译、数字人、AI智能体等功能", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Camera": {}, "Gallery": {}, "Audio": {}, "VideoPlayer": {}, "Barcode": {}, "Share": {}, "Push": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"]}, "ios": {"capabilities": {"entitlements": {"com.apple.developer.associated-domains": ["applinks:your-domain.com"]}}}, "sdkConfigs": {"ad": {}, "push": {}, "share": {}, "oauth": {}, "payment": {}, "statics": {}}}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false}, "usingComponents": true, "permission": {"scope.userLocation": {"desc": "您的位置信息将用于提供更好的服务"}}, "requiredPrivateInfos": ["getLocation"]}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "mp-qq": {"usingComponents": true}, "h5": {"title": "AI系统移动端", "template": "index.html", "router": {"mode": "hash", "base": "./"}, "async": {"loading": "AsyncLoading", "error": "AsyncError", "delay": 20, "timeout": 60000}, "devServer": {"https": false, "disableHostCheck": true}, "publicPath": "./", "sdkConfigs": {"maps": {}}, "optimization": {"treeShaking": {"enable": true}}}}